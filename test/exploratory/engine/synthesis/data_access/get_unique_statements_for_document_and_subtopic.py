import argparse
import os

from dotenv import load_dotenv  # Import dotenv to load environment variables


class GetUniqueStatementsForDocumentAndSubtopic:

    def __init__(self, env: str):
        """Initialize with the provided environment and load the appropriate configuration."""
        self.env = env
        self.load_environment()

    def load_environment(self):
        """
        Load the .env file based on the provided environment (dev, prod, demo).
        :param env: The environment name (e.g., 'dev', 'prod', 'demo')
        """
        env_file = f"test/exploratory/.env/.env.{self.env}"  # e.g., .env.dev, .env.prod, .env.demo

        env_file_path = os.path.abspath(env_file)
        print(f"Loading environment from: {env_file_path}")

        load_dotenv(env_file, override=True)
        print(f"Loaded environment: {self.env}")

        aws_database = os.getenv("AWS_POSTGRES_DATABASE")
        print(f"AWS Postgres Database: {aws_database}")

    def run(self, input_params):
        """Runs the method for the specified environment."""
        # Now that the environment is loaded, import the module that depends on it
        from exploratory.exploratory_library import ExploratoryLibrary

        library = ExploratoryLibrary()
        return self._execute(library, input_params)

    def _execute(self, library, input_params):
        """Executes the method and returns the result as JSON."""
        result_json = library.call_method(
            "get_unique_statements_for_document_and_subtopic", input_params
        )
        return result_json


def main():
    # Define command-line arguments
    parser = argparse.ArgumentParser(
        description="Run for different environments with input parameters"
    )
    parser.add_argument(
        "--env", type=str, required=True, choices=["dev", "prod", "demo"], help="Environment to use"
    )
    parser.add_argument("--document_id", type=int, required=True, help="ID of the document")
    parser.add_argument("--subtopic_id", type=int, required=True, help="ID of the subtopic")
    parser.add_argument(
        "--statement_type_id", type=int, required=True, help="ID of the statement type"
    )

    # Parse arguments
    args = parser.parse_args()

    # Create instance of the class with the environment
    statements_processor = GetUniqueStatementsForDocumentAndSubtopic(args.env)

    # Input parameters for the method
    input_params = {
        "document_id": args.document_id,
        "subtopic_id": args.subtopic_id,
        "statement_type_id": args.statement_type_id,
    }

    # Run the process
    result_json = statements_processor.run(input_params)

    # Print the JSON result
    print(result_json)


if __name__ == "__main__":
    main()
