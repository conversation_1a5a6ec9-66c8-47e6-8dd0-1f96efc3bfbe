import json
import uuid
from datetime import datetime
from enum import Enum

from dotenv import load_dotenv
from sqlalchemy import inspect

from engine.synthesis.data_access import DataAccess


class ExploratoryLibrary:

    def __init__(self):
        """
        Initialize the ExploratoryLibrary with a specified environment.
        Load environment-specific configurations and create a session factory.
        :param env: The environment name (e.g., 'dev', 'prod', 'demo')
        """
        self.data_access = DataAccess()

    def call_method(self, method_name: str, input_params: dict):
        """
        Call a method from the DataAccess class dynamically with the given input parameters.
        method_name: Name of the method to call (e.g., 'get_unique_statements_for_document_and_subtopic')
        input_params: Dictionary containing the arguments for the method.
        """
        # Get the method from the DataAccess instance
        method = getattr(self.data_access, method_name, None)

        if not method:
            raise AttributeError(f"Method '{method_name}' not found in DataAccess class.")

        # Dynamically call the method with the input_params dictionary
        result = method(**input_params)

        # Return the result as JSON
        return self._to_json_file(result)

    def _to_json_file(self, query_results):
        """
        Convert the SQLAlchemy query results to a JSON file and return the filename.
        """

        def serialize(obj):
            if isinstance(obj, Enum):
                return obj.value  # Convert the enum to its value (string or int)
            raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")

        results = []

        for result in query_results:
            # Use SQLAlchemy inspect to get column keys dynamically
            obj_dict = {c.key: getattr(result, c.key) for c in inspect(result).mapper.column_attrs}

            # Handle enums explicitly by converting them to their value
            obj_dict["rating"] = serialize(obj_dict.get("rating"))

            results.append(obj_dict)

        # Generate a unique filename using timestamp or UUID
        filename = (
            f"query_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex}.json"
        )

        # Write the results to a file
        with open(filename, "w") as json_file:
            json.dump(results, json_file, default=serialize, indent=4)

        # Print the filename
        print(f"Results written to file: {filename}")

        return filename
