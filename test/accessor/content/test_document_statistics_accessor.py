import unittest
from datetime import datetime, UTC

from accessor.content.content_accessor import ContentAccessor
from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO


class TestDocumentStatisticsAccessor(unittest.TestCase):
    """
    Integration tests for create_document_statistics using a real DB connection.
    Ensure you have a test or dev DB set up before running.
    """

    @classmethod
    def setUpClass(cls):
        """
        Called once before any tests in this class run.
        This is where we initialize our ContentAccessor with real config,
        so that it can connect to the actual database.
        """
        cls.content_accessor = ContentAccessor(logger_name="test_logger")

    def test_01_create_statistics_valid_document(self):
        """
        Happy-path test:
        1) Use content_accessor to create stats for Document
        2) Verify the stats were created properly
        """

        # 2) Build a valid DocumentStatisticsDTO and call create_document_statistics
        stats_dto = DocumentStatisticsDTO(
            word_count=1000,
            difficult_word_count=100,
            average_sentence_length=15.2,
            average_word_length=4.7,
            flesch_grade_level=8,
            flesch_score=55,
            flesch_reading_ease="Fairly Easy",
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        )
        created_stats = self.content_accessor.create_document_statistics(
            document_id=907, stats_dto=stats_dto
        )

        # 3) Verify the newly created stats
        self.assertIsNotNone(created_stats, "Expected a non-None DocumentStatisticsDTO.")
        self.assertEqual(stats_dto.word_count, created_stats.word_count)
        self.assertEqual(stats_dto.difficult_word_count, created_stats.difficult_word_count)
        self.assertAlmostEqual(
            stats_dto.average_sentence_length, created_stats.average_sentence_length, places=2
        )
        self.assertAlmostEqual(
            stats_dto.average_word_length, created_stats.average_word_length, places=2
        )
        self.assertEqual(stats_dto.flesch_grade_level, created_stats.flesch_grade_level)
        self.assertEqual(stats_dto.flesch_score, created_stats.flesch_score)
        self.assertEqual(stats_dto.flesch_reading_ease, created_stats.flesch_reading_ease)

    def test_02_create_statistics_invalid_document(self):
        """
        Test attempting to create stats for a document_id that doesn't exist in DB.
        Expect an exception or error (depending on your model constraints).
        """
        invalid_doc_id = 9999999  # Some document ID unlikely to exist
        stats_dto = DocumentStatisticsDTO(
            word_count=500,
            difficult_word_count=10,
            average_sentence_length=12.5,
            average_word_length=4.2,
            flesch_grade_level=5,
            flesch_score=60,
            flesch_reading_ease="Readable",
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        )

        with self.assertRaises(Exception):
            # Or self.assertIsNone(...), depending on how you handle foreign key errors
            self.content_accessor.create_document_statistics(invalid_doc_id, stats_dto)

    def test_03_create_statistics_and_retrieve(self):
        """
        End-to-end test: create stats, then retrieve them via get_document_statistics_by_document_id,
        confirming the data matches.
        """

        doc_id = 906
        # 2) Create stats for that document
        stats_dto_in = DocumentStatisticsDTO(
            word_count=200,
            difficult_word_count=15,
            average_sentence_length=10.5,
            average_word_length=4.5,
            flesch_grade_level=9,
            flesch_score=65,
            flesch_reading_ease="Somewhat Easy",
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC),
        )
        stats_created = self.content_accessor.create_document_statistics(doc_id, stats_dto_in)
        self.assertIsNotNone(stats_created)

        # 3) Retrieve the stats
        stats_doc, stats_original_doc = (
            self.content_accessor.get_document_statistics_by_document_id(doc_id)
        )

        self.assertIsNotNone(stats_doc)
        self.assertIsNone(
            stats_original_doc,
            "Expected no original document stats if there's no original_doc relationship.",
        )

        # 4) Validate retrieved stats
        self.assertEqual(stats_doc.word_count, stats_dto_in.word_count)
        self.assertEqual(stats_doc.difficult_word_count, stats_dto_in.difficult_word_count)
        self.assertAlmostEqual(
            stats_doc.average_sentence_length, stats_dto_in.average_sentence_length, places=2
        )
        self.assertAlmostEqual(
            stats_doc.average_word_length, stats_dto_in.average_word_length, places=2
        )
        self.assertEqual(stats_doc.flesch_grade_level, stats_dto_in.flesch_grade_level)
        self.assertEqual(stats_doc.flesch_score, stats_dto_in.flesch_score)
        self.assertEqual(stats_doc.flesch_reading_ease, stats_dto_in.flesch_reading_ease)


if __name__ == "__main__":
    unittest.main()
