"""Tests for JobItemWorkflowAccessor."""

import concurrent.futures
import threading
from typing import Set

import pytest
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from accessor.document.job_item_workflow import JobItemWorkflow
from shared.enums.job_enums import JobI<PERSON>TypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from shared.models.job_item_workflow import JobItemWorkflow as JobItemWorkflowModel
from shared.models.job_workflow import JobWorkflow
from shared.models.workflow_type_enum import WorkflowTypeEnum
from util.database import engine as postgres_engine
from shared.models import (
    Base,
    Document,
    Job,
    JobStatus,
    JobType,
    WorkflowType,
    UniqueStatement,
    Subtopic,
    UniqueStatementSubtopicAssociation,
)

# Test configuration
NUM_ITEMS = 10
NUM_THREADS = 5
WORKFLOW_ID = 1
JOB_ID = 1
WORKFLOW_TYPE_ID = 1

@pytest.fixture
def engine():
    """Use the Postgres engine for testing."""
    return postgres_engine

@pytest.fixture
def session_factory(engine):
    """Create a session factory for the test database."""
    return sessionmaker(bind=engine)

@pytest.fixture
def session(session_factory):
    """Create a session for the test database."""
    session = session_factory()

    # Clean up any existing test data
    session.execute(text("DELETE FROM public.job_item_workflow WHERE job_workflow_id IN (SELECT job_workflow_id FROM public.job_workflow WHERE job_id IN (SELECT job_id FROM public.job WHERE reference_id = :document_id))"), {"document_id": 873})
    session.execute(text("DELETE FROM public.job_workflow WHERE job_id IN (SELECT job_id FROM public.job WHERE reference_id = :document_id)"), {"document_id": 873})
    session.execute(text("DELETE FROM public.job WHERE reference_id = :document_id"), {"document_id": 873})
    session.execute(text("DELETE FROM public.chunk_image WHERE chunk_id IN (SELECT chunk_id FROM public.chunk WHERE document_id = :document_id)"), {"document_id": 873})
    session.execute(text("DELETE FROM public.chunk WHERE document_id = :document_id"), {"document_id": 873})
    session.commit()

    # Ensure required foreign key rows exist
    # Insert job row (use ON CONFLICT DO NOTHING to avoid duplicate key errors)
    session.execute(text("INSERT INTO public.job (job_id, reference_id, job_status_id, job_type_id) VALUES (:job_id, :reference_id, :job_status_id, :job_type_id) ON CONFLICT (job_id) DO NOTHING"), {"job_id": JOB_ID, "reference_id": 873, "job_status_id": 1, "job_type_id": 1})
    # Insert workflow_type row (use ON CONFLICT DO NOTHING to avoid duplicate key errors)
    session.execute(text("INSERT INTO public.workflow_type (workflow_type_id, name) VALUES (:workflow_type_id, :name) ON CONFLICT (workflow_type_id) DO NOTHING"), {"workflow_type_id": WORKFLOW_TYPE_ID, "name": "Test Workflow Type"})
    session.commit()

    # Create test workflow
    workflow = JobWorkflow(
        job_workflow_id=WORKFLOW_ID,
        job_id=JOB_ID,
        workflow_type_id=WORKFLOW_TYPE_ID,
        status=JobWorkflowStatusEnum.STARTING,
        run_count=0
    )
    session.add(workflow)
    session.commit()

    yield session

    # Cleanup
    session.execute(text("DELETE FROM public.job_item_workflow WHERE job_workflow_id = :workflow_id"), {"workflow_id": WORKFLOW_ID})
    session.execute(text("DELETE FROM public.job_workflow WHERE job_workflow_id = :workflow_id"), {"workflow_id": WORKFLOW_ID})
    session.execute(text("DELETE FROM public.job WHERE job_id = :job_id"), {"job_id": JOB_ID})
    session.execute(text("DELETE FROM public.chunk_image WHERE chunk_id IN (SELECT chunk_id FROM public.chunk WHERE document_id = :document_id)"), {"document_id": 873})
    session.execute(text("DELETE FROM public.chunk WHERE document_id = :document_id"), {"document_id": 873})
    session.commit()
    session.close()

@pytest.fixture
def accessor(session_factory):
    """Create a JobItemWorkflowAccessor instance."""
    return JobItemWorkflow(session_factory)

def test_seed_items(accessor, session):
    """Test that seed_items creates the correct number of items."""
    # Seed items
    item_keys = [str(i) for i in range(NUM_ITEMS)]
    accessor.seed_items(WORKFLOW_ID, item_keys, JobItemTypeEnum.CHUNK)

    # Verify items were created
    items = session.query(JobItemWorkflowModel).all()
    assert len(items) == NUM_ITEMS

    # Verify each item has correct attributes
    for item in items:
        assert item.job_workflow_id == WORKFLOW_ID
        assert item.status == JobWorkflowStatusEnum.STARTING
        assert item.item_type == JobItemTypeEnum.CHUNK
        assert item.run_count == 0
        assert item.item_key in item_keys

def test_concurrent_reservations(accessor, session):
    """Test that concurrent reservations return each item exactly once."""
    # Seed items
    item_keys = [str(i) for i in range(NUM_ITEMS)]
    accessor.seed_items(WORKFLOW_ID, item_keys, JobItemTypeEnum.CHUNK)

    # Track reserved items
    reserved_items: Set[str] = set()
    reserved_items_lock = threading.Lock()

    def reserve_item():
        """Reserve an item and track it."""
        item_key = accessor.reserve_next_pending(WORKFLOW_ID)
        if item_key:
            with reserved_items_lock:
                reserved_items.add(item_key)
        return item_key

    # Use thread pool to reserve items concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        futures = [executor.submit(reserve_item) for _ in range(NUM_ITEMS * 2)]  # Try to reserve more than available
        non_none_results = [f.result() for f in futures if f.result() is not None]

    # Verify results
    assert len(non_none_results) == NUM_ITEMS  # Each item reserved exactly once
    assert len(reserved_items) == NUM_ITEMS  # No duplicates
    assert reserved_items == set(item_keys)  # All items were reserved

    # Verify no more items can be reserved
    assert accessor.reserve_next_pending(WORKFLOW_ID) is None

def test_mark_done(accessor, session):
    """Test that mark_done updates item status correctly."""
    # Seed and reserve an item
    accessor.seed_items(WORKFLOW_ID, ["test_item"], JobItemTypeEnum.CHUNK)
    item_key = accessor.reserve_next_pending(WORKFLOW_ID)
    assert item_key is not None

    # Mark as done
    accessor.mark_done(WORKFLOW_ID, item_key)

    # Verify status
    updated_item = session.query(JobItemWorkflowModel).filter_by(
        job_workflow_id=WORKFLOW_ID,
        item_key=item_key
    ).first()
    assert updated_item.status == JobWorkflowStatusEnum.COMPLETED
    assert updated_item.run_count == 1

def test_mark_failed(accessor, session):
    """Test that mark_failed updates item status correctly."""
    # Seed and reserve an item
    accessor.seed_items(WORKFLOW_ID, ["test_item"], JobItemTypeEnum.CHUNK)
    item_key = accessor.reserve_next_pending(WORKFLOW_ID)
    assert item_key is not None

    # Mark as failed
    error_msg = "Test error"
    accessor.mark_failed(WORKFLOW_ID, item_key, error_msg)

    # Verify status
    updated_item = session.query(JobItemWorkflowModel).filter_by(
        job_workflow_id=WORKFLOW_ID,
        item_key=item_key
    ).first()
    assert updated_item.status == JobWorkflowStatusEnum.FAILED
    assert updated_item.run_count == 1
    assert updated_item.last_error == error_msg

def test_list_incomplete(accessor, session):
    """Test that list_incomplete returns correct items."""
    # Seed items
    item_keys = [str(i) for i in range(NUM_ITEMS)]
    accessor.seed_items(WORKFLOW_ID, item_keys, JobItemTypeEnum.CHUNK)

    # Complete some items
    for i in range(NUM_ITEMS // 2):
        item_key = accessor.reserve_next_pending(WORKFLOW_ID)
        accessor.mark_done(WORKFLOW_ID, item_key)

    # List incomplete items
    incomplete_keys = accessor.list_incomplete(WORKFLOW_ID)
    assert len(incomplete_keys) == NUM_ITEMS // 2

    # Verify all incomplete items are in STARTING status
    for key in incomplete_keys:
        row = session.query(JobItemWorkflowModel).filter_by(item_key=key).first()
        assert row.status == JobWorkflowStatusEnum.STARTING