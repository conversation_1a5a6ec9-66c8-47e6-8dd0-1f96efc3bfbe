import os
import unittest

from util.langfuse.helpers.burst_langfuse_helpers import BurstLangfuseHelpers


@unittest.skipUnless(
    os.getenv("LANGFUSE_PUBLIC_KEY") and os.getenv("LANGFUSE_SECRET_KEY"),
    "Skipping live test: Required Lang<PERSON> credentials not set.",
)
class TestBurstLangfuseHelpers(unittest.TestCase):
    def test_prefetch_prompts_live(self):
        # Instantiate the helper class under test.
        helpers = BurstLangfuseHelpers()
        label = "latest"  # Adjust the label as needed

        try:
            prompts = helpers.prefetch_prompts(label)
        except Exception as e:
            self.fail(f"prefetch_prompts raised an exception: {e}")

        # Assert that the result is a list and that none of the items are None.
        self.assertIsInstance(prompts, list, "Expected a list of prompts")
        for prompt in prompts:
            self.assertIsNotNone(prompt, "Found a None prompt in the results")

        # Optionally, print the results for debugging.
        print(f"Retrieved {len(prompts)} prompts.")


if __name__ == "__main__":
    unittest.main()
