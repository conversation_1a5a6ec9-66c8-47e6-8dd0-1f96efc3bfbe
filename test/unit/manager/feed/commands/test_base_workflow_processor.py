"""Unit tests for BaseWorkflowProcessor."""

import unittest
from unittest.mock import MagicMock, patch

from manager.feed.commands.base_workflow_processor import BaseWorkflowProcessor
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.enums.job_enums import JobWorkflowStatusEnum
from shared.models.job_workflow import JobWorkflow
from shared.models.workflow_type_enum import WorkflowTypeEnum


class TestWorkflowProcessor(BaseWorkflowProcessor):
    """Test implementation of BaseWorkflowProcessor."""

    @property
    def workflow_type(self) -> WorkflowTypeEnum:
        return WorkflowTypeEnum.PROCESS_DOCUMENT_CHUNKS

    def _seed_item_rows(self) -> None:
        pass

    def process_item(self, item_key: str):
        pass

    def cleanup_item(self, item_key: str):
        pass


class TestBaseWorkflowProcessor(unittest.TestCase):
    """Unit tests for BaseWorkflowProcessor."""

    def setUp(self):
        """Set up test environment."""
        self.mock_dependency_factory = MagicMock(spec=DependencyFactory)
        self.mock_document_accessor = MagicMock()
        self.mock_dependency_factory.get_document_accessor.return_value = self.mock_document_accessor

        self.mock_sqs_client = MagicMock()
        self.mock_sns_client = MagicMock()
        self.mock_logger = MagicMock()

        self.processor = TestWorkflowProcessor(
            self.mock_dependency_factory,
            document_id=1,
            job_id=2,
            receipt_handle="test-receipt",
            sqs_client=self.mock_sqs_client,
            sns_client=self.mock_sns_client,
            logger=self.mock_logger,
        )

    def test_execute_creates_new_workflow(self):
        """Test execute() creates a new workflow when none exists."""
        # Setup
        self.mock_document_accessor.get_job_workflow.return_value = None
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.STARTING
        mock_workflow.job_workflow_id = 123
        self.mock_document_accessor.create_job_workflow.return_value = mock_workflow

        # Execute
        self.processor.execute()

        # Verify
        self.mock_document_accessor.get_job_workflow.assert_called_once()
        self.mock_document_accessor.create_job_workflow.assert_called_once()
        self.assertEqual(self.processor.job_workflow, mock_workflow)
        self.assertEqual(self.processor.job_workflow_id, 123)

    def test_execute_resumes_existing_workflow(self):
        """Test execute() resumes an existing workflow."""
        # Setup
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.FAILED
        mock_workflow.job_workflow_id = 123
        mock_workflow.run_count = 1
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow

        # Execute
        self.processor.execute()

        # Verify
        self.mock_document_accessor.get_job_workflow.assert_called_once()
        self.mock_document_accessor.create_job_workflow.assert_not_called()
        self.assertEqual(mock_workflow.run_count, 2)
        self.mock_document_accessor.update_job_workflow.assert_any_call(
            mock_workflow, JobWorkflowStatusEnum.RETRYING
        )

    def test_execute_skips_completed_workflow(self):
        """Test execute() skips a completed workflow."""
        # Setup
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.COMPLETED
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow

        # Execute
        self.processor.execute()

        # Verify
        self.mock_document_accessor.get_job_workflow.assert_called_once()
        self.mock_document_accessor.update_job_workflow.assert_not_called()
        self.mock_document_accessor.reserve_next_pending.assert_not_called()

    def test_seeds_items_on_first_run(self):
        """Test _seed_item_rows() is called only on first run."""
        # Setup
        self.mock_document_accessor.get_job_workflow.return_value = None
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.STARTING
        mock_workflow.job_workflow_id = 123
        self.mock_document_accessor.create_job_workflow.return_value = mock_workflow

        # Execute
        with patch.object(self.processor, "_seed_item_rows") as mock_seed:
            self.processor.execute()
            mock_seed.assert_called_once()

        # Now test resume case
        mock_workflow.status = JobWorkflowStatusEnum.FAILED
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow

        with patch.object(self.processor, "_seed_item_rows") as mock_seed:
            self.processor.execute()
            mock_seed.assert_not_called()

    def test_process_single_item_marks_done(self):
        """Test processing a single item marks it done."""
        # Setup
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.STARTING
        mock_workflow.job_workflow_id = 123
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow
        self.mock_document_accessor.reserve_next_pending.side_effect = ["item-1", None]

        # Execute
        with patch.object(self.processor, "process_item") as mock_process:
            self.processor.execute()

        # Verify
        mock_process.assert_called_once_with("item-1")
        self.mock_document_accessor.mark_done.assert_called_once_with(123, "item-1")

    def test_handles_failing_item(self):
        """Test handling a failing item continues processing."""
        # Setup
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.STARTING
        mock_workflow.job_workflow_id = 123
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow
        self.mock_document_accessor.reserve_next_pending.side_effect = ["bad-item", "good-item", None]

        # Execute
        with patch.object(self.processor, "process_item") as mock_process:
            mock_process.side_effect = [Exception("Bad item"), None]  # Fail first item
            self.processor.execute()

        # Verify
        self.mock_document_accessor.mark_failed.assert_called_once_with(123, "bad-item", "Bad item")
        self.mock_document_accessor.mark_done.assert_called_once_with(123, "good-item")

    def test_cleanup_partial_on_failed_resume(self):
        """Test _cleanup_partial is called on FAILED resume."""
        # Setup
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.FAILED
        mock_workflow.job_workflow_id = 123
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow

        # Execute
        with patch.object(self.processor, "_cleanup_partial") as mock_cleanup:
            self.processor.execute()

        # Verify
        mock_cleanup.assert_called_once()

    def test_deletes_sqs_message_on_error(self):
        """Test SQS message is deleted even when processing fails."""
        # Setup
        mock_workflow = MagicMock(spec=JobWorkflow)
        mock_workflow.status = JobWorkflowStatusEnum.STARTING
        mock_workflow.job_workflow_id = 123
        self.mock_document_accessor.get_job_workflow.return_value = mock_workflow
        self.mock_document_accessor.reserve_next_pending.side_effect = ["item-1", None]

        # Execute
        with patch.object(self.processor, "process_item") as mock_process:
            mock_process.side_effect = Exception("Processing failed")
            self.processor.execute()

        # Verify
        self.mock_sqs_client.delete_message.assert_called_once() 