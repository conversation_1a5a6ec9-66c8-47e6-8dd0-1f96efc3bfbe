# Unit tests for the CreateDocument class inside document_routes.py
import sys
from unittest.mock import MagicMock, patch

from shared.models.acceptance_criteria import AcceptanceCriteria
from shared.models.rating_enum import RatingEnum

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

import pytest
from flask import Flask
from flask_restx import Api

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.routes.acceptance_criteria_routes import AcceptanceCriteriaResource

# Import mock data
from .mock_data import (
    MOCK_API_UPDATE_ENDPOINT_REQUEST,
    MOCK_DOCUMENT_COUNTS_ALL_LEVELS,
    MOCK_GET_ACCEPTANCE_CRITERIA_BY_ID_DATA,
    MOCK_HEADERS,
)


@pytest.fixture
def client():
    app = Flask(__name__)
    api = Api(app)
    api.add_resource(
        AcceptanceCriteriaResource, "/acceptance_criteria/<int:acceptance_criteria_id>"
    )
    with app.test_client() as test_client:
        yield test_client


@pytest.fixture
def mock_content_accessor():
    with patch("api.routes.acceptance_criteria_routes.content_accessor") as mock:
        yield mock


@pytest.fixture
def mock_create_engine():
    with patch("sqlalchemy.create_engine") as mock:
        yield mock


@pytest.mark.usefixtures("client", "mock_content_accessor", "mock_create_engine")
class TestUpdateAcceptanceCriteria:
    BASE_URL = "/acceptance_criteria/1"

    @pytest.fixture(autouse=True)
    def setup_method(self, request):
        self.client = request.getfixturevalue("client")
        self.mock_content_accessor = request.getfixturevalue("mock_content_accessor")
        self.mock_create_engine = request.getfixturevalue("mock_create_engine")

    def test_acceptance_criteria_not_found(self):
        # Arrange
        # Mock `update_acceptance_criteria` to raise `NotFoundException`
        self.mock_content_accessor.update_acceptance_criteria.side_effect = NotFoundException(
            "Acceptance criteria not found"
        )

        # Act
        response = self.client.put(
            self.BASE_URL, json=MOCK_API_UPDATE_ENDPOINT_REQUEST, headers=MOCK_HEADERS
        )

        # Assert
        assert response.status_code == 404
        assert b"Acceptance criteria not found" in response.data

    def test_no_fields_to_update(self):

        # Act
        response = self.client.put(self.BASE_URL, json={}, headers=MOCK_HEADERS)

        # Assert
        assert response.status_code == 400
        assert b"No fields to update" in response.data

    def test_update_description_only(self):
        updated_description = "Updating only description field"
        self._update_single_field_test("description", updated_description, "description")

    def test_update_rating_only(self):
        updated_rating = "positive"
        self._update_single_field_test("rating", updated_rating, "rating")

    def test_update_all_fields(self):
        # Arrange
        # Update the mock data with the new value for the specified field
        # request
        updated_description = "Updated description"
        updated_rating = "negative"
        update_data_request = {
            "description": updated_description,
            "rating": updated_rating,
        }

        mock_updated_data = AcceptanceCriteria(
            acceptance_criteria_id=1,
            user_story_id=1,
            description="This is a test description",
            identifier="AC001.**************.01",
            rating=RatingEnum.negative,
            edited=False,
            user_entered=True,
        )
        setattr(mock_updated_data, "description", updated_description)
        setattr(mock_updated_data, "rating", updated_rating)

        self.mock_content_accessor.update_acceptance_criteria.return_value = mock_updated_data
        self.mock_content_accessor.get_document_id_by_acceptance_criteria_id.return_value = 1
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_DOCUMENT_COUNTS_ALL_LEVELS
        )
        self.mock_content_accessor.check_acceptance_criteria_has_children.return_value = True

        # Act
        response = self.client.put(self.BASE_URL, json=update_data_request, headers=MOCK_HEADERS)

        # Assert
        assert response.status_code == 200
        json_data = response.get_json()
        assert json_data["description"] == updated_description
        assert json_data["rating"] == updated_rating
        assert "counts_all_levels" in json_data

    def test_invalid_rating_value(self):
        # Arrange
        self.mock_content_accessor.get_acceptance_criteria_by_id.return_value = (
            MOCK_GET_ACCEPTANCE_CRITERIA_BY_ID_DATA
        )
        invalid_rating = "invalid_rating"

        # Act
        response = self.client.put(
            self.BASE_URL, json={"rating": invalid_rating}, headers=MOCK_HEADERS
        )

        # Assert
        assert response.status_code == 400
        json_data = response.get_json()
        assert json_data["error"] == "Rating must be 'negative', 'positive', or 'unrated'."

    def test_exception_handling(self):
        # Arrange
        self.mock_content_accessor.update_acceptance_criteria.side_effect = (
            ContentAccessorException("Database error")
        )

        # Act
        response = self.client.put(
            self.BASE_URL, json={"description": "New description"}, headers=MOCK_HEADERS
        )

        # Assert
        assert response.status_code == 500
        assert b"Error updating acceptance criteria: Database error" in response.data

    def test_missing_api_key(self):
        # Act
        response = self.client.put(
            self.BASE_URL, json={"description": "New description"}
        )  # No headers

        # Assert
        assert response.status_code == 401
        json_data = response.get_json()
        assert json_data["message"] == "API key is missing!"

    def test_invalid_api_key(self):
        # Act
        response = self.client.put(
            self.BASE_URL,
            json={"description": "New description"},
            headers={"X-api-key": "invalid_key"},  # Incorrect API key
        )

        # Assert
        assert response.status_code == 401
        json_data = response.get_json()
        assert json_data["message"] == "Invalid API key!"

    def test_update_no_rating_submitted(self):
        # Arrange
        # Mock the AcceptanceCriteria object with initial values
        mock_updated_data = AcceptanceCriteria(
            acceptance_criteria_id=1,
            user_story_id=1,
            description="This is a test description",  # Initial description
            identifier="AC001.**************.01",
            rating=RatingEnum.positive,  # Expected default rating
            edited=True,
            user_entered=True,
        )

        # Define the updated description to simulate the incoming request
        updated_description = "Updated description"
        update_data_request = {
            "description": updated_description,
            # Notice: no 'rating' is included here to test the default behavior
        }

        # Update the mock to reflect the new description after update
        mock_updated_data.description = updated_description

        # Set up mock responses for the accessor methods
        self.mock_content_accessor.update_acceptance_criteria.return_value = mock_updated_data
        self.mock_content_accessor.get_document_id_by_acceptance_criteria_id.return_value = 1
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_DOCUMENT_COUNTS_ALL_LEVELS
        )
        self.mock_content_accessor.check_acceptance_criteria_has_children.return_value = True

        # Act
        response = self.client.put(self.BASE_URL, json=update_data_request, headers=MOCK_HEADERS)

        # Assert
        assert response.status_code == 200, "Expected status code 200 for successful update"

        # Retrieve and inspect JSON response data
        json_data = response.get_json()
        
        # Verify the response contains the updated description
        assert (
            json_data["description"] == updated_description
        ), "Description should match the updated value"

        # Verify the rating defaults to 'positive' in the absence of a submitted rating
        assert (
            json_data["rating"] == "positive"
        ), "Rating should default to 'positive' when not specified"

        # Check that counts_all_levels data is present in the response
        assert "counts_all_levels" in json_data, "Response should include counts_all_levels data"

    def test_update_with_rating_submitted(self):
        # Arrange
        # Mock the AcceptanceCriteria object with initial values
        mock_updated_data = AcceptanceCriteria(
            acceptance_criteria_id=1,
            user_story_id=1,
            description="This is a test description",  # Initial description
            identifier="AC001.**************.01",
            rating=RatingEnum.negative,  # Updated to reflect the provided rating
            edited=True,
            user_entered=True,
        )

        # Define the updated description and rating to simulate the incoming request
        updated_description = "Updated description"
        updated_rating = "negative"  # Explicitly set rating
        update_data_request = {
            "description": updated_description,
            "rating": updated_rating,
        }

        # Update the mock to reflect the new description and rating after update
        mock_updated_data.description = updated_description
        mock_updated_data.rating = RatingEnum(updated_rating)  # Set mock rating to 'negative'

        # Set up mock responses for the accessor methods
        self.mock_content_accessor.update_acceptance_criteria.return_value = mock_updated_data
        self.mock_content_accessor.get_document_id_by_acceptance_criteria_id.return_value = 1
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_DOCUMENT_COUNTS_ALL_LEVELS
        )
        self.mock_content_accessor.check_acceptance_criteria_has_children.return_value = True

        # Act
        response = self.client.put(self.BASE_URL, json=update_data_request, headers=MOCK_HEADERS)

        # Assert
        assert response.status_code == 200, "Expected status code 200 for successful update"

        # Retrieve and inspect JSON response data
        json_data = response.get_json()

        # Verify the response contains the updated description
        assert (
            json_data["description"] == updated_description
        ), "Description should match the updated value"

        # Verify the rating is updated to 'negative' as provided in the request
        assert (
            json_data["rating"] == "negative"
        ), "Rating should be updated to 'negative' as specified in the request"

        # Check that counts_all_levels data is present in the response
        assert "counts_all_levels" in json_data, "Response should include counts_all_levels data"

    # Helpers
    def _update_single_field_test(self, field_name: str, updated_value: str, expected_field: str):
        # Arrange
        mock_updated_data = AcceptanceCriteria(
            acceptance_criteria_id=1,
            user_story_id=1,
            description="This is a test description",
            identifier="AC001.**************.01",
            rating=RatingEnum.positive,
            edited=False,
            user_entered=True,
        )
        setattr(
            mock_updated_data, field_name, updated_value
        )  # Set the updated field directly on the object

        self.mock_content_accessor.update_acceptance_criteria.return_value = mock_updated_data
        self.mock_content_accessor.get_document_id_by_acceptance_criteria_id.return_value = 1
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_DOCUMENT_COUNTS_ALL_LEVELS
        )
        self.mock_content_accessor.check_acceptance_criteria_has_children.return_value = True

        # Act
        response = self.client.put(
            self.BASE_URL, json={field_name: updated_value}, headers=MOCK_HEADERS
        )

        # Assert
        assert response.status_code == 200
        json_data = response.get_json()

        # Check that the expected field was updated correctly
        assert json_data[expected_field] == updated_value
        assert "counts_all_levels" in json_data
