# mock_data.py
from api.middleware.auth import authentication_key

# Mocked data for CreateDocument test
MOCK_DOCUMENT_RESPONSE = {
    "document_id": 1,
    "domain_id": 123,
    "name": "Test Document",
    "s3_location": "s3://bucket/test.pdf",
    "business_id": 456,
    "effective_datetime": "2024-10-25T00:00:00+00:00",
    "start_page": 1,
    "end_page": 50,
    "job_id": 789,
}

MOCK_REQUEST_DATA = {
    "domain_id": 123,
    "name": "Test Document",
    "s3_location": "s3://bucket/test.pdf",
    "business_id": 456,
    "effective_datetime": "2024-10-25T00:00:00+00:00",
    "start_page": 1,
    "end_page": 50,
}

MOCK_HEADERS = {"X-api-key": authentication_key}

MOCK_REQUEST_MINIMAL_DATA = {
    "name": "Test Document",
    "s3_location": "s3://bucket/test.pdf",
    "domain_id": 123,
    "effective_datetime": "2024-10-25T00:00:00+00:00",
}

MOCK_DOCUMENT_MINIMAL_RESPONSE = {
    "document_id": 1,
    "domain_id": 123,
    "name": "Test Document",
    "s3_location": "s3://bucket/test.pdf",
    "business_id": 1,
    "effective_datetime": "2024-10-25T00:00:00+00:00",
    "start_page": 1,
    "end_page": -1,
    "job_id": 789,
}
