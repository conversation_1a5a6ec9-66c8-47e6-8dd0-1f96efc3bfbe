# Unit tests for the CreateDocument class inside document_routes.py
import sys
from unittest.mock import MagicMock

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from unittest.mock import MagicMock, patch

import pytest
from flask import Flask
from flask_restx import Api

from api.routes.document_routes import CreateDocument  # Import your resource class

# Import mock data
from .mock_data import (
    MOCK_DOCUMENT_MINIMAL_RESPONSE,
    MOCK_DOCUMENT_RESPONSE,
    MOCK_HEADERS,
    MOCK_REQUEST_DATA,
    MOCK_REQUEST_MINIMAL_DATA,
)


@pytest.fixture
def client():
    app = Flask(__name__)
    api = Api(app)
    api.add_resource(CreateDocument, "/documents")
    with app.test_client() as test_client:
        yield test_client


@pytest.fixture
def mock_dependencies():
    with patch("sqlalchemy.create_engine") as mock_create_engine, patch(
        "api.routes.document_routes.feed_manager_service"
    ) as mock_feed_manager_service, patch(
        "api.routes.document_routes.content_accessor.save_document"
    ) as mock_save_document:

        # Set up any common arrangements
        mock_create_engine.return_value = MagicMock()
        mock_feed_manager_service.return_value = MagicMock()

        # Yield the mocks for use in the class
        yield mock_create_engine, mock_feed_manager_service, mock_save_document


@pytest.mark.usefixtures("mock_dependencies")
class TestCreateDocument:
    @pytest.fixture(autouse=True)
    def setup_method(self, request):
        # Access the mock_dependencies fixture
        self.mock_create_engine, self.mock_feed_manager_service, self.mock_save_document = (
            request.getfixturevalue("mock_dependencies")
        )
        self.client = request.getfixturevalue("client")

    def test_create_document_success(self):
        # Arrange
        self.mock_save_document.return_value = MOCK_DOCUMENT_RESPONSE

        # Act
        response = self.client.post("/documents", json=MOCK_REQUEST_DATA, headers=MOCK_HEADERS)

        # Assert
        assert response.status_code == 201

        # Check that the response JSON matches the expected dictionary
        assert response.json == MOCK_DOCUMENT_RESPONSE

        # Ensure the save_document method was called once
        self.mock_save_document.assert_called_once()

    def test_create_document_success_minimal_fields(self):
        # Arrange
        self.mock_save_document.return_value = MOCK_DOCUMENT_MINIMAL_RESPONSE

        # Act
        response = self.client.post(
            "/documents", json=MOCK_REQUEST_MINIMAL_DATA, headers=MOCK_HEADERS
        )

        # Assert
        assert response.status_code == 201

        # Check that the response JSON matches the expected dictionary
        assert response.json == MOCK_DOCUMENT_MINIMAL_RESPONSE

        # Ensure the save_document method was called once
        self.mock_save_document.assert_called_once()

    def test_create_document_missing_domain_id_required_field(
        self,
    ):
        self.run_missing_required_field_test("domain_id")

    def test_create_document_missing_name_required_field(self):
        self.run_missing_required_field_test("name")

    def test_create_document_missing_s3_location_required_field(self):
        self.run_missing_required_field_test("s3_location")

    def test_create_document_missing_effective_datetime_required_field(self):
        self.run_missing_required_field_test("effective_datetime")

    def test_create_document_invalid_domain_id_type(self):
        self.run_failing_type_check_test(
            "domain_id", "invalid_string", "Domain ID must be a non-negative integer."
        )

    def test_create_document_invalid_name_type(self):
        self.run_failing_type_check_test("name", 5, "Name must be a string.")

    def test_create_document_invalid_s3_location_type(self):
        self.run_failing_type_check_test("s3_location", 5, "S3 location must be a string.")

    def test_create_document_business_id_type(self):
        self.run_failing_type_check_test(
            "business_id", "invalid string", "Business ID must be a non-negative integer."
        )

    def test_create_document_start_page_type(self):
        self.run_failing_type_check_test(
            "start_page",
            "invalid string",
            "Start page must be an integer greater than or equal to 1.",
        )

    def test_create_document_end_page_type(self):
        self.run_failing_type_check_test(
            "end_page",
            "invalid string",
            "End page must be -1 or an integer greater than or equal to 1.",
        )

    def test_create_document_boundary_domain_id_neg_1(self):
        self.run_failing_type_check_test(
            "domain_id", -1, "Domain ID must be a non-negative integer."
        )

    def test_create_document_boundary_business_id_neg_1(self):
        self.run_failing_type_check_test(
            "business_id", -1, "Business ID must be a non-negative integer."
        )

    def test_create_document_boundary_start_page_neg_1(self):
        self.run_failing_type_check_test(
            "start_page", -1, "Start page must be an integer greater than or equal to 1."
        )

    def test_create_document_boundary_start_page_zero(self):
        self.run_failing_type_check_test(
            "start_page", 0, "Start page must be an integer greater than or equal to 1."
        )

    def test_create_document_boundary_end_page_zero(self):
        self.run_failing_type_check_test(
            "end_page", 0, "End page must be -1 or an integer greater than or equal to 1."
        )

    # Helper methods
    def run_missing_required_field_test(self, missing_field):
        # Arrange
        # Remove the specified required field from the request data
        incomplete_data = MOCK_REQUEST_DATA.copy()
        incomplete_data.pop(missing_field)

        # Act
        response = self.client.post("/documents", json=incomplete_data, headers=MOCK_HEADERS)

        # Assert
        assert response.status_code == 400
        assert f"Missing required fields: {missing_field}" in response.json["error"]
        self.mock_save_document.assert_not_called()

    def run_failing_type_check_test(self, field, invalid_value, expected_error):
        # Arrange: Modify the request data to set the field to an invalid type
        invalid_data = MOCK_REQUEST_DATA.copy()
        invalid_data[field] = invalid_value

        # Act: Send the request with invalid data
        response = self.client.post("/documents", json=invalid_data, headers=MOCK_HEADERS)

        # Assert: Check if the response status is 400 and contains the expected error message
        assert response.status_code == 400
        assert expected_error in response.json.get("error", "")
        self.mock_save_document.assert_not_called()
