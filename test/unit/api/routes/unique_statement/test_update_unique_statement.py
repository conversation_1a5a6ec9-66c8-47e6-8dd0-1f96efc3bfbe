import sys
from unittest.mock import MagicMock, patch

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

import pytest
from flask import Flask
from flask_restx import Api

from api.routes.unique_statement_routes import SingleUniqueStatement

# Import mock data
from .mock_data import (
    MOCK_HEADERS,
    MOCK_SUBTOPIC,
    MOCK_UNIQUE_STATEMENT_REQUEST,
    MOCK_UNIQUE_STATEMENT_RESPONSE,
    MOCK_UPDATE_COUNTS_ALL_LEVELS_RETURN_VALUE,
    MOCK_UPDATE_COUNTS_RETURN_VALUE,
    MOCK_UNIQUE_STATEMENT,
    MOCK_UNIQUE_STATEMENT_OPTIONAL_FIELD_DEFAULTS,
)


@pytest.fixture
def client():
    app = Flask(__name__)
    api = Api(app)
    api.add_resource(SingleUniqueStatement, "/unique-statement/<int:unique_statement_id>")
    with app.test_client() as test_client:
        yield test_client


@pytest.fixture
def mock_content_accessor():
    with patch("api.routes.unique_statement_routes.content_accessor") as mock:
        yield mock


@pytest.fixture
def mock_create_engine():
    with patch("sqlalchemy.create_engine") as mock:
        yield mock


@pytest.mark.usefixtures("client", "mock_content_accessor", "mock_create_engine")
class TestUpdateUniqueStatement:
    BASE_URL = "/unique-statement/1"

    @pytest.fixture(autouse=True)
    def setup_method(self, request):
        self.client = request.getfixturevalue("client")
        self.mock_content_accessor = request.getfixturevalue("mock_content_accessor")
        self.mock_create_engine = request.getfixturevalue("mock_create_engine")

    def test_update_unique_statement_success(self):
        # Arrange
        self.mock_content_accessor.update_unique_statement.return_value = MOCK_UNIQUE_STATEMENT
        self.mock_content_accessor.get_document_unique_requirement_counts.return_value = (
            MOCK_UPDATE_COUNTS_RETURN_VALUE
        )
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_UPDATE_COUNTS_ALL_LEVELS_RETURN_VALUE
        )

        self.mock_content_accessor.get_document_counts_all_levels.return_value = {
            "unrated_count": 0,
            "positive_count": 1,
            "negative_count": 0,
        }

        # Act
        response = self.client.put(
            self.BASE_URL,
            json=MOCK_UNIQUE_STATEMENT_REQUEST,
            headers=MOCK_HEADERS,
        )

        # Assert

        # Parse the JSON response
        data = response.json

        assert response.status_code == 200

        # Reference values from the mock data directly
        assert data["unique_statement_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["unique_statement_id"]
        assert data["domain_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["domain_id"]
        assert data["document_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["document_id"]
        assert data["statement_type_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["statement_type_id"]
        assert data["text"] == MOCK_UNIQUE_STATEMENT_RESPONSE["text"]
        assert data["identifier"] == MOCK_UNIQUE_STATEMENT_RESPONSE["identifier"]
        assert data["rating"] == MOCK_UNIQUE_STATEMENT_RESPONSE["rating"]
        assert data["user_entered"] == MOCK_UNIQUE_STATEMENT_RESPONSE["user_entered"]
        assert data["edited"] == MOCK_UNIQUE_STATEMENT_RESPONSE["edited"]

        # Verify counts
        for count_type in ["unrated_count", "positive_count", "negative_count"]:
            assert (
                data["counts_all_levels"][count_type]
                == self.mock_content_accessor.get_document_counts_all_levels.return_value[
                    count_type
                ]
            )

        # Verify subtopics by referencing mock data directly
        assert len(data["subtopics"]) == len(MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"])
        for i, subtopic in enumerate(data["subtopics"]):
            expected_subtopic = MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]
            assert subtopic["subtopic_id"] == expected_subtopic["subtopic_id"]
            assert (
                subtopic["name"] == expected_subtopic["subtopic_name"]
            ), f"Subtopic name mismatch at index {i}. {subtopic['name']} doesn't match {expected_subtopic['subtopic_name']}"
            assert subtopic["topic_name"] == expected_subtopic["topic_name"]

    def test_invalid_statement_type_id_type(self):
        self._assert_invalid_type(
            field_name="statement_type_id",
            invalid_value=5.5,  # float instead of int
            expected_error="statement_type_id must be a non-negative integer and <= 2147483647.",
        )

    def test_invalid_subtopic_id_type(self):
        # Create a copy of the mock request data with an invalid subtopic_id type in subtopics
        invalid_request_data = MOCK_UNIQUE_STATEMENT_REQUEST.copy()
        invalid_request_data["subtopics"] = [
            {"subtopic_id": "invalid_id", "is_primary": True}
        ]  # string instead of int

        # Act
        response = self.client.put(
            self.BASE_URL,
            json=invalid_request_data,
            headers=MOCK_HEADERS,
        )

        # Assert
        assert response.status_code == 400
        assert (
            "Each subtopic must have a valid non-negative integer subtopic_id."
            in response.json["error"]
        )

    def test_invalid_text_type_non_string(self):
        self._assert_invalid_type(
            field_name="text",
            invalid_value=123,  # int instead of string
            expected_error="Text cannot be blank and must be a string.",
        )

    def test_invalid_text_type_empty_string(self):
        self._assert_invalid_type(
            field_name="text",
            invalid_value="",  # empty string
            expected_error="Text cannot be blank and must be a string.",
        )

    def test_invalid_rating_value(self):
        self._assert_invalid_type(
            field_name="rating",
            invalid_value="unknown",  # invalid rating option
            expected_error="Rating must be 'negative', 'positive', or 'unrated'.",
        )

    def test_successful_update_with_optional_fields(self):
        # Arrange
        self.mock_content_accessor.update_unique_statement.return_value = (
            MOCK_UNIQUE_STATEMENT_OPTIONAL_FIELD_DEFAULTS
        )
        self.mock_content_accessor.get_document_unique_requirement_counts.return_value = (
            MOCK_UPDATE_COUNTS_RETURN_VALUE
        )
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_UPDATE_COUNTS_ALL_LEVELS_RETURN_VALUE
        )
        self.mock_content_accessor.get_document_counts_all_levels.return_value = {
            "unrated_count": 0,
            "positive_count": 1,
            "negative_count": 0,
        }

        # Act: Send PUT request with all fields
        response = self.client.put(
            self.BASE_URL,
            json=MOCK_UNIQUE_STATEMENT_REQUEST,
            headers=MOCK_HEADERS,
        )

        # Assert: Check status code and response data
        assert response.status_code == 200
        data = response.json

        # Validate required and optional fields in response against MOCK_UNIQUE_STATEMENT_RESPONSE
        assert data["unique_statement_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["unique_statement_id"]
        assert data["document_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["document_id"]
        assert data["statement_type_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["statement_type_id"]
        assert data["text"] == MOCK_UNIQUE_STATEMENT_RESPONSE["text"]
        assert data["domain_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["domain_id"]

        # Validate other attributes in response
        assert data["rating"] == MOCK_UNIQUE_STATEMENT_RESPONSE["rating"]
        assert data["user_entered"] == MOCK_UNIQUE_STATEMENT_RESPONSE["user_entered"]
        assert data["edited"] == MOCK_UNIQUE_STATEMENT_RESPONSE["edited"]

        # Validate counts
        for count_type in ["unrated_count", "positive_count", "negative_count"]:
            assert (
                data["counts"][count_type] == MOCK_UNIQUE_STATEMENT_RESPONSE["counts"][count_type]
            ), f"{count_type} should match"

        # Validate subtopics
        assert len(data["subtopics"]) == len(MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"])
        for i, subtopic in enumerate(data["subtopics"]):
            assert (
                subtopic["subtopic_id"]
                == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["subtopic_id"]
            )
            assert (
                subtopic["name"] == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["subtopic_name"]
            )
            assert (
                subtopic["description"]
                == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["description"]
            ), f"{subtopic['description']} doesn't equal {MOCK_UNIQUE_STATEMENT_RESPONSE['subtopics'][i]['description']}"
            assert (
                subtopic["topic_name"]
                == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["topic_name"]
            )

    def test_update_unique_statement_missing_optional_fields(self):
        # Arrange: Prepare request data without optional fields
        required_fields_only_request = MOCK_UNIQUE_STATEMENT_REQUEST.copy()

        # Mock the return value of save_unique_statement to return MOCK_UNIQUE_STATEMENT_RESPONSE
        self.mock_content_accessor.update_unique_statement.return_value = (
            MOCK_UNIQUE_STATEMENT_OPTIONAL_FIELD_DEFAULTS
        )
        self.mock_content_accessor.get_document_unique_requirement_counts.return_value = (
            MOCK_UPDATE_COUNTS_RETURN_VALUE
        )
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_UPDATE_COUNTS_ALL_LEVELS_RETURN_VALUE
        )
        self.mock_content_accessor.get_document_counts_all_levels.return_value = {
            "unrated_count": 0,
            "positive_count": 1,
            "negative_count": 0,
        }

        # Remove optional fields
        optional_fields = ["domain_id", "identifier", "rating", "user_entered"]
        for field in optional_fields:
            required_fields_only_request.pop(field, None)

        # Act
        response = self.client.put(
            self.BASE_URL,
            json=required_fields_only_request,
            headers=MOCK_HEADERS,
        )

        # Assert
        assert response.status_code == 200, "Creation should succeed even without optional fields"
        data = response.json

        # Validate required and optional fields in response against MOCK_UNIQUE_STATEMENT_RESPONSE
        assert data["unique_statement_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["unique_statement_id"]
        assert data["document_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["document_id"]
        assert data["statement_type_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["statement_type_id"]
        assert data["text"] == MOCK_UNIQUE_STATEMENT_RESPONSE["text"]
        assert data["domain_id"] == MOCK_UNIQUE_STATEMENT_RESPONSE["domain_id"]

        # Validate other attributes in response
        assert data["rating"] == MOCK_UNIQUE_STATEMENT_RESPONSE["rating"]
        assert data["user_entered"] == MOCK_UNIQUE_STATEMENT_RESPONSE["user_entered"]
        assert data["edited"] == MOCK_UNIQUE_STATEMENT_RESPONSE["edited"]

        # Validate counts
        for count_type in ["unrated_count", "positive_count", "negative_count"]:
            assert (
                data["counts"][count_type] == MOCK_UNIQUE_STATEMENT_RESPONSE["counts"][count_type]
            ), f"{count_type} should match"

        # Validate subtopics
        assert len(data["subtopics"]) == len(MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"])
        for i, subtopic in enumerate(data["subtopics"]):
            assert (
                subtopic["subtopic_id"]
                == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["subtopic_id"]
            )
            assert (
                subtopic["name"] == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["subtopic_name"]
            )
            assert (
                subtopic["description"]
                == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["description"]
            ), f"{subtopic['description']} doesn't equal {MOCK_UNIQUE_STATEMENT_RESPONSE['subtopics'][i]['description']}"
            assert (
                subtopic["topic_name"]
                == MOCK_UNIQUE_STATEMENT_RESPONSE["subtopics"][i]["topic_name"]
            )

    #     def test_boundary_values_for_integer_fields(self):
    #         # Arrange
    #         self.mock_content_accessor.save_unique_statement.return_value = (
    #             MOCK_UNIQUE_STATEMENT_RESPONSE
    #         )

    #         # Define boundary values for testing
    #         valid_min_value = 0
    #         valid_max_value = 2147483647
    #         invalid_negative_value = -1
    #         invalid_above_max_value = 2147483648

    #         # Test each integer field with valid and invalid boundary values
    #         for field in ["document_id", "statement_type_id"]:
    #             self._test_valid_boundaries_for_field(field, valid_min_value, valid_max_value)
    #             self._test_invalid_boundaries_for_field(
    #                 field, invalid_negative_value, invalid_above_max_value
    #             )

    #         # Test subtopic_id within subtopics list
    #         self._test_valid_boundaries_for_subtopic_id(valid_min_value, valid_max_value)
    #         self._test_invalid_boundaries_for_subtopic_id(
    #             invalid_negative_value, invalid_above_max_value
    #         )

    #     def test_no_primary_subtopic(self):
    #         # Arrange: Create a request with no primary subtopic
    #         request_data = MOCK_UNIQUE_STATEMENT_REQUEST.copy()
    #         request_data["subtopics"] = [
    #             {"subtopic_id": 1, "is_primary": False},
    #             {"subtopic_id": 2, "is_primary": False},
    #         ]

    #         # Act
    #         response = self.client.post(
    #             self.BASE_URL,
    #             json=request_data,
    #             headers=MOCK_HEADERS,
    #         )

    #         # Assert
    #         assert response.status_code == 400
    #         assert "At least one subtopic must be marked as primary." in response.json["error"]

    #     def test_multiple_primary_subtopics(self):
    #         # Arrange: Create a request with more than one primary subtopic
    #         request_data = MOCK_UNIQUE_STATEMENT_REQUEST.copy()
    #         request_data["subtopics"] = [
    #             {"subtopic_id": 1, "is_primary": True},
    #             {"subtopic_id": 2, "is_primary": True},
    #         ]

    #         # Act
    #         response = self.client.post(
    #             self.BASE_URL,
    #             json=request_data,
    #             headers=MOCK_HEADERS,
    #         )

    #         # Assert
    #         assert response.status_code == 400
    #         assert "Only one subtopic can be marked as primary." in response.json["error"]

    # Helper methods

    def _assert_invalid_type(self, field_name, invalid_value, expected_error):
        # Arrange: Copy valid request data and set the specified field to an invalid type
        request_data = MOCK_UNIQUE_STATEMENT_REQUEST.copy()
        request_data[field_name] = invalid_value

        # Act
        response = self.client.put(
            self.BASE_URL,
            json=request_data,
            headers=MOCK_HEADERS,
        )

        # Assert
        assert response.status_code == 400
        assert expected_error in response.json["error"]


#     def _test_valid_boundaries_for_field(self, field, min_value, max_value):
#         """Test that the field accepts minimum and maximum valid values."""
#         # Test minimum valid value
#         response = self._send_request_with_value(field, min_value)
#         assert response.status_code == 201, f"{field} should accept minimum valid value {min_value}"

#         # Test maximum valid value
#         response = self._send_request_with_value(field, max_value)
#         assert response.status_code == 201, f"{field} should accept maximum valid value {max_value}"

#     def _test_invalid_boundaries_for_field(self, field, negative_value, above_max_value):
#         """Test that the field rejects negative and above maximum values."""
#         # Test negative value
#         response = self._send_request_with_value(field, negative_value)
#         assert (
#             response.status_code == 400
#         ), f"{field} should not accept negative value ({negative_value})"
#         assert (
#             f"{field} must be a non-negative integer and <= 2147483647." in response.json["error"]
#         )

#         # Test above maximum valid value
#         response = self._send_request_with_value(field, above_max_value)
#         assert response.status_code == 400, f"{field} cannot exceed ({above_max_value})"
#         assert (
#             f"{field} must be a non-negative integer and <= 2147483647." in response.json["error"]
#         )

#     def _test_valid_boundaries_for_subtopic_id(self, min_value, max_value):
#         """Test that subtopic_id within subtopics list accepts minimum and maximum valid values."""
#         response = self._send_request_with_value("subtopic_id", min_value, is_subtopic=True)
#         assert (
#             response.status_code == 201
#         ), f"subtopic_id should accept minimum valid value {min_value}"

#         response = self._send_request_with_value("subtopic_id", max_value, is_subtopic=True)
#         assert (
#             response.status_code == 201
#         ), f"subtopic_id should accept maximum valid value {max_value}"

#     def _test_invalid_boundaries_for_subtopic_id(self, negative_value, above_max_value):
#         """Test that subtopic_id within subtopics list rejects negative and above maximum values."""
#         response = self._send_request_with_value("subtopic_id", negative_value, is_subtopic=True)
#         assert (
#             response.status_code == 400
#         ), f"subtopic_id should not accept negative value ({negative_value})"
#         assert (
#             "Each subtopic must have a valid non-negative integer subtopic_id."
#             in response.json["error"]
#         )

#         response = self._send_request_with_value("subtopic_id", above_max_value, is_subtopic=True)
#         assert response.status_code == 400, f"subtopic_id cannot exceed ({above_max_value})"
#         assert "subtopic_id cannot exceed 2147483647." in response.json["error"]

#     def _send_request_with_value(self, field_name, value, is_subtopic=False):
#         """Helper to send a POST request with a specific field value."""
#         # Copy the base request data
#         request_data = MOCK_UNIQUE_STATEMENT_REQUEST.copy()

#         if is_subtopic:
#             # Modify `subtopic_id` within `subtopics` list
#             request_data["subtopics"] = [{"subtopic_id": value, "is_primary": True}]
#         else:
#             # Modify integer field directly
#             request_data[field_name] = value

#         # Send the request and return the response
#         return self.client.post(
#             self.BASE_URL,
#             json=request_data,
#             headers=MOCK_HEADERS,
#         )


# # TODO unit tests
# # 1. Boundary Tests for String Length
# #   Test text and identifier fields with boundary values for string lengths:
# #   Empty string (if allowed).
# #   Maximum allowable string length (if there’s a constraint).
# #   Exceeding maximum length (if applicable).
# # 2. Duplicate Text with Different Subtopics
# #   Test creating a unique statement with the same text and statement_type_id but a different subtopic_id, and verify it’s processed correctly with a new subtopic association.

# # 3. Duplicate Text and Subtopic
# #   Attempt to create a unique statement with the same text, statement_type_id, and subtopic_id to ensure it doesn’t create a duplicate record.

# # 4. Valid Request with All Fields
# #   Test a successful creation with all fields provided, including optional fields like domain_id, identifier, rating, and user_entered.

# # 5. Test with Non-standard Characters in Text
# #   Test the text field with special characters, unicode, or emojis to ensure they are handled appropriately.

# # 6. Test Invalid Enum Value for Rating
# #   Verify that an invalid enum value in rating (other than "positive", "negative", or "unrated") results in a validation error.

# # 7. Database Exception Handling
# #   Simulate a database error (e.g., by forcing a session rollback) to ensure that the application correctly handles exceptions and returns the appropriate error message.
