import io
import json
from datetime import datetime
from test.unit.utils.test_auth_utils import mock_auth, mock_user_ctx  # noqa: F401
from unittest.mock import patch

import pytest
from flask import Flask
from flask_restx import Api
from werkzeug.datastructures import FileStorage, MultiDict

from api.routes.file_routes import file_namespace
from shared.enums.job_enums import JobStatusEnum, JobTypeEnum
from shared.models import Document, Job
from util.audit.user_ctx import set_user_ctx

# Mock data
MOCK_DOCUMENT = Document(
    document_id=1,
    name="test_document.pdf",
    business_id=1,
    start_page=1,
    end_page=10,
    effective_datetime=datetime.now(),
    s3_location="s3://test-bucket/test_document.pdf",
)

MOCK_JOB = Job(
    job_id=1,
    reference_id=1,
    job_status_id=JobStatusEnum.INITIAL_PROCESSING.value,
    job_type_id=JobTypeEnum.ARTIFACT_GENERATION.value,
)


@pytest.fixture
def client(mock_auth, mock_user_ctx):
    with mock_auth("api.routes.file_routes"):

        app = Flask(__name__)
        api = Api(app)
        api.add_namespace(file_namespace)

        # Set up the user context before each request
        @app.before_request
        def before_request():
            set_user_ctx(mock_user_ctx.return_value)

        with app.test_client() as test_client:
            yield test_client


@pytest.fixture
def client_without_auth(mock_user_ctx):

    app = Flask(__name__)
    api = Api(app)
    api.add_namespace(file_namespace)

    # Set up the user context before each request
    @app.before_request
    def before_request():
        set_user_ctx(mock_user_ctx.return_value)

    with app.test_client() as test_client:
        yield test_client


@pytest.fixture
def mock_content_accessor():
    with patch("api.routes.file_routes.content_accessor") as mock:
        yield mock


@pytest.fixture
def mock_feed_manager_service():
    with patch("api.routes.file_routes.feed_manager_service") as mock:
        mock.submit_initial_document.return_value = {"status": "success"}
        yield mock


@pytest.fixture
def mock_aws_service():
    with patch("api.routes.file_routes.aws_service") as mock:
        mock.upload_file_to_s3.return_value = ("s3://test-bucket/test_document.pdf", None)
        yield mock


@pytest.fixture
def mock_file_upload():
    with patch("api.routes.file_routes.file_upload") as mock:
        mock.is_extension_allowed.return_value = True
        mock.get_file_extension.return_value = "pdf"
        yield mock


@pytest.fixture
def mock_create_document():
    with patch("api.routes.file_routes.document_accessor.create_document") as mock:
        mock.return_value = {"document_id": 1}
        yield mock


@pytest.fixture
def mock_create_document_and_job():
    with patch("api.routes.file_routes.document_accessor.create_document_and_job") as mock:
        mock.return_value = {"document_id": 2, "job_id": 3}
        yield mock


@pytest.mark.usefixtures(
    "client",
    "client_without_auth",
    "mock_content_accessor",
    "mock_user_ctx",
    "mock_file_upload",
    "mock_create_document",
    "mock_create_document_and_job",
)
class TestCompareFileVersions:
    BASE_URL = "/file/compare-file-versions"

    @pytest.fixture(autouse=True)
    def assign_dependencies(self, request):
        self.client = request.getfixturevalue("client")
        self.client_without_auth = request.getfixturevalue("client_without_auth")
        self.mock_content_accessor = request.getfixturevalue("mock_content_accessor")

    def test_compare_files_success(self, mock_user_ctx):
        """Test successful file comparison"""

        test_files = [
            FileStorage(
                stream=io.BytesIO(b"test file content 1"),
                filename="test1.pdf",
                content_type="application/pdf",
            ),
            FileStorage(
                stream=io.BytesIO(b"test file content 2"),
                filename="test2.pdf",
                content_type="application/pdf",
            ),
        ]

        form_data = MultiDict(
            [
                ("name", "comparison_test"),
                ("job_type_id", str(JobTypeEnum.VERSION_COMPARISON.value)),
            ]
        )

        # Create a MultiDict for the files
        files = MultiDict([("files", test_files[0]), ("files", test_files[1])])

        # Combine form data and files
        data = MultiDict(form_data)
        data.update(files)

        response = self.client.post(
            self.BASE_URL,
            data=data,
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 201
        response_data = json.loads(response.data)
        assert "document_id" in response_data
        assert response_data["document_id"] == 2
        assert "job_id" in response_data
        assert response_data["job_id"] == 3

    def test_compare_files_no_files(self, mock_user_ctx):
        """Test file comparison with no files"""

        data = MultiDict(
            [
                ("name", "comparison_test"),
                ("job_type_id", str(JobTypeEnum.VERSION_COMPARISON.value)),
            ]
        )

        response = self.client.post(
            self.BASE_URL,
            data=data,
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "message" in response_data
        assert "No files uploaded" in response_data["message"]

    def test_compare_files_single_file(self, mock_user_ctx):
        """Test file comparison with only one file"""

        test_file = FileStorage(
            stream=io.BytesIO(b"test file content"),
            filename="test.pdf",
            content_type="application/pdf",
        )

        form_data = MultiDict(
            [
                ("name", "comparison_test"),
                ("job_type_id", str(JobTypeEnum.VERSION_COMPARISON.value)),
            ]
        )

        files = MultiDict([("files", test_file)])
        data = MultiDict(form_data)
        data.update(files)

        response = self.client.post(
            self.BASE_URL,
            data=data,
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "message" in response_data
        assert "Please upload at least two files" in response_data["message"]

    def test_compare_files_invalid_extension(self, mock_user_ctx, mock_file_upload):
        """Test file comparison with invalid file extension"""
        mock_file_upload.is_extension_allowed.return_value = False

        test_files = [
            FileStorage(
                stream=io.BytesIO(b"test file content 1"),
                filename="test1.txt",
                content_type="text/plain",
            ),
            FileStorage(
                stream=io.BytesIO(b"test file content 2"),
                filename="test2.txt",
                content_type="text/plain",
            ),
        ]

        form_data = MultiDict(
            [
                ("name", "comparison_test"),
                ("job_type_id", str(JobTypeEnum.VERSION_COMPARISON.value)),
            ]
        )

        files = MultiDict([("files", test_files[0]), ("files", test_files[1])])
        data = MultiDict(form_data)
        data.update(files)

        response = self.client.post(
            self.BASE_URL,
            data=data,
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "message" in response_data
        assert "File validation error" in response_data["message"]

    def test_compare_files_missing_required_fields(self, mock_user_ctx):
        """Test file comparison with missing required fields"""

        test_files = [
            FileStorage(
                stream=io.BytesIO(b"test file content 1"),
                filename="test1.pdf",
                content_type="application/pdf",
            ),
            FileStorage(
                stream=io.BytesIO(b"test file content 2"),
                filename="test2.pdf",
                content_type="application/pdf",
            ),
        ]

        # Missing name field
        form_data = MultiDict([("job_type_id", str(JobTypeEnum.VERSION_COMPARISON.value))])

        files = MultiDict([("files", test_files[0]), ("files", test_files[1])])
        data = MultiDict(form_data)
        data.update(files)

        response = self.client.post(
            self.BASE_URL,
            data=data,
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "error" in response_data
        assert "name" in response_data["error"].lower()

    def test_compare_files_with_optional_fields(self, mock_user_ctx):
        """Test file comparison with optional fields"""

        test_files = [
            FileStorage(
                stream=io.BytesIO(b"test file content 1"),
                filename="test1.pdf",
                content_type="application/pdf",
            ),
            FileStorage(
                stream=io.BytesIO(b"test file content 2"),
                filename="test2.pdf",
                content_type="application/pdf",
            ),
        ]

        form_data = MultiDict(
            [
                ("name", "comparison_test"),
                ("job_type_id", str(JobTypeEnum.VERSION_COMPARISON.value)),
                ("business_id", "2"),
                ("start_page", "1"),
                ("end_page", "10"),
            ]
        )

        files = MultiDict([("files", test_files[0]), ("files", test_files[1])])
        data = MultiDict(form_data)
        data.update(files)

        response = self.client.post(
            self.BASE_URL,
            data=data,
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 201
        response_data = json.loads(response.data)
        assert "document_id" in response_data
        assert response_data["document_id"] == 2
        assert "job_id" in response_data
        assert response_data["job_id"] == 3

    def test_missing_api_key(self):
        """Test request without API key"""
        response = self.client_without_auth.post(
            self.BASE_URL,
            json={"description": "New description"},
        )  # No headers

        assert response.status_code == 401
        json_data = response.get_json()
        assert json_data["message"] == "Missing auth token"

    def test_invalid_api_key(self):
        """Test request with invalid API key"""
        response = self.client_without_auth.post(
            self.BASE_URL,
            json={"description": "New description"},
            headers={"X-api-key": "invalid_key"},  # Incorrect API key
        )

        assert response.status_code == 401
        json_data = response.get_json()
        assert json_data["message"] == "Missing auth token"
