import io
import json
from datetime import datetime
from test.unit.utils.test_auth_utils import mock_auth, mock_identity_accessor, mock_user_ctx
from unittest.mock import patch

import pytest
from flask import Flask
from flask_restx import Api

from api.routes.file_routes import FileUpload, file_namespace
from shared.enums.job_enums import JobStatusEnum, JobTypeEnum
from shared.models import Document, Job
from util.audit.user_ctx import set_user_ctx

# Mock data
MOCK_DOCUMENT = Document(
    document_id=1,
    name="test_document.pdf",
    domain_id=1,
    business_id=1,
    start_page=1,
    end_page=10,
    effective_datetime=datetime.now(),
    s3_location="s3://test-bucket/test_document.pdf",
)

MOCK_JOB = Job(
    job_id=1,
    reference_id=1,
    job_status_id=JobStatusEnum.INITIAL_PROCESSING.value,
    job_type_id=JobTypeEnum.ARTIFACT_GENERATION.value,
)


@pytest.fixture
def client(mock_auth, mock_user_ctx):
    with mock_auth("api.routes.file_routes"):
        from api.routes.file_routes import FileUpload, file_namespace
        from util.audit.user_ctx import set_user_ctx

        app = Flask(__name__)
        api = Api(app)
        api.add_namespace(file_namespace)

        # Set up the user context before each request
        @app.before_request
        def before_request():
            set_user_ctx(mock_user_ctx.return_value)

        with app.test_client() as test_client:
            yield test_client


@pytest.fixture
def client_without_auth(mock_user_ctx):
    from api.routes.file_routes import FileUpload, file_namespace
    from util.audit.user_ctx import set_user_ctx

    app = Flask(__name__)
    api = Api(app)
    api.add_namespace(file_namespace)

    # Set up the user context before each request
    @app.before_request
    def before_request():
        set_user_ctx(mock_user_ctx.return_value)

    with app.test_client() as test_client:
        yield test_client


@pytest.fixture
def mock_content_accessor():
    with patch("api.routes.file_routes.content_accessor") as mock:
        mock.save_document.return_value = MOCK_DOCUMENT
        mock.save_job.return_value = MOCK_JOB
        yield mock


@pytest.fixture
def mock_feed_manager_service():
    with patch("api.routes.file_routes.feed_manager_service") as mock:
        mock.submit_initial_document.return_value = {"status": "success"}
        yield mock


@pytest.fixture
def mock_aws_service():
    with patch("api.routes.file_routes.aws_service") as mock:
        mock.upload_file_to_s3.return_value = ("s3://test-bucket/test_document.pdf", None)
        yield mock


@pytest.fixture
def mock_file_upload():
    with patch("api.routes.file_routes.file_upload") as mock:
        mock.is_extension_allowed.return_value = True
        mock.get_file_extension.return_value = "pdf"
        yield mock


class TestFileUpload:
    BASE_URL = "/file/upload-file"

    @pytest.fixture(autouse=True)
    def assign_dependencies(self, request):
        self.client = request.getfixturevalue("client")
        self.client_without_auth = request.getfixturevalue("client_without_auth")
        self.mock_content_accessor = request.getfixturevalue("mock_content_accessor")
        self.mock_feed_manager_service = request.getfixturevalue("mock_feed_manager_service")
        self.mock_aws_service = request.getfixturevalue("mock_aws_service")

    def test_upload_file_success(self, mock_user_ctx):
        """Test successful file upload"""
        # Configure mocks
        self.mock_content_accessor.save_document.return_value = MOCK_DOCUMENT
        self.mock_content_accessor.save_job.return_value = MOCK_JOB
        self.mock_feed_manager_service.submit_initial_document.return_value = {"status": "success"}
        self.mock_aws_service.upload_file_to_s3.return_value = (
            "s3://test-bucket/test_document.pdf",
            None,
        )

        test_file = (io.BytesIO(b"test file content"), "test.pdf")
        data = {
            "name": "test_document.pdf",
            "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
            "business_id": 1,
            "start_page": 1,
            "end_page": 10,
        }

        response = self.client.post(
            "/file/upload-file",
            data={**data, "file": test_file},
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 201
        response_data = json.loads(response.data)
        assert "document_id" in response_data
        assert "job_id" in response_data
        assert response_data["success"] == "File uploaded and records created successfully"

        # Verify mock calls
        self.mock_aws_service.upload_file_to_s3.assert_called_once()
        self.mock_content_accessor.save_document.assert_called_once()
        self.mock_content_accessor.save_job.assert_called_once()
        self.mock_feed_manager_service.submit_initial_document.assert_called_once_with(
            MOCK_DOCUMENT.document_id, MOCK_JOB.job_id
        )

    def test_upload_file_missing_required_fields(self, mock_user_ctx):
        """Test file upload with missing required fields"""
        test_file = (io.BytesIO(b"test file content"), "test.pdf")
        data = {"job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value}

        response = self.client.post(
            "/file/upload-file",
            data={**data, "file": test_file},
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "error" in response_data
        assert "Missing required field(s): name" == response_data["error"]

    def test_upload_file_invalid_file_type(self, mock_user_ctx):
        """Test file upload with invalid file type"""
        test_file = (io.BytesIO(b"test file content"), "test.txt")
        data = {"name": "test_document.txt", "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value}

        response = self.client.post(
            "/file/upload-file",
            data={**data, "file": test_file},
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "error" in response_data["message"].lower()
        assert (
            f"File validation error: File extension 'txt' not allowed for job_type_id of {JobTypeEnum.ARTIFACT_GENERATION.value}"
            in response_data["message"]
        )

    def test_upload_file_invalid_job_type(self, mock_user_ctx):
        """Test file upload with invalid job type ID"""
        test_file = (io.BytesIO(b"test file content"), "test.pdf")
        data = {
            "name": "test_document.pdf",
            "job_type_id": 999,  # Invalid job type ID
            "business_id": 1,
        }

        response = self.client.post(
            "/file/upload-file",
            data={**data, "file": test_file},
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "error" in response_data["message"].lower()

    def test_upload_file_invalid_business_id(self, mock_user_ctx):
        """Test file upload with invalid business ID"""
        test_file = (io.BytesIO(b"test file content"), "test.pdf")
        data = {
            "name": "test_document.pdf",
            "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
            "business_id": "invalid",  # Invalid business ID
        }

        response = self.client.post(
            "/file/upload-file",
            data={**data, "file": test_file},
            content_type="multipart/form-data",
            headers={"Authorization": "Bearer test_token"},
        )

        assert response.status_code == 400
        response_data = json.loads(response.data)
        assert "error" in response_data

    # def test_upload_file_invalid_page_numbers(self, mock_user_ctx):
    #     """Test file upload with invalid page numbers"""
    #     test_file = (io.BytesIO(b"test file content"), "test.pdf")
    #     data = {
    #         "name": "test_document.pdf",
    #         "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
    #         "start_page": -1,  # Invalid start page
    #         "end_page": 0,  # Invalid end page
    #     }

    #     response = self.client.post(
    #         "/file/upload-file",
    #         data={**data, "file": test_file},
    #         content_type="multipart/form-data",
    #     )

    #     assert response.status_code == 400
    #     response_data = json.loads(response.data)
    #     assert "error" in response_data

    # def test_upload_file_with_additional_data(self, mock_user_ctx):
    #     """Test file upload with additional data"""
    #     test_file = (io.BytesIO(b"test file content"), "test.pdf")
    #     additional_data = {"key": "value", "metadata": {"version": "1.0"}}
    #     data = {
    #         "name": "test_document.pdf",
    #         "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
    #         "additional_data": json.dumps(additional_data),
    #     }

    #     response = self.client.post(
    #         "/file/upload-file",
    #         data={**data, "file": test_file},
    #         content_type="multipart/form-data",
    #     )

    #     assert response.status_code == 201
    #     response_data = json.loads(response.data)
    #     assert "additional_data" in response_data
    #     assert response_data["additional_data"] == additional_data

    # def test_upload_file_s3_failure(self, mock_user_ctx):
    #     """Test file upload with S3 upload failure"""
    #     mock_aws_service.upload_file_to_s3.return_value = (None, "S3 upload failed")
    #     test_file = (io.BytesIO(b"test file content"), "test.pdf")
    #     data = {
    #         "name": "test_document.pdf",
    #         "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
    #     }

    #     response = self.client.post(
    #         "/file/upload-file",
    #         data={**data, "file": test_file},
    #         content_type="multipart/form-data",
    #     )

    #     assert response.status_code == 500
    #     response_data = json.loads(response.data)
    #     assert "error" in response_data
    #     assert "S3" in response_data["error"]

    # def test_upload_file_content_accessor_failure(self, mock_user_ctx):
    #     """Test file upload with content accessor failure"""
    #     mock_content_accessor.save_document.side_effect = Exception("Content accessor error")
    #     test_file = (io.BytesIO(b"test file content"), "test.pdf")
    #     data = {
    #         "name": "test_document.pdf",
    #         "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
    #     }

    #     response = self.client.post(
    #         "/file/upload-file",
    #         data={**data, "file": test_file},
    #         content_type="multipart/form-data",
    #     )

    #     assert response.status_code == 500
    #     response_data = json.loads(response.data)
    #     assert "error" in response_data
    #     assert "Content accessor" in response_data["error"]

    # def test_upload_file_feed_manager_failure(self, mock_user_ctx):
    #     """Test file upload with feed manager service failure"""
    #     mock_feed_manager_service.submit_initial_document.side_effect = Exception(
    #         "Feed manager error"
    #     )
    #     test_file = (io.BytesIO(b"test file content"), "test.pdf")
    #     data = {
    #         "name": "test_document.pdf",
    #         "job_type_id": JobTypeEnum.ARTIFACT_GENERATION.value,
    #     }

    #     response = self.client.post(
    #         "/file/upload-file",
    #         data={**data, "file": test_file},
    #         content_type="multipart/form-data",
    #     )

    #     assert response.status_code == 500
    #     response_data = json.loads(response.data)
    #     assert "error" in response_data
    #     assert "Feed manager" in response_data["error"]
