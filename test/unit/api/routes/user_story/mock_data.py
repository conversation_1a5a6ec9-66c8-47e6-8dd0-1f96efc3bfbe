# mock_data.py
from api.middleware.auth import authentication_key
from shared.models.rating_enum import RatingEnum
from shared.models.user_story import UserStory

MOCK_API_ENDPOINT_RESPONSE = {
    "user_story_id": 1,
    "description": "NOAH TEST",
    "identifier": "TEST",
    "rating": "negative",
    "user_entered": "True",
    "edited": "False",
    "counts_all_levels": {
        "unrated_count": 762,
        "positive_count": 0,
        "negative_count": 1,
        "unrated_requirements_count": 3,
        "positive_requirements_count": 0,
        "negative_requirements_count": 0,
        "unrated_user_story_count": 11,
        "positive_user_story_count": 0,
        "negative_user_story_count": 1,
        "unrated_acceptance_criteria_count": 82,
        "positive_acceptance_criteria_count": 0,
        "negative_acceptance_criteria_count": 0,
        "unrated_test_case_count": 666,
        "positive_test_case_count": 0,
        "negative_test_case_count": 0,
    },
}

MOCK_API_CREATE_ENDPOINT_REQUEST = {
    "unique_statement_id": 1,
    "description": "This is a test description",
    "identifier": "Test user story identifier",
    "rating": "positive",
}

MOCK_API_UPDATE_ENDPOINT_REQUEST = {
    "description": "This is a test description",
    "identifier": "Test user story identifier",
    "rating": "positive",
}

MOCK_HEADERS = {"X-api-key": authentication_key}

MOCK_UPDATE_USER_STORY_DATA = {
    "user_story_id": 1,
    "description": "This is a test description",
    "identifier": "Test user story identifier",
    "rating": "positive",
}

MOCK_UPDATE_USER_STORY_RETURN_VALUE = UserStory(
    user_story_id=1,
    unique_statement_id=1,
    description="This is a test description",
    identifier="US001.**************.01",
    rating=RatingEnum.positive,
    edited=False,
    user_entered=True,
)

MOCK_CREATE_USER_STORY_RETURN_VALUE = UserStory(
    user_story_id=1,
    unique_statement_id=1,
    description="This is a test description",
    identifier="REQ001.001.001.001",
    rating=RatingEnum.positive,
    edited=False,
    user_entered=True,
)

MOCK_DOCUMENT_COUNTS_ALL_LEVELS = {
    "unrated_count": 762,
    "positive_count": 0,
    "negative_count": 1,
    "unrated_requirements_count": 3,
    "positive_requirements_count": 0,
    "negative_requirements_count": 0,
    "unrated_user_story_count": 11,
    "positive_user_story_count": 0,
    "negative_user_story_count": 1,
    "unrated_acceptance_criteria_count": 82,
    "positive_acceptance_criteria_count": 0,
    "negative_acceptance_criteria_count": 0,
    "unrated_test_case_count": 666,
    "positive_test_case_count": 0,
    "negative_test_case_count": 0,
}
