# Unit tests for the CreateDocument class inside unique_statement_routes.py
import sys
from unittest.mock import MagicMock

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from unittest.mock import MagicMock, patch

import pytest
from flask import Flask
from flask_restx import Api

from api.routes.user_story_routes import CreateUserStory

# Import mock data
from .mock_data import (
    MOCK_DOCUMENT_COUNTS_ALL_LEVELS,
    MOCK_HEADERS,
    MOCK_CREATE_USER_STORY_RETURN_VALUE,
    MOCK_API_CREATE_ENDPOINT_REQUEST,
)


@pytest.fixture
def client():
    app = Flask(__name__)
    api = Api(app)
    api.add_resource(CreateUserStory, "/user-story")
    with app.test_client() as test_client:
        yield test_client


@pytest.fixture
def mock_content_accessor():
    with patch("api.routes.user_story_routes.content_accessor") as mock:
        yield mock


@pytest.fixture
def mock_create_engine():
    with patch("sqlalchemy.create_engine") as mock:
        yield mock


@pytest.mark.usefixtures("client", "mock_content_accessor", "mock_create_engine")
class TestCreateUserStory:
    BASE_URL = "/user-story"

    @pytest.fixture(autouse=True)
    def setup_method(self, request):
        self.client = request.getfixturevalue("client")
        self.mock_content_accessor = request.getfixturevalue("mock_content_accessor")
        self.mock_create_engine = request.getfixturevalue("mock_create_engine")

    def test_create_user_story_success(self):
        # Arrange
        self.mock_content_accessor.save_user_story.return_value = (
            MOCK_CREATE_USER_STORY_RETURN_VALUE
        )
        self.mock_content_accessor.get_document_id_by_user_story_id.return_value = 1
        self.mock_content_accessor.get_document_counts_all_levels.return_value = (
            MOCK_DOCUMENT_COUNTS_ALL_LEVELS
        )

        # Act
        response = self.client.post(
            self.BASE_URL,
            json=MOCK_API_CREATE_ENDPOINT_REQUEST,
            headers=MOCK_HEADERS,
        )

        # Assert
        assert response.status_code == 201

        # Parse the JSON response
        data = response.json

        # Reference values from the mock data directly
        assert (
            data["user_story_id"] == MOCK_CREATE_USER_STORY_RETURN_VALUE.user_story_id
        ), "User story ID should match"
        assert (
            data["description"] == MOCK_CREATE_USER_STORY_RETURN_VALUE.description
        ), "Description should match"
        assert (
            data["identifier"] == MOCK_CREATE_USER_STORY_RETURN_VALUE.identifier
        ), "Identifier should match"
        assert (
            data["rating"] == MOCK_CREATE_USER_STORY_RETURN_VALUE.rating.value
        ), "Rating should match"
        assert (
            data["user_entered"] == MOCK_CREATE_USER_STORY_RETURN_VALUE.user_entered
        ), f"User entered flag should match, got {data['user_entered']} instead of {MOCK_CREATE_USER_STORY_RETURN_VALUE.user_entered}"
        assert (
            data["edited"] == MOCK_CREATE_USER_STORY_RETURN_VALUE.edited
        ), f"Edited flag should match, got {data['edited']} instead of {MOCK_CREATE_USER_STORY_RETURN_VALUE.edited}"

        # Verify counts
        for count_type in ["unrated_count", "positive_count", "negative_count"]:
            assert (
                data["counts_all_levels"][count_type] == MOCK_DOCUMENT_COUNTS_ALL_LEVELS[count_type]
            ), f"{count_type} should match"

    def test_missing_required_field_unique_statement_id(self):
        self._assert_missing_required_field(
            fields_to_remove=["unique_statement_id"],
            expected_error="Missing required field(s): unique_statement_id",
        )

    def test_missing_required_field_description(self):
        self._assert_missing_required_field(
            fields_to_remove=["description"],
            expected_error="Missing required field(s): description",
        )

    def test_missing_all_required_fields(self):
        self._assert_missing_required_field(
            fields_to_remove=["unique_statement_id", "description"],
            # Catches first field
            expected_error="Missing required field(s): unique_statement_id, description",
        )

    def test_invalid_user_story_id_type(self):
        self._assert_invalid_type(
            field_name="unique_statement_id",
            invalid_value="invalid_id",  # string instead of int
            expected_error="unique_statement_id must be a non-negative integer and <= 2147483647.",
        )

    def test_invalid_description_type(self):
        self._assert_invalid_type(
            field_name="description",
            invalid_value=5.5,  # float instead of string
            expected_error="description cannot be blank and must be a string.",
        )

    # Helper methods

    def _assert_missing_required_field(self, fields_to_remove, expected_error):

        # Arrange: Remove specified required fields from the request data
        incomplete_request_data = MOCK_API_CREATE_ENDPOINT_REQUEST.copy()
        for field in fields_to_remove:
            # If removing 'subtopics', ensure it's removed as a whole list
            if field == "subtopics":
                incomplete_request_data.pop(field, None)
            else:
                incomplete_request_data.pop(field, None)

        # Act
        response = self.client.post(
            self.BASE_URL,
            json=incomplete_request_data,
            headers=MOCK_HEADERS,
        )

        # Assert
        assert response.status_code == 400
        assert expected_error in response.json["error"]

    def _assert_invalid_type(self, field_name, invalid_value, expected_error):
        # Arrange: Copy valid request data and set the specified field to an invalid type
        request_data = MOCK_API_CREATE_ENDPOINT_REQUEST.copy()
        request_data[field_name] = invalid_value

        # Act
        response = self.client.post(
            self.BASE_URL,
            json=request_data,
            headers=MOCK_HEADERS,
        )

        # Assert
        assert response.status_code == 400
        assert expected_error in response.json["error"]
