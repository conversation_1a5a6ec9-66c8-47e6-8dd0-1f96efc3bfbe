"""Test configuration and fixtures."""

import os
import pytest

# Mock environment variables required by the application
MOCK_ENV_VARS = {
    'OPENAI_API_KEY': 'mock-key',
    'PINECONE_API_KEY': 'mock-key',
    'FEDERAL_REGISTER_API': 'mock-api',
    'CODE_OF_FEDERAL_REGULATIONS_API': 'mock-api',
    'AWS_S3_BUCKET_NAME': 'mock-bucket',
    'AWS_REGION': 'us-east-1',
    'AWS_S3_ACCESS_KEY': 'mock-key',
    'AWS_S3_SECRET_KEY': 'mock-secret',
    'AWS_POSTGRES_USERNAME': 'mock-user',
    'AWS_POSTGRES_PASSWORD': 'mock-pass',
    'AWS_POSTGRES_HOST': 'mock-host',
    'AWS_POSTGRES_PORT': '5432',
    'AWS_POSTGRES_DATABASE': 'mock-db',
    'A<PERSON>_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN': 'mock-arn',
    'AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN': 'mock-arn',
    'AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN': 'mock-arn',
    'AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN': 'mock-arn',
    'AWS_SNS_JOB_GENERATE_USER_STORIES_ARN': 'mock-arn',
    'AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN': 'mock-arn',
    'AWS_SNS_JOB_GENERATE_TEST_CASES_ARN': 'mock-arn',
    'AWS_SNS_JOB_ARN': 'mock-arn',
    'AWS_FEED_MANAGER_REGION': 'us-east-1',
    'AWS_FEED_MANAGER_ACCESS_KEY': 'mock-key',
    'AWS_FEED_MANAGER_SECRET_KEY': 'mock-secret',
    'AWS_FEED_MANAGER_QUEUE': 'mock-queue',
    'AUTH0_DOMAIN': 'mock-domain',
    'AUTH0_CLIENT_ID': 'mock-client',
    'AUTH0_CLIENT_SECRET': 'mock-secret',
    'LOG_LEVEL': 'INFO',
    'THIRD_PARTY_LOG_LEVEL': 'INFO',
    'LANGFUSE_SECRET_KEY': 'mock-key',
    'LANGFUSE_PUBLIC_KEY': 'mock-key',
    'LANGFUSE_HOST': 'mock-host',
    'FEED_MANAGER_API_URL': 'mock-url',
    'ENVIRONMENT_NAME': 'test'
}

@pytest.fixture(autouse=True)
def mock_env_vars():
    """Set up mock environment variables for all tests."""
    # Store original environment
    original_env = dict(os.environ)
    
    # Set mock environment variables
    os.environ.update(MOCK_ENV_VARS)
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env) 