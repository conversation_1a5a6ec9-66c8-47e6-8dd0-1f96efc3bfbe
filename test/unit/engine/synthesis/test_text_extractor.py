import unittest
from unittest.mock import MagicMock
from engine.synthesis.text_extractor import TextExtractor


class TestTextExtractor(unittest.TestCase):

    def setUp(self):
        # Create an instance of the class and mock the logger
        self.obj = TextExtractor("test-text-extractor")
        self.obj.logger = MagicMock()

    def test_extract_chunk_id_valid(self):
        # Test case 1: Proper integer input
        self.assertEqual(self.obj.extract_chunk_id("32781"), 32781)

        # Test case 2: Input with extra text, containing an integer
        self.assertEqual(self.obj.extract_chunk_id("chunk_id: 32781"), 32781)

        # Test case 3: Input with leading/trailing spaces
        self.assertEqual(self.obj.extract_chunk_id(" 32781 "), 32781)

    def test_extract_chunk_id_invalid(self):
        # Test case 4: Input is None
        self.assertIsNone(self.obj.extract_chunk_id(None))
        self.obj.logger.error.assert_called_with("raw_chunk_id is None")

        # Test case 5: Input is an empty string
        self.assertIsNone(self.obj.extract_chunk_id(""))
        self.obj.logger.error.assert_called_with("raw_chunk_id is None")

        # Test case 6: Input does not contain an integer
        self.assertIsNone(self.obj.extract_chunk_id("No numbers here"))
        self.obj.logger.error.assert_called_with(
            "No integer found in raw_chunk_id: %s", "No numbers here"
        )

        # Test case 7: Input is a non-string type
        self.assertIsNone(self.obj.extract_chunk_id(1234))  # Integer input
        self.obj.logger.error.assert_called_with("raw_chunk_id is not a string: %s", type(1234))

        self.assertIsNone(self.obj.extract_chunk_id(["32781"]))  # List input
        self.obj.logger.error.assert_called_with(
            "raw_chunk_id is not a string: %s", type(["32781"])
        )

    def test_extract_chunk_id_multiple_integers(self):
        # Test case 8: Input contains multiple integers; should extract the first one
        self.assertEqual(self.obj.extract_chunk_id("chunk 1234 and chunk 5678"), 1234)


# The standard boilerplate to run the tests
if __name__ == "__main__":
    unittest.main()
