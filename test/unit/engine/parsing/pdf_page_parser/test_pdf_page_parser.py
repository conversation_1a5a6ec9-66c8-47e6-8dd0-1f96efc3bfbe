import io
import unittest
from unittest.mock import MagicMock, patch

from engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser import (
    PdfPageParser,
)


class TestPdfPageParser(unittest.TestCase):
    @patch(
        "engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser.AiAccessor.get_completion"
    )
    def test_parse_nodes(self, mock_get_completion):
        mock_get_completion.return_value = '{"content": [{"type": "text", "text": "Sample text"}]}'

        chunker = PdfPageParser()
        fake_pdf_bytes = io.BytesIO(b"%PDF-1.4 Sample text")

        with patch.object(
            chunker,
            "_process_pdf_with_gpt",
            return_value=[{"page_number": 1, "content": [{"type": "text", "text": "Sample text"}]}],
        ):
            nodes = chunker.parse_nodes(fake_pdf_bytes)

        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].metadata["page_number"], 1)
        self.assertEqual(nodes[0].text, "Sample text")

    def test_parse_nodes_invalid_start_page(self):
        chunker = PdfPageParser()
        fake_pdf_bytes = io.BytesIO(b"%PDF-1.4 Sample text")
        with self.assertRaises(ValueError):
            chunker.parse_nodes(fake_pdf_bytes, start_page=0)

    def test_parse_nodes_invalid_end_page(self):
        chunker = PdfPageParser()
        fake_pdf_bytes = io.BytesIO(b"%PDF-1.4 Sample text")
        with self.assertRaises(ValueError):
            chunker.parse_nodes(fake_pdf_bytes, start_page=2, end_page=1)

    def test_build_text_empty_content(self):
        """Test _build_text with empty content"""
        chunker = PdfPageParser()
        page = {"content": []}
        result = chunker._build_text(page)
        self.assertEqual(result, "")

    def test_build_text_no_text_blocks(self):
        """Test _build_text with no text blocks"""
        chunker = PdfPageParser()
        page = {
            "content": [
                {"type": "image", "text": "Image description"},
                {"type": "table", "text": "Table content"}
            ]
        }
        result = chunker._build_text(page)
        self.assertEqual(result, "")

    def test_build_text_single_text_block(self):
        """Test _build_text with a single text block"""
        chunker = PdfPageParser()
        page = {
            "content": [
                {"type": "text", "text": "Hello World"}
            ]
        }
        result = chunker._build_text(page)
        self.assertEqual(result, "Hello World")

    def test_build_text_multiple_text_blocks(self):
        """Test _build_text with multiple text blocks"""
        chunker = PdfPageParser()
        page = {
            "content": [
                {"type": "text", "text": "First block"},
                {"type": "text", "text": "Second block"},
                {"type": "text", "text": "Third block"}
            ]
        }
        result = chunker._build_text(page)
        self.assertEqual(result, "First block Second block Third block")

    def test_build_text_mixed_content(self):
        """Test _build_text with mixed content types"""
        chunker = PdfPageParser()
        page = {
            "content": [
                {"type": "text", "text": "Text block"},
                {"type": "image", "text": "Image description"},
                {"type": "text", "text": "Another text block"},
                {"type": "table", "text": "Table content"}
            ]
        }
        result = chunker._build_text(page)
        self.assertEqual(result, "Text block Another text block")

    # TODO: This is failing on 4/1/2025, so might be a bug in the PDF page parser - not sure if we want to concatenate empty text blocks
    def test_build_text_with_empty_text(self):
        """Test _build_text with empty text in blocks"""
        chunker = PdfPageParser()
        page = {
            "content": [
                {"type": "text", "text": ""},
                {"type": "text", "text": "Valid text"},
                {"type": "text", "text": ""}
            ]
        }
        result = chunker._build_text(page)
        self.assertEqual(result, "Valid text")

    def test_build_metadata_with_bbox(self):
        """Test _build_metadata with bbox"""
        chunker = PdfPageParser()
        page = {
            "page_number": 1,
            "bbox": [0, 0, 100, 200]
        }
        result = chunker._build_metadata(page)
        self.assertEqual(result["page_number"], 1)
        self.assertEqual(result["blocks"][0]["bbox"], [0, 0, 100, 200])
        self.assertEqual(result["starting_page_number"], 1)
        self.assertEqual(result["ending_page_number"], 1)
        self.assertEqual(result["header_name"], "")

    def test_build_metadata_without_bbox(self):
        """Test _build_metadata without bbox"""
        chunker = PdfPageParser()
        page = {
            "page_number": 1
        }
        result = chunker._build_metadata(page)
        self.assertEqual(result["page_number"], 1)
        self.assertEqual(result["blocks"][0]["bbox"], [])
        self.assertEqual(result["starting_page_number"], 1)
        self.assertEqual(result["ending_page_number"], 1)
        self.assertEqual(result["header_name"], "")

    def test_build_metadata_with_header(self):
        """Test _build_metadata with header_name"""
        chunker = PdfPageParser()
        page = {
            "page_number": 1,
            "bbox": [0, 0, 100, 200],
        }
        result = chunker._build_metadata(page)
        self.assertEqual(result["blocks"][0]["bbox"], [0, 0, 100, 200])
        self.assertEqual(result["header_name"], "")
        self.assertEqual(result["starting_page_number"], 1)
        self.assertEqual(result["ending_page_number"], 1)
        self.assertEqual(result["page_number"], 1)

    @patch("engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser.fitz.open")
    def test_pdf_page_to_image(self, mock_fitz_open):
        """Test _pdf_page_to_image method"""
        chunker = PdfPageParser()
        
        # Mock the PDF document
        mock_doc = MagicMock()
        mock_doc.__len__.return_value = 3
        
        # Mock a page
        mock_page = MagicMock()
        mock_page.rect.x0 = 0
        mock_page.rect.y0 = 0
        mock_page.rect.x1 = 100
        mock_page.rect.y1 = 200
        mock_page.get_pixmap.return_value.tobytes.return_value = b"fake_image_data"
        
        # Mock document loading
        mock_doc.load_page.return_value = mock_page
        mock_fitz_open.return_value = mock_doc
        
        # Test with specific page range
        result = chunker._pdf_page_to_image(mock_doc, start_page=1, end_page=2)
        
        # Verify results
        self.assertEqual(len(result), 2)
        for page_num, img_bytes, bbox in result:
            self.assertEqual(img_bytes, b"fake_image_data")
            self.assertEqual(bbox, [0, 0, 100, 200])
        
        # Verify page loading
        self.assertEqual(mock_doc.load_page.call_count, 2)
        mock_doc.load_page.assert_any_call(0)  # start_page - 1
        mock_doc.load_page.assert_any_call(1)  # end_page - 1

    @patch("engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser.fitz.open")
    def test_pdf_page_to_image_all_pages(self, mock_fitz_open):
        """Test _pdf_page_to_image method with all pages"""
        chunker = PdfPageParser()
        
        # Mock the PDF document
        mock_doc = MagicMock()
        mock_doc.__len__.return_value = 3
        
        # Mock a page
        mock_page = MagicMock()
        mock_page.rect.x0 = 0
        mock_page.rect.y0 = 0
        mock_page.rect.x1 = 100
        mock_page.rect.y1 = 200
        mock_page.get_pixmap.return_value.tobytes.return_value = b"fake_image_data"
        
        # Mock document loading
        mock_doc.load_page.return_value = mock_page
        mock_fitz_open.return_value = mock_doc
        
        # Test with all pages (end_page = -1)
        result = chunker._pdf_page_to_image(mock_doc, start_page=1, end_page=-1)
        
        # Verify results
        self.assertEqual(len(result), 3)
        for page_num, img_bytes, bbox in result:
            self.assertEqual(img_bytes, b"fake_image_data")
            self.assertEqual(bbox, [0, 0, 100, 200])
        
        # Verify page loading
        self.assertEqual(mock_doc.load_page.call_count, 3)
        for i in range(3):
            mock_doc.load_page.assert_any_call(i)


if __name__ == "__main__":
    unittest.main()
