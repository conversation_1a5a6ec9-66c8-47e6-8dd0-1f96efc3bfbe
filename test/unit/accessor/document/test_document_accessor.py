# import os
# import unittest
# from accessor.document.document_accessor import Document<PERSON>ccessor
# from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY

# class IntegratedDocumentJobProcessor(unittest.TestCase):


#     def test_document_accessor_success(self):
#         self.document_id = 110  # Replace with the actual document_id
#         self.job_id = 92  # Replace with the actual job_id

#         accessor = DocumentAccessor(AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY)
#         self.assertIsNotNone(accessor)

#         dto = accessor.prepare_document_in_initial_processing(self.document_id, self.job_id)

#         self.assertIsNotNone(dto, "No matching document found.")
#         self.assertEqual(dto.document_id, self.document_id)
#         self.assertIsInstance(dto.document_bytes, bytes)
#         self.assertEqual(dto.job_id, self.job_id)
#         self.assertEqual(dto.job_type, 'process document')
#         self.assertEqual(dto.job_status, 'Initial Processing')
#         self.assertIsInstance(dto.domain_id, int)
#         self.assertIsInstance(dto.domain_name, str)
#         self.assertIsInstance(dto.document_name, str)  # Added assertion
#         self.assertIsInstance(dto.document_start_page, int)     # Added assertion
#         self.assertIsInstance(dto.document_end_page, int)       # Added assertion
#         self.assertGreaterEqual(dto.document_start_page, 0)     # Ensure start_page is non-negative
#         self.assertGreaterEqual(dto.document_end_page, dto.document_start_page)  # Ensure end_page is not before start_page`

# if __name__ == '__main__':
#     unittest.main()
