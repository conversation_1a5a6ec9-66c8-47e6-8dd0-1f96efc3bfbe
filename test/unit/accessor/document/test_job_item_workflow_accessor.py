"""Tests for JobItemWorkflowAccessor."""

import concurrent.futures
import threading
from typing import Set

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from accessor.document.job_item_workflow import JobItemWorkflow
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.job_item_workflow import JobItemWorkflow as JobItemWorkflowModel
from shared.models.job_workflow import JobWorkflow
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum

# Test configuration
NUM_ITEMS = 10
NUM_THREADS = 5
WORKFLOW_ID = 1

@pytest.fixture
def engine():
    """Create an in-memory SQLite database for testing."""
    return create_engine('sqlite:///:memory:')

@pytest.fixture
def session(engine):
    """Create a session for the test database."""
    # Create all tables
    JobWorkflow.metadata.create_all(engine)
    JobItemWorkflowModel.metadata.create_all(engine)
    
    # Create session factory
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Create test workflow
    workflow = JobWorkflow(
        job_workflow_id=WORKFLOW_ID,
        status=JobWorkflowStatusEnum.STARTING
    )
    session.add(workflow)
    session.commit()
    
    yield session
    
    # Cleanup
    session.close()
    JobWorkflow.metadata.drop_all(engine)
    JobItemWorkflowModel.metadata.drop_all(engine)

@pytest.fixture
def accessor(session):
    """Create a JobItemWorkflowAccessor instance."""
    def session_factory():
        return session
    return JobItemWorkflow(session_factory)

def test_seed_items(accessor, session):
    """Test that seed_items creates the correct number of items."""
    # Seed items
    item_keys = [str(i) for i in range(NUM_ITEMS)]
    accessor.seed_items(WORKFLOW_ID, item_keys, JobItemTypeEnum.CHUNK)
    
    # Verify items were created
    items = session.query(JobItemWorkflowModel).all()
    assert len(items) == NUM_ITEMS
    
    # Verify each item has correct attributes
    for item in items:
        assert item.job_workflow_id == WORKFLOW_ID
        assert item.status == JobWorkflowStatusEnum.STARTING
        assert item.item_type == JobItemTypeEnum.CHUNK
        assert item.run_count == 0
        assert item.item_key in item_keys

def test_concurrent_reservations(accessor, session):
    """Test that concurrent reservations return each item exactly once."""
    # Seed items
    item_keys = [str(i) for i in range(NUM_ITEMS)]
    accessor.seed_items(WORKFLOW_ID, item_keys, JobItemTypeEnum.CHUNK)
    
    # Track reserved items
    reserved_items: Set[str] = set()
    reserved_items_lock = threading.Lock()
    
    def reserve_item():
        """Reserve an item and track it."""
        item_key = accessor.reserve_next_pending(WORKFLOW_ID)
        if item_key:
            with reserved_items_lock:
                reserved_items.add(item_key)
        return item_key
    
    # Use thread pool to reserve items concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
        futures = [executor.submit(reserve_item) for _ in range(NUM_ITEMS * 2)]  # Try to reserve more than available
        non_none_results = [f.result() for f in futures if f.result() is not None]
    
    # Verify results
    assert len(non_none_results) == NUM_ITEMS  # Each item reserved exactly once
    assert len(reserved_items) == NUM_ITEMS  # No duplicates
    assert reserved_items == set(item_keys)  # All items were reserved
    
    # Verify no more items can be reserved
    assert accessor.reserve_next_pending(WORKFLOW_ID) is None

def test_mark_done(accessor, session):
    """Test that mark_done updates item status correctly."""
    # Seed and reserve an item
    accessor.seed_items(WORKFLOW_ID, ["test_item"], JobItemTypeEnum.CHUNK)
    item_key = accessor.reserve_next_pending(WORKFLOW_ID)
    assert item_key is not None
    
    # Mark as done
    accessor.mark_done(WORKFLOW_ID, item_key)
    
    # Verify status
    updated_item = session.query(JobItemWorkflowModel).filter_by(
        job_workflow_id=WORKFLOW_ID,
        item_key=item_key
    ).first()
    assert updated_item.status == JobWorkflowStatusEnum.COMPLETED
    assert updated_item.run_count == 1

def test_mark_failed(accessor, session):
    """Test that mark_failed updates item status correctly."""
    # Seed and reserve an item
    accessor.seed_items(WORKFLOW_ID, ["test_item"], JobItemTypeEnum.CHUNK)
    item_key = accessor.reserve_next_pending(WORKFLOW_ID)
    assert item_key is not None
    
    # Mark as failed
    error_msg = "Test error"
    accessor.mark_failed(WORKFLOW_ID, item_key, error_msg)
    
    # Verify status
    updated_item = session.query(JobItemWorkflowModel).filter_by(
        job_workflow_id=WORKFLOW_ID,
        item_key=item_key
    ).first()
    assert updated_item.status == JobWorkflowStatusEnum.FAILED
    assert updated_item.run_count == 1
    assert updated_item.last_error == error_msg

def test_list_incomplete(accessor, session):
    """Test that list_incomplete returns correct items."""
    # Seed items
    item_keys = [str(i) for i in range(NUM_ITEMS)]
    accessor.seed_items(WORKFLOW_ID, item_keys, JobItemTypeEnum.CHUNK)
    
    # Complete some items
    for i in range(NUM_ITEMS // 2):
        item_key = accessor.reserve_next_pending(WORKFLOW_ID)
        accessor.mark_done(WORKFLOW_ID, item_key)
    
    # List incomplete items
    incomplete_keys = accessor.list_incomplete(WORKFLOW_ID)
    assert len(incomplete_keys) == NUM_ITEMS // 2
    
    # Verify all incomplete items are in STARTING status
    for key in incomplete_keys:
        row = session.query(JobItemWorkflowModel).filter_by(item_key=key).first()
        assert row.status == JobWorkflowStatusEnum.STARTING 