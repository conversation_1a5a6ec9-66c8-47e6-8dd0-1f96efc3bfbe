import sys
from unittest.mock import MagicMock, patch

import pytest
from shared.models.unique_statement_subtopic_association import UniqueStatementSubtopicAssociation
from .mock_data import MOCK_UNIQUE_STATEMENT_INSERT_FIELDS

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from accessor.content.accessors import UniqueStatementAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models import RatingEnum, UniqueStatement


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def unique_statement_accessor(mock_session_factory):
    return UniqueStatementAccessor(mock_session_factory)


def setup_mock_no_unique_statement_found_session(mock_session, added_instance):
    # Mock session.add to track objects added to the session.
    # This allows us to capture and inspect instances of UniqueStatement and
    # UniqueStatementSubtopicAssociation that are added during the execution of the method being tested.
    # - If the added instance is a UniqueStatement, it is appended to the added_instance list for verification.
    # - For UniqueStatementSubtopicAssociation objects, no action is taken here, but they are still tracked
    #   via the mock_session.add.call_args_list for further assertions if needed.
    def add_side_effect(instance):
        if isinstance(instance, UniqueStatement):
            added_instance.append(instance)
        elif isinstance(instance, UniqueStatementSubtopicAssociation):
            pass
        return instance

    mock_session.add.side_effect = add_side_effect

    # Mock the behavior of the session context manager.
    # When the session is used in a 'with' statement, __enter__ is called, and this ensures
    # that the mocked session object is returned, mimicking the behavior of a real session.
    mock_session.__enter__.return_value = mock_session

    # Mock the query behavior of the session.
    # - Create a MagicMock object to represent the query.
    # - Chain mock methods (filter_by and options) to simulate a SQLAlchemy query.
    # - Set one_or_none.return_value to None to simulate the case where no existing UniqueStatement
    #   matches the query, ensuring the code path for creating a new UniqueStatement is triggered.
    # - Assign the mocked query object to mock_session.query, so any call to session.query(...)
    #   returns this mock and follows the defined behavior.
    mock_query = MagicMock()
    mock_query.filter_by.return_value = mock_query
    mock_query.options.return_value = mock_query
    mock_query.one_or_none.return_value = None  # Simulate no existing UniqueStatement
    mock_session.query.return_value = mock_query

    # Simulate the behavior of session.query(...).one() when fetching a UniqueStatement.
    # - The side effect function query_one_side_effect checks if any UniqueStatement has been added
    #   to the session (stored in added_instance).
    # - If added_instance contains at least one UniqueStatement, it returns the first one,
    #   simulating a successful query result.
    # - If no UniqueStatement is found, an exception is raised, mimicking the behavior of a
    #   real query that fails to find a matching record.
    # - This side effect is assigned to mock_query.one, ensuring that calls to one() on the
    #   mocked query follow this logic.
    def query_one_side_effect():
        if added_instance:
            return added_instance[0]
        raise Exception("No UniqueStatement found")

    mock_query.one.side_effect = query_one_side_effect
    mock_session.query.return_value = mock_query


def setup_mock_unique_statement_found_session(mock_session, existing_instance, added_instance):
    # Mock session.add to track objects added to the session.
    # This allows us to capture and inspect instances of UniqueStatement and
    # UniqueStatementSubtopicAssociation that are added during the execution of the method being tested.
    # - If the added instance is a UniqueStatement, no action is taken here, but they are still tracked
    #   via the mock_session.add.call_args_list for further assertions if needed.
    # - For UniqueStatementSubtopicAssociation objects, it is appended to the added_instance list for verification.
    def add_side_effect(instance):
        if isinstance(instance, UniqueStatement):
            added_instance.append(instance)  # Should not happen in this case
        elif isinstance(instance, UniqueStatementSubtopicAssociation):
            added_instance.append(instance)  # Track added associations
        return instance

    mock_session.add.side_effect = add_side_effect

    # Mock the behavior of the session context manager.
    # When the session is used in a 'with' statement, __enter__ is called, and this ensures
    # that the mocked session object is returned, mimicking the behavior of a real session.
    mock_session.__enter__.return_value = mock_session

    # Mock the query behavior of the session.
    # - Create a MagicMock object to represent the query.
    # - Chain mock methods (filter_by and options) to simulate a SQLAlchemy query.
    # - Set one_or_none.return_value to None to simulate the case where an existing UniqueStatement
    #   matches the query, ensuring the code path for creating a new UniqueStatement is triggered.
    # - Assign the mocked query object to mock_session.query, so any call to session.query(...)
    #   returns this mock and follows the defined behavior.
    mock_query = MagicMock()
    mock_query.filter_by.return_value = mock_query
    mock_query.options.return_value = mock_query
    mock_query.one_or_none.return_value = existing_instance  # Simulate an existing UniqueStatement
    mock_session.query.return_value = mock_query

    # Simulate the behavior of session.query(...).one() for the existing UniqueStatement.
    # Always return the existing_instance directly.
    def query_one_side_effect():
        return existing_instance  # Simulate a query returning an existing UniqueStatement

    mock_query.one.side_effect = query_one_side_effect
    mock_session.query.return_value = mock_query


def assert_session_add_calls_for_new_unique_statement(mock_session, expected_types):
    """
    Helper to verify the types of objects added to the session in order.

    :param mock_session: The mocked session to inspect.
    :param expected_types: List of expected types, in the order they should be added.
    """
    # Assert the number of calls matches the expected count
    assert mock_session.add.call_count == len(
        expected_types
    ), f"Expected {len(expected_types)} calls to session.add, but found {mock_session.add.call_count}"

    # Extract the types of objects added
    added_types = [call[0][0].__class__ for call in mock_session.add.call_args_list]

    # Assert each call matches the expected type
    for added_type, expected_type in zip(added_types, expected_types):
        assert (
            added_type == expected_type
        ), f"Expected type {expected_type.__name__}, but got {added_type.__name__}"


def test_save_unique_statement_record_success(unique_statement_accessor, mock_session):
    # Arrange: Define the fields for creating a new unique statement
    insert_fields = {
        "document_id": 1,
        "domain_id": 2,
        "statement_type_id": 1,
        "text": "This is a test description",
        "rating": RatingEnum.positive,
        "subtopics": [
            {"subtopic_id": 96, "is_primary": False},
            {"subtopic_id": 97, "is_primary": True},
        ],
    }

    # Initialize and setup the mock session
    added_instance = []
    setup_mock_no_unique_statement_found_session(mock_session, added_instance)

    # Act
    result = unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)

    # Assert:

    # - Verify a UniqueStatement object was added
    # - Verify the field values are the same as that of inserted_fields
    # - Verify the default values are set correctly
    assert len(added_instance) == 1
    added_unique_statement = added_instance[0]
    assert isinstance(added_unique_statement, UniqueStatement)
    for field in ["document_id", "domain_id", "statement_type_id", "text", "rating"]:
        assert getattr(added_unique_statement, field) == insert_fields[field]
    assert result.user_entered is False  # default
    assert result.edited is False  # default

    # Verify that we had 3 different calls to add these types
    expected_types = [
        UniqueStatement,
        UniqueStatementSubtopicAssociation,
        UniqueStatementSubtopicAssociation,
    ]
    assert_session_add_calls_for_new_unique_statement(mock_session, expected_types)

    # Verify session.flush and session.commit calls
    mock_session.flush.assert_called_once()
    assert mock_session.commit.call_count == 1
    mock_session.refresh.assert_called_once_with(added_unique_statement)


def test_save_unique_statement_with_default_values(unique_statement_accessor, mock_session):
    # Arrange: Define minimal fields, omitting optional ones like `rating` and `user_entered`
    insert_fields = {
        "document_id": 1,
        "statement_type_id": 1,
        "text": "Statement with defaults",
        "subtopics": [
            {"subtopic_id": 96, "is_primary": False},
            {"subtopic_id": 97, "is_primary": True},
        ],
    }

    # Capture the instance
    added_instance = []
    setup_mock_no_unique_statement_found_session(mock_session, added_instance)

    # Act
    result = unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)

    # - Verify a UniqueStatement object was added
    # - Verify the field values are the same as that of inserted_fields
    # - Verify the default values are set correctly
    assert len(added_instance) == 1
    added_unique_statement = added_instance[0]
    assert isinstance(added_unique_statement, UniqueStatement)
    assert result == added_unique_statement
    assert result.document_id == insert_fields["document_id"]
    assert result.statement_type_id == insert_fields["statement_type_id"]
    assert result.text == insert_fields["text"]
    assert result.domain_id is None  # Default rating
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is False  # Default user_entered
    assert result.edited is False  # Default edited

    # Expected types: 1 UniqueStatement and 2 UniqueStatementSubtopicAssociation
    expected_types = [
        UniqueStatement,
        UniqueStatementSubtopicAssociation,
        UniqueStatementSubtopicAssociation,
    ]

    # Use the helper to validate the session.add calls
    assert_session_add_calls_for_new_unique_statement(mock_session, expected_types)

    # Verify session.flush and session.commit calls
    mock_session.flush.assert_called_once()
    assert mock_session.commit.call_count == 1
    mock_session.refresh.assert_called_once_with(added_unique_statement)


def test_save_unique_statement_existing_record(unique_statement_accessor, mock_session):
    # Arrange: Define an existing UniqueStatement and the fields for the new statement
    existing_instance = UniqueStatement(
        document_id=1,
        domain_id=2,
        statement_type_id=1,
        text="This is a test description",
        rating=RatingEnum.positive,
        user_entered=False,
        edited=False,
    )
    added_instance = []
    setup_mock_unique_statement_found_session(mock_session, existing_instance, added_instance)

    insert_fields = {
        "document_id": 1,
        "domain_id": 2,
        "statement_type_id": 1,
        "text": "This is a test description",
        "rating": RatingEnum.positive,
        "subtopics": [
            {"subtopic_id": 96, "is_primary": False},
            {"subtopic_id": 97, "is_primary": True},
        ],
    }

    # Act
    result = unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)
    
    # Assert:

    # - Verify no new UniqueStatement object was added
    assert len(added_instance) == 2  # Only associations should be added
    for obj in added_instance:
        assert isinstance(obj, UniqueStatementSubtopicAssociation)

    # Verify the returned result matches the existing UniqueStatement
    assert result == existing_instance
    for field in ["document_id", "domain_id", "statement_type_id", "text", "rating"]:
        assert getattr(result, field) == insert_fields[field]
    assert result.user_entered is False
    assert result.edited is False

    # Verify that only 2 SubtopicAssociations were added
    expected_types = [
        UniqueStatementSubtopicAssociation,
        UniqueStatementSubtopicAssociation,
    ]
    assert_session_add_calls_for_new_unique_statement(mock_session, expected_types)

    # Verify session.flush and session.commit calls
    mock_session.flush.assert_not_called()  # No flush called when no new UniqueStatement was added
    assert mock_session.commit.call_count == 1  # Only associations were committed
    mock_session.refresh.assert_called_once_with(existing_instance)


def test_save_unique_statement_commit_failure(unique_statement_accessor, mock_session):
    # Arrange: Define valid fields
    insert_fields = {
        "document_id": 1,
        "statement_type_id": 1,
        "text": "Commit failure test",
        "subtopics": [{"subtopic_id": 96, "is_primary": True}],
    }

    # Configure `commit` to raise an exception
    mock_session.commit.side_effect = Exception("Commit failed")

    # Act & Assert: Expecting ContentAccessorException due to commit failure
    with pytest.raises(
        ContentAccessorException, match="Error saving unique statement: Commit failed"
    ):
        unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)

    # Verify rollback was called
    mock_session.rollback.assert_called_once()


def test_save_unique_statement_with_positive_rating_and_subtopics(
    unique_statement_accessor, mock_session
):
    # Arrange: Define fields with `rating` set to `positive` and subtopics
    insert_fields = {
        "document_id": 1,
        "statement_type_id": 1,
        "text": "Test with positive rating and subtopics",
        "rating": RatingEnum.positive,
        "subtopics": [
            {"subtopic_id": 101, "is_primary": True},
            {"subtopic_id": 102, "is_primary": False},
        ],
    }

    # Capture the instance
    added_instance = []
    setup_mock_no_unique_statement_found_session(mock_session, added_instance)

    # Act
    result = unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)

    # - Verify a UniqueStatement object was added
    # - Verify the field values are the same as that of inserted_fields
    # - Verify the default values are set correctly
    assert len(added_instance) == 1
    added_unique_statement = added_instance[0]
    assert isinstance(added_unique_statement, UniqueStatement)
    assert result == added_unique_statement
    assert result.document_id == insert_fields["document_id"]
    assert result.statement_type_id == insert_fields["statement_type_id"]
    assert result.text == insert_fields["text"]
    assert result.rating == insert_fields["rating"]
    assert result.domain_id is None  # Default rating
    assert result.user_entered is False  # Default user_entered
    assert result.edited is False  # Default edited

    # Expected types: 1 UniqueStatement and 2 UniqueStatementSubtopicAssociation
    expected_types = [
        UniqueStatement,
        UniqueStatementSubtopicAssociation,
        UniqueStatementSubtopicAssociation,
    ]

    # Use the helper to validate the session.add calls
    assert_session_add_calls_for_new_unique_statement(mock_session, expected_types)

    # Verify session.flush and session.commit calls
    mock_session.flush.assert_called_once()
    assert mock_session.commit.call_count == 1
    mock_session.refresh.assert_called_once_with(added_unique_statement)


def test_save_unique_statement_with_negative_rating_and_subtopics(
    unique_statement_accessor, mock_session
):
    # Arrange: Define fields with `rating` set to `negative` and subtopics
    insert_fields = {
        "document_id": 1,
        "statement_type_id": 1,
        "text": "Test with negative rating and subtopics",
        "rating": RatingEnum.negative,
        "subtopics": [
            {"subtopic_id": 201, "is_primary": False},
            {"subtopic_id": 202, "is_primary": True},
        ],
    }

    # Capture the instance
    added_instance = []
    setup_mock_no_unique_statement_found_session(mock_session, added_instance)

    # Act
    result = unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)

    # - Verify a UniqueStatement object was added
    # - Verify the field values are the same as that of inserted_fields
    # - Verify the default values are set correctly
    assert len(added_instance) == 1
    added_unique_statement = added_instance[0]
    assert isinstance(added_unique_statement, UniqueStatement)
    assert result == added_unique_statement
    assert result.document_id == insert_fields["document_id"]
    assert result.statement_type_id == insert_fields["statement_type_id"]
    assert result.text == insert_fields["text"]
    assert result.rating == insert_fields["rating"]
    assert result.domain_id is None  # Default rating
    assert result.user_entered is False  # Default user_entered
    assert result.edited is False  # Default edited

    # Expected types: 1 UniqueStatement and 2 UniqueStatementSubtopicAssociation
    expected_types = [
        UniqueStatement,
        UniqueStatementSubtopicAssociation,
        UniqueStatementSubtopicAssociation,
    ]

    # Use the helper to validate the session.add calls
    assert_session_add_calls_for_new_unique_statement(mock_session, expected_types)

    # Verify session.flush and session.commit calls
    mock_session.flush.assert_called_once()
    assert mock_session.commit.call_count == 1
    mock_session.refresh.assert_called_once_with(added_unique_statement)


def test_save_unique_statement_with_unrated_rating_and_subtopics(
    unique_statement_accessor, mock_session
):
    # Arrange: Define fields with `rating` set to `unrated` and subtopics
    insert_fields = {
        "document_id": 1,
        "statement_type_id": 1,
        "text": "Test with unrated rating and subtopics",
        "rating": RatingEnum.unrated,
        "subtopics": [
            {"subtopic_id": 301, "is_primary": True},
            {"subtopic_id": 302, "is_primary": False},
        ],
    }

    # Capture the instance
    added_instance = []
    setup_mock_no_unique_statement_found_session(mock_session, added_instance)

    # Act
    result = unique_statement_accessor.save_unique_statement(insert_fields=insert_fields)

    # - Verify a UniqueStatement object was added
    # - Verify the field values are the same as that of inserted_fields
    # - Verify the default values are set correctly
    assert len(added_instance) == 1
    added_unique_statement = added_instance[0]
    assert isinstance(added_unique_statement, UniqueStatement)
    assert result == added_unique_statement
    assert result.document_id == insert_fields["document_id"]
    assert result.statement_type_id == insert_fields["statement_type_id"]
    assert result.text == insert_fields["text"]
    assert result.rating == insert_fields["rating"]
    assert result.domain_id is None  # Default rating
    assert result.user_entered is False  # Default user_entered
    assert result.edited is False  # Default edited

    # Expected types: 1 UniqueStatement and 2 UniqueStatementSubtopicAssociation
    expected_types = [
        UniqueStatement,
        UniqueStatementSubtopicAssociation,
        UniqueStatementSubtopicAssociation,
    ]

    # Use the helper to validate the session.add calls
    assert_session_add_calls_for_new_unique_statement(mock_session, expected_types)

    # Verify session.flush and session.commit calls
    mock_session.flush.assert_called_once()
    assert mock_session.commit.call_count == 1
    mock_session.refresh.assert_called_once_with(added_unique_statement)
