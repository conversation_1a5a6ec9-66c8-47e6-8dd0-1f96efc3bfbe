import sys
import copy
from unittest.mock import <PERSON><PERSON>ock, patch

import pytest
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.accessors import UniqueStatementAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models import RatingEnum, UniqueStatement, Subtopic, Topic

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def unique_statement_accessor(mock_session_factory):
    return UniqueStatementAccessor(session_factory=mock_session_factory)


def setup_mock_query(mock_session, return_value):
    """Sets up the query chain on the mock session to return a specific value."""
    query_mock = mock_session.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = return_value


def setup_mock_session(mock_session):
    """Sets up the mock session to act as a context manager."""
    mock_session_instance = mock_session.return_value
    mock_session_instance.__enter__.return_value = mock_session_instance
    return mock_session_instance


def create_mock_unique_statement():
    return UniqueStatement(
        unique_statement_id=1,
        text="This is a test description",
        statement_type_id=1,
        rating=RatingEnum.unrated,
        subtopics=[
            Subtopic(
                subtopic_id=4,
                topic=Topic(topic_id=1, name="Collateral"),
                name="Mock Subtopic",
                description="A test subtopic",
            )
        ],
    )


@patch("util.database.Session")
def test_update_unique_statement_successful(mock_session):
    # Arrange
    # Create a new instance to isolate the unit test results
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    # Ensure that the entire query chain returns the mock_unique_statement instance
    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    # Create a mock session factory that returns `mock_session_instance`
    mock_session_factory = MagicMock(return_value=mock_session_instance)

    # Instantiate the accessor with the mock session factory
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock methods that will be called by update_unique_statement
    unique_statement_accessor._process_subtopic_associations = MagicMock()

    # Act: Call the method to test
    result = unique_statement_accessor.update_unique_statement(1, {"text": "Updated text"})

    unique_statement_accessor._process_subtopic_associations.assert_not_called()  # No subtopics passed

    # Verify session handling
    mock_session_instance.commit.assert_called_once()
    mock_session_instance.refresh.assert_called_once_with(
        local_unique_statement
    )  # Ensure refresh is called on the correct instance

    # Ensure the returned result is as expected
    assert result == local_unique_statement


@patch("util.database.Session")
def test_update_unique_statement_with_subtopics(mock_session):
    # Create the mock session instance
    # Create a new instance to isolate the unit test results
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    # Create a mock session factory that returns `mock_session_instance`
    mock_session_factory = MagicMock(return_value=mock_session_instance)

    # Instantiate the accessor with the mock session factory
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock methods that will be called by update_unique_statement
    unique_statement_accessor._process_subtopic_associations = MagicMock()

    # Act
    result = unique_statement_accessor.update_unique_statement(
        1, {"subtopics": [{"subtopic_id": 1, "is_primary": True}]}
    )

    # Verify `_process_subtopic_associations` was called with expected arguments
    unique_statement_accessor._process_subtopic_associations.assert_called_once_with(
        mock_session_instance,
        local_unique_statement.unique_statement_id,
        [{"subtopic_id": 1, "is_primary": True}],
    )

    # Verify `refresh` was called on `MOCK_UNIQUE_STATEMENT`
    mock_session_instance.refresh.assert_called_once_with(local_unique_statement)

    # Ensure the return value is as expected
    assert result == local_unique_statement


def test_update_unique_statement_not_found(unique_statement_accessor, mock_session):
    # Arrange: Set up mock to return None for the query, simulating the unique statement not being found
    mock_query = mock_session.query.return_value
    mock_query.options.return_value.filter_by.return_value.one_or_none.return_value = None

    # Act and Assert: Expect NotFoundException to be raised
    with pytest.raises(NotFoundException, match="Unique statement with ID 1 does not exist."):
        unique_statement_accessor.update_unique_statement(1, {"text": "Some update"})


def test_update_unique_statement_sqlalchemy_error(unique_statement_accessor, mock_session):

    # Set the commit to raise a SQLAlchemyError, not ContentAccessorException
    mock_session.commit.side_effect = SQLAlchemyError("Database error")

    # Act and Assert
    with pytest.raises(
        ContentAccessorException, match="Error updating unique statement: Database error"
    ):
        unique_statement_accessor.update_unique_statement(1, {"text": "Some update"})

    # Verify that rollback was called
    mock_session.rollback.assert_called_once()


@patch("util.database.Session")
def test_update_unique_statement_multiple_primary_subtopics(mock_session):
    # Create the mock session instance
    # Create a new instance to isolate the unit test results
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    # Set up the query chain to return `mock_unique_statement_instance`
    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    # Create a mock session factory that returns `mock_session_instance`
    mock_session_factory = MagicMock(return_value=mock_session_instance)

    # Instantiate the accessor with the mock session factory
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock the methods
    unique_statement_accessor._apply_updates = MagicMock()
    unique_statement_accessor._process_subtopic_associations = MagicMock(
        side_effect=ContentAccessorException("Only one subtopic can be marked as primary")
    )

    # Act and Assert
    with pytest.raises(
        ContentAccessorException, match="Only one subtopic can be marked as primary"
    ):
        unique_statement_accessor.update_unique_statement(
            1,
            {
                "subtopics": [
                    {"subtopic_id": 1, "is_primary": True},
                    {"subtopic_id": 2, "is_primary": True},
                ]
            },
        )


@patch("util.database.Session")
def test_update_unique_statement_statement_type_id_only(mock_session, unique_statement_accessor):
    # Arrange
    # Create a new instance to isolate the unit test results
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    # Ensure that the entire query chain returns the mock_unique_statement instance
    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    # Create a mock session factory that returns `mock_session_instance`
    mock_session_factory = MagicMock(return_value=mock_session_instance)

    # Instantiate the accessor with the mock session factory
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock methods that will be called by update_unique_statement
    unique_statement_accessor._process_subtopic_associations = MagicMock()

    # Act: Only change `statement_type_id`
    result = unique_statement_accessor.update_unique_statement(
        1, {"statement_type_id": 2, "rating": RatingEnum.positive}
    )

    assert result.rating == RatingEnum.positive
    mock_session_instance.commit.assert_called_once()


@patch("util.database.Session")
def test_update_unique_statement_text_only(mock_session, unique_statement_accessor):
    # Arrange
    # Create a copy of `MOCK_UNIQUE_STATEMENT` to ensure isolation
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    # Ensure that the entire query chain returns the mock_unique_statement instance
    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    # Create a mock session factory that returns `mock_session_instance`
    mock_session_factory = MagicMock(return_value=mock_session_instance)

    # Instantiate the accessor with the mock session factory
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock this method because it makes a call to the database
    unique_statement_accessor._process_subtopic_associations = MagicMock()

    # Act: Only change `text`
    result = unique_statement_accessor.update_unique_statement(
        1, {"text": "New text", "rating": "positive"}
    )

    assert result.rating == RatingEnum.positive.value
    mock_session_instance.commit.assert_called_once()


@patch("util.database.Session")
def test_update_unique_statement_text_and_statement_type_id(
    mock_session, unique_statement_accessor
):
    # Arrange
    # Create a copy of `MOCK_UNIQUE_STATEMENT` to ensure isolation
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    # Ensure that the entire query chain returns the mock_unique_statement instance
    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    # Create a mock session factory that returns `mock_session_instance`
    mock_session_factory = MagicMock(return_value=mock_session_instance)

    # Instantiate the accessor with the mock session factory
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock this method because it makes a call to the database
    unique_statement_accessor._process_subtopic_associations = MagicMock()

    # Act: Change both `text` and `statement_type_id`
    result = unique_statement_accessor.update_unique_statement(
        1, {"text": "New text", "statement_type_id": 2, "rating": "positive"}
    )

    assert result.rating == RatingEnum.positive.value
    mock_session_instance.commit.assert_called_once()


@patch("util.database.Session")
def test_update_unique_statement_other_fields_only(mock_session, unique_statement_accessor):
    # Arrange
    # Create a copy of `MOCK_UNIQUE_STATEMENT` to ensure isolation
    local_unique_statement = create_mock_unique_statement()

    mock_session_instance = setup_mock_session(mock_session)
    setup_mock_query(mock_session_instance, local_unique_statement)

    query_mock = mock_session_instance.query.return_value
    query_mock.options.return_value.filter_by.return_value.one_or_none.return_value = (
        local_unique_statement
    )

    mock_session_factory = MagicMock(return_value=mock_session_instance)
    unique_statement_accessor = UniqueStatementAccessor(session_factory=mock_session_factory)

    # Mock `_process_subtopic_associations` to avoid database interactions
    unique_statement_accessor._process_subtopic_associations = MagicMock()

    assert local_unique_statement.rating == RatingEnum.unrated

    # Act: Only update `subtopics`, not `text` or `statement_type_id`
    result = unique_statement_accessor.update_unique_statement(
        1, {"subtopics": [{"subtopic_id": 1, "is_primary": True}]}
    )

    # Assert: Confirm that `rating` did not change to `positive`
    assert result.rating == RatingEnum.unrated  # Ensure the rating has not changed
    mock_session_instance.commit.assert_called_once()
