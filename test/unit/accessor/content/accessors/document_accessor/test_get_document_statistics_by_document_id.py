import unittest
from unittest.mock import MagicMock
from types import SimpleNamespace
from datetime import datetime, timezone

from accessor.content.accessors import DocumentAccessor
from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO
from accessor.content.exceptions import ContentAccessorException


class TestDocumentAccessorGetDocumentStatistics(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.accessor = DocumentAccessor(self.mock_session_factory)

    def test_stats_with_document_and_original(self):
        now = datetime.now(timezone.utc)

        # Mock result with stats for both document and original
        mock_result = SimpleNamespace(
            ds1_word_count=1000,
            ds1_difficult_word_count=200,
            ds1_avg_sentence_length=12.5,
            ds1_avg_word_length=5.2,
            ds1_flesch_grade_level=8.1,
            ds1_flesch_score=65.2,
            ds1_flesch_reading_ease=70.0,
            ds1_created_at=now,
            ds1_updated_at=now,
            ds2_word_count=800,
            ds2_difficult_word_count=180,
            ds2_avg_sentence_length=11.1,
            ds2_avg_word_length=4.8,
            ds2_flesch_grade_level=9.0,
            ds2_flesch_score=60.0,
            ds2_flesch_reading_ease=65.0,
            ds2_created_at=now,
            ds2_updated_at=now,
        )

        self.mock_session.query.return_value.select_from.return_value.outerjoin.return_value.outerjoin.return_value.filter.return_value.first.return_value = (
            mock_result
        )

        doc_stats, orig_stats = self.accessor.get_document_statistics_by_document_id(document_id=1)

        self.assertIsInstance(doc_stats, DocumentStatisticsDTO)
        self.assertIsInstance(orig_stats, DocumentStatisticsDTO)
        self.assertEqual(doc_stats.word_count, 1000)
        self.assertEqual(orig_stats.word_count, 800)

    def test_stats_none_returned(self):
        self.mock_session.query.return_value.select_from.return_value.outerjoin.return_value.outerjoin.return_value.filter.return_value.first.return_value = (
            None
        )

        doc_stats, orig_stats = self.accessor.get_document_statistics_by_document_id(document_id=1)

        self.assertIsNone(doc_stats)
        self.assertIsNone(orig_stats)

    def test_stats_only_document_present(self):
        now = datetime.now(timezone.utc)

        mock_result = SimpleNamespace(
            ds1_word_count=950,
            ds1_difficult_word_count=150,
            ds1_avg_sentence_length=10.0,
            ds1_avg_word_length=5.0,
            ds1_flesch_grade_level=7.5,
            ds1_flesch_score=68.0,
            ds1_flesch_reading_ease=72.0,
            ds1_created_at=now,
            ds1_updated_at=now,
            ds2_word_count=None,  # Original stats are None
            ds2_difficult_word_count=None,
            ds2_avg_sentence_length=None,
            ds2_avg_word_length=None,
            ds2_flesch_grade_level=None,
            ds2_flesch_score=None,
            ds2_flesch_reading_ease=None,
            ds2_created_at=None,
            ds2_updated_at=None,
        )

        self.mock_session.query.return_value.select_from.return_value.outerjoin.return_value.outerjoin.return_value.filter.return_value.first.return_value = (
            mock_result
        )

        doc_stats, orig_stats = self.accessor.get_document_statistics_by_document_id(document_id=2)

        self.assertIsInstance(doc_stats, DocumentStatisticsDTO)
        self.assertIsNone(orig_stats)

    def test_stats_with_enterprise_id_filter(self):
        now = datetime.now(timezone.utc)

        mock_result = SimpleNamespace(
            ds1_word_count=1000,
            ds1_difficult_word_count=200,
            ds1_avg_sentence_length=12.5,
            ds1_avg_word_length=5.2,
            ds1_flesch_grade_level=8.1,
            ds1_flesch_score=65.2,
            ds1_flesch_reading_ease=70.0,
            ds1_created_at=now,
            ds1_updated_at=now,
            ds2_word_count=None,
            ds2_difficult_word_count=None,
            ds2_avg_sentence_length=None,
            ds2_avg_word_length=None,
            ds2_flesch_grade_level=None,
            ds2_flesch_score=None,
            ds2_flesch_reading_ease=None,
            ds2_created_at=None,
            ds2_updated_at=None,
        )

        query_mock = self.mock_session.query.return_value
        filter_result = (
            query_mock.select_from.return_value.outerjoin.return_value.outerjoin.return_value.filter.return_value
        )

        filter_result.filter.return_value.first.return_value = mock_result

        doc_stats, orig_stats = self.accessor.get_document_statistics_by_document_id(
            document_id=1, enterprise_id=42
        )

        self.assertIsInstance(doc_stats, DocumentStatisticsDTO)
        self.assertIsNone(orig_stats)

    def test_stats_raises_exception(self):
        self.mock_session.query.side_effect = Exception("DB failure")

        with self.assertRaises(ContentAccessorException) as context:
            self.accessor.get_document_statistics_by_document_id(document_id=1)

        self.assertIn("Error fetching document statistics", str(context.exception))


if __name__ == "__main__":
    unittest.main()
