import sys
import unittest
from datetime import datetime
from unittest.mock import MagicMock, patch

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from sqlalchemy.exc import IntegrityError

from accessor.content.accessors import DocumentAccessor
from accessor.content.accessors.dto import DocumentInsertDTO
from shared.models import (  # noqa: F401 - import needed to resolve relationship
    Business,
    Document,
    Enterprise,
    JobStatus,
    StatementType,
    Subtopic,
    User,
)


class TestDocumentAccessorSaveData(unittest.TestCase):

    # Runs before each test in this class
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()

        # Simulate the context manager behavior for the session
        # Needed because of this line in the implementation methods: with self.session_factory() as session:
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session

        # Pass the mock session factory to the DocumentAccessor
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_save_document_success(self):
        # Arrange: Define the expected attributes for the new Document
        expected_document_attrs = {
            "domain_id": 1,
            "name": "Test Document",
            "s3_location": "s3://test/location",
            "business_id": 100,
            "effective_datetime": datetime(2023, 10, 12),
            "start_page": 1,
            "end_page": 10,
        }

        # Create a mock for the returned document instance
        mock_document_instance = Document(
            document_id=1,  # Simulate the auto-generated primary key
            **expected_document_attrs,
        )

        # Mock session behavior
        self.mock_session.add.return_value = None
        self.mock_session.commit.return_value = None
        self.mock_session.query.return_value.options.return_value.get.return_value = (
            mock_document_instance
        )

        # Create the DTO
        insert_dto = DocumentInsertDTO(**expected_document_attrs)

        # Act: Call the method
        result = self.document_accessor.save_document(insert_dto)

        # Assert: Ensure session.add() was called with a Document instance
        self.mock_session.add.assert_called_once()
        added_document = self.mock_session.add.call_args[0][0]  # Extract the added document

        # Verify attributes of the added document
        for attr, value in expected_document_attrs.items():
            self.assertEqual(getattr(added_document, attr), value)

        # Assert commit was called
        self.mock_session.commit.assert_called_once()

        # Verify the returned result matches the mock
        self.assertEqual(result.document_id, mock_document_instance.document_id)
        self.assertEqual(result.name, mock_document_instance.name)
        self.assertEqual(result.s3_location, mock_document_instance.s3_location)

    @patch("accessor.content.accessors.document_accessor.Document")  # Patch the Document model
    def test_save_document_failure(self, mock_document):
        # Arrange: Simulate an exception being raised during the session commit
        self.mock_session.commit.side_effect = Exception("Commit failed")

        insert_dto = DocumentInsertDTO(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=1,
            end_page=10,
        )

        # Act & Assert: Ensure the exception is raised and rollback is called
        with self.assertRaises(Exception) as context:
            self.document_accessor.save_document(insert_dto)

        self.assertTrue("Commit failed" in str(context.exception))
        self.mock_session.rollback.assert_called_once()

    @patch("accessor.content.accessors.document_accessor.Document")  # Patch the Document model
    def test_save_document_commit_failure(self, mock_document):
        # Arrange: Simulate commit failure
        self.mock_session.commit.side_effect = Exception("Commit failed")

        insert_dto = DocumentInsertDTO(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=1,
            end_page=10,
        )

        # Act & Assert
        with self.assertRaises(Exception) as context:
            self.document_accessor.save_document(insert_dto)

        self.mock_session.rollback.assert_called_once()  # Ensure rollback is called
        self.assertTrue("Commit failed" in str(context.exception))

    def test_save_document_partial_data(self):
        # Arrange: Define the expected attributes of the added Document instance
        expected_document_attrs = {
            "domain_id": 2,
            "name": "Test Document",
            "s3_location": "s3://test/location",
            "business_id": 100,
            "effective_datetime": datetime(2023, 10, 12),
            "start_page": 1,
            "end_page": 10,
        }

        # Mock session behavior
        self.mock_session.add.return_value = None
        self.mock_session.commit.return_value = None
        self.mock_session.rollback.return_value = None

        # Mock the query chain to return a Document instance
        self.mock_session.query.return_value.options.return_value.get.return_value = Document(
            document_id=1, **expected_document_attrs
        )

        # Create the DTO with partial data
        insert_dto = DocumentInsertDTO(**expected_document_attrs)

        # Act: Call the method under test
        result = self.document_accessor.save_document(insert_dto)

        # Assert: Verify `add` was called with a Document instance having the expected attributes
        self.mock_session.add.assert_called_once()
        added_document = self.mock_session.add.call_args[0][
            0
        ]  # Extract the first argument passed to `add`

        # Verify the attributes of the added Document
        for attr, value in expected_document_attrs.items():
            self.assertEqual(getattr(added_document, attr), value)

        # Verify `commit` was called
        self.mock_session.commit.assert_called_once()

        # Verify the returned document matches the mock
        self.assertEqual(result.start_page, 1)
        self.assertEqual(result.end_page, 10)

    @patch("accessor.content.accessors.document_accessor.Document")
    def test_save_document_unique_constraint_violation(self, mock_document):
        # Simulate IntegrityError when adding a duplicate document
        self.mock_session.commit.side_effect = IntegrityError("Duplicate entry", None, None)

        insert_dto = DocumentInsertDTO(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=1,
            end_page=10,
        )

        # Act & Assert: Expect an IntegrityError
        with self.assertRaises(IntegrityError):
            self.document_accessor.save_document(insert_dto)

        # Assert rollback is called due to the failed commit
        self.mock_session.rollback.assert_called_once()

    # TODO: Determine the boundary values
    def test_save_document_boundary_values(self):
        # Arrange
        insert_dto = DocumentInsertDTO(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime.now(),  # Valid datetime
            start_page=0,  # Boundary value
            end_page=-1,  # Invalid boundary value
        )

        # Create a mock Document instance with attributes matching the DTO
        mock_document = Document(
            document_id=1,
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime.now(),
            start_page=0,
            end_page=-1,
        )

        # Mock session behavior
        self.mock_session.add.return_value = None
        self.mock_session.commit.return_value = None
        self.mock_session.rollback.return_value = None

        # Mock the `session.query(Document).get` chain
        self.mock_session.query.return_value.options.return_value.get.return_value = mock_document

        # Act
        result = self.document_accessor.save_document(insert_dto)

        # Assert
        self.assertEqual(result.start_page, 0)
        self.assertEqual(result.end_page, -1)

    def test_save_document_session_closed(self):
        # Arrange
        insert_dto = DocumentInsertDTO(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=1,
            end_page=10,
        )

        # Act
        self.document_accessor.save_document(insert_dto)

    #     # If you're using session.close() or some cleanup, ensure it's called
    #     self.mock_session.close.assert_called_once()  # Replace with the appropriate method

    def set_mock_document_values(self, mock_document_instance):
        # Define some expected values
        mock_document_instance.document_id = 1
        mock_document_instance.domain_id = 2
        mock_document_instance.name = "Test Document"
        mock_document_instance.s3_location = "s3://test/location"
        mock_document_instance.business_id = 100
        mock_document_instance.effective_datetime = datetime(2024, 10, 12)
        mock_document_instance.start_page = 1
        mock_document_instance.end_page = 10

    def test_save_document_with_enterprise_id(self):
        # Arrange
        expected_attrs = {
            "domain_id": 1,
            "name": "Enterprise Document",
            "s3_location": "s3://test/enterprise",
            "business_id": 200,
            "effective_datetime": datetime(2023, 12, 1),
            "start_page": 1,
            "end_page": 20,
            "enterprise_id": 42,  # New field
        }

        insert_dto = DocumentInsertDTO(**expected_attrs)

        mock_document = Document(document_id=123, **expected_attrs)
        self.mock_session.query.return_value.options.return_value.get.return_value = mock_document

        # Act
        result = self.document_accessor.save_document(insert_dto)

        # Assert
        self.mock_session.add.assert_called_once()
        added_doc = self.mock_session.add.call_args[0][0]
        self.assertEqual(added_doc.enterprise_id, 42)
        self.assertEqual(result.enterprise_id, 42)
