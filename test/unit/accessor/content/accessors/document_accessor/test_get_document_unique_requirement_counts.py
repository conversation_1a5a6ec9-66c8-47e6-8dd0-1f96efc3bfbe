import unittest
from unittest.mock import MagicMock
from types import SimpleNamespace
from sqlalchemy.sql.elements import BinaryExpression
from accessor.content.accessors import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException
from shared.models import Document


class TestDocumentAccessorGetUniqueRequirementCounts(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_get_document_unique_requirement_counts_with_enterprise_id(self):
        # Arrange
        expected_counts = SimpleNamespace(unrated_count=2, positive_count=1, negative_count=0)

        query_mock = self.mock_session.query.return_value
        join_result = query_mock.join.return_value
        filter_result = join_result.filter.return_value
        final_filter_result = filter_result.filter.return_value
        group_by_result = final_filter_result.group_by.return_value
        group_by_result.one_or_none.return_value = expected_counts

        # Act
        result = self.document_accessor.get_document_unique_requirement_counts(
            document_id=1,
            enterprise_id=99,
        )

        # Assert counts
        self.assertEqual(result["unrated_count"], 2)
        self.assertEqual(result["positive_count"], 1)
        self.assertEqual(result["negative_count"], 0)

        # ✅ Check if any of the filter calls included `Document.enterprise_id == 99`
        found = False
        for call in filter_result.filter.call_args_list:
            for arg in call[0]:
                if (
                    isinstance(arg, BinaryExpression)
                    and hasattr(arg.left, "name")
                    and arg.left.name == "enterprise_id"
                    and getattr(arg.right, "value", None) == 99
                ):
                    found = True
                    break

        self.assertTrue(found, "Expected enterprise_id filter was not found.")

    # Keep the other tests unchanged
    def test_get_document_unique_requirement_counts_no_data(self):
        self.mock_session.query.return_value.join.return_value.filter.return_value.group_by.return_value.one_or_none.return_value = (
            None
        )

        result = self.document_accessor.get_document_unique_requirement_counts(document_id=1)

        self.assertEqual(result["unrated_count"], 0)
        self.assertEqual(result["positive_count"], 0)
        self.assertEqual(result["negative_count"], 0)

    def test_get_document_unique_requirement_counts_raises_exception(self):
        self.mock_session.query.side_effect = Exception("DB failure")

        with self.assertRaises(ContentAccessorException) as context:
            self.document_accessor.get_document_unique_requirement_counts(document_id=1)

        self.assertIn("Error fetching document unique requirement counts", str(context.exception))


if __name__ == "__main__":
    unittest.main()
