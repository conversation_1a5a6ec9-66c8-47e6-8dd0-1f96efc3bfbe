import sys
import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from sqlalchemy.exc import IntegrityError

from accessor.content.accessors import DocumentAccessor

from shared.models import (
    Document,
    StatementType,
    Subtopic,
    JobStatus,
    Business,
    Enterprise,
    User,
)  # noqa: F401 - import needed to resolve relationship


class TestDocumentAccessorSaveData(unittest.TestCase):

    # Runs before each test in this class
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()

        # Simulate the context manager behavior for the session
        # Needed because of this line in the implementation methods: with self.session_factory() as session:
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session

        # Pass the mock session factory to the DocumentAccessor
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    @patch("accessor.content.accessors.document_accessor.Document")  # Patch the Document model
    def test_save_document_success(self, mock_document):
        # Arrange: Mock the document instance
        mock_document_instance = MagicMock()
        mock_document.return_value = mock_document_instance

        self.set_mock_document_values(mock_document_instance)

        # Act: Call the method
        result = self.document_accessor.save_document(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=1,
            end_page=10,
        )

        # Assert: Ensure session.add() was called with the correct mock document
        self.mock_session.add.assert_called_once_with(
            mock_document_instance
        )  # Ensure the mock instance is added
        self.mock_session.commit.assert_called_once()  # Ensure commit is called

        # Verify the returned result
        self.assertEqual(result["document_id"], mock_document_instance.document_id)
        self.assertEqual(result["name"], mock_document_instance.name)
        self.assertEqual(result["s3_location"], mock_document_instance.s3_location)

    @patch("accessor.content.accessors.document_accessor.Document")  # Patch the Document model
    def test_save_document_failure(self, mock_document):
        # Arrange: Simulate an exception being raised during the session commit
        self.mock_session.commit.side_effect = Exception("Commit failed")

        # Act & Assert: Ensure the exception is raised and rollback is called
        with self.assertRaises(Exception) as context:
            self.document_accessor.save_document(
                domain_id=2,
                name="Test Document",
                s3_location="s3://test/location",
                business_id=100,
                effective_datetime=datetime(2023, 10, 12),
                start_page=1,
                end_page=10,
            )

        self.assertTrue("Commit failed" in str(context.exception))
        self.mock_session.rollback.assert_called_once()

    def test_save_document_missing_required_fields(self):
        with self.assertRaises(ValueError):  # Assuming the method raises ValueError
            self.document_accessor.save_document(
                domain_id=2,
                name=None,  # Invalid value for a required field
                s3_location="s3://test/location",
                business_id=100,
                effective_datetime=datetime(2023, 10, 12),
                start_page=1,
                end_page=10,
            )

    @patch("accessor.content.accessors.document_accessor.Document")  # Patch the Document model
    def test_save_document_commit_failure(self, mock_document):
        # Arrange: Simulate commit failure
        self.mock_session.commit.side_effect = Exception("Commit failed")

        # Act & Assert
        with self.assertRaises(Exception) as context:
            self.document_accessor.save_document(
                domain_id=2,
                name="Test Document",
                s3_location="s3://test/location",
                business_id=100,
                effective_datetime=datetime(2023, 10, 12),
                start_page=1,
                end_page=10,
            )

        self.mock_session.rollback.assert_called_once()  # Ensure rollback is called
        self.assertTrue("Commit failed" in str(context.exception))

    @patch("accessor.content.accessors.document_accessor.Document")  # Patch the Document model
    def test_save_document_partial_data(self, mock_document):
        # Arrange: Mock the document instance with some missing data
        mock_document_instance = MagicMock()
        mock_document.return_value = mock_document_instance

        # Set expected values for the mock document instance
        self.set_mock_document_values(mock_document_instance)

        # Act: Call the method with partial data
        result = self.document_accessor.save_document(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=None,  # Missing or None
            end_page=None,  # Missing or None
        )

        # Assert: Ensure that it still calls add and commit
        self.mock_session.add.assert_called_once_with(mock_document_instance)
        self.mock_session.commit.assert_called_once()

        # Assert the result contains None for missing fields
        self.assertEqual(result["start_page"], 1)
        self.assertEqual(result["end_page"], 10)

    @patch("accessor.content.accessors.document_accessor.Document")
    def test_save_document_unique_constraint_violation(self, mock_document):
        # Simulate IntegrityError when adding a duplicate document
        self.mock_session.commit.side_effect = IntegrityError("Duplicate entry", None, None)

        # Act & Assert: Expect an IntegrityError
        with self.assertRaises(IntegrityError):
            self.document_accessor.save_document(
                domain_id=2,
                name="Test Document",  # Assume 'Test Document' already exists
                s3_location="s3://test/location",
                business_id=100,
                effective_datetime=datetime(2023, 10, 12),
                start_page=1,
                end_page=10,
            )

        # Assert rollback is called due to the failed commit
        self.mock_session.rollback.assert_called_once()

    def test_save_document_invalid_data_type(self):
        with self.assertRaises(TypeError):  # Or ValueError, depending on how you handle it
            self.document_accessor.save_document(
                domain_id=2,
                name="Test Document",
                s3_location="s3://test/location",
                business_id=100,
                effective_datetime="Invalid Date",  # Invalid type (should be a datetime)
                start_page=1,
                end_page=10,
            )

    # TODO: Determine the boundary values
    def test_save_document_boundary_values(self):
        result = self.document_accessor.save_document(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=0,  # Boundary value
            end_page=-1,  # Invalid boundary value
        )
        self.assertEqual(result["start_page"], 0)
        self.assertEqual(result["end_page"], -1)  # Depending on how you handle it

    def test_save_document_session_closed(self):
        self.document_accessor.save_document(
            domain_id=2,
            name="Test Document",
            s3_location="s3://test/location",
            business_id=100,
            effective_datetime=datetime(2023, 10, 12),
            start_page=1,
            end_page=10,
        )

    #     # If you're using session.close() or some cleanup, ensure it's called
    #     self.mock_session.close.assert_called_once()  # Replace with the appropriate method

    def set_mock_document_values(self, mock_document_instance):
        # Define some expected values
        mock_document_instance.document_id = 1
        mock_document_instance.domain_id = 2
        mock_document_instance.name = "Test Document"
        mock_document_instance.s3_location = "s3://test/location"
        mock_document_instance.business_id = 100
        mock_document_instance.effective_datetime = datetime(2024, 10, 12)
        mock_document_instance.start_page = 1
        mock_document_instance.end_page = 10


# if __name__ == "__main__":
#     unittest.main()
