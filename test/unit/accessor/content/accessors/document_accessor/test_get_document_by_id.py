import unittest
from unittest.mock import MagicMock
from shared.models import Document, Job
from accessor.content.accessors import DocumentAccessor


class TestDocumentAccessorGetDocumentById(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_get_document_by_id_without_enterprise_id(self):
        # Arrange
        mock_document = Document(document_id=1, name="Test Doc")
        self.mock_session.query.return_value.options.return_value.filter_by.return_value.one_or_none.return_value = (
            mock_document
        )

        # Act
        result = self.document_accessor.get_document_by_id(document_id=1)

        # Assert
        self.assertEqual(result, mock_document)
        self.mock_session.query.assert_called_once()
        self.mock_session.query.return_value.options.assert_called_once()

    def test_get_document_by_id_with_enterprise_id(self):
        # Arrange
        mock_document = Document(document_id=1, name="Test Doc", enterprise_id=5)

        query_mock = self.mock_session.query.return_value
        options_result = query_mock.options.return_value
        # Simulate chaining: .filter_by(document_id=1) => then .filter_by(enterprise_id=5)
        filter_by_result = options_result.filter_by.return_value
        filter_result = filter_by_result.filter_by.return_value
        filter_result.one_or_none.return_value = mock_document

        # Act
        result = self.document_accessor.get_document_by_id(document_id=1, enterprise_id=5)

        # Assert
        self.assertEqual(result, mock_document)
        filter_result.one_or_none.assert_called_once()

    def test_get_document_by_id_not_found(self):
        # Arrange
        self.mock_session.query.return_value.options.return_value.filter_by.return_value.one_or_none.return_value = (
            None
        )

        # Act
        result = self.document_accessor.get_document_by_id(document_id=999)

        # Assert
        self.assertIsNone(result)


if __name__ == "__main__":
    unittest.main()
