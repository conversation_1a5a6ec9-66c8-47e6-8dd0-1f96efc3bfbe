import unittest
from unittest.mock import MagicMock, patch
from accessor.content.accessors import Document<PERSON>ccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException


class TestDocumentAccessorGetSubtopicCounts(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.accessor = DocumentAccessor(self.mock_session_factory)

    @patch.object(DocumentAccessor, "verify_document_exists")
    def test_get_subtopic_counts_success(self, mock_verify_exists):
        # Arrange
        mock_verify_exists.return_value = True

        mock_result = [
            {
                "unrated_requirements_count": 3,
                "unrated_artifact_counts": 5,
                "subtopic_id": 1,
                "topic_id": 10,
                "subtopic_name": "Foo",
                "subtopic_description": "Bar",
                "business_id": 123,
                "topic_name": "Baz",
            }
        ]

        # Simulate return value of .all()
        self.mock_session.query.return_value.join.return_value.join.return_value.join.return_value.join.return_value.filter.return_value.group_by.return_value.all.return_value = (
            mock_result
        )

        # Act
        result = self.accessor.get_subtopic_counts(document_id=1)

        # Assert
        self.assertEqual(result, mock_result)

    @patch.object(DocumentAccessor, "verify_document_exists")
    def test_get_subtopic_counts_document_not_found(self, mock_verify_exists):
        # Arrange
        mock_verify_exists.return_value = False

        # Act & Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.accessor.get_subtopic_counts(document_id=999)

        self.assertIn(
            "Document with ID 999 does not exist or is not accessible", str(context.exception)
        )

    @patch.object(DocumentAccessor, "verify_document_exists")
    def test_get_subtopic_counts_query_raises_error(self, mock_verify_exists):
        # Arrange
        mock_verify_exists.return_value = True
        self.mock_session.query.side_effect = Exception("DB failure")

        # Act & Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.accessor.get_subtopic_counts(document_id=1)

        self.assertIn("Error fetching document subtopic artifact counts", str(context.exception))


if __name__ == "__main__":
    unittest.main()
