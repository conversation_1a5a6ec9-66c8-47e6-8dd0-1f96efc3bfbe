import unittest
from unittest.mock import MagicMock, patch
from types import SimpleNamespace

from accessor.content.accessors import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException


class TestDocumentAccessorGetDocumentSubtopicCounts(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.accessor = DocumentAccessor(self.mock_session_factory)

    @patch.object(DocumentAccessor, "verify_document_exists")
    def test_get_document_subtopic_counts_success(self, mock_verify_exists):
        # Arrange
        mock_verify_exists.return_value = True

        mock_result = SimpleNamespace(
            unrated_requirements_count=5,
            unrated_artifact_counts=10,
            subtopic_id=123,
            topic_id=456,
            subtopic_name="Mock Subtopic",
            subtopic_description="Description here",
            business_id=789,
            topic_name="Mock Topic",
        )

        query = self.mock_session.query.return_value
        query.join.return_value = query
        query.outerjoin.return_value = query
        query.filter.return_value = query
        query.group_by.return_value.all.return_value = [mock_result]

        # Act
        result = self.accessor.get_document_subtopic_counts(document_id=1)

        # Assert
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0].subtopic_id, 123)
        self.assertEqual(result[0].topic_name, "Mock Topic")

    @patch.object(DocumentAccessor, "verify_document_exists")
    def test_get_document_subtopic_counts_document_not_found(self, mock_verify_exists):
        # Arrange
        mock_verify_exists.return_value = False

        # Act & Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.accessor.get_document_subtopic_counts(document_id=999)

        self.assertIn("does not exist or is not accessible", str(context.exception))


if __name__ == "__main__":
    unittest.main()
