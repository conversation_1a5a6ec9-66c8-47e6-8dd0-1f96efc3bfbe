import unittest
from unittest.mock import <PERSON>Mock

from sqlalchemy.exc import Integrity<PERSON>rror

from accessor.content.accessors import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException
from shared.models import Document


class TestDocumentAccessorVerifyDocumentExists(unittest.TestCase):

    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()

        # Simulate the context manager behavior for the session
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session

        # Pass the mock session factory to the DocumentAccessor
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_verify_document_exists_false(self):
        # Arrange
        mock_inner_query = MagicMock()
        mock_outer_query = MagicMock()

        # Ensure the context manager returns the correct mock session
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session

        # Set up the inner and outer query behavior
        self.mock_session.query.return_value = mock_outer_query
        mock_outer_query.scalar.return_value = False
        self.mock_session.query(Document).filter_by.return_value.exists.return_value = (
            mock_inner_query
        )
        mock_inner_query.scalar.return_value = False

        # Act
        result = self.document_accessor.verify_document_exists(999)

        # Assert
        assert result is False
        self.mock_session.query.assert_called_with(mock_inner_query)
        self.mock_session.query(Document).filter_by.assert_called_once_with(document_id=999)

    def test_verify_document_exists_true(self):
        # Arrange
        mock_inner_query = MagicMock()
        mock_outer_query = MagicMock()

        # Set up the mock to return True
        self.mock_session.query.return_value = mock_outer_query
        mock_outer_query.scalar.return_value = True
        self.mock_session.query(Document).filter_by.return_value.exists.return_value = (
            mock_inner_query
        )
        mock_inner_query.scalar.return_value = True

        # Act
        result = self.document_accessor.verify_document_exists(1)

        # Assert
        assert result is True
        self.mock_session.query.assert_called_with(mock_inner_query)
        self.mock_session.query(Document).filter_by.assert_called_once_with(document_id=1)

    def test_verify_document_exists_raises_exception(self):
        # Arrange
        self.mock_session.query.side_effect = IntegrityError("Mock Error", "params", "orig")

        # Act & Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.document_accessor.verify_document_exists(1)

        # Verify that the underlying exception is an IntegrityError
        self.assertIsInstance(context.exception.__cause__, IntegrityError)
        self.assertIn("Mock Error", str(context.exception.__cause__))

    def test_verify_document_exists_invalid_input(self):
        # Arrange
        # Here, you could simulate behavior when `document_id` is None
        with self.assertRaises(TypeError):
            self.document_accessor.verify_document_exists(None)

    def test_verify_document_exists_raises_content_accessor_exception(self):
        # Arrange: Simulate the IntegrityError when the inner query is called
        self.mock_session_factory.return_value.__enter__.return_value.query.side_effect = (
            IntegrityError("Mock Error", "params", "orig")
        )

        # Act & Assert: Expecting ContentAccessorException
        with self.assertRaises(ContentAccessorException) as context:
            self.document_accessor.verify_document_exists(1)

        # Verify the exception message
        assert "Error verifying that document exists" in str(context.exception)
        assert "Mock Error" in str(context.exception)
