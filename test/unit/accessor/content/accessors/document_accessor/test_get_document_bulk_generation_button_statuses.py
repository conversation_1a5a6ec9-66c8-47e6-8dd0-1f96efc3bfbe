import unittest
from unittest.mock import MagicMock
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.accessors import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException


class TestDocumentAccessorGetBulkGenerationButtonStatuses(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_both_flags_true(self):
        # Arrange
        self.mock_session.query.return_value.scalar.side_effect = [True, True]

        # Act
        result = self.document_accessor.get_document_bulk_generation_button_statuses(document_id=1)

        # Assert
        self.assertTrue(result["can_generate_ac"])
        self.assertTrue(result["can_generate_tc"])

    def test_both_flags_false(self):
        # Arrange
        self.mock_session.query.return_value.scalar.side_effect = [False, False]

        # Act
        result = self.document_accessor.get_document_bulk_generation_button_statuses(document_id=1)

        # Assert
        self.assertFalse(result["can_generate_ac"])
        self.assertFalse(result["can_generate_tc"])

    def test_ac_true_tc_false(self):
        # Arrange
        self.mock_session.query.return_value.scalar.side_effect = [True, False]

        # Act
        result = self.document_accessor.get_document_bulk_generation_button_statuses(document_id=1)

        # Assert
        self.assertTrue(result["can_generate_ac"])
        self.assertFalse(result["can_generate_tc"])

    def test_ac_false_tc_true(self):
        # Arrange
        self.mock_session.query.return_value.scalar.side_effect = [False, True]

        # Act
        result = self.document_accessor.get_document_bulk_generation_button_statuses(document_id=1)

        # Assert
        self.assertFalse(result["can_generate_ac"])
        self.assertTrue(result["can_generate_tc"])

    def test_exception_raised(self):
        # Arrange
        self.mock_session.query.side_effect = SQLAlchemyError("Boom")

        # Act / Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.document_accessor.get_document_bulk_generation_button_statuses(document_id=1)

        self.assertIn("Error determining bulk generation button statuses", str(context.exception))


if __name__ == "__main__":
    unittest.main()
