import unittest
from unittest.mock import MagicMock, patch
from types import SimpleNamespace

from accessor.content.accessors import DocumentAccessor


@patch("accessor.content.accessors.document_accessor.JobAccessor.get_job_status_statement_filters")
class TestDocumentAccessorGetStatementCounts(unittest.TestCase):
    def setUp(self):
        self.mock_session_factory = MagicMock()
        self.mock_session = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_get_document_statement_counts_with_results(self, mock_filters):
        # Arrange
        mock_filters.return_value = []

        expected_counts = SimpleNamespace(
            unrated_count=5,
            positive_count=3,
            negative_count=2,
        )

        # Mock the full SQLAlchemy query chain
        self.mock_session.query.return_value.join.return_value.join.return_value.filter.return_value.group_by.return_value.one_or_none.return_value = (
            expected_counts
        )

        # Act
        result = self.document_accessor.get_document_statement_counts(document_id=1)

        # Assert
        self.assertEqual(result["unrated_count"], 5)
        self.assertEqual(result["positive_count"], 3)
        self.assertEqual(result["negative_count"], 2)

    def test_get_document_statement_counts_no_results(self, mock_filters):
        # Arrange
        mock_filters.return_value = []

        self.mock_session.query.return_value.join.return_value.join.return_value.filter.return_value.group_by.return_value.one_or_none.return_value = (
            None
        )

        # Act
        result = self.document_accessor.get_document_statement_counts(document_id=123)

        # Assert
        self.assertEqual(result["unrated_count"], 0)
        self.assertEqual(result["positive_count"], 0)
        self.assertEqual(result["negative_count"], 0)

    def test_get_document_statement_counts_with_enterprise_id(self, mock_filters):
        # Arrange
        mock_filters.return_value = []

        expected_counts = SimpleNamespace(
            unrated_count=2,
            positive_count=6,
            negative_count=1,
        )

        # Build the query chain
        query_mock = self.mock_session.query.return_value
        join1 = query_mock.join.return_value
        join2 = join1.join.return_value
        filtered = join2.filter.return_value
        filtered.filter.return_value = filtered  # simulate .filter().filter() call
        group_by_result = filtered.group_by.return_value
        group_by_result.one_or_none.return_value = expected_counts

        # Act
        result = self.document_accessor.get_document_statement_counts(
            document_id=42, enterprise_id=99
        )

        # Assert
        self.assertEqual(result["unrated_count"], 2)
        self.assertEqual(result["positive_count"], 6)
        self.assertEqual(result["negative_count"], 1)


if __name__ == "__main__":
    unittest.main()
