import unittest
from unittest.mock import MagicMock, patch
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.accessors import DocumentAccessor
from shared.models import Document


class TestDocumentAccessorDeleteDocument(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session

        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    @patch.object(DocumentAccessor, "get_document_by_id")
    def test_delete_document_success(self, mock_get_document_by_id):
        # Arrange
        mock_document = Document(document_id=1)
        mock_get_document_by_id.return_value = mock_document

        # Act
        result = self.document_accessor.delete_document(document_id=1, enterprise_id=42)

        # Assert
        mock_get_document_by_id.assert_called_once_with(1, 42)
        self.mock_session.delete.assert_called_once_with(mock_document)
        self.mock_session.commit.assert_called_once()
        self.assertEqual(result, "Document deleted successfully")

    @patch.object(DocumentAccessor, "get_document_by_id")
    def test_delete_document_not_found(self, mock_get_document_by_id):
        # Arrange
        mock_get_document_by_id.return_value = None

        # Act
        result = self.document_accessor.delete_document(document_id=99, enterprise_id=7)

        # Assert
        self.assertEqual(result, "Document not found or access denied")
        self.mock_session.delete.assert_not_called()
        self.mock_session.commit.assert_not_called()

    @patch.object(DocumentAccessor, "get_document_by_id")
    def test_delete_document_database_error(self, mock_get_document_by_id):
        # Arrange
        mock_document = Document(document_id=1)
        mock_get_document_by_id.return_value = mock_document

        self.mock_session.delete.side_effect = SQLAlchemyError("DB error")

        # Act
        result = self.document_accessor.delete_document(document_id=1)

        # Assert
        self.mock_session.rollback.assert_called_once()
        self.assertIn("Error deleting document", result)
        self.assertIn("DB error", result)


if __name__ == "__main__":
    unittest.main()
