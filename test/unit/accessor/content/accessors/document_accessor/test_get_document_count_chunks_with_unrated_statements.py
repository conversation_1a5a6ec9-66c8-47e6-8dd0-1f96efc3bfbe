import unittest
from unittest.mock import MagicMock, patch
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.accessors import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException


@patch("accessor.content.accessors.document_accessor.JobAccessor.get_job_status_statement_filters")
class TestDocumentAccessorGetChunkCountWithUnratedStatements(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_get_document_count_chunks_with_unrated_statements_success(self, mock_filters):
        # Arrange
        mock_filters.return_value = []
        self.mock_session.query.return_value.join.return_value.join.return_value.filter.return_value.scalar.return_value = (
            7
        )

        # Act
        result = self.document_accessor.get_document_count_chunks_with_unrated_statements(1)

        # Assert
        self.assertEqual(result, 7)

    def test_get_document_count_chunks_with_unrated_statements_with_enterprise(self, mock_filters):
        # Arrange
        mock_filters.return_value = []
        mock_query = (
            self.mock_session.query.return_value.join.return_value.join.return_value.filter.return_value
        )

        mock_query.filter.return_value.scalar.return_value = 4

        # Act
        result = self.document_accessor.get_document_count_chunks_with_unrated_statements(
            document_id=1, enterprise_id=10
        )

        # Assert
        self.assertEqual(result, 4)

    def test_get_document_count_chunks_with_unrated_statements_no_results(self, mock_filters):
        # Arrange
        mock_filters.return_value = []
        self.mock_session.query.return_value.join.return_value.join.return_value.filter.return_value.scalar.return_value = (
            0
        )

        # Act
        result = self.document_accessor.get_document_count_chunks_with_unrated_statements(1)

        # Assert
        self.assertEqual(result, 0)

    def test_get_document_count_chunks_with_unrated_statements_raises_exception(self, mock_filters):
        # Arrange
        mock_filters.return_value = []
        self.mock_session.query.side_effect = SQLAlchemyError("DB error")

        # Act & Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.document_accessor.get_document_count_chunks_with_unrated_statements(1)

        self.assertIn("Error counting unrated statement chunks", str(context.exception))


if __name__ == "__main__":
    unittest.main()
