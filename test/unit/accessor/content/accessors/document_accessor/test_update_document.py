import unittest
from unittest.mock import MagicMock
from datetime import datetime
from accessor.content.accessors import DocumentAccessor
from accessor.content.accessors.dto import DocumentUpdateDTO
from shared.models import Document


class TestDocumentAccessorUpdateDocument(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session

        self.document_accessor = DocumentAccessor(self.mock_session_factory)

        # Prevent session.refresh() from corrupting test object
        self.mock_session.refresh.side_effect = lambda obj: None

    def test_update_document_success(self):
        # Arrange
        original_document = Document(
            document_id=1,
            domain_id=10,
            name="Old Name",
            s3_location="s3://old/location",
            business_id=1,
            effective_datetime=datetime(2023, 1, 1),
            start_page=1,
            end_page=5,
        )

        # Mock the full query chain
        query_mock = self.mock_session.query.return_value
        options_result = query_mock.options.return_value
        filter_by_result = options_result.filter_by.return_value
        filter_result = filter_by_result.filter.return_value
        filter_result.one_or_none.return_value = original_document

        # Prevent refresh from modifying the object
        self.mock_session.refresh.side_effect = lambda obj: None

        update_dto = DocumentUpdateDTO(
            name="New Name", s3_location="s3://new/location", start_page=2, end_page=20
        )

        # Act
        result = self.document_accessor.update_document(1, update_dto, enterprise_id=10)

        # Assert: values were updated
        self.assertEqual(result.name, "New Name")
        self.assertEqual(result.s3_location, "s3://new/location")
        self.assertEqual(result.start_page, 2)
        self.assertEqual(result.end_page, 20)

        # Ensure commit and refresh were called
        self.mock_session.commit.assert_called_once()
        self.mock_session.refresh.assert_called_once_with(original_document)

    def test_update_document_partial_update(self):
        # Arrange
        original_document = Document(
            document_id=1,
            domain_id=10,
            name="Original Name",
            s3_location="s3://old/location",
            business_id=1,
            effective_datetime=datetime(2023, 1, 1),
            start_page=1,
            end_page=10,
        )

        # Mock the query chain
        query_mock = self.mock_session.query.return_value
        options_result = query_mock.options.return_value
        filter_by_result = options_result.filter_by.return_value
        filter_result = filter_by_result.filter.return_value
        filter_result.one_or_none.return_value = original_document

        # Prevent refresh from mutating the object
        self.mock_session.refresh.side_effect = lambda obj: None

        # Only updating name; s3_location should remain unchanged
        update_dto = DocumentUpdateDTO(name="Updated Name")

        # Act
        result = self.document_accessor.update_document(
            document_id=1, update_dto=update_dto, enterprise_id=10
        )

        # Assert
        self.assertEqual(result.name, "Updated Name")
        self.assertEqual(result.s3_location, "s3://old/location")
        self.assertEqual(result.start_page, 1)
        self.assertEqual(result.end_page, 10)

    def test_update_document_not_found(self):
        # Arrange
        # Build the mock chain: query().options().filter_by().filter().one_or_none()
        query_mock = self.mock_session.query.return_value
        options_result = query_mock.options.return_value
        filter_by_result = options_result.filter_by.return_value
        filter_result = filter_by_result.filter.return_value
        filter_result.one_or_none.return_value = None

        # DTO doesn't matter in this case because we simulate not finding the document
        update_dto = DocumentUpdateDTO(name="Doesn't Matter")

        # Act & Assert: Expect ValueError due to document not being found
        with self.assertRaises(ValueError) as context:
            self.document_accessor.update_document(
                document_id=999, update_dto=update_dto, enterprise_id=7
            )

        self.assertIn("not found or access is denied", str(context.exception))

        # Ensure that commit is not called
        self.mock_session.commit.assert_not_called()

    def test_update_document_commit_failure_rolls_back(self):
        doc = Document(document_id=1, name="Old")
        self.mock_session.query.return_value.filter_by.return_value.filter.return_value.one_or_none.return_value = (
            doc
        )
        self.mock_session.commit.side_effect = Exception("DB failure")

        update_dto = DocumentUpdateDTO(name="New Name")

        with self.assertRaises(Exception) as context:
            self.document_accessor.update_document(1, update_dto, enterprise_id=99)

        self.assertEqual(str(context.exception), "DB failure")
        self.mock_session.rollback.assert_called_once()

    def test_update_document_without_enterprise_id(self):
        doc = Document(document_id=1, name="Old")
        self.mock_session.query.return_value.filter_by.return_value.one_or_none.return_value = doc

        update_dto = DocumentUpdateDTO(name="Updated")

        result = self.document_accessor.update_document(1, update_dto)

        self.assertEqual(result.name, "Updated")
        self.mock_session.commit.assert_called_once()


if __name__ == "__main__":
    unittest.main()
