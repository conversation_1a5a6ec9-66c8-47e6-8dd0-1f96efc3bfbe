import unittest
from unittest.mock import MagicMock
from accessor.content.accessors import Document<PERSON>ccessor
from shared.models import Document
from accessor.content.exceptions import ContentAccessorException


class TestDocumentAccessorGetByAcceptanceCriteriaId(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.document_accessor = DocumentAccessor(self.mock_session_factory)

    def test_get_document_by_acceptance_criteria_id_success(self):
        # Arrange
        mock_document = Document(document_id=1, name="Test Doc")
        self.mock_session.query.return_value.join.return_value.join.return_value.join.return_value.filter.return_value.one_or_none.return_value = (
            mock_document
        )

        # Act
        result = self.document_accessor.get_document_by_acceptance_criteria_id(
            acceptance_criteria_id=99
        )

        # Assert
        self.assertEqual(result, mock_document)

    def test_get_document_by_acceptance_criteria_id_not_found(self):
        # Arrange
        self.mock_session.query.return_value.join.return_value.join.return_value.join.return_value.filter.return_value.one_or_none.return_value = (
            None
        )

        # Act
        result = self.document_accessor.get_document_by_acceptance_criteria_id(
            acceptance_criteria_id=1234
        )

        # Assert
        self.assertIsNone(result)

    def test_get_document_by_acceptance_criteria_id_raises_exception(self):
        # Arrange
        self.mock_session.query.side_effect = Exception("Something went wrong")

        # Act & Assert
        with self.assertRaises(ContentAccessorException) as context:
            self.document_accessor.get_document_by_acceptance_criteria_id(
                acceptance_criteria_id=1234
            )

        self.assertIn("Error fetching document by acceptance criteria id", str(context.exception))


if __name__ == "__main__":
    unittest.main()
