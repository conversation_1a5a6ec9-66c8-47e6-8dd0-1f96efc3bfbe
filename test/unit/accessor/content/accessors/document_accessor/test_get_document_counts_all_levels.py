import unittest
from unittest.mock import MagicMock, patch
from types import SimpleNamespace
from accessor.content.accessors import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException


class TestDocumentAccessorGetDocumentCountsAllLevels(unittest.TestCase):
    def setUp(self):
        self.mock_session = MagicMock()
        self.mock_session_factory = MagicMock()
        self.mock_session_factory.return_value.__enter__.return_value = self.mock_session
        self.accessor = DocumentAccessor(self.mock_session_factory)

    def test_valid_counts_returned(self):
        # Mock result objects for each level
        unique_statement_counts = SimpleNamespace(
            unrated_count=1, positive_count=2, negative_count=3
        )
        user_story_counts = SimpleNamespace(unrated_count=4, positive_count=5, negative_count=6)
        ac_counts = SimpleNamespace(unrated_count=7, positive_count=8, negative_count=9)
        tc_counts = SimpleNamespace(unrated_count=10, positive_count=11, negative_count=12)

        # Create separate query mock chains for each .query() call
        unique_query = MagicMock()
        unique_query.join.return_value.filter.return_value.one.return_value = (
            unique_statement_counts
        )

        user_story_query = MagicMock()
        user_story_query.join.return_value.join.return_value.filter.return_value.one.return_value = (
            user_story_counts
        )

        ac_query = MagicMock()
        ac_query.join.return_value.join.return_value.join.return_value.filter.return_value.one.return_value = (
            ac_counts
        )

        tc_query = MagicMock()
        tc_query.join.return_value.join.return_value.join.return_value.join.return_value.filter.return_value.one.return_value = (
            tc_counts
        )

        # Assign to mock_session.query(). Use side_effect to return different chains per call
        self.mock_session.query.side_effect = [unique_query, user_story_query, ac_query, tc_query]

        result = self.accessor.get_document_counts_all_levels(document_id=1)

        self.assertEqual(result["unrated_count"], 22)  # 1 + 4 + 7 + 10
        self.assertEqual(result["positive_count"], 26)  # 2 + 5 + 8 + 11
        self.assertEqual(result["negative_count"], 30)  # 3 + 6 + 9 + 12

        self.assertEqual(result["unrated_requirements_count"], 1)
        self.assertEqual(result["positive_requirements_count"], 2)
        self.assertEqual(result["negative_requirements_count"], 3)

        self.assertEqual(result["unrated_user_story_count"], 4)
        self.assertEqual(result["positive_user_story_count"], 5)
        self.assertEqual(result["negative_user_story_count"], 6)

        self.assertEqual(result["unrated_acceptance_criteria_count"], 7)
        self.assertEqual(result["positive_acceptance_criteria_count"], 8)
        self.assertEqual(result["negative_acceptance_criteria_count"], 9)

        self.assertEqual(result["unrated_test_case_count"], 10)
        self.assertEqual(result["positive_test_case_count"], 11)
        self.assertEqual(result["negative_test_case_count"], 12)

    def test_invalid_document_id_type_raises(self):
        with self.assertRaises(TypeError) as context:
            self.accessor.get_document_counts_all_levels(document_id={"invalid": "type"})

        self.assertIn("Expected an integer or string", str(context.exception))

    def test_query_failure_raises_content_accessor_exception(self):
        self.mock_session.query.side_effect = Exception("DB boom")

        with self.assertRaises(Exception) as context:
            self.accessor.get_document_counts_all_levels(document_id=1)

        self.assertIn("DB boom", str(context.exception))


if __name__ == "__main__":
    unittest.main()
