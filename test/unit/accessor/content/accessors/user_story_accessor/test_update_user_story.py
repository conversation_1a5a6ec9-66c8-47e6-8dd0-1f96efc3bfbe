import sys
from unittest.mock import MagicMock, patch

import pytest

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from accessor.content.accessors import UserStoryAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models.rating_enum import RatingEnum
from shared.models.user_story import UserStory


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def user_story_accessor(mock_session_factory):
    return UserStoryAccessor(mock_session_factory)


@pytest.fixture
def mock_user_story():
    with patch("accessor.content.accessors.user_story_accessor.UserStory") as MockUserStory:
        yield MockUserStory


@pytest.fixture
def mock_user_story_instance():
    # Create a real UserStory instance with initial values
    return UserStory(
        user_story_id=1,
        unique_statement_id=1,
        description="Initial description",
        rating=RatingEnum("positive"),
        edited=False,
        user_entered=True,
    )


def test_update_user_story_success(user_story_accessor, mock_session, mock_user_story_instance):
    # Arrange: Set up the mock to return the UserStory instance
    mock_session.query.return_value.get.return_value = mock_user_story_instance

    # Act: Call the method with updated fields
    result = user_story_accessor.update_user_story(
        user_story_id=1,
        updated_fields={
            "description": "Updated description",
            "rating": "negative",
        },
    )

    # Assert: Check that commit and refresh was called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify that result is a UserStory instance with the updated values
    assert isinstance(result, UserStory)
    assert result.user_story_id == 1
    assert result.description == "Updated description"
    assert result.rating == RatingEnum("negative")


def test_update_user_story_partial_update(
    user_story_accessor, mock_session, mock_user_story_instance
):
    # Mock the get method to return the existing UserStory instance
    mock_session.query.return_value.get.return_value = mock_user_story_instance

    # Perform the update with partial fields (only description in this case)
    result = user_story_accessor.update_user_story(
        user_story_id=1, updated_fields={"description": "Only updating description"}
    )

    # Assert: Check that commit and refresh was called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify that the updated UserStory instance attributes are as expected
    assert isinstance(result, UserStory)
    assert result.user_story_id == 1
    assert result.description == "Only updating description"
    assert result.rating == RatingEnum("positive")


def test_update_user_story_no_fields(user_story_accessor, mock_session, mock_user_story_instance):
    # Arrange: Set up the mock to return the UserStory instance
    mock_session.query.return_value.get.return_value = mock_user_story_instance

    # Act: Call the method with an empty updated_fields dictionary (no fields to update)
    result = user_story_accessor.update_user_story(user_story_id=1, updated_fields={})

    # Assert: Check that commit and refresh was called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify the attributes on the UserStory instance remain unchanged
    assert isinstance(result, UserStory)
    assert result.user_story_id == 1
    assert result.description == "Initial description"  # Unchanged
    assert result.rating == RatingEnum("positive")  # Unchanged Enum value


def test_update_user_story_non_existent_id(user_story_accessor, mock_session):
    # Arrange: Set up the mock to return None, simulating a non-existent user story
    mock_session.query.return_value.get.return_value = None

    # Expect `ContentAccessorException` due to the `NotFoundException` in the method logic
    with pytest.raises(NotFoundException, match="User story with ID 999 does not exist."):
        user_story_accessor.update_user_story(
            user_story_id=999, updated_fields={"description": "New description"}
        )


def test_update_user_story_commit_error(
    user_story_accessor, mock_session, mock_user_story_instance
):
    user_story_accessor.get_user_story_by_id = MagicMock(return_value=mock_user_story_instance)
    mock_session.merge.return_value = mock_user_story_instance
    mock_session.commit.side_effect = Exception("Commit failed")

    with pytest.raises(ContentAccessorException):
        user_story_accessor.update_user_story(
            user_story_id=1, updated_fields={"description": "New description"}
        )
