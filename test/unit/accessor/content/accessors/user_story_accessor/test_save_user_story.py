import sys
from unittest.mock import MagicMock, patch

import pytest

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from accessor.content.accessors import UserStoryAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models.rating_enum import RatingEnum
from shared.models.user_story import UserStory


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def user_story_accessor(mock_session_factory):
    return UserStoryAccessor(mock_session_factory)


def test_save_user_story_success(user_story_accessor, mock_session):
    # Arrange: Define the fields for creating a new user story
    insert_fields = {
        "unique_statement_id": 1,
        "description": "New user story description",
        "rating": RatingEnum.positive,
        "user_entered": True,
    }

    # Act: Call the save_user_story method
    result = user_story_accessor.save_user_story(insert_fields=insert_fields)

    # Assert: Check that add, commit, and refresh were called once
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify the result has the expected attributes
    assert isinstance(result, UserStory)
    assert result.unique_statement_id == insert_fields["unique_statement_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum("positive")
    assert result.user_entered is True


def test_save_user_story_partial_fields(user_story_accessor, mock_session):
    # Arrange: Only provide required fields (unique_statement_id and description)
    insert_fields = {"unique_statement_id": 1, "description": "Partial user story description"}

    # Act: Call the save_user_story method
    result = user_story_accessor.save_user_story(insert_fields=insert_fields)

    # Assert: Verify that the rating defaults to 'unrated' and user_entered defaults to False
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    assert isinstance(result, UserStory)
    assert result.unique_statement_id == insert_fields["unique_statement_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is False  # Default user_entered value


def test_save_user_story_commit_error(user_story_accessor, mock_session):
    # Arrange: Define fields and simulate a commit error
    insert_fields = {"unique_statement_id": 1, "description": "This should fail on commit"}
    mock_session.commit.side_effect = Exception("Commit failed")

    # Act & Assert: Expect ContentAccessorException to be raised
    with pytest.raises(ContentAccessorException, match="Error saving user story: Commit failed"):
        user_story_accessor.save_user_story(insert_fields=insert_fields)

    # Verify rollback was called on exception
    mock_session.rollback.assert_called_once()


def test_save_user_story_identifier_generation(user_story_accessor, mock_session):
    # Arrange: Define fields and mock identifier generation method
    insert_fields = {"unique_statement_id": 1, "description": "User story with custom identifier"}

    with patch.object(
        user_story_accessor, "_generate_user_story_identifier", return_value="USR-001"
    ) as mock_generate_id:

        # Act: Call the save_user_story method
        result = user_story_accessor.save_user_story(insert_fields=insert_fields)

        # Assert: Verify identifier generation and that identifier is set correctly
        mock_generate_id.assert_called_once_with(insert_fields["unique_statement_id"])
        assert result.identifier == "USR-001"


def test_save_user_story_default_rating(user_story_accessor, mock_session):
    # Arrange: Only provide required fields (unique_statement_id and description)
    insert_fields = {
        "unique_statement_id": 1,
        "description": "User story with default rating",
        "user_entered": True,
    }

    # Act: Call the save_user_story method
    result = user_story_accessor.save_user_story(insert_fields=insert_fields)

    # Assert: Verify that the rating defaults to 'unrated'
    assert isinstance(result, UserStory)
    assert result.unique_statement_id == insert_fields["unique_statement_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is True


def test_save_user_story_default_user_entered(user_story_accessor, mock_session):
    # Arrange: Provide fields without specifying user_entered
    insert_fields = {
        "unique_statement_id": 1,
        "description": "User story without user_entered field",
        "rating": RatingEnum.positive,
    }

    # Act: Call the save_user_story method
    result = user_story_accessor.save_user_story(insert_fields=insert_fields)

    # Assert: Verify that user_entered defaults to False
    assert isinstance(result, UserStory)
    assert result.unique_statement_id == insert_fields["unique_statement_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.positive
    assert result.user_entered is False  # Default user_entered value


def test_save_multiple_user_stories(user_story_accessor, mock_session):
    # Arrange: Define fields for multiple user stories
    insert_fields_1 = {
        "unique_statement_id": 1,
        "description": "First story",
        "rating": RatingEnum.positive,
    }
    insert_fields_2 = {
        "unique_statement_id": 2,
        "description": "Second story",
        "rating": RatingEnum.negative,
    }

    # Act: Call save_user_story twice with different inputs
    result_1 = user_story_accessor.save_user_story(insert_fields=insert_fields_1)
    result_2 = user_story_accessor.save_user_story(insert_fields=insert_fields_2)

    # Assert: Verify both stories are saved with correct fields
    assert result_1.description == "First story"
    assert result_2.description == "Second story"
    assert result_1.rating == RatingEnum.positive
    assert result_2.rating == RatingEnum.negative
    assert result_1.unique_statement_id != result_2.unique_statement_id


def test_save_user_story_full_optional_fields(user_story_accessor, mock_session):
    # Arrange: Define the fields with all optional fields
    insert_fields = {
        "unique_statement_id": 1,
        "description": "User story with full fields",
        "rating": RatingEnum.positive,
        "user_entered": True,
    }

    # Act: Call the save_user_story method
    result = user_story_accessor.save_user_story(insert_fields=insert_fields)

    # Assert: Verify that all fields are set as expected
    assert isinstance(result, UserStory)
    assert result.unique_statement_id == insert_fields["unique_statement_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.positive
    assert result.user_entered is True
