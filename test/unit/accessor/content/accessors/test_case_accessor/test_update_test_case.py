import sys
from unittest.mock import MagicMock, patch

import pytest

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from accessor.content.accessors import TestCaseAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models import RatingEnum, TestCase


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def test_case_accessor(mock_session_factory):
    return TestCaseAccessor(mock_session_factory)


@pytest.fixture
def mock_test_case():
    with patch("accessor.content.accessors.test_case_accessor.TestCase") as MockTestCase:
        yield MockTestCase


@pytest.fixture
def mock_test_case_instance():
    # Create a real TestCase instance with initial values
    return TestCase(
        test_case_id=1,
        acceptance_criteria_id=1,
        description="Initial description",
        rating=RatingEnum("positive"),
        edited=False,
        user_entered=True,
    )


def test_update_test_case_success(test_case_accessor, mock_session, mock_test_case_instance):
    # Arrange: Set up the mock to return the UserStory instance
    mock_session.query.return_value.get.return_value = mock_test_case_instance

    # Act: Call the method with updated fields
    result = test_case_accessor.update_test_case(
        test_case_id=1,
        updated_fields={
            "description": "Updated description",
            "rating": "negative",
        },
    )

    # Assert: Check that commit and refresh was called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify that result is a UserStory instance with the updated values
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == 1
    assert result.description == "Updated description"
    assert result.rating == RatingEnum("negative")


def test_update_test_case_partial_update(test_case_accessor, mock_session, mock_test_case_instance):
    # Mock the get method to return the existing UserStory instance
    mock_session.query.return_value.get.return_value = mock_test_case_instance

    # Perform the update with partial fields (only description in this case)
    result = test_case_accessor.update_test_case(
        test_case_id=1, updated_fields={"description": "Only updating description"}
    )

    # Assert: Check that commit and refresh was called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify that the updated UserStory instance attributes are as expected
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == 1
    assert result.description == "Only updating description"
    assert result.rating == RatingEnum("positive")


def test_update_test_case_no_fields(test_case_accessor, mock_session, mock_test_case_instance):
    # Arrange: Set up the mock to return the UserStory instance
    mock_session.query.return_value.get.return_value = mock_test_case_instance

    # Act: Call the method with an empty updated_fields dictionary (no fields to update)
    result = test_case_accessor.update_test_case(test_case_id=1, updated_fields={})

    # Assert: Check that commit and refresh was called
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify the attributes on the UserStory instance remain unchanged
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == 1
    assert result.description == "Initial description"  # Unchanged
    assert result.rating == RatingEnum("positive")  # Unchanged Enum value


def test_update_test_case_non_existent_id(test_case_accessor, mock_session):
    # Arrange: Set up the mock to return None, simulating a non-existent user story
    mock_session.query.return_value.get.return_value = None

    with pytest.raises(NotFoundException, match="Test Case with ID 999 does not exist."):
        test_case_accessor.update_test_case(
            test_case_id=999, updated_fields={"description": "New description"}
        )


def test_update_test_case_commit_error(test_case_accessor, mock_session, mock_test_case_instance):
    test_case_accessor.get_test_case_by_id = MagicMock(return_value=mock_test_case_instance)
    mock_session.merge.return_value = mock_test_case_instance
    mock_session.commit.side_effect = Exception("Commit failed")

    with pytest.raises(ContentAccessorException):
        test_case_accessor.update_test_case(
            test_case_id=1, updated_fields={"description": "New description"}
        )
