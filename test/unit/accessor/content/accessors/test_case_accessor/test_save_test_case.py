import sys
from unittest.mock import MagicMock, patch

import pytest

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from accessor.content.accessors import TestCaseAccessor
from accessor.content.exceptions import ContentAccessorException
from shared.models import RatingEnum, TestCase


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def test_case_accessor(mock_session_factory):
    return TestCaseAccessor(mock_session_factory)


# Use `patch.object` to mock `_generate_test_case_identifier` directly
@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_test_case_success(mock_generate_identifier, test_case_accessor, mock_session):
    # Arrange: Define the fields for creating a new test case
    insert_fields = {
        "acceptance_criteria_id": 1,
        "description": "New test case description",
        "rating": RatingEnum.positive,
        "user_entered": True,
    }

    # Act: Call the save_test_case method
    result = test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Assert: Check that add, commit, and refresh were called once
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify the result has the expected attributes
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == insert_fields["acceptance_criteria_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum("positive")
    assert result.user_entered is True
    assert result.identifier == "TC-001"  # Ensure the identifier is as mocked


@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_test_case_partial_fields(mock_generate_identifier, test_case_accessor, mock_session):
    # Arrange: Only provide required fields (acceptance_criteria_id and description)
    insert_fields = {"acceptance_criteria_id": 1, "description": "Partial user story description"}

    # Act: Call the save_test_case method
    result = test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Assert: Verify that the rating defaults to 'unrated' and user_entered defaults to False
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == insert_fields["acceptance_criteria_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is False  # Default user_entered value
    assert result.identifier == "TC-001"  # Ensure the identifier is as mocked


def test_save_test_case_commit_error(test_case_accessor, mock_session):
    # Arrange: Define fields and simulate a commit error
    insert_fields = {"acceptance_criteria_id": 1, "description": "This should fail on commit"}
    mock_session.commit.side_effect = Exception("Commit failed")

    # Act & Assert: Expect ContentAccessorException to be raised
    with pytest.raises(ContentAccessorException, match="Error saving test case: Commit failed"):
        test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Verify rollback was called on exception
    mock_session.rollback.assert_called_once()


@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_test_case_identifier_generation(mock_generate_identifier, test_case_accessor):
    # Arrange: Define fields and mock identifier generation method
    insert_fields = {
        "acceptance_criteria_id": 1,
        "description": "User story with custom identifier",
    }

    # Act: Call the save_test_case method
    result = test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Assert: Verify identifier generation and that identifier is set correctly
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == insert_fields["acceptance_criteria_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is False  # Default user_entered value
    assert result.identifier == "TC-001"


@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_test_case_default_rating(mock_generate_identifier, test_case_accessor):
    # Arrange: Only provide required fields (acceptance_criteria_id and description)
    insert_fields = {
        "acceptance_criteria_id": 1,
        "description": "User story with default rating",
        "user_entered": True,
    }

    # Act: Call the save_test_case method
    result = test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Assert: Verify that the rating defaults to 'unrated'
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == insert_fields["acceptance_criteria_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is True


@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_test_case_default_user_entered(mock_generate_identifier, test_case_accessor):
    # Arrange: Provide fields without specifying user_entered
    insert_fields = {
        "acceptance_criteria_id": 1,
        "description": "User story without user_entered field",
        "rating": RatingEnum.positive,
    }

    # Act: Call the save_test_case method
    result = test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Assert: Verify that user_entered defaults to False
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == insert_fields["acceptance_criteria_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.positive
    assert result.user_entered is False  # Default user_entered value


@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_multiple_user_stories(mock_generate_identifier, test_case_accessor):
    # Arrange: Define fields for multiple user stories
    insert_fields_1 = {
        "acceptance_criteria_id": 1,
        "description": "First story",
        "rating": RatingEnum.positive,
    }
    insert_fields_2 = {
        "acceptance_criteria_id": 2,
        "description": "Second story",
        "rating": RatingEnum.negative,
    }

    # Act: Call save_test_case twice with different inputs
    result_1 = test_case_accessor.save_test_case(insert_fields=insert_fields_1)
    result_2 = test_case_accessor.save_test_case(insert_fields=insert_fields_2)

    # Assert: Verify both stories are saved with correct fields
    assert result_1.description == "First story"
    assert result_2.description == "Second story"
    assert result_1.rating == RatingEnum.positive
    assert result_2.rating == RatingEnum.negative
    assert result_1.acceptance_criteria_id != result_2.acceptance_criteria_id


@patch.object(TestCaseAccessor, "_generate_test_case_identifier", return_value="TC-001")
def test_save_test_case_full_optional_fields(mock_generate_identifier, test_case_accessor):
    # Arrange: Define the fields with all optional fields
    insert_fields = {
        "acceptance_criteria_id": 1,
        "description": "User story with full fields",
        "rating": RatingEnum.positive,
        "user_entered": True,
    }

    # Act: Call the save_test_case method
    result = test_case_accessor.save_test_case(insert_fields=insert_fields)

    # Assert: Verify that all fields are set as expected
    assert isinstance(result, TestCase)
    assert result.acceptance_criteria_id == insert_fields["acceptance_criteria_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.positive
    assert result.user_entered is True
