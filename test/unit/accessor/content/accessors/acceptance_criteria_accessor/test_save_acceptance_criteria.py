import sys
from unittest.mock import MagicMock, patch

import pytest

# Mock psycopg2 globally for the test environment
sys.modules["psycopg2"] = MagicMock()

from accessor.content.accessors import AcceptanceCriteriaAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models import RatingEnum, AcceptanceCriteria


@pytest.fixture
def mock_session():
    return MagicMock()


@pytest.fixture
def mock_session_factory(mock_session):
    # Simulate the context manager behavior for the session factory
    factory = MagicMock()
    factory.return_value.__enter__.return_value = mock_session
    return factory


@pytest.fixture
def acceptance_criteria_accessor(mock_session_factory):
    return AcceptanceCriteriaAccessor(mock_session_factory)


def test_save_acceptance_criteria_success(acceptance_criteria_accessor, mock_session):
    # Arrange: Define the fields for creating a new user story
    insert_fields = {
        "user_story_id": 1,
        "description": "New user story description",
        "rating": RatingEnum.positive,
        "user_entered": True,
    }

    # Act: Call the save_acceptance_criteria method
    result = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

    # Assert: Check that add, commit, and refresh were called once
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    # Verify the result has the expected attributes
    assert isinstance(result, AcceptanceCriteria)
    assert result.user_story_id == insert_fields["user_story_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum("positive")
    assert result.user_entered is True


def test_save_acceptance_criteria_partial_fields(acceptance_criteria_accessor, mock_session):
    # Arrange: Only provide required fields (user_story_id and description)
    insert_fields = {"user_story_id": 1, "description": "Partial user story description"}

    # Act: Call the save_acceptance_criteria method
    result = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

    # Assert: Verify that the rating defaults to 'unrated' and user_entered defaults to False
    mock_session.add.assert_called_once()
    mock_session.commit.assert_called_once()
    mock_session.refresh.assert_called_once()

    assert isinstance(result, AcceptanceCriteria)
    assert result.user_story_id == insert_fields["user_story_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is False  # Default user_entered value


def test_save_acceptance_criteria_commit_error(acceptance_criteria_accessor, mock_session):
    # Arrange: Define fields and simulate a commit error
    insert_fields = {"user_story_id": 1, "description": "This should fail on commit"}
    mock_session.commit.side_effect = Exception("Commit failed")

    # Act & Assert: Expect ContentAccessorException to be raised
    with pytest.raises(
        ContentAccessorException, match="Error saving acceptance criteria: Commit failed"
    ):
        acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

    # Verify rollback was called on exception
    mock_session.rollback.assert_called_once()


def test_save_acceptance_criteria_identifier_generation(acceptance_criteria_accessor, mock_session):
    # Arrange: Define fields and mock identifier generation method
    insert_fields = {
        "user_story_id": 1,
        "description": "User story with custom identifier",
    }

    with patch.object(
        acceptance_criteria_accessor,
        "_generate_acceptance_criteria_identifier",
        return_value="USR-001",
    ) as mock_generate_id:

        # Act: Call the save_acceptance_criteria method
        result = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

        # Assert: Verify identifier generation and that identifier is set correctly
        mock_generate_id.assert_called_once_with(insert_fields["user_story_id"])
        assert result.identifier == "USR-001"


def test_save_acceptance_criteria_default_rating(acceptance_criteria_accessor, mock_session):
    # Arrange: Only provide required fields (user_story_id and description)
    insert_fields = {
        "user_story_id": 1,
        "description": "User story with default rating",
        "user_entered": True,
    }

    # Act: Call the save_acceptance_criteria method
    result = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

    # Assert: Verify that the rating defaults to 'unrated'
    assert isinstance(result, AcceptanceCriteria)
    assert result.user_story_id == insert_fields["user_story_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.unrated  # Default rating
    assert result.user_entered is True


def test_save_acceptance_criteria_default_user_entered(acceptance_criteria_accessor, mock_session):
    # Arrange: Provide fields without specifying user_entered
    insert_fields = {
        "user_story_id": 1,
        "description": "User story without user_entered field",
        "rating": RatingEnum.positive,
    }

    # Act: Call the save_acceptance_criteria method
    result = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

    # Assert: Verify that user_entered defaults to False
    assert isinstance(result, AcceptanceCriteria)
    assert result.user_story_id == insert_fields["user_story_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.positive
    assert result.user_entered is False  # Default user_entered value


def test_save_multiple_user_stories(acceptance_criteria_accessor, mock_session):
    # Arrange: Define fields for multiple user stories
    insert_fields_1 = {
        "user_story_id": 1,
        "description": "First story",
        "rating": RatingEnum.positive,
    }
    insert_fields_2 = {
        "user_story_id": 2,
        "description": "Second story",
        "rating": RatingEnum.negative,
    }

    # Act: Call save_acceptance_criteria twice with different inputs
    result_1 = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields_1)
    result_2 = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields_2)

    # Assert: Verify both stories are saved with correct fields
    assert result_1.description == "First story"
    assert result_2.description == "Second story"
    assert result_1.rating == RatingEnum.positive
    assert result_2.rating == RatingEnum.negative
    assert result_1.user_story_id != result_2.user_story_id


def test_save_acceptance_criteria_full_optional_fields(acceptance_criteria_accessor, mock_session):
    # Arrange: Define the fields with all optional fields
    insert_fields = {
        "user_story_id": 1,
        "description": "User story with full fields",
        "rating": RatingEnum.positive,
        "user_entered": True,
    }

    # Act: Call the save_acceptance_criteria method
    result = acceptance_criteria_accessor.save_acceptance_criteria(insert_fields=insert_fields)

    # Assert: Verify that all fields are set as expected
    assert isinstance(result, AcceptanceCriteria)
    assert result.user_story_id == insert_fields["user_story_id"]
    assert result.description == insert_fields["description"]
    assert result.rating == RatingEnum.positive
    assert result.user_entered is True
