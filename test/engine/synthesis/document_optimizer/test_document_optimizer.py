import json
import os
import unittest
from unittest.mock import MagicMock

from accessor.content.content_accessor import ContentAccessor
from engine.synthesis.document_optimizer import DocumentOptimizer


class TestDocumentOptimizer(unittest.TestCase):
    def setUp(self):
        """Set up a DocumentOptimizer instance with a mocked ContentAccessor and load test chunks."""
        self.logger_name = "TestLogger"

        self.mock_content_accessor = MagicMock(spec=ContentAccessor)

        self.optimizer = DocumentOptimizer(self.logger_name)
        self.optimizer.content_accessor = self.mock_content_accessor  # Keep the mock

        test_file_path = os.path.join(os.path.dirname(__file__), "parsed_nodes.json")
        self.test_chunks = self._load_test_chunks(test_file_path)

    def _load_test_chunks(self, file_path):
        """Load test chunks from a JSON file and return a list of MagicMock objects."""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Missing test data file: {file_path}")

        with open(file_path, "r", encoding="utf-8") as file:
            chunk_data = json.load(file)

        if not isinstance(chunk_data, dict) or "nodes" not in chunk_data:
            raise ValueError("Expected 'nodes' key in test_chunks.json")

        return [MagicMock(text_content=chunk["text_content"]) for chunk in chunk_data["nodes"]]

    def test_optimize_document(self):
        """Test that optimize_document correctly processes chunks and concatenates results."""
        self.mock_content_accessor.get_chunks_by_document.return_value = self.test_chunks

        result = self.optimizer.optimize_document(document_id=1)

        print("\n--- Optimized Document Output ---")
        for chunk in self.test_chunks:
            print(f"Original: {chunk.text_content}\n")

        print("\n--- Final Optimized Output ---")
        print(result)

        self.mock_content_accessor.get_chunks_by_document.assert_called_once_with(1)

        self.assertGreater(len(result), 0, "Expected length greater than 0.")
        with open("optimized_text.md", "w", encoding="utf-8") as f:
            f.write(result)

    def test_optimize_document_with_no_chunks(self):
        """Test optimize_document when no chunks are returned (empty document)."""
        self.mock_content_accessor.get_chunks_by_document.return_value = []

        result = self.optimizer.optimize_document(document_id=1)

        self.assertEqual(result, "")
        self.mock_content_accessor.get_chunks_by_document.assert_called_once_with(1)


if __name__ == "__main__":
    unittest.main()
