import unittest

# Adjust the import paths as necessary.
from engine.analysis.analysis_engine import AnalysisEngine
from engine.analysis.dto.readability_dto import ReadabilityDTO


class TestAnalysisEngine(unittest.TestCase):
    def setUp(self):
        # Instantiate the AnalysisEngine.
        self.engine = AnalysisEngine()

    def test_analyze_readability_basic(self):
        """
        Test the analyze_readability method with a sample text.
        Verifies that a ReadabilityDTO is returned with reasonable metric values.
        """
        sample_text = (
            "This is a detailed test document. It contains multiple sentences, "
            "some of which are longer than others. The goal of this test is to "
            "analyze how the readability engine processes text with different "
            "sentence structures. Additionally, we want to ensure that numerical "
            "values such as word count, sentence length, and readability scores "
            "are computed accurately. Longer texts provide a better estimation "
            "of readability metrics, as they contain more varied linguistic "
            "features, including punctuation, conjunctions, and clauses. "
            "This should be sufficient to surpass the 100-word requirement."
            "This is a detailed test document. It contains multiple sentences, "
            "some of which are longer than others. The goal of this test is to "
            "analyze how the readability engine processes text with different "
            "sentence structures. Additionally, we want to ensure that numerical "
            "values such as word count, sentence length, and readability scores "
            "are computed accurately. Longer texts provide a better estimation "
            "of readability metrics, as they contain more varied linguistic "
            "features, including punctuation, conjunctions, and clauses. "
            "This should be sufficient to surpass the 100-word requirement."
        )

        dto = self.engine.analyze_readability(sample_text)

        # Check that the returned object is an instance of ReadabilityDTO.
        self.assertIsInstance(dto, ReadabilityDTO)

        # Verify that the word count is as expected.
        # For the sample text, we expect a non-zero word count.
        self.assertGreater(dto.word_count, 0)

        # Ensure that average sentence length and average word length are positive numbers.
        self.assertGreater(dto.avg_sentence_length, 0)
        self.assertGreater(dto.avg_word_length, 0)

        # Flesch Reading Ease should be a float.
        self.assertIsInstance(dto.flesch_reading_ease, float)

        # Flesch-Kincaid grade level should be a float (or int convertible to float).
        self.assertIsInstance(dto.flesch_kincaid_grade, (float, int))

    def test_analyze_readability_empty_text(self):
        """
        Test the analyze_readability method with an empty string.
        This test verifies that the metrics handle an empty text gracefully.
        """
        dto = self.engine.analyze_readability("")

        # Expect the word count to be 0.
        self.assertEqual(dto.word_count, 0)

        # Other metrics may default to 0 or a similar value; adjust expectations as needed.
        self.assertGreaterEqual(dto.flesch_reading_ease, 0)
        self.assertGreaterEqual(dto.avg_sentence_length, 0)
        self.assertGreaterEqual(dto.avg_word_length, 0)
        self.assertGreaterEqual(dto.difficult_words, 0)


if __name__ == "__main__":
    unittest.main()
