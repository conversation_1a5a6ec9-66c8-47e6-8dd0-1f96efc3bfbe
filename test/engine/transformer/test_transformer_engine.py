import io
import os
import unittest

from docx import Document

# Adjust the import path as necessary to point to your TransformEngine.
from engine.transformer.transform_engine import TransformEngine


class TestTransformerEngine(unittest.TestCase):
    """
    Class of tests for the Transformer Engine.
    """

    def create_sample_docx(self) -> bytes:
        """
        Creates a sample DOCX file in memory with two paragraphs,
        and returns the DOCX file as bytes.
        """
        doc = Document()
        doc.add_paragraph("This is the first paragraph.")
        doc.add_paragraph("This is the second paragraph.")
        bytes_io = io.BytesIO()
        doc.save(bytes_io)
        return bytes_io.getvalue()

    # def test_01_transform_docx_to_markdown_with_sample(self):
    #     """
    #     Tests the transform_docx_bytes_to_markdown method using an in-memory sample DOCX.
    #     Expected markdown: paragraphs joined with two newlines.
    #     """
    #     engine = TransformEngine("TestLogger")
    #     sample_docx = self.create_sample_docx()
    #     markdown_text = engine.transform_docx_bytes_to_markdown(sample_docx, 1)

    #     expected = "This is the first paragraph.\n\nThis is the second paragraph."
    #     self.assertEqual(markdown_text.strip(), expected.strip())

    # def test_02_transform_specific_docx_to_markdown(self):
    #     """
    #     Tests the transform_docx_bytes_to_markdown method on a specific DOCX file.
    #     Assumes the DOCX file is located at tests/data/specific.docx and that an expected
    #     markdown output is available at tests/data/expected_markdown.md.
    #     """
    #     engine = TransformEngine("TestLogger")
    #     # Determine the base directory of the test file.
    #     base_dir = os.path.dirname(os.path.abspath(__file__))
    #     # Construct the file paths for the DOCX input and expected Markdown output.
    #     specific_docx_path = os.path.join(base_dir, "test.docx")
    #     expected_md_path = os.path.join(base_dir, "expected_markdown.md")

    #     # Read the DOCX bytes.
    #     with open(specific_docx_path, "rb") as f:
    #         docx_bytes = f.read()

    #     # Process the DOCX file.
    #     markdown_text = engine.transform_docx_bytes_to_markdown(docx_bytes, 1)

    #     # Option 1: If you have an expected markdown output, compare it.
    #     if os.path.exists(expected_md_path):
    #         with open(expected_md_path, "r", encoding="utf-8") as f:
    #             expected_markdown = f.read().strip()
    #         self.assertEqual(markdown_text.strip(), expected_markdown)
    #     else:
    #         # Option 2: Otherwise, assert that the markdown text is not empty.
    #         self.assertTrue(markdown_text.strip() != "", "Markdown output should not be empty.")
    #         # Optionally, print the output for manual inspection.
    #         print(markdown_text)
    #     with open("test_output.md", "w", encoding="utf-8") as f:
    #         f.write(markdown_text)

    def test_03_transform_markdown_to_docx(self):
        """
        Tests the transform_markdown_to_docx method on a specific Markdown file.
        Assumes the file is located at tests/data/test.md.

        Verifies that the resulting DOCX is non-empty and can be parsed by python-docx,
        and writes the final docx to 'test_output.docx' in the local directory.
        """
        engine = TransformEngine("TestLogger")
        base_dir = os.path.dirname(os.path.abspath(__file__))
        test_md_path = os.path.join(base_dir, "optimized_text.md")

        # Read the sample Markdown
        with open(test_md_path, "r", encoding="utf-8") as f:
            markdown_text = f.read()

        # Invoke your new method (assumes it is implemented on TransformEngine).
        docx_bytes = engine.transform_markdown_to_docx(markdown_text)

        # Write the docx to the local directory for inspection
        output_docx_path = os.path.join(base_dir, "test_output.docx")
        with open(output_docx_path, "wb") as out_file:
            out_file.write(docx_bytes)

        # Parse the result with python-docx for some quick checks
        doc = Document(io.BytesIO(docx_bytes))

        # Basic assertion: check there's at least 1 paragraph
        self.assertGreater(
            len(doc.paragraphs), 0, "The resulting DOCX should contain at least one paragraph."
        )
        # More specific checks could go here (e.g., verifying text or images).

    def test_04_conversion_returns_pdf_bytes(self):
        """
        Test that converting a DOCX byte stream returns PDF bytes
        that start with the PDF header.
        """
        # Create a sample DOCX byte stream.
        docx_bytes = self.create_sample_docx()

        # Call the conversion method.
        engine = TransformEngine("TestLogger")
        pdf_bytes = engine.convert_docx_to_pdf(docx_bytes)

        # Check that pdf_bytes is not empty and starts with the PDF header.
        self.assertTrue(pdf_bytes, "The PDF byte stream is empty.")
        self.assertTrue(
            pdf_bytes.startswith(b"%PDF-"),
            "The output does not appear to be a valid PDF file (missing %PDF- header).",
        )
        # Optionally, check that the file length is above some threshold.
        self.assertGreater(len(pdf_bytes), 100, "The PDF output seems unexpectedly small.")


if __name__ == "__main__":
    unittest.main()
