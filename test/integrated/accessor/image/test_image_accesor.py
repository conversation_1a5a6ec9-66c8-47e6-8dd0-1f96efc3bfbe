"""
Integrated test of ImageAccessor
"""

import unittest

from accessor.image import IChunk<PERSON>mageAccessor, IImageAccessor, ImageAccessor
from accessor.image.dto.image_content_dto import ImageContentDTO

LOGGER_NAME = "test-image-accessor"


class TestImageAccessor(unittest.TestCase):
    """
    Contains tests for ImageAccessor
    """

    def test_store_chunk_image(self):
        """
        Test storing a chunk image.
        """
        accessor: IChunkImageAccessor = ImageAccessor(LOGGER_NAME)
        file_content = b"\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\n"
        file_name = "test_image.png"
        metadata = {"author": "test_author"}
        chunk_id = 52778

        # Execute the store_chunk_image method
        result = accessor.store_chunk_image(
            file_content=file_content, file_name=file_name, metadata=metadata, chunk_id=chunk_id
        )

        # Verify that the result is an instance of ImageDTO with correct properties
        self.assertIsInstance(result, int)
        self.assertGreater(result, 0)  # Verify the file name matches

    def test_get_image(self):
        "Test getting image"
        accessor: IImageAccessor = ImageAccessor(LOGGER_NAME)
        image: ImageContentDTO = accessor.get_image_content(10)
        self.assertIsInstance(image, ImageContentDTO)


# Run the test
if __name__ == "__main__":
    unittest.main()
