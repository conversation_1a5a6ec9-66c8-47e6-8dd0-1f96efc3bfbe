import pytest
from accessor.reference.reference_accessor import ReferenceAccessor
from accessor.reference.exceptions import (
    CitationNumberNotFoundException,
    CitationServiceInternalError,
    MalformedCitationNumberException,
    MissingRequiredTagsException,
)

# 1. Valid citation number: e.g., "89 FR 99705"
#     -> return back document info
# 2. Invalid citation number: e.g., "invalid-citation"
#     -> raise CitationValidationSystemError
# 3. Citation number without proper format: e.g., "89 FR 99-705"
#     -> raise CitationValidationSystemError
# 4. Citation number with additional spacing: e.g., "89 FR 99 7 0 5"
#     -> raise MalformedCitationNumberException
# 5. Citation number that doesn't exist: e.g., "999 FR 99999"
#     -> raise CitationNumberNotFoundException


@pytest.mark.integration
class TestValidateFederalCitationNumberIntegration:
    @pytest.fixture(scope="class")
    def accessor(self):
        accessor = ReferenceAccessor(logger_name="test_logger")
        return accessor

    def test_valid_citation(self, accessor):
        # Arrange: Use a known good citation number in correct format
        citation = "89 FR 99705"

        # Act & Assert
        # Should not raise any exception for valid citation
        try:
            accessor.validate_citation_number(citation)
        except Exception as e:
            pytest.fail(f"validate_citation_number raised an exception unexpectedly: {e}")

    def test_nonexistent_citation(self, accessor):
        # Arrange: This should exist structurally but not actually exist in the Federal Register
        citation = "invalid-citation"

        # Act & Assert
        with pytest.raises(CitationServiceInternalError):
            accessor.validate_citation_number(citation)

    def test_citation_with_dash(self, accessor):
        # Arrange:
        citation = "89 FR 99-705"

        # Act & Assert
        with pytest.raises(CitationServiceInternalError):
            accessor.validate_citation_number(citation)

    def test_citation_with_additional_spaces(self, accessor):
        # Arrange: Use a valid citation number but with additional spaces
        citation = "89 FR 99 705"

        # Act & Assert
        with pytest.raises(MalformedCitationNumberException):
            accessor.validate_citation_number(citation)

    def test_totally_invalid_citation(self, accessor):
        # Arrange: Use a citation number that is completely invalid
        citation = "999 FR 99999"

        # Act & Assert
        with pytest.raises(CitationNumberNotFoundException):
            accessor.validate_citation_number(citation)

    def test_valid_citation_no_sections_to_parse(self, accessor):
        # Arrange: Use a citation number that is completely invalid
        citation = "89 FR 7274"

        # Act & Assert
        with pytest.raises(MissingRequiredTagsException):
            accessor.validate_citation_number(citation)

    def test_valid_citation_2_no_sections_to_parse(self, accessor):
        # Arrange: Use a citation number that is completely invalid
        citation = "89 FR 22083"

        # Act & Assert
        with pytest.raises(MissingRequiredTagsException):
            accessor.validate_citation_number(citation)
