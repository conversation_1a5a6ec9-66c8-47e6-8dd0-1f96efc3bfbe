"""
Pytest configuration for AI Accessor integration tests.
"""

import os
import sys
import pytest

# Add project root to Python path if needed
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))

# Define fixtures that can be used by all tests

@pytest.fixture(scope="session", autouse=True)
def setup_environment():
    """Set up environment variables for testing if not already set."""
    # Sample OPENAI API key for testing (will be overridden by actual env var if set)
    if not os.environ.get("OPENAI_API_KEY"):
        os.environ["OPENAI_API_KEY"] = os.environ.get("OPENAI_API_KEY", "dummy_openai_key")
    
    # Sample Langfuse keys for testing
    if not os.environ.get("LANGFUSE_PUBLIC_KEY"):
        os.environ["LANGFUSE_PUBLIC_KEY"] = os.environ.get("LANGFUSE_PUBLIC_KEY", "dummy_langfuse_public")
        os.environ["LANGFUSE_SECRET_KEY"] = os.environ.get("LANGFUSE_SECRET_KEY", "dummy_langfuse_secret") 