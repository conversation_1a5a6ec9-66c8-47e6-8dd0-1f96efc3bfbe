import os
import pytest
import sys
import re
from unittest.mock import patch, MagicMock
from langfuse.decorators import observe
# Add src directory to path if needed
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../')))

from src.accessor.ai.ai_accessor import AiAccessor
from engine.synthesis.prompt_definitions import FallbackPrompts
from src.accessor.ai.providers.bedrock.bedrock_model_config import MODEL_TOKEN_LIMITS

# List of all models to test
ALL_MODELS_TO_TEST = [
    "gpt4 omni", "gpt-4 omni", "gpt4o mini", "gpt-4o mini", "gpt-4o-mini", "gpt4 omni mini",
    "gpt4.1", "gpt-4.1", "gpt 4.1", "gpt4.1 mini", "gpt-4.1 mini", "gpt-4.1-mini",
    "gpt4.1 nano", "gpt-4.1 nano", "gpt-4.1-nano", "o3", "openai o3", "opus 3",
    "o4 mini", "o4-mini", "openai o4 mini", "o3 mini", "o3-mini", "openai o3 mini",
    "o1 mini", "o1-mini", "openai o1 mini", "claude 3.7 sonnet", "claude3.7 sonnet", "claude-3.7-sonnet",
    "claude 3.7", "claude 3 opus", "claude-3-opus",
    "deepseek r1", "deepseek-r1", "deepseek", "r1", "llama 3 70b", "llama3 70b", "llama-3-70b",
    "llama 70b", "llama 2 90b", "llama2 90b", "llama-2-90b", "llama 90b", "llama 2 3b",
    "llama2 3b", "llama-2-3b", "llama 3b", "llama 2 1b", "llama2 1b", "llama-2-1b",
    "llama 1b", "llama 2 11b", "llama2 11b", "llama-2-11b", "llama 11b"
]

class TestAiAccessor:
    """Integration tests for the AiAccessor class."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Setup for the tests."""
        # Add any necessary setup here
        # Ensure environment variables are set for AWS if testing Bedrock model
        if not os.environ.get("AWS_ACCESS_KEY_ID"):
            os.environ["AWS_ACCESS_KEY_ID"] = os.environ.get("AWS_ACCESS_KEY_ID", "test_key")
            os.environ["AWS_SECRET_ACCESS_KEY"] = os.environ.get("AWS_SECRET_ACCESS_KEY", "test_secret")
            os.environ["AWS_REGION_NAME"] = os.environ.get("AWS_REGION_NAME", "us-east-1")
        
        # Make sure OPENAI_API_KEY is set
        if not os.environ.get("OPENAI_API_KEY"):
            os.environ["OPENAI_API_KEY"] = "sk-dummy-key-for-testing"
        
        # Add required Langfuse environment variables 
        if not os.environ.get("LANGFUSE_PUBLIC_KEY"):
            os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-dummy"
            os.environ["LANGFUSE_SECRET_KEY"] = "sk-lf-dummy"
        
        # We need to patch at module level to ensure our mocks work
        self.fetch_patch = patch('src.accessor.ai.ai_accessor.AiAccessor._burst_langfuse_helper.fetch_langfuse_prompt')
        self.mock_fetch = self.fetch_patch.start()
        
        self.model_patch = patch('src.accessor.ai.ai_accessor.AiAccessor._burst_langfuse_helper.get_model_config')
        self.mock_model = self.model_patch.start()
        
        yield
        
        # Clean up patches after test
        self.fetch_patch.stop()
        self.model_patch.stop()

    def get_mock_prompt_client(self):
        """Create a mock PromptClient object."""
        class MockPromptClient:
            def __init__(self):
                self.version = "1"
                self.name = "test_prompt"
                self.prompt = [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Answer questions to the best of your ability."}
                ]
                self.is_fallback = False
        return MockPromptClient()

    def test_gpt4o_mini_completion(self):
        """Test that we can successfully invoke the gpt-4o-mini model."""
        # Set up mocks
        mock_prompt_client = self.get_mock_prompt_client()
        compiled_prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is 2+2?"}
        ]
        
        self.mock_fetch.return_value = (mock_prompt_client, compiled_prompt)
        self.mock_model.return_value = {"model": "gpt-4o-mini"}
        
        # Run the test
        response = AiAccessor.get_completion(
            prompt_name="test_prompt",
            prompt_variables={"query": "What is 2+2?"},
            inference_config={"temperature": 0.1, "max_tokens": 50}
        )
        
        # Verify response
        assert response is not None
        assert isinstance(response, str)
        assert len(response) > 0
        
        # Verify mocks were called correctly
        self.mock_fetch.assert_called_once()
        self.mock_model.assert_called_once()
        
        # Log the response for verification
        print(f"\n\n=== GPT-4o-mini RESPONSE ===\n{response}\n=======================\n")

    @observe()
    @pytest.mark.parametrize("model_name", ALL_MODELS_TO_TEST)
    def test_model_variations(self, model_name):
        """Test that we can successfully invoke all model variations."""
        # Set up mocks
        mock_prompt_client = self.get_mock_prompt_client()
        compiled_prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Test query for model variant."}
        ]
        
        self.mock_fetch.return_value = (mock_prompt_client, compiled_prompt)
        self.mock_model.return_value = {"model": model_name}
        
        # Run the test
        response = AiAccessor.get_completion(
            prompt_name="test_prompt",
            prompt_variables={"query": f"Test query for {model_name}"},
        )
        
        # Verify response
        assert response is not None
        assert isinstance(response, str)
        assert len(response) > 0
        
        # Verify mocks were called correctly
        self.mock_fetch.assert_called()
        self.mock_model.assert_called()
        
        # Log the response for verification
        print(f"\n=== {model_name} RESPONSE ===\n{response[:50]}...\n=======================\n")

    @pytest.mark.skipif(
        not os.environ.get("AWS_ACCESS_KEY_ID") or 
        not os.environ.get("AWS_SECRET_ACCESS_KEY") or 
        not os.environ.get("AWS_REGION_NAME"),
        reason="AWS credentials required for Bedrock model test"
    )
    def test_bedrock_llama_completion(self):
        """Test that we can successfully invoke the Llama3 model via Bedrock."""
        # Set up mocks
        mock_prompt_client = self.get_mock_prompt_client()
        compiled_prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Explain quantum computing in one sentence."}
        ]
        
        self.mock_fetch.return_value = (mock_prompt_client, compiled_prompt)
        self.mock_model.return_value = {"model": "us.meta.llama3-2-1b-instruct-v1:0"}
        
        # Force Bedrock usage for this test
        os.environ["FORCE_BEDROCK"] = "true"
        
        try:
            # Run the test
            response = AiAccessor.get_completion(
                prompt_name="test_prompt",
                prompt_variables={"query": "Explain quantum computing in one sentence."},
                inference_config={"temperature": 0.1, "max_tokens": 50}
            )
            
            # Verify response
            assert response is not None
            assert isinstance(response, str)
            assert len(response) > 0
            
            # Verify mocks were called correctly
            assert self.mock_fetch.call_count >= 1
            assert self.mock_model.call_count >= 1
            
            # Log the response for verification
            print(f"\n\n=== LLAMA3 VIA BEDROCK RESPONSE ===\n{response}\n================================\n")
        finally:
            # Clean up environment variable
            if "FORCE_BEDROCK" in os.environ:
                del os.environ["FORCE_BEDROCK"]

    @pytest.mark.skipif(
        not os.environ.get("AWS_ACCESS_KEY_ID") or 
        not os.environ.get("AWS_SECRET_ACCESS_KEY") or 
        not os.environ.get("AWS_REGION_NAME"),
        reason="AWS credentials required for Bedrock model test"
    )
    def test_bedrock_token_limits(self):
        """
        Test that the max_tokens limits in MODEL_TOKEN_LIMITS are accurate.
        This test attempts to use a very high max_tokens value for each model,
        then extracts the actual limit from the error message.
        """
        # Set up mocks
        mock_prompt_client = self.get_mock_prompt_client()
        compiled_prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say hello."}
        ]
        
        self.mock_fetch.return_value = (mock_prompt_client, compiled_prompt)
        
        try:
            
            models_to_test = list(MODEL_TOKEN_LIMITS.keys())
            print(f"\nTesting all {len(models_to_test)} models in MODEL_TOKEN_LIMITS")
            
            # Track results
            results = []
            
            for model_to_test in models_to_test:
                expected_limit = MODEL_TOKEN_LIMITS.get(model_to_test)
                
                if not expected_limit:
                    print(f"Skipping model {model_to_test} - not found in MODEL_TOKEN_LIMITS")
                    continue
                    
                self.mock_model.return_value = {"model": model_to_test}
                
                print(f"\nTesting token limit for model: {model_to_test}")
                print(f"Expected limit from MODEL_TOKEN_LIMITS: {expected_limit}")
                
                # Try with an excessively high max_tokens value
                excessive_tokens = 9999999
                
                try:
                    # Expect an error due to excessive max_tokens
                    with pytest.raises(Exception) as excinfo:
                        AiAccessor.get_completion(
                            prompt_name="test_prompt",
                            prompt_variables={"query": "Say hello."},
                            inference_config={"temperature": 0.1, "max_tokens": excessive_tokens},
                            retry=False
                        )
                    
                    # Extract the reported limit from the error message
                    error_message = str(excinfo.value)
                    print(f"Error message: {error_message}")
                    
                    # Updated regex pattern for Bedrock error messages
                    # The error format is usually: "...model limit of 8192. Try again with a maximum tokens value that is lower than 8192..."
                    limit_match = re.search(r"model limit of (\d+)|lower than (\d+)|is not less or equal to (\d+)", error_message)
                    
                    if limit_match:
                        # Get the first non-None group
                        reported_limit = int(next(group for group in limit_match.groups() if group is not None))
                        print(f"Reported limit from error: {reported_limit}")
                        
                        # Track result
                        results.append({
                            "model": model_to_test,
                            "expected_limit": expected_limit,
                            "reported_limit": reported_limit,
                            "matches": reported_limit == expected_limit
                        })
                        
                        assert reported_limit == expected_limit, f"Expected limit: {expected_limit}, but error reported: {reported_limit}"
                    else:
                        # Alternate pattern for different error message formats
                        limit_match = re.search(r"(\d+)", error_message)
                        if limit_match and limit_match.group(1).isdigit():
                            reported_limit = int(limit_match.group(1))
                            if 1000 <= reported_limit <= 200000:  # Reasonable token limit range
                                print(f"Possible limit from error: {reported_limit}")
                                
                                # Track result
                                results.append({
                                    "model": model_to_test,
                                    "expected_limit": expected_limit,
                                    "reported_limit": reported_limit,
                                    "matches": reported_limit == expected_limit
                                })
                                
                                assert reported_limit == expected_limit, f"Expected limit: {expected_limit}, but error reported: {reported_limit}"
                            else:
                                print(f"Found number {reported_limit} but it doesn't look like a token limit")
                                results.append({
                                    "model": model_to_test,
                                    "expected_limit": expected_limit,
                                    "reported_limit": None,
                                    "matches": False,
                                    "error": f"Found number {reported_limit} but it doesn't look like a token limit"
                                })
                        else:
                            print("Could not extract token limit from error message")
                            # If we can't parse the error message, we'll skip assertion
                            # but still log the information for manual verification
                            results.append({
                                "model": model_to_test,
                                "expected_limit": expected_limit,
                                "reported_limit": None,
                                "matches": False,
                                "error": "Could not extract token limit from error message"
                            })
                except Exception as e:
                    print(f"Error testing model {model_to_test}: {str(e)}")
                    results.append({
                        "model": model_to_test,
                        "expected_limit": expected_limit,
                        "reported_limit": None,
                        "matches": False,
                        "error": str(e)
                    })
            
            # Print summary of results
            print("\n=== TOKEN LIMIT TEST RESULTS ===")
            for result in results:
                match_status = "✅ MATCH" if result.get("matches") else "❌ MISMATCH"
                reported = result.get("reported_limit", "N/A")
                expected = result.get("expected_limit", "N/A")
                model = result.get("model")
                
                print(f"{match_status} | {model} | Expected: {expected} | Reported: {reported}")
                if not result.get("matches") and "error" in result:
                    print(f"  Error: {result['error']}")
            
            # Check if any mismatches occurred
            mismatches = [r for r in results if not r.get("matches")]
            if mismatches:
                print(f"\n❌ Found {len(mismatches)} mismatched token limits!")
                pytest.fail(f"Found {len(mismatches)} mismatched token limits. Check the output for details.")
            else:
                print(f"\n✅ All {len(results)} tested token limits match!")
                
        finally:
            # Clean up environment variable
            if "FORCE_BEDROCK" in os.environ:
                del os.environ["FORCE_BEDROCK"]

    def test_retry_and_fallback_logic(self):
        """Test the retry and fallback logic when a model fails."""
        # Set up mocks
        mock_prompt_client = self.get_mock_prompt_client()
        compiled_prompt = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What is the meaning of life?"}
        ]
        
        self.mock_fetch.return_value = (mock_prompt_client, compiled_prompt)
        # Use a non-existent model name that will definitely fail
        self.mock_model.return_value = {"model": "non-existent-model-xyz-123"}
        
        # Save the original default model to restore it later
        original_default_model = AiAccessor._default_model
        
        try:            
            # Run the test - this should fail with the non-existent model,
            # then retry and eventually fall back to the default model
            response = AiAccessor.get_completion(
                prompt_name="test_prompt",
                prompt_variables={"query": "What is the meaning of life?"}
            )
            
            # If we get here, it means the fallback mechanism worked
            assert response is not None
            assert isinstance(response, str)
            assert len(response) > 0
            
            print(f"\n\n=== RETRY & FALLBACK RESPONSE ===\n{response[:100]}...\n=======================\n")
            
        finally:
            # Restore the original default model
            AiAccessor._default_model = original_default_model


if __name__ == "__main__":
    pytest.main(["-v", __file__]) 