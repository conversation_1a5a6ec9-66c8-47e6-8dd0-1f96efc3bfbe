# AI Accessor Integration Tests

This directory contains integration tests for the `AiAccessor` class that validates we can successfully invoke different AI models.

## Requirements

- Python 3.8+
- pytest
- The required environment variables:
  - For OpenAI models (like `gpt-4o-mini`):
    - `OPENAI_API_KEY`
  - For AWS Bedrock models (like `llama3-2-1b-instruct`):
    - `AWS_ACCESS_KEY_ID`
    - `AWS_SECRET_ACCESS_KEY`
    - `AWS_REGION_NAME`
  - For Langfuse (optional if mocked):
    - `LANGFUSE_PUBLIC_KEY`
    - `LANGFUSE_SECRET_KEY`
    - `LANGFUSE_HOST` (optional)

## Running the Tests

Make sure you're in the project root directory, then run:

```bash
# Add project to PYTHONPATH if needed
export PYTHONPATH=$PYTHONPATH:$(pwd)

# Run all tests in this directory
pytest test/integrated/accessor/ai/

# Run a specific test 
pytest test/integrated/accessor/ai/test_ai_accessor.py::TestAiAccessor::test_gpt4o_mini_completion

# Run with verbose output
pytest -v test/integrated/accessor/ai/
```

## Test Details

The tests perform the following:

1. `test_gpt4o_mini_completion`: Tests the OpenAI `gpt-4o-mini` model with a simple question.
2. `test_bedrock_llama_completion`: Tests the AWS Bedrock `llama3-2-1b-instruct` model with a simple query.

Each test mocks the Langfuse interactions but makes real calls to the AI providers.

## Troubleshooting

If you encounter path-related issues, ensure you've added the project root to your PYTHONPATH:

```bash
export PYTHONPATH=$PYTHONPATH:/path/to/your/project/root
```

If you're having authentication issues, verify that your API keys and credentials are correctly set in the environment. 