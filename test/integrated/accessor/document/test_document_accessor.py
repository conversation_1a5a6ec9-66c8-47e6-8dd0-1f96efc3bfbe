import unittest

from accessor.document.document_accessor import DocumentAccessor
from accessor.document.dto import JobStatusDTO
from accessor.document.enum import JobSortOrder
from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY


class TestDocumentAccessor(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # Initialize the DocumentAccessor with mock parameters
        cls.accessor = DocumentAccessor(
            AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY, logger_name="test_logger"
        )

    def test_01_get_jobs_with_business_id_1(self):
        # Test with business_id = 1
        jobs = self.accessor.get_jobs(business_id=1)
        # Verify that the result is not empty
        self.assertGreater(len(jobs), 0, "Expected non-empty result for business_id 1")

    def test_02_get_jobs_with_business_id_2(self):
        # Test with business_id = 2
        jobs = self.accessor.get_jobs(business_id=2)
        # Verify that the result is not empty
        self.assertGreater(len(jobs), 0, "Expected non-empty result for business_id 2")

    def test_03_get_jobs_with_business_id_3(self):
        # Test with business_id = 3
        jobs = self.accessor.get_jobs(business_id=3)
        # Verify that the result is empty
        self.assertEqual(len(jobs), 0, "Expected no results for business_id 3")

    def test_04_get_jobs_with_none_business_id(self):
        # Test with business_id = None (should return all non-deleted jobs)
        jobs = self.accessor.get_jobs(business_id=None)
        # Verify that the result is not empty
        self.assertGreater(len(jobs), 0, "Expected non-empty result for business_id None")

    def test_05_default_sort_order(self):
        # Test default sort order (ascending)
        jobs = self.accessor.get_jobs(sort_by=JobSortOrder.DEFAULT, ascending=True)
        self.assertTrue(
            self._is_sorted_by_default(jobs, ascending=True),
            "Jobs should be sorted by default order (ascending).",
        )

        # Test default sort order (descending)
        jobs = self.accessor.get_jobs(sort_by=JobSortOrder.DEFAULT, ascending=False)
        self.assertTrue(
            self._is_sorted_by_default(jobs, ascending=False),
            "Jobs should be sorted by default order (descending).",
        )

    def test_06_created_date_sort_order(self):
        # Test sorting by created date (ascending)
        jobs = self.accessor.get_jobs(sort_by=JobSortOrder.CREATED_DATE, ascending=True)
        self.assertTrue(
            self._is_sorted_by_created_date(jobs, ascending=True),
            "Jobs should be sorted by created date (ascending).",
        )

        # Test sorting by created date (descending)
        jobs = self.accessor.get_jobs(sort_by=JobSortOrder.CREATED_DATE, ascending=False)
        self.assertTrue(
            self._is_sorted_by_created_date(jobs, ascending=False),
            "Jobs should be sorted by created date (descending).",
        )

    # def test_07_name_sort_order(self):
    #     # Test sorting by name (ascending)
    #     jobs = self.accessor.get_jobs(sort_by=JobSortOrder.NAME, ascending=True)
    #     self.assertTrue(
    #         self._is_sorted_by_name(jobs, ascending=True),
    #         "Jobs should be sorted by name (ascending).",
    #     )

    #     # Test sorting by name (descending)
    #     jobs = self.accessor.get_jobs(sort_by=JobSortOrder.NAME, ascending=False)
    #     self.assertTrue(
    #         self._is_sorted_by_name(jobs, ascending=False),
    #         "Jobs should be sorted by name (descending).",
    #     )

    def test_08_status_sort_order(self):
        # Test sorting by status (ascending)
        jobs = self.accessor.get_jobs(sort_by=JobSortOrder.STATUS, ascending=True)
        self.assertTrue(
            self._is_sorted_by_status(jobs, ascending=True),
            "Jobs should be sorted by status (ascending).",
        )

        # Test sorting by status (descending)
        jobs = self.accessor.get_jobs(sort_by=JobSortOrder.STATUS, ascending=False)
        self.assertTrue(
            self._is_sorted_by_status(jobs, ascending=False),
            "Jobs should be sorted by status (descending).",
        )

    def test_09_get_jobs_with_domain_id_only(self):
        # Test filtering by domain_id only
        jobs = self.accessor.get_jobs(domain_id=18)
        self.assertTrue(
            all(job.domain_id == 18 for job in jobs),
            "All jobs should belong to domain_id 18",
        )

    def test_10_get_jobs_with_job_status_id_only(self):
        # Test filtering by job_status_id only
        jobs = self.accessor.get_jobs(job_status_id=2)
        self.assertTrue(
            all(job.job_status_id == 2 for job in jobs), "All jobs should have job_status_id 2"
        )

    def test_11_get_jobs_with_business_and_domain(self):
        # Test filtering by both business_id and domain_id
        jobs = self.accessor.get_jobs(business_id=1, domain_id=18)
        self.assertTrue(
            all(job.business_id == 1 and job.domain_id == 18 for job in jobs),
            "All jobs should belong to business_id 1 and domain_id 18",
        )

    def test_12_get_jobs_with_business_and_status(self):
        # Test filtering by both business_id and job_status_id
        jobs = self.accessor.get_jobs(business_id=1, job_status_id=2)
        self.assertTrue(
            all(job.business_id == 1 and job.job_status_id == 2 for job in jobs),
            "All jobs should belong to business_id 1 and have job_status_id 2",
        )

    def test_13_get_jobs_with_domain_and_status(self):
        # Test filtering by both domain_id and job_status_id
        jobs = self.accessor.get_jobs(domain_id=18, job_status_id=2)
        self.assertTrue(
            all(job.domain_id == 18 and job.job_status_id == 2 for job in jobs),
            "All jobs should belong to domain_id 18 and have job_status_id 2",
        )

    def test_14_get_jobs_with_all_filters(self):
        # Test filtering by business_id, domain_id, and job_status_id
        jobs = self.accessor.get_jobs(business_id=1, domain_id=18, job_status_id=2)
        self.assertTrue(
            all(
                job.business_id == 1 and job.domain_id == 18 and job.job_status_id == 2
                for job in jobs
            ),
            "All jobs should belong to business_id 1, domain_id 18, and have job_status_id 2",
        )

    def test_15_get_job_statuses_with_sort_order(self):
        """
        Test that get_job_statuses_with_sort_order returns the expected job statuses
        in the correct sort order.
        """
        # Call the method
        job_statuses = self.accessor.get_job_statuses_with_sort_order()

        # Check that the result is a list of JobStatusDTO instances
        self.assertIsInstance(job_statuses, list)
        self.assertTrue(all(isinstance(status, JobStatusDTO) for status in job_statuses))

        # Check that the list is sorted by sort_order
        sort_orders = [status.sort_order for status in job_statuses]
        self.assertEqual(
            sort_orders, sorted(sort_orders), "The job statuses are not sorted by sort order"
        )

        # Optionally, you can print the results for debugging
        for status in job_statuses:
            print(
                f"ID: {status.job_status_id}, Name: {status.name}, Sort Order: {status.sort_order}"
            )

    # Helper methods to check if the list is sorted correctly
    def _is_sorted_by_default(self, jobs, ascending=True):
        return self._is_sorted_by_status(jobs, ascending=ascending)

    def _is_sorted_by_created_date(self, jobs, ascending=True):
        # Check if jobs are sorted by created date
        sort_key = self._sort_by_created_date
        return self._is_sorted(jobs, sort_key, ascending)

    def _is_sorted_by_name(self, jobs, ascending=True):
        # Check if jobs are sorted by name
        sort_key = self._sort_by_name
        return self._is_sorted(jobs, sort_key, ascending)

    def _is_sorted_by_status(self, jobs, ascending=True):
        # Define the custom sort order mapping
        sort_order_map = {1: 1, 2: 2, 3: 3, 4: 4, 16: 5, 17: 6, 18: 7, 19: 8, 7: 9, 8: 10}

        # Use a regular function as the sort key
        sort_key = self._sort_by_status(sort_order_map)
        return self._is_sorted(jobs, sort_key, ascending)

    def _is_sorted(self, items, sort_key, ascending=True):
        # General helper to check if a list is sorted based on a key and direction
        sorted_items = sorted(items, key=sort_key, reverse=not ascending)
        return items == sorted_items

    def _sort_by_created_date(self, job):
        return job.created_at

    def _sort_by_name(self, job):
        return job.job_name

    def _sort_by_status(self, sort_order_map):
        # Return a function that sorts by status using the given sort_order_map
        def sort_key(job):
            return sort_order_map.get(job.job_status_id, float("inf"))

        return sort_key


if __name__ == "__main__":
    unittest.main()
