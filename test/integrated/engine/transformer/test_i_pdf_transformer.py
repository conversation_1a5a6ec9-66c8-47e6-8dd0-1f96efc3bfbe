import json
from io import BytesIO
from typing import List

import fitz  # PyMuPDF
from PIL import Image

from engine.transformer import IPdfTransformer, TransformEngine
from engine.transformer.dto import Block, ImageData, TransformConfig
from engine.transformer.enum import ImageFormatEnum

# Test data based on your blocks
valeri_document_blocks: List[Block] = [
    {"bbox": [72.0, 125.21673583984375, 518.2555541992188, 152.4229736328125], "page_number": 76},
    {
        "bbox": [110.4000015258789, 161.21673583984375, 366.4553527832031, 174.62298583984375],
        "page_number": 76,
    },
    {
        "bbox": [107.5199966430664, 183.7767333984375, 535.8939819335938, 197.1829833984375],
        "page_number": 76,
    },
    {
        "bbox": [117.0, 197.57672119140625, 248.49398803710938, 210.98297119140625],
        "page_number": 76,
    },
    {"bbox": [72.0, 219.5367431640625, 533.013916015625, 232.9429931640625], "page_number": 76},
    {"bbox": [72.0, 242.3657684326172, 318.5071105957031, 254.69952392578125], "page_number": 76},
    {"bbox": [72.0, 558.2967529296875, 410.8539733886719, 571.7030029296875], "page_number": 76},
    {
        "bbox": [109.07998657226562, 580.7355346679688, 183.09396362304688, 595.4367065429688],
        "page_number": 76,
    },
    {
        "bbox": [109.0799789428711, 598.7355346679688, 183.09396362304688, 631.5567016601562],
        "page_number": 76,
    },
    {
        "bbox": [109.07996368408203, 634.8555297851562, 201.09393310546875, 649.5567016601562],
        "page_number": 76,
    },
    {
        "bbox": [109.07994842529297, 652.8555297851562, 205.17391967773438, 667.5567016601562],
        "page_number": 76,
    },
    {
        "bbox": [109.0799331665039, 670.9755249023438, 273.2138977050781, 685.6766967773438],
        "page_number": 76,
    },
    {
        "bbox": [107.51991271972656, 689.336669921875, 542.6138916015625, 702.742919921875],
        "page_number": 76,
    },
    {
        "bbox": [116.99990844726562, 703.13671875, 373.17388916015625, 716.54296875],
        "page_number": 76,
    },
    {
        "bbox": [268.55999755859375, 742.61669921875, 346.65399169921875, 756.02294921875],
        "page_number": 76,
    },
    {"bbox": [72.0, 72.416748046875, 538.1738891601562, 85.822998046875], "page_number": 77},
    {
        "bbox": [71.99996948242188, 87.2967529296875, 174.69395446777344, 115.5830078125],
        "page_number": 77,
    },
    {"bbox": [72.0, 125.00578308105469, 250.62535095214844, 137.33953857421875], "page_number": 77},
    {
        "bbox": [107.51998901367188, 397.61669921875, 483.9339599609375, 411.02294921875],
        "page_number": 77,
    },
    {
        "bbox": [109.07998657226562, 419.09552001953125, 479.61395263671875, 433.79669189453125],
        "page_number": 77,
    },
    {
        "bbox": [109.0799560546875, 437.09552001953125, 366.8139343261719, 451.79669189453125],
        "page_number": 77,
    },
    {
        "bbox": [109.07994079589844, 455.21551513671875, 494.25390625, 469.91668701171875],
        "page_number": 77,
    },
    {
        "bbox": [109.07992553710938, 473.21551513671875, 541.6541137695312, 487.91668701171875],
        "page_number": 77,
    },
    {
        "bbox": [127.07994079589844, 487.6166687011719, 215.8539276123047, 501.0229187011719],
        "page_number": 77,
    },
    {
        "bbox": [110.39994049072266, 505.856689453125, 506.01544189453125, 519.262939453125],
        "page_number": 77,
    },
    {
        "bbox": [116.99993896484375, 520.3766479492188, 291.09393310546875, 533.7828979492188],
        "page_number": 77,
    },
    {
        "bbox": [71.99995422363281, 542.336669921875, 305.49395751953125, 555.742919921875],
        "page_number": 77,
    },
    {
        "bbox": [109.07997131347656, 564.7755126953125, 435.9339599609375, 579.4766845703125],
        "page_number": 77,
    },
    {
        "bbox": [109.07998657226562, 582.7755126953125, 379.89398193359375, 597.4766845703125],
        "page_number": 77,
    },
    {"bbox": [72.0, 601.3766479492188, 369.573974609375, 614.7828979492188], "page_number": 77},
    {
        "bbox": [110.39999389648438, 624.53662109375, 505.4176330566406, 652.462890625],
        "page_number": 77,
    },
    {
        "bbox": [116.99999237060547, 652.856689453125, 383.8539733886719, 666.262939453125],
        "page_number": 77,
    },
    {"bbox": [72.0, 674.816650390625, 539.7339477539062, 688.222900390625], "page_number": 77},
    {"bbox": [90.0, 689.6967163085938, 514.4140014648438, 717.9829711914062], "page_number": 77},
    {
        "bbox": [268.55999755859375, 742.61669921875, 346.65399169921875, 756.02294921875],
        "page_number": 77,
    },
    {"bbox": [72.0, 72.416748046875, 489.0966796875, 85.822998046875], "page_number": 78},
    {"bbox": [90.0, 87.2967529296875, 218.73402404785156, 100.7030029296875], "page_number": 78},
    {
        "bbox": [107.52000427246094, 110.09674072265625, 535.4141845703125, 123.50299072265625],
        "page_number": 78,
    },
    {
        "bbox": [117.00000762939453, 123.896728515625, 455.13397216796875, 137.302978515625],
        "page_number": 78,
    },
    {"bbox": [72.0, 145.85675048828125, 217.4139862060547, 159.26300048828125], "page_number": 78},
    {"bbox": [72.0, 168.56578063964844, 255.42774963378906, 180.8995361328125], "page_number": 78},
    {"bbox": [72.0, 396.7767333984375, 326.13397216796875, 425.06298828125], "page_number": 78},
    {
        "bbox": [110.39999389648438, 434.9367370605469, 539.3766479492188, 490.34295654296875],
        "page_number": 78,
    },
    {
        "bbox": [116.99993896484375, 490.7366943359375, 394.533935546875, 504.1429443359375],
        "page_number": 78,
    },
    {
        "bbox": [110.39996337890625, 513.0567016601562, 541.4154052734375, 554.782958984375],
        "page_number": 78,
    },
    {
        "bbox": [116.99996948242188, 555.1766967773438, 255.69395446777344, 568.5829467773438],
        "page_number": 78,
    },
]
fragments = [
    # Fragment 1
    """{"blocks": [{"bbox": [90.02400207519531, 197.3607177734375, 540.4243774414062, 225.77000427246094], "page_number": 2}, {"bbox": [108.0199966430664, 229.25003051757812, 363.1658630371094, 240.29002380371094], "page_number": 2}, {"bbox": [90.02400207519531, 241.43072509765625, 540.6009521484375, 255.29002380371094], "page_number": 2}, {"bbox": [108.0199966430664, 258.7699890136719, 530.4317016601562, 284.88525390625], "page_number": 2}, {"bbox": [144.02000427246094, 287.80999755859375, 363.04583740234375, 298.8500061035156], "page_number": 2}, {"bbox": [162.02000427246094, 300.56585693359375, 423.7658386230469, 313.25], "page_number": 2}, {"bbox": [126.0199966430664, 315.8190002441406, 530.4866333007812, 328.32525634765625], "page_number": 2}, {"bbox": [144.02000427246094, 331.25, 426.5258483886719, 342.2900085449219], "page_number": 2}, {"bbox": [162.02000427246094, 344.1258544921875, 540.4844360351562, 356.80999755859375], "page_number": 2}, {"bbox": [180.02000427246094, 360.19000244140625, 312.745849609375, 371.2300109863281], "page_number": 2}, {"bbox": [126.0199966430664, 373.7989807128906, 337.5858459472656, 386.30523681640625], "page_number": 2}, {"bbox": [162.02000427246094, 387.58587646484375, 538.0116577148438, 400.27001953125], "page_number": 2}, {"bbox": [180.02000427246094, 403.75, 340.8258361816406, 414.7900085449219], "page_number": 2}, {"bbox": [162.02000427246094, 416.505859375, 524.2432861328125, 458.83001708984375], "page_number": 2}, {"bbox": [216.0500030517578, 462.30999755859375, 273.3858642578125, 473.3500061035156], "page_number": 2}, {"bbox": [198.02000427246094, 474.4906921386719, 539.6663208007812, 488.3500061035156], "page_number": 2}, {"bbox": [216.0500030517578, 491.8299865722656, 364.7258605957031, 502.8699951171875], "page_number": 2}], "domain": "fnma", "word_count": 209, "header_name": "2. Verify the borrower's payments are considered current.  ", "token_count": 252, "character_count": 1283, "ending_page_number": 2, "starting_page_number": 2, "ending_printed_page_number": 0, "starting_printed_page_number": 0}""",
    # Fragment 2
    """{"blocks": [{"bbox": [72.02400207519531, 150.74002075195312, 223.70587158203125, 161.78001403808594], "page_number": 1}, {"bbox": [90.02400207519531, 171.0806884765625, 533.43408203125, 199.9399871826172], "page_number": 1}, {"bbox": [108.0199966430664, 203.42001342773438, 166.908203125, 214.4600067138672], "page_number": 1}, {"bbox": [90.02400207519531, 215.75067138671875, 532.5950317382812, 229.60997009277344], "page_number": 1}, {"bbox": [108.0199966430664, 232.97000122070312, 166.908203125, 244.00999450683594], "page_number": 1}, {"bbox": [90.02400207519531, 245.27069091796875, 470.14373779296875, 304.3699951171875], "page_number": 1}], "domain": "fnma", "word_count": 77, "header_name": "Introduction  ", "token_count": 91, "character_count": 603, "ending_page_number": 1, "starting_page_number": 1, "ending_printed_page_number": 0, "starting_printed_page_number": 0}""",
    # Fragment 3
    """{"blocks": [{"bbox": [90.02400207519531, 361.210693359375, 512.9558715820312, 390.19000244140625], "page_number": 1}], "domain": "fnma", "word_count": 28, "header_name": "Automatic Termination of Conventional Mortgage Insurance  ", "token_count": 33, "character_count": 173, "ending_page_number": 1, "starting_page_number": 1, "ending_printed_page_number": 0, "starting_printed_page_number": 0}""",
    # Fragment 4
    """{"blocks": [{"bbox": [90.02400207519531, 420.3706970214844, 323.9058532714844, 434.2300109863281], "page_number": 1}, {"bbox": [126.0199966430664, 436.7989807128906, 515.9141845703125, 478.2252502441406], "page_number": 1}, {"bbox": [90.02400207519531, 489.19000244140625, 405.8858642578125, 500.2300109863281], "page_number": 1}, {"bbox": [90.02400207519531, 509.44073486328125, 511.76904296875, 523.300048828125], "page_number": 1}, {"bbox": [108.0199966430664, 526.7799682617188, 228.3858642578125, 537.8200073242188], "page_number": 1}, {"bbox": [126.0199966430664, 540.2689819335938, 542.5342407226562, 566.739990234375], "page_number": 1}, {"bbox": [144.02000427246094, 570.219970703125, 528.4761962890625, 595.780029296875], "page_number": 1}, {"bbox": [180.02000427246094, 599.1399536132812, 377.56585693359375, 610.1799926757812], "page_number": 1}, {"bbox": [162.02000427246094, 612.015869140625, 540.57177734375, 639.2200317382812], "page_number": 1}, {"bbox": [180.02000427246094, 642.719970703125, 368.80584716796875, 653.760009765625], "page_number": 1}, {"bbox": [90.02400207519531, 669.4207153320312, 532.923828125, 697.7999877929688], "page_number": 1}, {"bbox": [108.0199966430664, 701.2799682617188, 194.87586975097656, 712.3200073242188], "page_number": 1}, {"bbox": [126.0199966430664, 73.26900482177734, 538.1402587890625, 99.73998260498047], "page_number": 2}, {"bbox": [144.02000427246094, 103.22000122070312, 256.3458557128906, 114.26000213623047], "page_number": 2}, {"bbox": [72.02400207519531, 125.66000366210938, 533.5958251953125, 136.6999969482422], "page_number": 2}], "domain": "fnma", "word_count": 258, "header_name": "1. Determine when the MI is due to automatically terminate.  ", "token_count": 315, "character_count": 1524, "ending_page_number": 2, "starting_page_number": 1, "ending_printed_page_number": 0, "starting_printed_page_number": 0}""",
]

# Parse the fragments and extract the blocks
all_document_blocks = []

for fragment_str in fragments:
    fragment = json.loads(fragment_str)
    blocks = fragment["blocks"]
    all_document_blocks.append(blocks)


# Running the test
def test_transform_pdf_to_cropped_image():
    pdf_path = "valeri-servicer-user-guide.pdf"
    pdf_doc = fitz.open(pdf_path)

    # Initialize the transformer
    pdf_transformer: IPdfTransformer = TransformEngine(logger_name="test-i-pdf-transformer")

    # Set up the transformation configuration
    config = TransformConfig(
        image_format=ImageFormatEnum.PNG,
        scaling_factor=2.0,  # Use the desired scaling factor for your test
        dpi=(300, 300),  # Use the desired DPI settings for your test
    )

    # Run the transformation
    transformed_image_data: ImageData = pdf_transformer.transform_pdf_to_cropped_image(
        doc=pdf_doc, document_blocks=valeri_document_blocks, config=config
    )
    # Check if the output is in the correct format and not empty
    assert transformed_image_data.image_format == ImageFormatEnum.PNG, "Incorrect image format"
    assert transformed_image_data.image_bytes, "Image data should not be empty"

    # Display or save the output image for visual verification (optional)
    with BytesIO(transformed_image_data.image_bytes) as img_io:
        image = Image.open(img_io)
        image.save("transformed_image_output.png")  # Save image in cwd
        print("Image saved as 'transformed_image_output.png'")


def test_fragment_loop():
    pdf_path = "FNMA_Termination_Of_Conv_MI_Sec_-_2_Pg_Test_Doc.pdf"
    pdf_doc = fitz.open(pdf_path)

    # Initialize the transformer
    pdf_transformer: IPdfTransformer = TransformEngine(logger_name="test-i-pdf-transformer")

    config = TransformConfig(
        image_format=ImageFormatEnum.PNG,
        scaling_factor=2.0,  # Adjust as needed
        dpi=(300, 300),  # Adjust as needed
    )

    # Loop through each set of blocks and process them
    for idx, document_blocks in enumerate(all_document_blocks, start=1):
        # Run the transformation for the current set of blocks
        transformed_image_data: ImageData = pdf_transformer.transform_pdf_to_cropped_image(
            doc=pdf_doc, document_blocks=document_blocks, config=TransformConfig()
        )

        # Check if the output is in the correct format and not empty
        assert transformed_image_data.image_format == ImageFormatEnum.PNG, "Incorrect image format"
        assert transformed_image_data.image_bytes, "Image data should not be empty"

        # Save the output image for visual verification
        print(f"Image bytes: {len(transformed_image_data.image_bytes)}")
        with BytesIO(transformed_image_data.image_bytes) as img_io:
            image = Image.open(img_io)
            output_filename = f"transformed_image_output_{idx}.png"
            image.save(output_filename)  # Save image in the current working directory
            print(f"Image saved as '{output_filename}'")


# Run the test
# test_transform_pdf_to_cropped_image()
test_fragment_loop()
