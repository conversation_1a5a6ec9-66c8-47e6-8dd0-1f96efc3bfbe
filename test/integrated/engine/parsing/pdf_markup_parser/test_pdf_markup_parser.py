import unittest
from io import BytesIO
import fitz  # PyMuPDF
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.colors import black

from engine.parsing.llama_index_extensions.parser.pdf_markup_parser.pdf_markup_parser import (
    PdfMarkupParser,
)


def create_pdf_with_markup():
    """Create a test PDF with markup (strikethrough and underline)"""
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    
    # Page 1: Simple text with strikethrough
    c.setFont("Helvetica", 12)
    # Using target red color (0.82, 0.203, 0.219)
    c.setFillColorRGB(0.82, 0.203, 0.219)
    c.drawString(100, 750, "The Agency must show that any reduction in the servicer's loss claim which corresponds with the servicer's action or failure to act.")
    c.setFillColor(black)
    c.drawString(100, 750, "The Agency must show that any reduction in the servicer's loss claim which corresponds with the servicer's action or failure to act.")
    c.setFillColorRGB(0.82, 0.203, 0.219)
    c.line(100, 745, 600, 745)  # Strikethrough line
    
    # Page 2: Text with underline
    c.showPage()
    c.setFont("Helvetica", 12)
    # Using target blue color (0.0, 0.469, 0.832)
    c.setFillColorRGB(0.0, 0.469, 0.832)
    c.drawString(100, 750, "The Agency must show that any reduction to the servicer's loss claim corresponds with the servicer's action or failure to act.")
    c.setFillColor(black)
    c.drawString(100, 750, "The Agency must show that any reduction to the servicer's loss claim corresponds with the servicer's action or failure to act.")
    c.setFillColorRGB(0.0, 0.469, 0.832)
    c.line(100, 748, 600, 748)  # Underline closer to text (2 points below)
    
    # Page 3: Mixed markup
    c.showPage()
    c.setFont("Helvetica", 12)
    # First line: strikethrough
    c.setFillColorRGB(0.82, 0.203, 0.219)
    c.drawString(100, 750, "The Agency was evaluating the claims and determined the funding should continue.")
    c.setFillColor(black)
    c.drawString(100, 750, "The Agency was evaluating the claims and determined the funding should continue.")
    c.setFillColorRGB(0.82, 0.203, 0.219)
    c.line(100, 745, 500, 745)
    
    # Second line: underline
    c.setFillColorRGB(0.0, 0.469, 0.832)
    c.drawString(100, 730, "The Agency evaluated the claims and determined the funding should continue.")
    c.setFillColor(black)
    c.drawString(100, 730, "The Agency evaluated the claims and determined the funding should continue.")
    c.setFillColorRGB(0.0, 0.469, 0.832)
    c.line(100, 728, 500, 728)  # Underline closer to text (2 points below)
    
    c.save()
    buffer.seek(0)
    return buffer.getvalue()


class TestPdfMarkupParser(unittest.TestCase):
    def setUp(self):
        self.parser = PdfMarkupParser()
        self.test_pdf = BytesIO(create_pdf_with_markup())

    def test_none_document(self):
        """Test handling of None input"""
        with self.assertRaises(ValueError):
            self.parser.parse_nodes(None)

    def test_empty_pdf(self):
        """Test handling of empty PDF"""
        empty_pdf = BytesIO()
        with self.assertRaises(Exception):
            self.parser.parse_nodes(empty_pdf)

    def test_single_page_with_strikethrough(self):
        """Test processing a single page with strikethrough markup"""
        nodes = self.parser.parse_nodes(self.test_pdf, start_page=1, end_page=1)

        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].metadata["page_number"], 1)
        # The actual content will be determined by the real OpenAI API
        self.assertIsNotNone(nodes[0].text)

    def test_single_page_with_underline(self):
        """Test processing a single page with underline markup"""
        nodes = self.parser.parse_nodes(self.test_pdf, start_page=2, end_page=2)

        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].metadata["page_number"], 2)
        # The actual content will be determined by the real OpenAI API
        self.assertIsNotNone(nodes[0].text)

    def test_mixed_markup_page(self):
        """Test processing a page with both strikethrough and underline"""
        nodes = self.parser.parse_nodes(self.test_pdf, start_page=3, end_page=3)

        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].metadata["page_number"], 3)
        # The actual content will be determined by the real OpenAI API
        self.assertIsNotNone(nodes[0].text)

    def test_full_document_processing(self):
        """Test processing the entire document"""
        nodes = self.parser.parse_nodes(self.test_pdf)

        self.assertEqual(len(nodes), 3)
        for i, node in enumerate(nodes, start=1):
            self.assertEqual(node.metadata["page_number"], i)
            # The actual content will be determined by the real OpenAI API
            self.assertIsNotNone(node.text)

    def test_valid_page_range(self):
        """Test processing a specific page range"""
        nodes = self.parser.parse_nodes(self.test_pdf, start_page=2, end_page=2)
        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].metadata["page_number"], 2)
        # The actual content will be determined by the real OpenAI API
        self.assertIsNotNone(nodes[0].text)

    def test_invalid_page_range(self):
        """Test handling of invalid page ranges"""
        with self.assertRaises(ValueError):
            self.parser.parse_nodes(self.test_pdf, start_page=3, end_page=2)

    # def test_error_handling(self):
    #     """Test error handling with invalid PDF"""
    #     invalid_pdf = BytesIO(b"Not a PDF file")
    #     with self.assertLogs(level="ERROR") as log:
    #         nodes = self.parser.parse_nodes(invalid_pdf)
    #     self.assertEqual(len(nodes), 0)
    #     self.assertTrue(any("Error" in msg for msg in log.output))


if __name__ == "__main__":
    unittest.main() 