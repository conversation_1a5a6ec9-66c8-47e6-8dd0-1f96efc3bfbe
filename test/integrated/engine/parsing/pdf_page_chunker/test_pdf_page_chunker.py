import unittest
from io import Bytes<PERSON>
from unittest.mock import MagicMock, patch
from langfuse.decorators import observe

from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas

# Import your PdfPageParser
from engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser import (
    PdfPageParser,
)


def create_minimal_pdf():
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    c.drawString(100, 750, "Hello World")
    c.showPage()
    c.save()
    buffer.seek(0)
    return buffer.getvalue()


def create_multi_page_pdf():
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    for page_number in range(3):
        c.drawString(100, 750, f"Page {page_number + 1}")
        c.showPage()
    c.save()
    buffer.seek(0)
    return buffer.getvalue()


def create_large_pdf(num_pages):
    buffer = BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    for page_number in range(num_pages):
        c.drawString(100, 750, f"Page {page_number + 1}")
        c.showPage()
    c.save()
    buffer.seek(0)
    return buffer.getvalue()


class TestPdfPageParser(unittest.TestCase):
    def setUp(self):
        self.chunker = PdfPageParser()
        # Create a simple one-page PDF for testing
        self.single_page_pdf = BytesIO(create_minimal_pdf())

    @observe()
    def test_none_document(self):
        with self.assertRaises(ValueError):
            self.chunker.parse_nodes(None)

    @observe()
    def test_empty_pdf(self):
        empty_pdf = BytesIO()
        with self.assertRaises(Exception):
            self.chunker.parse_nodes(empty_pdf)

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_single_page_pdf(self, mock_converse):
        # Mock AiAccessor response
        mock_converse.return_value = '{"content": [{"type": "text", "text": "Hello World"}]}'

        nodes = self.chunker.parse_nodes(self.single_page_pdf)

        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].metadata["page_number"], 1)
        self.assertEqual(nodes[0].text, "Hello World")

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_multi_page_pdf(self, mock_converse):
        # Mock responses for each page
        mock_converse.side_effect = [
            '{"content": [{"type": "text", "text": "Content of page 1"}]}',
            '{"content": [{"type": "text", "text": "Content of page 2"}]}',
            '{"content": [{"type": "text", "text": "Content of page 3"}]}',
        ]

        multi_page_pdf = BytesIO(create_multi_page_pdf())
        nodes = self.chunker.parse_nodes(multi_page_pdf)

        self.assertEqual(len(nodes), 3)
        for i, node in enumerate(nodes, start=1):
            self.assertEqual(node.metadata["page_number"], i)
            self.assertEqual(node.text, f"Content of page {i}")

    @observe()
    def test_valid_page_range(self):
        # Assume multi_page_pdf from previous test
        multi_page_pdf = BytesIO(create_multi_page_pdf())

        @patch("accessor.ai.AiAccessor.get_completion")
        def inner_test(mock_converse):
            # Mock only for page 2
            mock_converse.return_value = '{"content": [{"type": "text", "text": "Content of page 2"}]}'

            nodes = self.chunker.parse_nodes(multi_page_pdf, start_page=2, end_page=2)
            self.assertEqual(len(nodes), 1)
            self.assertEqual(nodes[0].metadata["page_number"], 2)
            self.assertEqual(nodes[0].text, "Content of page 2")

        inner_test()

    @observe()
    def test_invalid_page_range(self):
        multi_page_pdf = BytesIO(create_multi_page_pdf())
        with self.assertRaises(ValueError):
            self.chunker.parse_nodes(multi_page_pdf, start_page=3, end_page=2)

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_llm_api_failure(self, mock_converse):
        # Define a RuntimeError for AI accessor failure
        class AiAccessorError(RuntimeError):
            pass
            
        # Simulate an AI accessor error
        mock_converse.side_effect = RuntimeError("AI service failure")

        multi_page_pdf = BytesIO(create_multi_page_pdf())
        with self.assertLogs(level="ERROR") as log:
            nodes = self.chunker.parse_nodes(multi_page_pdf)

        # Check that the error was logged
        self.assertIn("Error processing page with vision model", log.output[0])
        # Nodes should be empty or contain partial results depending on implementation

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_page_with_images_and_tables(self, mock_converse):
        mock_converse.return_value = (
            """
            {
                "content": [
                    {"type": "text", "text": "This is some text."},
                    {"type": "image_description", "text": "An image of a cat."},
                    {"type": "text", "text": "Here is a table:"},
                    {"type": "text", "text": "If condition A, then do B."}
                ]
            }
            """
        )

        multi_page_pdf = BytesIO(create_multi_page_pdf())
        nodes = self.chunker.parse_nodes(multi_page_pdf, start_page=1, end_page=1)

        self.assertEqual(len(nodes), 1)
        expected_text = (
            "This is some text. An image of a cat. Here is a table: If condition A, then do B."
        )
        self.assertEqual(nodes[0].text, expected_text)

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_retry_logic(self, mock_converse):
        # First two attempts raise an exception, third attempt succeeds
        mock_converse.side_effect = [
            RuntimeError("Temporary error"),
            RuntimeError("Temporary error"),
            '{"content": [{"type": "text", "text": "Recovered text"}]}'
        ]

        multi_page_pdf = BytesIO(create_minimal_pdf())
        with self.assertLogs(level="ERROR") as log:
            nodes = self.chunker.parse_nodes(multi_page_pdf)

        # Check that retries were logged
        error_logs = [record for record in log.output if "Attempt" in record]
        self.assertEqual(len(error_logs), 2)
        # Ensure the final attempt succeeded
        self.assertEqual(nodes[0].text, "Recovered text")

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_large_pdf(self, mock_converse):
        num_pages = 50  # Adjust as needed for testing
        mock_converse.return_value = '{"content": [{"type": "text", "text": "Page content"}]}'

        large_pdf = BytesIO(create_large_pdf(num_pages))
        nodes = self.chunker.parse_nodes(large_pdf)

        self.assertEqual(len(nodes), num_pages)
        for node in nodes:
            self.assertEqual(node.text, "Page content")

    @observe()
    @patch("accessor.ai.AiAccessor.get_completion")
    def test_exception_handling(self, mock_converse):
        # Simulate a JSON decode error in the assistant's reply
        mock_converse.return_value = "Invalid JSON"

        multi_page_pdf = BytesIO(create_minimal_pdf())
        with self.assertLogs(level="ERROR") as log:
            nodes = self.chunker.parse_nodes(multi_page_pdf)

        # Check that the error was logged
        self.assertIn("No JSON object found in the response", log.output[0])
        # Depending on implementation, nodes may be empty or contain partial results


if __name__ == "__main__":
    unittest.main()
