import io
import os
import unittest
from pathlib import Path
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.lib.colors import black

from engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser import (
    PdfPageParser,
)


def create_test_pdf_with_text():
    """Create a test PDF with various text elements"""
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    
    # Page 1: Simple text
    c.setFont("Helvetica", 12)
    c.drawString(100, 750, "This is a test document.")
    c.drawString(100, 730, "It contains multiple lines of text.")
    c.drawString(100, 710, "And some special characters: !@#$%^&*()")
    
    # Page 2: Text with different formatting
    c.showPage()
    c.setFont("Helvetica-Bold", 14)
    c.drawString(100, 750, "This is a bold heading")
    c.setFont("Helvetica", 12)
    c.drawString(100, 730, "This is regular text below the heading")
    
    # Page 3: Text with numbers and tables
    c.showPage()
    c.setFont("Helvetica", 12)
    c.drawString(100, 750, "Table of Contents:")
    c.drawString(100, 730, "1. Introduction")
    c.drawString(100, 710, "2. Main Content")
    c.drawString(100, 690, "3. Conclusion")
    
    c.save()
    buffer.seek(0)
    return buffer.getvalue()


def create_test_pdf_with_tables():
    """Create a test PDF with table-like structures"""
    buffer = io.BytesIO()
    c = canvas.Canvas(buffer, pagesize=letter)
    
    # Page 1: Simple table
    c.setFont("Helvetica", 12)
    c.drawString(100, 750, "| Header 1 | Header 2 |")
    c.drawString(100, 730, "|----------|----------|")
    c.drawString(100, 710, "| Cell 1   | Cell 2   |")
    c.drawString(100, 690, "| Cell 3   | Cell 4   |")
    
    # Page 2: Complex table with merged cells
    c.showPage()
    c.drawString(100, 750, "| Merged Header |")
    c.drawString(100, 730, "|---------------|")
    c.drawString(100, 710, "| Sub 1 | Sub 2 |")
    c.drawString(100, 690, "|-------|-------|")
    c.drawString(100, 670, "| Data  | Data  |")
    
    c.save()
    buffer.seek(0)
    return buffer.getvalue()


class TestPdfPageParserIntegrated(unittest.TestCase):
    def setUp(self):
        self.parser = PdfPageParser()
        self.test_pdf_text = create_test_pdf_with_text()
        self.test_pdf_tables = create_test_pdf_with_tables()

    def test_parse_simple_text_pdf(self):
        """Test parsing a PDF with simple text content"""
        pdf_bytes = io.BytesIO(self.test_pdf_text)
        nodes = self.parser.parse_nodes(pdf_bytes)
        
        self.assertEqual(len(nodes), 3)  # Should have 3 pages
        self.assertIn("This is a test document", nodes[0].text)
        self.assertIn("This is a bold heading", nodes[1].text)
        self.assertIn("Table of Contents", nodes[2].text)

    def test_parse_table_pdf(self):
        """Test parsing a PDF with table structures"""
        pdf_bytes = io.BytesIO(self.test_pdf_tables)
        nodes = self.parser.parse_nodes(pdf_bytes)
        
        self.assertEqual(len(nodes), 2)  # Should have 2 pages
        self.assertIn("Header 1", nodes[0].text)
        self.assertIn("Merged Header", nodes[1].text)

    def test_parse_specific_page_range(self):
        """Test parsing specific pages from a PDF"""
        pdf_bytes = io.BytesIO(self.test_pdf_text)
        nodes = self.parser.parse_nodes(pdf_bytes, start_page=2, end_page=2)
        
        self.assertEqual(len(nodes), 1)  # Should only have page 2
        self.assertIn("This is a bold heading", nodes[0].text)
        self.assertEqual(nodes[0].metadata["page_number"], 2)

    def test_parse_with_real_document(self):
        """Test parsing a real document from the test data directory"""
        test_data_dir = Path(__file__).parent.parent.parent.parent.parent / "test_data"
        test_pdf_path = test_data_dir / "sample_documents" / "sample.pdf"
        
        if not test_pdf_path.exists():
            self.skipTest(f"Test PDF not found at {test_pdf_path}")
        
        with open(test_pdf_path, "rb") as f:
            pdf_bytes = io.BytesIO(f.read())
            nodes = self.parser.parse_nodes(pdf_bytes)
            
            self.assertGreater(len(nodes), 0)
            for node in nodes:
                self.assertIsNotNone(node.text)
                self.assertIsNotNone(node.metadata["page_number"])

    def test_parse_with_large_document(self):
        """Test parsing a larger document to verify performance"""
        # Create a 10-page PDF
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)
        
        for i in range(10):
            c.setFont("Helvetica", 12)
            c.drawString(100, 750, f"This is page {i + 1}")
            c.drawString(100, 730, "With some sample content")
            if i < 9:  # Don't add a new page after the last page
                c.showPage()
        
        c.save()
        buffer.seek(0)
        
        nodes = self.parser.parse_nodes(buffer)
        self.assertEqual(len(nodes), 10)
        
        # Verify content of each page
        for i, node in enumerate(nodes, 1):
            self.assertIn(f"This is page {i}", node.text)
            self.assertEqual(node.metadata["page_number"], i)

    def test_parse_with_special_characters(self):
        """Test parsing a PDF with special characters and formatting"""
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)
        
        # Add various special characters and formatting
        c.setFont("Helvetica", 12)
        c.drawString(100, 750, "Special characters: !@#$%^&*()")
        c.drawString(100, 730, "Quotes: \"Single' and 'Double\"")
        c.drawString(100, 710, "Spaces:   Multiple   Spaces")
        c.drawString(100, 690, "Line breaks:\nNew line")
        
        c.save()
        buffer.seek(0)
        
        nodes = self.parser.parse_nodes(buffer)
        self.assertEqual(len(nodes), 1)
        self.assertIn("Special characters", nodes[0].text)
        self.assertIn("Quotes", nodes[0].text)

    def test_parse_with_mixed_content(self):
        """Test parsing a PDF with mixed content types"""
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=letter)
        
        # Add text, numbers, and table-like structures
        c.setFont("Helvetica", 12)
        c.drawString(100, 750, "Regular text content")
        c.drawString(100, 730, "Numbers: 1234567890")
        c.drawString(100, 710, "| Table | Header |")
        c.drawString(100, 690, "|-------|--------|")
        c.drawString(100, 670, "| Data  | Value  |")
        
        c.save()
        buffer.seek(0)
        
        nodes = self.parser.parse_nodes(buffer)
        self.assertEqual(len(nodes), 1)
        self.assertIn("Regular text content", nodes[0].text)
        self.assertIn("Numbers", nodes[0].text)
        self.assertIn("Table", nodes[0].text)


if __name__ == "__main__":
    unittest.main() 