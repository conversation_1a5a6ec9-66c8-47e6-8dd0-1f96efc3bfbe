import logging
import unittest

from accessor.content import ContentAccessor
from engine.synthesis.data_access import DataAccess
from engine.synthesis.text_extractor import TextExtractor
from engine.synthesis.unique_statement_to_chunk_mapper import (
    UniqueStatementToChunkMapper,
)


class TestUniqueStatementToChunkMapperIntegration(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        logger_name = "integration_test_logger"
        cls.logger = logging.getLogger(logger_name)
        cls.data_access = DataAccess()
        cls.content_accessor = ContentAccessor(logger_name)
        cls.text_extractor = TextExtractor(logger_name)

        cls.mapper = UniqueStatementToChunkMapper(
            data_access=cls.data_access, text_extractor=cls.text_extractor, logger=cls.logger
        )

    # def test_01_map_by_document_business_statement_type_integration(self):
    #     document_id = 436
    #     statement_type_ids = [1, 2, 6]  # List of statement_type_ids to loop through

    #     subtopics = self.content_accessor.get_subtopics_by_document(document_id)

    #     # Loop through each statement_type_id
    #     for statement_type_id in statement_type_ids:
    #         # Call the method for each statement_type_id
    #         self.mapper.map_by_document_statement_type(document_id, statement_type_id)

    #         # Fetch subtopics for the business_id

    #         for subtopic in subtopics:
    #             # Fetch unique statements for each subtopic and statement_type_id
    #             unique_statements = (
    #                 self.data_access.get_unique_statements_for_document_and_subtopic(
    #                     document_id, subtopic.subtopic_id, statement_type_id
    #                 )
    #             )
    #             # Assert that each unique statement has a chunk_id
    #             for unique_statement in unique_statements:
    #                 self.assertIsNotNone(
    #                     unique_statement.chunk_id,
    #                     f"Chunk ID is None for statement {unique_statement.unique_statement_id} with statement_type_id {statement_type_id}",
    #                 )

    def test_02_map_by_document_subtopic_statement_type_integration(self):
        document_id = 364
        subtopic_id = 196
        statement_type_id = 1

        self.mapper.map_by_document_subtopic_statement_type(
            document_id, subtopic_id, statement_type_id
        )

        unique_statements = self.content_accessor.get_unique_statements_for_document_and_subtopic(
            document_id, subtopic_id, statement_type_id
        )
        for unique_statement in unique_statements:
            self.assertIsNotNone(unique_statement.chunk_id)


if __name__ == "__main__":
    unittest.main()
