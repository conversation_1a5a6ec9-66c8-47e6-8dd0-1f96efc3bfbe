import pytest
import uuid
from datetime import datetime
from engine.synthesis.document_optimizer import DocumentOptimizer
from accessor.content.content_accessor import ContentAccessor
from accessor.content.accessors.dto.document_insert_dto import DocumentInsertDTO

@pytest.fixture
def document_optimizer():
    return DocumentOptimizer(logger_name="test_document_optimizer")

def test_optimize_document_with_real_content():
    # Create a test document with some sample content
    test_content = """
    # Main Section
    This is a test document with some content.
    
    ## Subsection 1
    Here are some steps to follow:
    1. First step
    2. Second step
    3. Third step
    
    ![Test Image](test.jpg){width="800" height="600"}
    
    ## Subsection 2
    More content here with some technical details.
    """
    
    # Initialize the optimizer and content accessor
    optimizer = DocumentOptimizer(logger_name="test_document_optimizer")
    content_accessor = ContentAccessor(logger_name="test_document_optimizer")
    
    # Create a test document in the database
    document_dto = DocumentInsertDTO(
        domain_id=1,  # Using default domain ID
        name="Test Document",
        s3_location="test/test_document.md",
        business_id=1,
        effective_datetime=datetime.now(),
        start_page=0,
        end_page=0
    )
    
    document = content_accessor.save_document(document_dto)
    document_id = document.document_id
    
    # Save the content as chunks
    content_accessor.save_chunk(
        document_id=document_id,
        formatted_text=test_content,
        text_content=test_content,
        vector_id=str(uuid.uuid4())
    )
    
    # Optimize the document
    optimized_content = optimizer.optimize_document(document_id)
    
    # Verify the optimization results
    assert optimized_content is not None
    assert len(optimized_content) > 0
    assert "![Test Image](test.jpg){width=\"800\" height=\"600\"}" in optimized_content  # Verify image was preserved
    assert "First step" in optimized_content  # Verify content was preserved
    assert "Second step" in optimized_content
    assert "Third step" in optimized_content
    
    # Clean up
    content_accessor.delete_document(document_id)

def test_optimize_document_with_complex_structure():
    # Create a more complex test document
    complex_content = """
    # Introduction
    Welcome to this complex document.
    
    ## Overview
    This document contains multiple sections and subsections.
    
    ### Technical Details
    Some technical information here.
    
    ## Implementation
    Here's how to implement the solution:
    
    ### Step 1: Setup
    1. First setup step
    2. Second setup step
    
    ### Step 2: Configuration
    1. Configure option A
    2. Configure option B
    
    ![Configuration Diagram](config.png){width="1000" height="800"}
    
    ## Conclusion
    This is the end of the document.
    """
    
    # Initialize the optimizer and content accessor
    optimizer = DocumentOptimizer(logger_name="test_document_optimizer")
    content_accessor = ContentAccessor(logger_name="test_document_optimizer")
    
    # Create a test document in the database
    document_dto = DocumentInsertDTO(
        domain_id=1,  # Using default domain ID
        name="Complex Test Document",
        s3_location="test/complex_document.md",
        business_id=1,
        effective_datetime=datetime.now(),
        start_page=0,
        end_page=0
    )
    
    document = content_accessor.save_document(document_dto)
    document_id = document.document_id
    
    # Save the content as chunks
    content_accessor.save_chunk(
        document_id=document_id,
        formatted_text=complex_content,
        text_content=complex_content,
        vector_id=str(uuid.uuid4())
    )
    
    # Optimize the document
    optimized_content = optimizer.optimize_document(document_id)
    
    # Verify the optimization results
    assert optimized_content is not None
    assert len(optimized_content) > 0
    assert "![Configuration Diagram](config.png){width=\"1000\" height=\"800\"}" in optimized_content
    assert "First setup step" in optimized_content
    assert "Configure option A" in optimized_content
    assert "Configure option B" in optimized_content
    
    # Clean up
    content_accessor.delete_document(document_id)

def test_optimize_document_with_empty_content():
    # Initialize the optimizer and content accessor
    optimizer = DocumentOptimizer(logger_name="test_document_optimizer")
    content_accessor = ContentAccessor(logger_name="test_document_optimizer")
    
    # Create a test document with empty content
    document_dto = DocumentInsertDTO(
        domain_id=1,  # Using default domain ID
        name="Empty Test Document",
        s3_location="test/empty_document.md",
        business_id=1,
        effective_datetime=datetime.now(),
        start_page=0,
        end_page=0
    )
    
    document = content_accessor.save_document(document_dto)
    document_id = document.document_id
    
    # Don't create any chunks - this matches the unit test behavior
    # where get_chunks_by_document returns an empty list
    
    # Optimize the document
    optimized_content = optimizer.optimize_document(document_id)
    
    # Verify the optimization results
    assert optimized_content is not None
    assert optimized_content == ""  # Should return empty string when no chunks exist
    
    # Clean up
    content_accessor.delete_document(document_id) 