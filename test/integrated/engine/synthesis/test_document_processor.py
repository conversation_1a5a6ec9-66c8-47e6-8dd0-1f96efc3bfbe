import logging
import unittest

from engine.synthesis.config import InitServices
from engine.synthesis.data_access import DataAccess
from engine.synthesis.text_extractor import TextExtractor
from engine.synthesis.unique_statement_to_chunk_mapper import (
    UniqueStatementToChunkMapper,
)


class TestDocumentProcessorIntegration(unittest.TestCase):
    """Integration test for DocumentProcessor class with Pinecone."""

    @classmethod
    def setUpClass(cls):
        """Set up Pinecone client and DocumentProcessor with lazy loading."""
        # Use a lambda to defer Pinecone initialization
        logger_name = "test-document-processor"
        logger = logging.getLogger(logger_name)
        cls.mapper = UniqueStatementToChunkMapper(DataAccess(), TextExtractor(logger_name), logger)

    @classmethod
    def tearDownClass(cls):
        """Clean up"""

    @classmethod
    def test_backfill_chunkids_for_unique_statements(self):
        """Integration test for backfilling chunk IDs for unique statements."""

        document_id = 327
        statement_type_id = 1

        # Call the method under test
        self.mapper.map_by_document_statement_type(
            document_id=document_id, statement_type_id=statement_type_id
        )


if __name__ == "__main__":
    unittest.main()
