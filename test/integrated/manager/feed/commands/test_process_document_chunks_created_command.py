"""Tests for ProcessDocumentChunksCreatedCommand."""

import json
import pytest
from unittest.mock import Mock, patch

from manager.feed.commands.process_document_chunks_created_command import ProcessDocumentChunksCreatedCommand
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.workflow_type_enum import WorkflowTypeEnum

@pytest.fixture
def mock_document_accessor():
    accessor = Mock()
    accessor.get_chunk_ids_for_document.return_value = [1, 2, 3]
    accessor.get_job_type_id_by_document.return_value = 42
    return accessor

@pytest.fixture
def mock_synthesis_engine():
    return Mock()

@pytest.fixture
def mock_sns_client():
    client = Mock()
    client.get_topic_arn.return_value = "arn:aws:sns:us-east-1:123456789012:test-topic"
    return client

@pytest.fixture
def command(mock_document_accessor, mock_synthesis_engine, mock_sns_client):
    dependency_factory = Mock()
    dependency_factory.get_document_accessor.return_value = mock_document_accessor
    dependency_factory.get_synthesis_engine.return_value = mock_synthesis_engine
    
    return ProcessDocumentChunksCreatedCommand(
        dependency_factory=dependency_factory,
        document_id=123,
        job_id=456,
        receipt_handle="test-receipt",
        sqs_client=Mock(),
        sns_client=mock_sns_client,
    )

def test_seed_item_rows(command, mock_document_accessor):
    """Test that seed_item_rows creates one row per chunk with correct type."""
    # Set a fake workflow ID since we're not running the full workflow
    command.job_workflow_id = 999
    command._seed_item_rows()
    
    mock_document_accessor.seed_items.assert_called_once_with(
        command.job_workflow_id,
        ["1", "2", "3"],
        item_type=JobItemTypeEnum.CHUNK
    )

def test_process_item(command, mock_document_accessor, mock_synthesis_engine):
    """Test that process_item calls synthesis engine with correct parameters."""
    command.process_item("1")
    
    mock_synthesis_engine.generate_document_statements.assert_called_once_with(
        document_id=123,
        job_type_id=42,
        chunk_id=1
    )

def test_post_workflow(command, mock_sns_client):
    """Test that post_workflow publishes SNS message with correct format."""
    command.post_workflow()
    
    mock_sns_client.publish.assert_called_once()
    call_args = mock_sns_client.publish.call_args[1]
    assert call_args["TopicArn"] == "arn:aws:sns:us-east-1:123456789012:test-topic"
    
    # Parse the JSON message and compare with expected dict
    json_msg = json.loads(call_args["Message"])
    assert json_msg == {
        "header": {"type": "job-process-document-statements-created"},
        "body": {"document_id": 123, "job_id": 456}
    }

def test_workflow_type(command):
    """Test that workflow_type returns correct enum."""
    assert command.workflow_type == WorkflowTypeEnum.PROCESS_DOCUMENT_CHUNKS_CREATED 