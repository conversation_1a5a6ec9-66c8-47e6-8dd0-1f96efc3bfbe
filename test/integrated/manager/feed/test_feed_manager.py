import unittest
import uuid
from unittest.mock import MagicMock

from langfuse.decorators import observe
from sqlalchemy import text

from manager.feed.commands.command_factory import CommandFactory
from manager.feed.commands.command_handler import CommandHandler
from manager.feed.commands.dependency_factory import DependencyFactory
from manager.feed.feed_manager import FeedManager
from shared.enums.job_enums import JobStatusEnum, MessageType
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from util.database import Session

# Static variables
DOCUMENT_ID = 1044
JOB_ID = 1012
RECEIPT_HANDLE = "test-receipt-handle"


class TestFeedManager(unittest.TestCase):
    """Unit tests for FeedManager class."""

    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.reset_document_job_state(DOCUMENT_ID, JOB_ID)

        cls.mock_sqs_client = MagicMock()
        cls.mock_sns_client = MagicMock()
        cls.mock_sns_client.publish.return_value = {
            "ResponseMetadata": {"HTTPStatusCode": 200},
            "MessageId": str(uuid.uuid4()),
        }
        cls.feed_manager = FeedManager("phoenix-burst", sqs_client=cls.mock_sqs_client)
        cls.dependency_factory = DependencyFactory("phoenix-burst")
        cls.command_factory = CommandFactory(cls.dependency_factory, cls.feed_manager.logger)
        cls.command_handler = CommandHandler(cls.feed_manager.logger)

    @classmethod
    def reset_document_job_state(cls, doc_id, job_id):
        """Reset document job state in the database."""
        with Session() as session:
            try:
                session.execute(text("SELECT reset_curation_state(:doc_id)"), {"doc_id": doc_id})
                session.execute(
                    text("UPDATE job SET job_status_id = :status_id WHERE reference_id = :doc_id"),
                    {
                        "status_id": JobStatusEnum.INITIAL_PROCESSING.value,
                        "doc_id": doc_id,
                    },
                )
                session.execute(
                    text(
                        """
                        UPDATE job_workflow
                        SET status = CAST(:status AS public."job_workflow_status")
                        WHERE job_id = :job_id
                        """
                    ),
                    {
                        "status": JobWorkflowStatusEnum.STARTING.value,
                        "job_id": job_id,
                    },
                )
                session.commit()

            except Exception as e:
                session.rollback()
                raise e

    @classmethod
    def clear_statements_for_document(cls, doc_id):
        """Clear out statements for job."""
        with Session() as session:
            try:
                session.execute(
                    text(
                        """DELETE
                        FROM statement
                        WHERE chunk_id IN (
                            SELECT chunk_id
                            FROM chunk
                            WHERE document_id = :doc_id
                        )"""
                    ),
                    {"doc_id": doc_id},
                )
                session.execute(
                    text("UPDATE job SET job_status_id = :status_id WHERE reference_id = :doc_id"),
                    {
                        "status_id": JobStatusEnum.INITIAL_PROCESSING.value,  # Use enum value
                        "doc_id": doc_id,
                    },
                )
                session.commit()

            except Exception as e:
                session.rollback()
                raise e

    @observe()
    def test_01_process_document_job_submitted(self):
        """Test processing document job submitted."""

        command = self.command_factory.get_command(
            MessageType.PROCESS_DOCUMENT_SUBMITTED.value,
            DOCUMENT_ID,
            JOB_ID,
            RECEIPT_HANDLE,
            self.mock_sqs_client,
            self.mock_sns_client,
            chunk_id=None,
        )

        self.command_handler.handle(command)

    # @observe()
    # def test_02_process_document_chunks_created(self):
    #     """Test processing document chunks created."""

    #     command = self.command_factory.get_command(
    #         MessageType.PROCESS_DOCUMENT_CHUNKS_CREATED.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #     )

    #     self.command_handler.handle(command)

    # @observe()
    # def test_03_process_document_statements_created(self):
    #     """Test processing document chunk images."""

    #     command = self.command_factory.get_command(
    #         MessageType.PROCESS_DOCUMENT_STATEMENTS_CREATED.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #     )
    #     self.command_handler.handle(command)

    # @observe()
    # def test_04_process_generate_unique_requirements(self):
    #     """Test processing generation of unique requirements."""
    #     command = self.command_factory.get_command(
    #         MessageType.GENERATE_UNIQUE_REQUIREMENTS.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #     )

    #     self.command_handler.handle(command)

    #     self.feed_manager.document_accessor.job_ready_for_requirement_curation(JOB_ID)

    # @observe()
    # def test_05_process_generate_user_stories(self):
    #     """Test processing generation of user stories."""
    #     command = self.command_factory.get_command(
    #         MessageType.GENERATE_USER_STORIES.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #     )

    #     self.command_handler.handle(command)

    # @observe()
    # def test_06_process_generate_acceptance_criteria_for_user_story(self):
    #     # Replace with valid id
    #     user_story_id = -1

    #     """Test generation of acceptance criteria for a specified user story."""
    #     self.feed_manager._process_generate_acceptance_criteria_for_user_story(user_story_id)

    # @observe()
    # def test_07_process_generate_test_cases_for_acceptance_criteria(self):
    #     # Replace with valid id
    #     acceptance_criteria_id = -1

    #     """Test generation of test cases for a specified acceptance criteria."""
    #     self.feed_manager._process_generate_test_cases_for_acceptance_criteria(
    #         acceptance_criteria_id
    #     )

    # @observe()
    # def test_08_process_bulk_generate_acceptance_criteria(self):

    #     command = self.command_factory.get_command(
    #         MessageType.GENERATE_ACCEPTANCE_CRITERIA.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #     )

    #     self.command_handler.handle(command)

    # @observe()
    # def test_09_process_bulk_generate_test_cases(self):

    #     command = self.command_factory.get_command(
    #         MessageType.GENERATE_TEST_CASES.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #     )

    #     self.command_handler.handle(command)

    # @observe()
    # def test_10_process_generate_requirements_from_document_domain_comparison(self):

    #     command = self.command_factory.get_command(
    #         MessageType.GENERATE_POLICY_REQUIREMENT_COMPARISON.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.feed_manager.sns_client,
    #         chunk_id=None,
    #         domain_id=33,  # Cenlar
    #     )

    #     self.command_handler.handle(command)

    # @observe()
    # def test_11_optimize_document(self):
    #     document_id = 1044
    #     job_id = 1012
    #     # self.reset_document_job_state(document_id, job_id)
    #     command = self.command_factory.get_command(
    #         MessageType.OPTIMIZE_PROCEDURE.value,
    #         document_id,
    #         job_id,
    #         RECEIPT_HANDLE,
    #         self.mock_sqs_client,
    #         self.mock_sns_client,
    #         chunk_id=None,
    #         domain_id=33,  # Cenlar
    #     )

    #     self.command_handler.handle(command)

    # @observe()
    # def test_12_process_change_statements_from_federal_register(self):

    #     command = self.command_factory.get_command(
    #         MessageType.GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS.value,
    #         DOCUMENT_ID,
    #         JOB_ID,
    #         RECEIPT_HANDLE,
    #         self.feed_manager.sqs_client.sqs_client,
    #         self.feed_manager.sns_client,
    #         chunk_id=None,
    #         domain_id=33,  # Cenlar
    #     )

    #     self.command_handler.handle(command)


if __name__ == "__main__":
    unittest.main()
