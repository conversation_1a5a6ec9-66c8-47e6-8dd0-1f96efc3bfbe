"""
Integrated tests for SynthesisEngine.TextExtractor.table_context_requirements_for_persona method
"""

import unittest

from langfuse.decorators import observe

from engine.synthesis.text_extractor import TextExtractor


class TestTableContextRequirementsForPersona(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        cls.logger_name = "test-text-extractor-logger"
        cls.extractor = TextExtractor(cls.logger_name)

    @observe()
    def test_01_requirements_contain_invalid_json_quotes_returns_valid_json(self):

        chunk_text = """This section is the first under the  GUS Underwriting Analysis Report  and reviews property-related data entered  into the loan application. Information in this section includes:   • "Property Address"  "Property Value"   • "Sales Contract Price" is listed if the "Loan Purpose" is "Purchase."  • "Balance of Mortgage Loans to be Paid Off in the Transaction" is listed if the "Loan Purpose" is "Refinance."  • "Purchase is"   "Const-Conv/Perm" is listed if the "Loan Purpose" is "Purchase."  • "Single Closing/Two-Closing" is listed if the "Loan Purpose" is "Purchase" and "Const-Conv/Perm" is "Yes."  • "Renovation" is listed if the "Loan Purpose" is "Purchase."   • "Type of Construction"  • "Estate Will Be Held In"  • "New Construction Requirements Met" is listed if the "Loan Purpose" is "Purchase" and "Purchase is" is  "New."  • "Existing Construction Requirements Met" is listed if the "Loan Purpose" is "Purchase" and "Purchase is" is  "Existing."  • "Structure Type"   USDA- RURAL DEVELOPMENT GUS LENDER USER GUIDE   P a g e   | """
        persona = "mortgage originator"
        persona_stripped_requirements = {
            "Statements": [
                {
                    "Statement_Text": '"Sales Contract Price" is listed if the "Loan Purpose" is "Purchase."',
                    "Behavior_Required": "List the Sales Contract Price",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "The Loan Purpose must be Purchase",
                },
                {
                    "Statement_Text": '"Purchase is" "Const-Conv/Perm" is listed if the "Loan Purpose" is "Purchase."',
                    "Behavior_Required": "List the Purchase is Const-Conv/Perm",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "The Loan Purpose must be Purchase",
                },
                {
                    "Statement_Text": '"Single Closing/Two-Closing" is listed if the "Loan Purpose" is "Purchase" and "Const-Conv/Perm" is "Yes."',
                    "Behavior_Required": "List Single Closing/Two-Closing",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "The Loan Purpose must be Purchase and Const-Conv/Perm must be Yes",
                },
                {
                    "Statement_Text": '"Renovation" is listed if the "Loan Purpose" is "Purchase."',
                    "Behavior_Required": "List Renovation",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "The Loan Purpose must be Purchase",
                },
                {
                    "Statement_Text": '"New Construction Requirements Met" is listed if the "Loan Purpose" is "Purchase" and "Purchase is" is "New."',
                    "Behavior_Required": "List New Construction Requirements Met",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "The Loan Purpose must be Purchase and Purchase must be New",
                },
                {
                    "Statement_Text": '"Existing Construction Requirements Met" is listed if the "Loan Purpose" is "Purchase" and "Purchase is" is "Existing."',
                    "Behavior_Required": "List Existing Construction Requirements Met",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "The Loan Purpose must be Purchase and Purchase must be Existing",
                },
            ]
        }

        table_contexted_requirements = self.extractor.table_context_requirements_for_persona(
            persona_stripped_requirements, persona, chunk_text
        )

        self.assertIsInstance(table_contexted_requirements, dict)

        self.assertIn("Statements", table_contexted_requirements)

        self.assertGreater(len(table_contexted_requirements["Statements"]), 0)

        first_statement = table_contexted_requirements["Statements"][0]
        self.assertIn("Statement_Text", first_statement)
        self.assertIn("Behavior_Required", first_statement)
        self.assertIn("Whose_Behavior", first_statement)
        self.assertIn("Conditions_From_Header", first_statement)

        self.assertTrue(first_statement["Statement_Text"])
        self.assertTrue(first_statement["Behavior_Required"])
        self.assertTrue(first_statement["Whose_Behavior"])
        self.assertTrue(first_statement["Conditions_From_Header"])

    @observe()
    def test_02_llm_response_exceeds_4096_tokens_valid_json_response(self):
        chunk_text = "AK; $2,000; ; Varies 13; $500; $400 AR; $1,700; ; Varies 13; $500; $400 AZ; $1,700; ; Varies 13; $400; $400 CA; $1,700 2; ; Varies 13; $550; $400 CO; $2,200; ; Varies 13; $450; $400 CT; ; $3,200 3,4; Varies 13; $400; $400 DC; $1,250 1; $2,875; Varies 13; $400; $400 DE; ; $2,450; Varies 13; $450; $400 FL; ; $4,100 11; Varies 13; $400; $400 GA; $1,700; ; Varies 13; $450; $400 GU; $2,225; ; Varies 13; $350; $400 HI; ; $9,000 7; Varies 13; $525; $400 IA; $1,275; $2,450; Varies 13; $350; $400 ID; $1,550; ; Varies 13; $400; $400 IL; ; $3,000; Varies 13; $400; $400 IN; ; $2,800; Varies 13; $450; $400 KS; ; $2,400; Varies 13; $400; $400 KY; ; $3,000; Varies 13; $400; $400 LA; ; $2,500; Varies 13; $500; $400 MA; $2,550; $3,400 3; Varies 13; $625; $400 MD; $3,000 5; ; Varies 13; $500; $400 ME; ; $3,950; Varies 13; $525; $400 MI; $1,900; ; Varies 13; $425; $400 MN; $1,775; $1,800; Varies 13; $400; $400 MO; $1,700; ; Varies 13; $450; $400 MS; $1,500 1; ; Varies 13; $400; $400 MT; $1,800; ; Varies 13; $400; $400 NC; $2,175; ; Varies 13; $400; $400 ND; ; $2,350; Varies 13; $350; $400 NE; $1,400; $1,950; Varies 13; $350; $400 NH; $1,725; ; Varies 13; $425; $400 NJ; ; $4,500; Varies 13; $500; $400 NM; ; $4,000; Varies 13; $400; $400 NV; $2,000; ; Varies 13; $650; $400 NY; $1,450 9; $5,225 3,9; Varies 13; $725; $400 OH; ; $3,000; Varies 13; $600; $400 OK; ; $2,700; Varies 13; $350; $400 OR; $1,700; $3,700; Varies 13; $400; $400 PA; ; $3,200; Varies 13; $450; $400 PR; ; $2,800 3,10; Varies 13; $300; $400 RI; $2,250; ; Varies 13; $525; $400 (03-09-16) SPECIAL PN Revised (02-09-23) PN 575 HB-1-3555 Attachment 18-C Page 2 of 3 SC; ; $2,850; Varies 13; $450; $400 TN; $1,500; ; Varies 13; $375; $400 TX; $1,700; $3,000; Varies 13; $400; $400 UT; $1,700; $925; Varies 13; $400; $400 VA; $1,700; ; Varies 13; $600; $400 VI; ; $2,650; Varies 13; $300; $400 VT; $1,600; $3,200; Varies 13; $375; $400 WA; $1,800; $3,050; Varies 13; $450; $400 WI; ; $2,600; Varies 13; $400; $400 WV; $1,450 1,6; ; Varies 13; $400; $400 WY; $1,550; ; Varies 13; $500; $400"
        persona = "mortgage originator"
        requirements = {
            "Statements": [
                {
                    "Statement_Text": "AK; $2,000; ; Varies 13; $500; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for AK",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "AR; $1,700; ; Varies 13; $500; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for AR",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "AZ; $1,700; ; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for AZ",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "CA; $1,700 2; ; Varies 13; $550; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for CA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "CO; $2,200; ; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for CO",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "CT; ; $3,200 3,4; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for CT",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "DC; $1,250 1; $2,875; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for DC",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "DE; ; $2,450; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for DE",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "FL; ; $4,100 11; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for FL",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "GA; $1,700; ; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for GA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "GU; $2,225; ; Varies 13; $350; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for GU",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "HI; ; $9,000 7; Varies 13; $525; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for HI",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "IA; $1,275; $2,450; Varies 13; $350; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for IA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "ID; $1,550; ; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for ID",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "IL; ; $3,000; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for IL",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "IN; ; $2,800; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for IN",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "KS; ; $2,400; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for KS",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "KY; ; $3,000; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for KY",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "LA; ; $2,500; Varies 13; $500; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for LA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MA; $2,550; $3,400 3; Varies 13; $625; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MD; $3,000 5; ; Varies 13; $500; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MD",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "ME; ; $3,950; Varies 13; $525; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for ME",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MI; $1,900; ; Varies 13; $425; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MI",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MN; $1,775; $1,800; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MN",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MO; $1,700; ; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MO",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MS; $1,500 1; ; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MS",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "MT; $1,800; ; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for MT",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NC; $2,175; ; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NC",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "ND; ; $2,350; Varies 13; $350; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for ND",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NE; $1,400; $1,950; Varies 13; $350; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NE",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NH; $1,725; ; Varies 13; $425; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NH",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NJ; ; $4,500; Varies 13; $500; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NJ",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NM; ; $4,000; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NM",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NV; $2,000; ; Varies 13; $650; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NV",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "NY; $1,450 9; $5,225 3,9; Varies 13; $725; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for NY",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "OH; ; $3,000; Varies 13; $600; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for OH",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "OK; ; $2,700; Varies 13; $350; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for OK",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "OR; $1,700; $3,700; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for OR",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "PA; ; $3,200; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for PA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "PR; ; $2,800 3,10; Varies 13; $300; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for PR",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "RI; $2,250; ; Varies 13; $525; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for RI",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "SC; ; $2,850; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for SC",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "TN; $1,500; ; Varies 13; $375; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for TN",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "TX; $1,700; $3,000; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for TX",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "UT; $1,700; $925; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for UT",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "VA; $1,700; ; Varies 13; $600; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for VA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "VI; ; $2,650; Varies 13; $300; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for VI",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "VT; $1,600; $3,200; Varies 13; $375; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for VT",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "WA; $1,800; $3,050; Varies 13; $450; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for WA",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "WI; ; $2,600; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for WI",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "WV; $1,450 1,6; ; Varies 13; $400; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for WV",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
                {
                    "Statement_Text": "WY; $1,550; ; Varies 13; $500; $400",
                    "Behavior_Required": "Provide information on acceptable liquidation costs and fees for WY",
                    "Whose_Behavior": "Mortgage originator",
                    "Conditions_From_Header": "Schedule of Standard Attorney/Trustee Fees",
                },
            ]
        }

        table_contexted_requirements = self.extractor.table_context_requirements_for_persona(
            requirements, persona, chunk_text
        )

        self.assertIsInstance(table_contexted_requirements, dict)

        self.assertIn("Statements", table_contexted_requirements)

        self.assertGreater(len(table_contexted_requirements["Statements"]), 0)

        first_statement = table_contexted_requirements["Statements"][0]
        self.assertIn("Statement_Text", first_statement)
        self.assertIn("Behavior_Required", first_statement)
        self.assertIn("Whose_Behavior", first_statement)
        self.assertIn("Conditions_From_Header", first_statement)

        self.assertTrue(first_statement["Statement_Text"])
        self.assertTrue(first_statement["Behavior_Required"])
        self.assertTrue(first_statement["Whose_Behavior"])
        self.assertTrue(first_statement["Conditions_From_Header"])


if __name__ == "__main__":
    unittest.main()
