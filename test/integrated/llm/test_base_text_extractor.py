"""
Integrated tests for SynthesisEngine.TextExtractor.
"""

import time
import unittest

from langfuse.decorators import observe

from engine.synthesis.text_extractor import TextExtractor


class TestBaseTextExtractor(unittest.TestCase):
    """Class representing tests for TextExtractor."""

    start_time = None

    @classmethod
    def setUpClass(cls):
        cls.logger_name = f"test-text-extractor-logger"
        cls.extractor = TextExtractor(cls.logger_name)
        cls.start_time = time.time()

    @classmethod
    def tearDownClass(cls):
        # Calculate total duration and log it
        duration = time.time() - cls.start_time
        print(f"\nTotal duration: {duration:.2f} seconds")
        print("")

    @observe()
    def test_01_identify_statement_chunk_source(self):
        """Determine if we can identify a statement's chunk source. Based on bug BAI-607."""

        statement_to_evaluate = "With the first modified payment due in the current month, the Servicer must report the principal and forecasted scheduled interest based on the newly modified terms in the current Accounting Cycle, in accordance with modification reporting and drafting requirements."

        candidate_statements = [
            {"chunk_id": 38781, "statement": "Borrower maintenance"},
            {"chunk_id": 38781, "statement": "Account changes"},
            {"chunk_id": 38781, "statement": "Payment collection"},
            {"chunk_id": 38781, "statement": "Payment allocation"},
            {"chunk_id": 38781, "statement": "Reamortization"},
            {"chunk_id": 38781, "statement": "Delinquency tracking and reporting"},
            {"chunk_id": 38781, "statement": "Delinquency reporting"},
            {"chunk_id": 38781, "statement": "Modified Terms"},
            {"chunk_id": 38781, "statement": "Industry Standards Adherence"},
            {
                "chunk_id": 38781,
                "statement": "Servicing Loan file Generation and Maintainence",
            },
            {"chunk_id": 38781, "statement": "Payment Processing Systems"},
            {"chunk_id": 38781, "statement": "Performing Loan Reporting"},
            {"chunk_id": 38781, "statement": "Payment Recording"},
            {
                "chunk_id": 38781,
                "statement": "The Servicer must comply with the following modification reporting and drafting requirements: (a) Before the first modified payment is due, the Servicer must report in accordance with the Note and Security Instrument, and any modification agreement, if applicable.",
            },
            {
                "chunk_id": 38781,
                "statement": "If the mortgage modification was settled in the current Accounting Cycle, the Servicer must report the next month's forecasted scheduled interest based on the modified terms in the current Accounting Cycle in accordance with the modification reporting and drafting requirements.",
            },
            {
                "chunk_id": 38781,
                "statement": "With the first modified payment due in the current month, the Servicer must report the principal and forecasted scheduled interest based on the newly modified terms in the current Accounting Cycle, in accordance with modification reporting and drafting requirements.",
            },
            {"chunk_id": 38781, "statement": "No Authorization statements found"},
            {"chunk_id": 38781, "statement": "No Opportunity statements found"},
            {"chunk_id": 38781, "statement": "No Definitions found"},
        ]

        chunk_id = self.extractor.identify_statement_chunk_source_candidates(
            statement_to_evaluate, candidate_statements
        )

        self.assertEqual(chunk_id[0]["chunk_id"], 38781, "The returned chunk_id should be 38781.")

    @observe()
    def test_02_create_valid_user_stories_as_a_mortgage_originator_with_implied_persona(self):
        """Creates user stories with simple requirement and 'station' as the user and ensures that 'As a mortgage originator' is at the start."""
        persona = "mortgage originator"
        unique_statement_text = (
            "In the event the existing direct loan is being serviced by a contract servicer, "
            "stations should contact Contract Assurance – Portfolio Loans for assistance in "
            "establishing the escrow on the new account"
        )

        user_stories = self.extractor.create_user_stories_for_requirement(
            persona, unique_statement_text
        )

        self.assertTrue(
            len(user_stories) > 0,
            f"Expected non-empty list of user stories but got: {user_stories}",
        )

        expected_start = "As a mortgage originator,"
        actual_first_story = user_stories[0]
        self.assertTrue(
            actual_first_story.startswith(expected_start),
            f"Expected first user story to start with '{expected_start}', but got: '{actual_first_story}'",
        )

    # TODO: Expected to fail currently. May require significant research and effort to resolve.
    @observe()
    def test_03_create_valid_user_stories_as_a_mortgage_originator_implied_persona(self):
        """Creates user stories with complex requirement and 'Stations' as the user and ensures that 'As a mortgage originator' is at the start."""
        persona = "mortgage originator"
        unique_statement_text = (
            "An application may be withdrawn by the Veteran upon his or her written request "
            "at any time prior to the actual closing of the loan, but such withdrawal will not "
            "relieve the Veteran of an obligation to pay any charges that have been incurred. "
            "Stations will acknowledge a Veteran's request for withdrawal by letter. Stations may "
            "also cancel cases if it becomes apparent that the loan will not be closed. A locally-"
            "developed letter, clearly stating the reasons for cancellation, may be used for this purpose. "
            "Any funds received from the Veteran that have not been expended will be refunded."
        )

        user_stories = self.extractor.create_user_stories_for_requirement(
            persona, unique_statement_text
        )

        self.assertTrue(
            len(user_stories) > 0,
            f"Expected at least one user story but got: {user_stories}",
        )

        expected_start = "As a mortgage originator,"
        actual_first_story = user_stories[0]
        self.assertTrue(
            actual_first_story.startswith(expected_start),
            f"Expected first user story to start with '{expected_start}', but got: '{actual_first_story}'",
        )

    @observe()
    def test_04_create_valid_user_stories_user_as_inspectors(self):
        """Creates user stories with simple requirement and 'inspectors' as the user and ensures that 'As a mortgage originator' is at the start."""
        persona = "mortgage originator"
        unique_statement_text = "Inspectors may charge, per inspection, the amount approved by the VA office for the area."

        user_stories = self.extractor.create_user_stories_for_requirement(
            persona, unique_statement_text
        )

        self.assertTrue(
            len(user_stories) > 0,
            f"Expected non-empty list of user stories but got: {user_stories}",
        )

        expected_start = "As a mortgage originator,"
        actual_first_story = user_stories[0]
        self.assertTrue(
            actual_first_story.startswith(expected_start),
            f"Expected first user story to start with '{expected_start}', but got: '{actual_first_story}'",
        )

    @observe()
    def test_05_create_valid_user_stories_user_as_technician(self):
        """Creates user stories with simple requirement and 'technician' as the user and ensures that 'As a mortgage originator' is at the start."""
        persona = "mortgage originator"
        unique_statement_text = (
            "The technician should use this opportunity to contact the Veteran to provide an explanation "
            "of the denial and to discuss ways the Veteran may improve their ability to qualify in the future."
        )

        user_stories = self.extractor.create_user_stories_for_requirement(
            persona, unique_statement_text
        )

        self.assertTrue(
            len(user_stories) > 0,
            f"Expected non-empty list of user stories but got: {user_stories}",
        )

        expected_start = "As a mortgage originator,"
        actual_first_story = user_stories[0]
        self.assertTrue(
            actual_first_story.startswith(expected_start),
            f"Expected first user story to start with '{expected_start}', but got: '{actual_first_story}'",
        )

    @observe()
    def test_06_assemble_requirement_valid_subtopics_one_requirement_generated(self):
        """Simple Fannie Mae statement expected to generate requirement for correct subtopic."""
        unique_statements = []
        existing_requirements = []
        node_text = "Fannie Mae will not reimburse the servicer when the servicer pays interest on an escrow account, whether required by law or voluntary."
        requirement_statements = [
            "Fannie Mae will not reimburse the servicer when the servicer pays interest on an escrow account, whether required by law or voluntary."
        ]
        valid_subtopics = [
            "Escrow account maintenance - Within the context of Escrow Management, the regular oversight and management of escrow accounts to ensure that all funds for property taxes, insurance premiums, and other related expenses are accurately tracked. This includes updating account balances, processing payments, addressing discrepancies, and ensuring compliance with relevant regulations. Effective maintenance also requires clear communication with borrowers regarding account status and any necessary adjustments.",
        ]

        for subtopic in valid_subtopics:
            result = self.extractor.assemble_requirements(
                node_text, requirement_statements, existing_requirements, subtopic
            )
            unique_statements.append(result)

        self.assertEqual(
            len(unique_statements), 1, "Expected exactly one unique statement to be generated."
        )

        # Assert that one of the generated unique statements is related to node_text
        closely_related_statement = any(node_text in statement for statement in unique_statements)
        self.assertTrue(
            closely_related_statement,
            "No statement closely related to node_text found in unique_statements.",
        )

    @observe()
    def test_07_assemble_requirement_incorrect_subtopics_no_requirements_generated(self):
        """Simple Fannie Mae statement expected to not generate unique requirement for incorrect subtopics."""
        unique_statements = []
        existing_requirements = []
        node_text = "Fannie Mae will not reimburse the servicer when the servicer pays interest on an escrow account, whether required by law or voluntary."
        requirement_statements = [
            "Fannie Mae will not reimburse the servicer when the servicer pays interest on an escrow account, whether required by law or voluntary."
        ]
        invalid_subtopics = [
            "Industry Standards Adherence - As part of quality control initiatives, mortgage servicers must evaluate adherence to mortgage servicing industry standards , guidelines, and best practives. This includes evaluating compliance with regulations set forth by entities like Fannie Mae, Freddie Mac, FHA, VA, and other regulatory bodies.",
            "Servicing Loan file Generation and Maintenance - Servicing loan file generation involves creating and maintaining comprehensive and accurate files for each loan serviced. These files include all relevant loan documents, borrower information, and transaction records. Proper generation and maintenance of these files ensure that all necessary information is readily available for servicing activities and regulatory compliance.",
            "Principal, interest, taxes and insurance (PITI) - Within the context of Payment Processing, refers to the four main components of a borrower's monthly mortgage payment. Principal is the portion of the payment that reduces the outstanding loan balance. Interest is the cost of borrowing the loan amount, calculated as a percentage of the principal. Taxes typically refer to property taxes, which are collected and held in an escrow account by the servicer until they are due to be paid to the local government. Insurance includes homeowner’s insurance and, if applicable, private mortgage insurance (PMI), which protects the lender and borrower against property damage and loan default, respectively. Proper management of PITI is essential for ensuring that all components are accurately accounted for and timely paid, maintaining compliance, and preventing borrower delinquency or escrow shortages.",
        ]

        for subtopic in invalid_subtopics:
            result = self.extractor.assemble_requirements(
                node_text, requirement_statements, existing_requirements, subtopic
            )
            unique_statements.append(result)

        self.assertEqual(
            len(unique_statements),
            0,
            "Unique statements were generated on inappropriate subtopics.",
        )

    @observe()
    def test_08_extract_subtopics_one_valid_topic_subtopic_one_match(self):
        """Simple Fannie Mae chunk expected to match correct topic and subtopic."""
        subtopics = [
            "Tax line management::Within the context of Escrow Management, the overseeing and handling of property tax payments for escrow accounts. This process ensures that property taxes are paid accurately and on time to prevent tax liens or penalties. It includes maintaining accurate records of tax liabilities, monitoring tax due dates, and coordinating with local tax authorities. Effective tax line management is crucial for avoiding defaults and maintaining the financial health of the escrow accounts.",
            "Insurance line management::Within the context of Escrow Management, the administration and oversight of homeowners insurance payments within escrow accounts. This process ensures that insurance premiums are paid accurately and on time, maintaining continuous coverage for the property. It includes tracking insurance policy details, renewal dates, and premium amounts, as well as coordinating with insurance providers. Effective insurance line management helps prevent lapses in coverage and protects both the homeowner and the lender from potential risks.",
            "Optional insurance::Within the context of Escrow Management, additional insurance coverages that homeowners can choose to include in their escrow accounts beyond the mandatory homeowners insurance. This can include policies such as flood insurance, earthquake insurance, or personal property insurance. Managing optional insurance involves ensuring that these additional premiums are accurately tracked and paid on time, just like mandatory insurance. It also requires clear communication with homeowners about their optional coverage choices and any changes in premium amounts. Effective management of optional insurance ensures comprehensive protection for the property and its contents, providing peace of mind to homeowners.",
            "Escrow account setup::Within the context of Escrow Management, the process of establishing an account to hold funds on behalf of a borrower for the payment of property-related expenses such as taxes and insurance. This includes gathering necessary documentation, determining initial deposit amounts, and setting up the account with an escrow agent or service provider. The setup process ensures that all relevant parties agree on the terms and that the account is funded appropriately to cover upcoming expenses. Proper setup is crucial for ensuring accurate and timely disbursements, preventing defaults, and maintaining compliance with legal and lender requirements.",
            "Escrow account maintenance::Within the context of Escrow Management, the regular oversight and management of escrow accounts to ensure that all funds for property taxes, insurance premiums, and other related expenses are accurately tracked. This includes updating account balances, processing payments, addressing discrepancies, and ensuring compliance with relevant regulations. Effective maintenance also requires clear communication with borrowers regarding account status and any necessary adjustments.",
            "Annual escrow analysis::Within the context of Escrow Management, the process of reviewing and adjusting the escrow account for an existing mortgage loan on a yearly basis. This analysis involves reassessing the anticipated costs for property taxes, insurance premiums, and other related expenses for the upcoming year. Based on this reassessment, the servicer calculates whether the current monthly escrow payments are sufficient to cover these costs. If necessary, adjustments are made to the monthly payment amounts to account for any changes in expenses or to address any existing surpluses or shortages in the escrow account. The purpose of the annual escrow analysis is to ensure that the account remains properly funded, thereby preventing payment defaults and maintaining compliance with regulatory requirements.",
            "Escrow Waiver::Within the context of Escrow Management, refers to a lender's decision to allow a borrower to pay things like taxes, insurance, homeowners association fees (any expenses, the nonpayment of which could threaten the lender's lien or the lender's security interest in the value of the property) on their own instead of regularly into an escrow account where the lender makes disbursements. Includes evaluating borrower requests for escrow waivers, lenders responsibility to monitor the borrower's payment of such expenses and circumstances that may require a servicer to establish escrow on the loan going forward.",
            "Short year statement::Within the context of Escrow Management, a special escrow account statement issued when there is a significant change in the loan servicing timeline that requires the escrow account to be analyzed and adjusted before the standard annual review date. This might occur due to events such as the sale or transfer of the property, refinancing of the loan, or changes in tax and insurance costs. The short year statement provides a snapshot of the escrow account's status, including any required adjustments to ensure that funds are sufficient to cover upcoming expenses. It helps maintain accuracy in escrow funding by recalculating the necessary payments over the shortened period, preventing discrepancies and ensuring timely payments of property-related obligations.",
        ]
        node_text = "Fannie Mae will not reimburse the servicer when the servicer pays interest on an escrow account, whether required by law or voluntary."
        expected_subtopics = ["Escrow account maintenance"]
        found_subtopics = self.extractor.extract_subtopics(node_text, subtopics)
        self.assertEqual(found_subtopics, expected_subtopics, "Failed to extract a valid subtopic.")

    @observe()
    def test_09_extract_subtopics_one_invalid_topic_no_matches(self):
        """Simple Fannie Mae chunk expected to not match incorrect topic and subtopics."""
        subtopics = [
            "Overpayments::Within the context of Payment Processing, occur when a borrower’s mortgage payment exceeds the required amount due. This can happen intentionally, such as when a borrower makes additional payments to reduce the principal balance faster, or unintentionally, due to calculation errors or miscommunication. When an overpayment is received, the servicer must accurately allocate the excess funds according to the borrower's instructions or, if no instructions are provided, typically towards reducing the principal balance. Proper handling of overpayments is essential for maintaining accurate account records, ensuring transparency, and providing borrowers with clear information about how their payments are applied. Effective management of overpayments supports borrower satisfaction and can contribute to faster loan payoff and reduced interest costs over the life of the loan."
            "Suspense payments ::Within the context of Payment Processing, refer to the payments that cannot be immediately applied to a borrower's account due to discrepancies or incomplete information. These payments are temporarily held in a suspense account until the issue is resolved. Common reasons for suspense payments include partial payments, unidentified payments, or payments with insufficient information. The servicer must investigate and resolve the discrepancies to correctly allocate the funds to the borrower’s account. Proper management of suspense payments is crucial for maintaining accurate account records, avoiding late fees or delinquencies, and ensuring transparent communication with borrowers regarding the status of their payments."
            "Payment waterfall::Within the context of Payment Processing, refer to the predefined sequence in which received mortgage payments are allocated to various components of the loan. Typically, the payment is first applied to any outstanding fees, followed by accrued interest, escrow accounts for taxes and insurance, and finally to the principal balance. This hierarchy ensures that all ancillary costs and obligations are met before reducing the principal amount owed. The payment waterfall is essential for maintaining accurate account balances, ensuring compliance with the terms of the mortgage agreement, and providing transparency to borrowers about how their payments are being applied. Proper management of the payment waterfall helps prevent delinquencies and supports the overall health of the loan portfolio."
            "Principal only payments::Within the context of Payment Processing, refer to payments made by borrowers that are specifically designated to reduce the principal balance of their mortgage loan, excluding interest, fees, and escrow. These payments can be part of a borrower’s strategy to pay off their loan faster and reduce the total interest paid over the life of the loan. When a principal only payment is received, the servicer must ensure it is correctly applied solely to the principal balance. Proper handling and accurate recording of principal only payments are crucial for maintaining transparent account records and supporting the borrower’s financial goals. This type of payment can help improve the borrower’s equity position and reduce the loan term."
            "Irregular payments::Within the context of Payment Processing, refer to mortgage payments that do not conform to the regular, scheduled payment amounts or dates. These can include partial payments, lump-sum payments, additional payments, or payments made outside the standard due date. Managing irregular payments requires careful attention to ensure they are correctly applied to the borrower's account according to the loan agreement terms and any specific borrower instructions. Proper handling of irregular payments is essential for maintaining accurate account balances, avoiding misapplication of funds, and ensuring clear communication with borrowers about the status and application of their payments. Effective management of irregular payments helps prevent confusion and supports the overall integrity of the loan servicing process."
            "Amortization schedule::Within the context of Payment Processing, is a detailed table that outlines each periodic payment on a mortgage loan over its term. This schedule breaks down each payment into portions that go toward the principal and interest, showing how the loan balance decreases over time. It helps borrowers understand how their payments are applied and how much interest they will pay over the life of the loan. Maintaining and providing accurate amortization schedules is essential for transparency, allowing borrowers to track their progress in repaying their mortgage. The amortization schedule also aids servicers in ensuring proper application of payments and adherence to the loan terms."
            "Prepayments::Within the context of Payment Processing, refer to any payments made by a borrower that exceed the scheduled monthly mortgage payment, intended to reduce the principal balance ahead of schedule. These payments can significantly reduce the total interest paid over the life of the loan and shorten the loan term. Proper handling of prepayments involves accurately applying the excess amount to the principal balance and adjusting the amortization schedule accordingly. Servicers must ensure that prepayment instructions are clear and followed precisely to maintain accurate account records. Effective management of prepayments supports borrower financial goals and enhances transparency in the loan servicing process."
            "Payoff quote::Within the context of Payment Processing, is an official statement provided by the servicer detailing the total amount required to fully repay the mortgage loan as of a specific date. This quote includes the remaining principal balance, accrued interest, any applicable fees, and potential prepayment penalties. The payoff quote ensures borrowers have an accurate figure for closing out their loan, whether through refinancing, selling the property, or making a lump-sum payment. Providing an accurate and timely payoff quote is essential for facilitating smooth transactions and maintaining clear communication with borrowers. Proper management of payoff quotes helps prevent discrepancies and ensures the integrity of the loan servicing process."
            "Payoff::Within the context of Payment Processing, refers to the complete repayment of the remaining balance on a mortgage loan, including the principal, accrued interest, and any applicable fees. The payoff process is initiated when a borrower requests a payoff quote and then remits the total quoted amount to settle the loan. This can occur through refinancing, selling the property, or making a lump-sum payment. Proper handling of the payoff ensures that the loan is accurately closed, all liens are released, and any remaining escrow funds are refunded to the borrower. Effective management of the payoff process is essential for maintaining clear records, ensuring borrower satisfaction, and adhering to regulatory requirements."
            "Paid in full::Within the context of Payment Processing, refers to the status of a mortgage loan when the borrower has completely satisfied all financial obligations associated with the loan. This includes paying off the remaining principal, accrued interest, and any applicable fees or penalties. Once a loan is marked as paid in full, the servicer will update the account records, release any liens on the property, and return any remaining escrow funds to the borrower. The borrower will also receive documentation confirming that the loan has been fully repaid. Properly managing the paid in full process is essential for ensuring accurate records, compliance with legal requirements, and providing borrowers with clear evidence of their completed obligation."
            "Late Payments::Within the context of Payment Processing, refer to the mortgage payments that are received after the due date specified in the loan agreement. When a payment is late, the servicer typically assesses a late fee and updates the borrower’s account to reflect the overdue status. Managing late payments involves notifying the borrower of the missed payment, applying any applicable late fees, and working with the borrower to bring the account current. Proper handling of late payments is crucial for maintaining accurate account records, minimizing the impact on the borrower's credit score, and ensuring compliance with regulatory requirements. Effective management helps in reducing delinquencies and supports the overall financial health of the loan portfolio."
            "Late charge assessment::Late charge assessment in mortgage loan servicing refers to the fee imposed by mortgage servicers when borrowers fail to make payments by the due date specified in their loan agreement. This fee compensates the servicer for the additional administrative costs and potential risks associated with late payments and is typically a percentage of the overdue payment or a fixed amount outlined in the mortgage contract. The servicer notifies the borrower of the late fee, which is added to the account balance and pursued for collection along with the overdue payment. Repeated late payments can harm the borrower’s credit score and potentially lead to foreclosure if not addressed. Mortgage servicers must comply with federal and state regulations governing the assessment and collection of these fees to ensure fairness and transparency."
            "Returned Payments::Within the context of Payment Processing, refer to payments that are not successfully processed due to issues such as insufficient funds, closed accounts, or incorrect payment information. When a payment is returned, the servicer must promptly reverse the payment from the borrower's account and notify the borrower of the issue. Handling returned payments involves identifying the cause of the return, applying any applicable fees, and coordinating with the borrower to resolve the problem and ensure future payments are successful. Proper management of returned payments is essential for maintaining accurate account records, preventing late fees and delinquencies, and ensuring clear communication with the borrower. Effective handling helps maintain borrower satisfaction and compliance with regulatory requirements."
            "Escrow Analysis and Adjustment::Within the context of Payment Processing, involves the periodic review and recalibration of a borrower's escrow account to ensure sufficient funds are available to cover property taxes, insurance premiums, and other related expenses. This process typically occurs annually and includes projecting future escrow needs based on current tax and insurance costs, as well as reviewing any surplus or shortage in the account. If adjustments are needed, the servicer will update the borrower's monthly escrow payment amount accordingly. Proper management of escrow analysis and adjustments is essential for avoiding payment shortfalls, ensuring timely disbursements, and maintaining borrower trust. Clear communication with borrowers regarding any changes to their escrow payments is also critical for transparency and compliance with regulatory guidelines."
            "Payment Corrections::Within the context of Payment Processing, refer to the adjustments made to rectify errors in the application of a borrower's mortgage payments. These errors can include misapplied payments, incorrect payment amounts, or payments allocated to the wrong accounts or loan components. The correction process involves identifying the mistake, reversing or adjusting the incorrect entries, and accurately applying the payments as intended. Proper handling of payment corrections is essential for maintaining accurate account records, preventing borrower confusion, and ensuring compliance with loan terms and regulatory requirements. Effective management of payment corrections helps maintain borrower trust and supports the overall integrity of the loan servicing process."
            "Auto-Debit Payments::Within the context of Payment Processing, refers to the automated system that allows borrowers to have their mortgage payments automatically deducted from their bank accounts on scheduled due dates. This payment method ensures timely payments, reduces the risk of late fees, and simplifies the payment process for borrowers. Setting up auto-debit payments involves obtaining borrower authorization, verifying bank account details, and scheduling the recurring transactions. Proper management of auto-debit payments is crucial for maintaining accurate payment records, ensuring funds are available on the scheduled dates, and providing clear communication with borrowers about the setup and any changes to the auto-debit arrangements. Effective use of auto-debit payments enhances borrower convenience and supports consistent cash flow for loan servicing operations."
            "Servicing Fees::Refers to the collection of a contractual servicing fee from the borrower's monthly payment, along with any rules or requirements related to servicing fees."
        ]

        node_text = "Fannie Mae will not reimburse the servicer when the servicer pays interest on an escrow account, whether required by law or voluntary."
        expected_subtopics = []
        found_subtopics = self.extractor.extract_subtopics(node_text, subtopics)
        self.assertEqual(
            found_subtopics, expected_subtopics, "Incorrectly extracted an invalid subtopic."
        )


# KA: Uncomment below to run tests across many models. Need a better solution.
# def create_test_class_for_model(model_name):
#     """Dynamically create a test class for the given model."""
#     class_name = f"TestBaseTextExtractor_{model_name.replace('-', '_')}"
#     return type(
#         class_name,
#         (TestBaseTextExtractor,),
#         {"model": model_name},
#     )


# models_to_test = [
#     "gpt-4-32k",
#     "gpt-4",
#     "o1-preview",
#     "gpt-4o",
#     "o1-mini",
#     "gpt-4o-2024-08-06",
#     "gpt-4-turbo",
#     "gpt-3.5-turbo-16k-0613",
#     "gpt-3.5-turbo",
# ]

# for model in models_to_test:
#     globals()[f"TestBaseTextExtractor_{model.replace('-', '_')}"] = create_test_class_for_model(
#         model
#     )


if __name__ == "__main__":
    unittest.main()
