"""
Integrated tests for SynthesisEngine.TextExtractor.filter_requirements_for_persona method
"""

import unittest

from langfuse.decorators import observe

from engine.synthesis.text_extractor import TextExtractor


class TestFilterRequirementsForPersona(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        cls.logger_name = "test-text-extractor-logger"
        cls.extractor = TextExtractor(cls.logger_name)

    @observe()
    def test_01_requirements_json_contains_single_quotes_returns_valid_json(self):

        persona = "mortgage originator"
        # fmt: off
        extracted_requirements_info = {
            'Statements': [
                {'Statement_Text': "Reamortization using the promissory note interest rate may be authorized when RHS determines that reamortization is required to enable the borrower to meet scheduled obligations, and only if the Government's lien priority is not adversely affected.", 'Behavior_Required': 'Determine if reamortization is required to enable the borrower to meet scheduled obligations.', 'Whose_Behavior': 'RHS (Rural Housing Service)', 'Conditions_From_Header': "Reamortization must not adversely affect the Government's lien priority."
                },
                {'Statement_Text': 'Reamortization at the promissory note interest rate may be used to accomplish a variety of servicing actions, including to: (1) Repay unauthorized assistance due to inaccurate information.', 'Behavior_Required': 'Use reamortization to repay unauthorized assistance due to inaccurate information.', 'Whose_Behavior': 'Mortgage originator', 'Conditions_From_Header': 'None specified in this statement.'
                },
                {'Statement_Text': "(3) Bring current an account under a delinquency workout agreement after the borrower has demonstrated the willingness and ability to meet the terms of the loan and delinquency workout agreement and reamortization is in the borrower's and Government's best interests.", 'Behavior_Required': 'Bring current an account under a delinquency workout agreement.', 'Whose_Behavior': 'Mortgage originator', 'Conditions_From_Header': 'Borrower must demonstrate willingness and ability to meet terms, and reamortization must be in the best interests of both the borrower and the Government.'
                },
                {'Statement_Text': '(6) Bring an account current where the National Appeals Division (NAD) reverses an adverse action, the borrower has adequate repayment ability, and RHS determines the reamortization is in the best interests of the Government and the borrower.', 'Behavior_Required': 'Bring an account current after NAD reverses an adverse action.', 'Whose_Behavior': 'Mortgage originator', 'Conditions_From_Header': 'Borrower must have adequate repayment ability, and RHS must determine that reamortization is in the best interests of both the Government and the borrower.'
                },
                {'Statement_Text': 'The remaining balance will be reamortized for a period not to exceed 10 years or the final due date of the note being reamortized, whichever is sooner.', 'Behavior_Required': 'Reamortize the remaining balance for a specified period.', 'Whose_Behavior': 'Mortgage originator', 'Conditions_From_Header': 'The period must not exceed 10 years or the final due date of the note, whichever is sooner.'
                }
            ]       
        }
        # fmt: on
        requirements = self.extractor.filter_requirements_for_persona(
            extracted_requirements_info, persona
        )

        self.assertIsInstance(requirements, dict)

        self.assertIn("Statements", requirements)

        self.assertGreater(len(requirements["Statements"]), 0)

        first_statement = requirements["Statements"][0]
        self.assertIn("Statement_Text", first_statement)
        self.assertIn("Behavior_Required", first_statement)
        self.assertIn("Whose_Behavior", first_statement)
        self.assertIn("Conditions_From_Header", first_statement)

        self.assertTrue(first_statement["Statement_Text"])
        self.assertTrue(first_statement["Behavior_Required"])
        self.assertTrue(first_statement["Whose_Behavior"])
        self.assertTrue(first_statement["Conditions_From_Header"])


if __name__ == "__main__":
    unittest.main()
