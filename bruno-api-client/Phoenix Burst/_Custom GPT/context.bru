meta {
  name: context
  type: http
  seq: 3
}

post {
  url: {{URL}}/custom-gpt/context
  body: json
  auth: none
}

body:json {
  {
    "action": "search",
    "domain": "FNMA",
    "prompt": "Can you give me the source reference where the following statement is contained? A source could be a heading or other sort of indicator about where in the document the statement lies. 'Unless otherwise provided in the related servicing contract, every month, lenders deduct their servicing fee from the borrower’s interest payment before remitting the remainder to Fannie Mae. Lenders will receive a servicing fee from Fannie Mae only for the period during which it serviced the loan on <PERSON><PERSON> Mae’s behalf.'"
  }
}
