meta {
  name: storyboard prompt
  type: http
  seq: 1
}

post {
  url: {{URL}}/custom-gpt/storyboard-prompt
  body: json
  auth: none
}

body:json {
  {
    "characterName": "Farooq",
    "storyboardPrompt": "User Story Title: Register on the VCF website; receive a claim number.\n\nUser Story Description: As a potential VCF claimant, I want to register on the VCF website and receive a claim number so that I can officially start the compensation claim process.\n\nUser Story Acceptance Criteria: \n1. Given I am a potential claimant, when I complete the registration process on the VCF website, then I should receive a unique claim number confirming my registration.\n2. Given I am entering my personal information for registration, when I submit the form, then I should receive a confirmation email with my claim number and next steps.\n3. Given I need to correct information post-registration, when I update my details, then the system should confirm the updates and reflect them immediately in my profile."
  }
}
