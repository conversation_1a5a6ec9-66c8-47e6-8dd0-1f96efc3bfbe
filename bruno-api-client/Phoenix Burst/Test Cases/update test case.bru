meta {
  name: update test case
  type: http
  seq: 4
}

put {
  url: {{URL}}/test-case/1
  body: json
  auth: none
}

body:json {
  {
    "acceptance_criteria_id": 1,
    "description": "Test Case 1: Eligibility Identification\nObjective: Verify that the system correctly identifies loans eligible for the military indulgence based on predefined criteria.\n\nSteps:\n\nSet up the system with various loan accounts, including some that meet the military indulgence criteria and some that do not.\nTrigger the eligibility check process within the system.\nRecord which accounts are identified as eligible.\nExpected Result:\n\nThe system should correctly identify all accounts that meet the predefined criteria as eligible, without any false positives or negatives.\nTest Data:\n\nAccount A: Meets all criteria for military indulgence.\nAccount B: Does not meet criteria (e.g., service member status not confirmed).\nAccount C: Meets all criteria except the service start date falls outside the required range."
  }
}
