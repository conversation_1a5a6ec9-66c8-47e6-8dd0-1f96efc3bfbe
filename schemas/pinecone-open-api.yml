openapi: 3.0.2
info:
  title: Pinecone API
  description: Pinecone is a vector database. This is an unofficial, community-managed OpenAPI spec that (should) accurately model the Pinecone API. This project was developed independent of and is unaffiliated with Pinecone Systems. Users should switch to the official API spec, if and when Pinecone releases it.
  contact:
    name: <PERSON>
    url: https://github.com/sigpwned/pinecone-openapi-spec
  version: "20230406.1"
servers:
  - url: https://api.pinecone.io
    variables:
      environment:
        default: us-east1-gcp
        description: The regional deployment to use. Must match API key.
        enum:
          - us-west1-gcp
          - us-west4-gcp
          - us-central1-gcp
          - us-east1-gcp
          - us-east4-gcp
          - eu-west1-gcp
          - us-east1-aws
externalDocs:
  url: https://docs.pinecone.io/
  description: The official Pinecone API documentation
tags:
  - name: Index Operations
    description: Endpoints for manipulating indexes
  - name: Vector Operations
    description: Endpoints for manipulating vectors
paths:

  /indexes:
    get:
      tags:
        - Index Operations
      summary: List indexes
      description: This operation returns a list of your Pinecone indexes.
      operationId: list_indexes
      responses:
        200:
          description: This operation returns a list of all the indexes that you have previously created, and which are associated with the given API key
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IndexesList'

  /vectors/fetch:
    get:
      tags:
        - Vector Operations
      summary:
      description:
      operationId: fetch_vectors
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FetchRequest'
      responses:
        200
          description:
          content:
            application/json:
              schema:
                type: object

components:
  responses:
    BadRequest:
      description: Quota exceeded, or invalid parameters.
      content:
        text/plain:
          schema:
            $ref: '#/components/schemas/ErrorMessage'
    IndexNotFound:
      description: Index not found.
      content:
        text/plain:
          schema:
            $ref: '#/components/schemas/ErrorMessage'
    CollectionNotFound:
      description: Collection not found.
      content:
        text/plain:
          schema:
            $ref: '#/components/schemas/ErrorMessage'
    InternalError:
      description: Internal error. Can be caused by invalid parameters.
      content:
        text/plain:
          schema:
            $ref: '#/components/schemas/ErrorMessage'

  securitySchemes:
    ApiKey:
      type: apiKey
      in: header
      name: Api-Key

  schemas:
    Environment:
      type: string
      enum:
        - us-west1-gcp
        - us-west4-gcp
        - us-central1-gcp
        - us-east1-gcp
        - us-east4-gcp
        - eu-west1-gcp
        - us-east1-aws
              
    CollectionName:
      description: The unique name of a collection.
      type: string
      format: CollectionName
      nullable: false
      example: "example"
      
    CollectionState:
      description: The current status of a collection.
      type: string
      format: CollectionState
      nullable: false
      example: "Ready"
  
    CollectionDefinition:
      type: object
      required:
        - name
        - source
      properties:
        name:
          $ref: '#/components/schemas/CollectionName'
        source:
          $ref: '#/components/schemas/IndexName'

    Collection:
      type: object
      required:
        - name
        - size
        - status
      properties:
        name:
          $ref: '#/components/schemas/CollectionName'
        size:
          type: integer
          format: int64
          example: 3590359
        status:
          $ref: '#/components/schemas/CollectionState'
  
    CollectionsList:
      type: array
      items:
        $ref: '#/components/schemas/CollectionName'
        
    VectorDimensionality:
      type: integer
      format: int32
      description: The number of dimensions in the vector representation
      minimum: 1
      maximum: 20000
        
    PodType:
      type: string
      description: The pod type
      enum:
        - s1.x1
        - s1.x2
        - s1.x4
        - s1.x8
        - p1.x1
        - p1.x2
        - p1.x4
        - p1.x8
        - p2.x1
        - p2.x2
        - p2.x4
        - p2.x8
        
    IndexState:
      description: The current status of a index.
      type: string
      format: IndexState
      nullable: false
      example: "Ready"
      enum:
        - Initializing
        - ScalingUp
        - ScalingDown
        - Terminating
        - Ready
      
    IndexMetric:
      type: string
      description: The vector similarity metric of the index
      enum:
        - euclidean
        - cosine
        - dotproduct
        
    IndexName:
      description: The unique name of an index.
      type: string
      format: IndexName
      nullable: false
      minLength: 1
      maxLength: 45
      example: "example"
  
    IndexesList:
      type: array
      items:
        $ref: '#/components/schemas/IndexName'
        
    IndexMetadataConfig:
      type: object
      description: Configuration for the behavior of Pinecone's internal metadata index. By default, all metadata is indexed; when metadata_config is present, only specified metadata fields are indexed.
      properties:
        indexed:
          type: array
          minLength: 0
          items:
            type: string
          example:
            - "hello"
            
    IndexDefinition:
      type: object
      required:
        - name
        - dimension
      properties:
        name: 
          $ref: '#/components/schemas/IndexName'
        dimension:
          $ref: '#/components/schemas/VectorDimensionality'
        metric:
          $ref: '#/components/schemas/IndexMetric'
        pods:
          type: integer
          format: int32
          description: The number of pods for the index to use,including replicas.
          minimum: 1
          default: 1
        replicas:
          type: integer
          format: int32
          description: The number of replicas. Replicas duplicate your index. They provide higher availability and throughput.
          minimum: 1
          default: 1
        pod_type:
          $ref: '#/components/schemas/PodType'
        metadata_config:
          $ref: '#/components/schemas/IndexMetadataConfig'
        source_collection:
          $ref: '#/components/schemas/CollectionName'
          
    IndexConfiguration:
      type: object
      properties:
        replicas:
          description: The desired number of replicas for the index.
          type: integer
          format: int32
          minimum: 1
          default: 1
        pod_type:
          $ref: '#/components/schemas/PodType'
          
    IndexDatabase:
      type: object
      properties:
        name:
          $ref: '#/components/schemas/IndexName'
        metric:
          $ref: '#/components/schemas/IndexMetric'
        dimension:
          $ref: '#/components/schemas/VectorDimensionality'
        replicas:
          type: integer
          format: int32
          minimum: 1
        shards:
          type: integer
          format: int32
          minimum: 1
        pods:
          type: integer
          format: int32
          minimum: 1
        pod_type:
          $ref: '#/components/schemas/PodType'
      
    IndexStatus:
      type: object
      properties:
        host:
          type: string
          format: Hostname
        port:
          type: integer
          format: int32
        state:
          $ref: '#/components/schemas/IndexState'
        ready:
          type: boolean

    Index:
      type: object
      properties:
        database:
          $ref: '#/components/schemas/IndexDatabase'
        status:
          $ref: '#/components/schemas/IndexStatus'
          
    NamespaceName:
      type: string
      format: NamespaceName
      description: An index namespace name
      example: "namespace-0"
      
    VectorId:
      type: string
      format: VectorId
      description: The unique ID of a vector
      example: "vector-0"
          
    VectorFilter:
      type: object
      description: If this parameter is present, the operation only affects vectors that satisfy the filter. See https://www.pinecone.io/docs/metadata-filtering/.
      additionalProperties: true
      example:
        hello:
          - "alpha"
          - "bravo"
      
    VectorMetadata:
      type: object
      additionalProperties: true
      example:
        hello: "alpha"
      
    VectorData:
      type: array
      description: Vector dense data. This should be the same length as the dimension of the index being queried.
      items:
        type: number
        format: float
      example:
        - 1.0
        - 2.0
        - 3.0
        
    SparseVectorData:
      type: object
      description: Vector sparse data. Represented as a list of indices and a list of corresponded values, which must be the same length.
      required:
        - indices
        - values
      properties:
        indices:
          description: The indices of the sparse data.
          type: array
          items:
            type: integer
            format: int64
          example:
            - 1
        values:
          description: The corresponding values of the sparse data, which must be the same length as the indices.
          type: array
          items:
            type: number
            format: float
          example:
            - 2
          
    DescribeIndexStatsRequest:
      type: object
      properties:
        filter:
          $ref: '#/components/schemas/VectorFilter'
          
    IndexNamespaceStats:
      type: object
      properties:
        vectorCount:
          type: integer
          format: int64
          
    DescribeIndexStatsResponse:
      type: object
      properties:
        namespaces:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/IndexNamespaceStats'
        dimension:
          $ref: '#/components/schemas/VectorDimensionality'
        indexFullness:
          type: number
          format: float
          description: The fullness of the index, regardless of whether a metadata filter expression was passed. The granularity of this metric is 10%.
        totalVectorCount:
          type: integer
          format: int64
          
    QueryRequest:
      type: object
      required:
        - topK
      properties:
        namespace:
          $ref: '#/components/schemas/NamespaceName'
        topK:
          type: integer
          format: int64
          default: 100
          minimum: 0
          maximum: 10000
          description: The number of results to return for each query.
        filter:
          $ref: '#/components/schemas/VectorFilter'
        includeValues:
          type: boolean
          default: false
        includeMetadata:
          type: boolean
          default: false
        vector:
          $ref: '#/components/schemas/VectorData'
        sparseVector:
          $ref: '#/components/schemas/SparseVectorData'
        id:
          $ref: '#/components/schemas/VectorId'
          
    QueryMatch:
      type: object
      required:
        - id
      properties:
        id:
          $ref: '#/components/schemas/VectorId'
        score:
          type: number
          format: float
        values:
          $ref: '#/components/schemas/VectorData'
        sparseValues:
          $ref: '#/components/schemas/SparseVectorData'
        metadata:
          $ref: '#/components/schemas/VectorMetadata'
          
    QueryResponse:
      type: object
      properties:
        matches:
          type: array
          items:
            $ref: '#/components/schemas/QueryMatch'
        namespace:
          $ref: '#/components/schemas/NamespaceName'
          
    DeleteRequest:
      type: object
      properties:
        ids:
          type: array
          maxLength: 1000
          items:
            $ref: '#/components/schemas/VectorId'
        deleteAll:
          type: boolean
          default: false
        namespace:
          $ref: '#/components/schemas/NamespaceName'
        filter:
          $ref: '#/components/schemas/VectorFilter'
          
    DeleteResponse:
      type: object
      properties: {}
      
    FetchRequest:
      type: object
      required:
        - ids
      properties:
        ids:
          type: array
          maxLength: 1000
          items:
            $ref: '#/components/schemas/VectorId'
        namespace:
          $ref: '#/components/schemas/NamespaceName'
          
    FetchResponse:
      type: object
      properties:
        vectors:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/VectorData'
        namespace:
          $ref: '#/components/schemas/NamespaceName'
          
    UpdateRequest:
      type: object
      required:
        - id
      properties:
        id:
          type: string
          format: VectorId
          description: The vector's unique ID
        values:
          $ref: '#/components/schemas/VectorData'
        sparseValues:
          $ref: '#/components/schemas/SparseVectorData'
        setMetadata:
          $ref: '#/components/schemas/VectorMetadata'
        namespace:
          $ref: '#/components/schemas/NamespaceName'
          
    UpdateResponse:
      type: object
      properties: {}
      
    UpsertVector:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/VectorId'
        values:
          $ref: '#/components/schemas/VectorData'
        sparseValues:
          $ref: '#/components/schemas/SparseVectorData'
        metadata:
          $ref: '#/components/schemas/VectorMetadata'
      
    UpsertRequest:
      type: object
      required:
        - vectors
      properties:
        vectors:
          type: array
          maxLength: 100
          items:
            $ref: '#/components/schemas/UpsertVector'
        namespace:
          $ref: '#/components/schemas/NamespaceName'
          
    UpsertResponse:
      type: object
      properties:
        upsertedCount:
          type: integer
          format: int64
  
    ErrorMessage:
      type: string
      
security:
  - ApiKey: []