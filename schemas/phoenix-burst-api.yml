openapi: 3.0.0
info:
  title: Phoenix Burst API
  version: 1.0.0
  description: API for text embedding and retrieval using Pinecone and OpenAI.
servers:
  - url: https://api.phoenixburst.com
paths:
  /domains:
    get:
      summary: Retrieve domains from the list in configuration
      description: Retrieve domains from the list in configuration
      operationId: domains
      responses:
        "200":
          description: An array of matches similar to the input text
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DomainResponse"
        "400":
          description: Bad request when no text is provided.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "500":
          description: Server error when embedding generation fails.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /context:
    post:
      summary: Retrieve context from a Pinecone vector database based on input parameters
      description: Retrieve context from a Pinecone vector database based on input parameters
      operationId: context
      requestBody:
        description: Query the Pinecone vector database
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/QueryRequest"
      responses:
        "200":
          description: An array of context matches similar
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ContextResponse"
        "400":
          description: Bad request when no text is provided.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "500":
          description: Server error when embedding generation fails.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /storyboard-prompt:
    post:
      summary: Returns a generated image from Dall-E using user story requirements as a parameter
      description: Returns a generated image from Dall-E
      operationId: storyboardPrompt
      requestBody:
        description: Type of action
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/StoryboardImageRequest"
      responses:
        "200":
          description: A URL of a generated image
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/StoryboardImageResponse"
        "404":
          description: Not found, if the image cannot be found.
  /compare-openai-claude:
    post:
      summary: Retrieve responses from both openai and claude
      description: Retrieve responses from both openai and claude
      operationId: compare-openai-claude
      requestBody:
        description: Prompt
        content:
          application/json:
            schema:
              type: object
              properties:
                prompt:
                  type: string
      responses:
        "200":
          description: A comparison of openai vs claude
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DomainResponse"
        "400":
          description: Bad request when no text is provided.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        "500":
          description: Server error when embedding generation fails.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
components:
  schemas:
    QueryRequest:
      type: object
      properties:
        action:
          type: string
          description: Type of action
          example: search
          enum:
            - search
            - training
            - storyBoard
            - userStoriesAcceptanceCriteria
        domain:
          type: string
          description: The domain(s) to include in the search
          example: VCF
          enum:
            - ALL
            - MORTGAGE
            - FNMA
            - VA
            - REGX_RESPA
            - FHA
            - USDA_RURAL_HOUSING
            - VA_MORTGAGE_SERVICING
            - MISMO
            - VCF
            - VCFPROPOSAL
        prompt:
          type: string
          description: The text content that is created and passed in by the user
    QueryResponse:
      type: array
      items:
        $ref: "#/components/schemas/Match"
    ContextResponse:
      type: array
      items:
        type: string
    StoryboardImageRequest:
      type: object
      properties:
        storyboardPrompt:
          type: string
          description: The text content that is created and passed in by the user
        characterName:
          type: string
          description: The character chosen for image
    StoryboardImageResponse:
      type: object
      properties:
        calculated:
          type: object
          description: The text content that is created and passed in by the user
        constants:
          type: object
          description: The character chosen for image
        created:
          type: number
          description: The character chosen for image
        data:
          type: object
          description: The character chosen for image
        parameters:
          type: object
          description: The character chosen for image
    Match:
      type: object
      properties:
        _node_content:
          type: string
        _node_type:
          type: string
        creation_date:
          type: string
        doc_id:
          type: string
        document_id:
          type: string
        file_name:
          type: string
        file_path:
          type: string
        file_size:
          type: number
        file_type:
          type: string
        last_modified_date:
          type: string
        page_label:
          type: string
        ref_doc_id:
          type: string
    Error:
      type: object
      properties:
        error:
          type: string
    DomainResponse:
      type: array
      items:
        $ref: "#/components/schemas/Domain"
    Domain:
      type: object
      properties:
        displayName:
          type: string
          description: The human readable text description that will be shown to users
        domain:
          type: string
          description: The enum that is passed into the context endpoint
    ComparisonResponse:
      type: object
      properties:
        openAIResponse:
          type: string
          description: The human readable text description that will be shown to users
        claudeResponse:
          type: string
          description: The enum that is passed into the context endpoint
