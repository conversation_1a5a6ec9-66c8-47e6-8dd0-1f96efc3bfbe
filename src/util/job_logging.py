from datetime import datetime
from typing import Dict, Any

from shared.models import Job
from util.database import Session
from util.logging_config import EnhancedLogger

class JobLogger:
    """Utility class for job-related logging operations."""

    def __init__(self, logger: EnhancedLogger):
        self.logger = logger

    def get_job_details(self, job_id: int) -> Dict[str, Any]:
        """
        Retrieves detailed information about a job for logging purposes.

        Args:
            job_id (int): The unique identifier of the job.

        Returns:
            Dict[str, Any]: A dictionary containing job details including:
                - job_type_id: The type of the job
                - document_id: The associated document ID
                - job_created_at: When the job was created
                - job_failed_at: When the job failed (current timestamp)
        """
        session = Session()
        try:
            job = session.query(Job).filter(Job.job_id == job_id).one()
            return {
                "job_type_id": job.job_type_id,
                "document_id": job.reference_id,
                "job_created_at": str(job.created_at),
                "job_failed_at": str(datetime.now())
            }
        finally:
            session.close()

    def log_job_error(self, job_id: int, exc_info: Exception = None):
        """
        Logs detailed information about a failed job.

        Args:
            job_id (int): The unique identifier of the job.
            exc_info (Exception, optional): Exception information if available.
        """
        try:
            job_details = self.get_job_details(job_id)

            # Get the original error message if available
            original_error = ""
            if exc_info:
                original_error = str(exc_info)
                # If it's a ValueError, try to get just the message part
                if isinstance(exc_info, ValueError):
                    original_error = original_error.split('\n')[0]

            # Build the error message with all details in one line
            error_message = (
                f"Job {job_id} failed - Type: {job_details['job_type_id']}, "
                f"Document: {job_details['document_id']}"
            )

            # Add the original error message if available
            if original_error:
                error_message += f" - {original_error}"

            # Log only once with all details
            self.logger.error(
                error_message,
                job_id=job_id,
                job_type_id=job_details['job_type_id'],
                document_id=job_details['document_id'],
                job_created_at=job_details['job_created_at'],
                job_failed_at=job_details['job_failed_at'],
                original_error=original_error,
                exc_info=exc_info
            )
        except Exception as e:
            self.logger.error(
                f"Failed to log job error details for job {job_id}",
                job_id=job_id,
                exc_info=e
            )
            raise
