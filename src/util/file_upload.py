import os

JOB_TYPE_ALLOWED_EXTENSIONS = {
    1: {"pdf"},  # process document
    2: {"pdf"},  # domain comparison
    3: {"pdf"},  # procedure import
    4: {"pdf"},  # artifact generation
    5: {"pdf"},  # markup change identification
    6: {"pdf"},  # policy requirement comparison
    7: {"pdf"},  # federal register change identification
    8: {"docx"},  # optimize procedure
    9: {"pdf"},  # version comparison
}


def is_extension_allowed(filename: str, job_type_id: int) -> bool:
    ext = get_file_extension(filename)

    if job_type_id not in JOB_TYPE_ALLOWED_EXTENSIONS:
        raise ValueError(f"Unknown job_type_id: {job_type_id}")

    return ext in JOB_TYPE_ALLOWED_EXTENSIONS.get(job_type_id, set())


def get_file_extension(filename):
    return os.path.splitext(filename)[1].lower()[1:]  # [1:] to remove the dot
