SCOPE_MAP = {
    # DELETE
    ("DELETE", "/job/<int:job_id>"): [
        "delete:job_from_dashboard",
        "delete:job_from_curated_artifacts",
        "delete:job_from_procedure_library",
    ],

    # GET
    ("GET", "/businesses"): ["read:procedure_library_tab"],

    ("GET", "/chunk/<int:chunk_id>"): [
        "read:subtopic_curator_screen",
        "read:statement_curator_screen",
        "read:change_statement_curator_screen",
    ],

    ("GET", "/chunks/document/<int:document_id>"): [
        "read:subtopic_curator_screen",
        "read:statement_curator_screen",
        "read:change_statement_curator_screen",
        "read:requirement_curator_screen",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    ("GET", "/document/<int:document_id>"): [
        "read:curated_artifacts_tab",
        "read:procedure_library_tab",
        "read:readability_score",
        "read:subtopic_curator_screen",
        "read:statement_curator_screen",
        "read:change_statement_curator_screen",
        "read:requirement_curator_screen",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    ("GET", "/document/<int:document_id>/statistics"): [
        "read:curated_artifacts_tab",
        "read:procedure_library_tab",
        "read:readability_score",
    ],

    ("GET", "/domains"): ["read:procedure_library_tab"],

    ("GET", "/images/<int:image_id>"): [
        "read:statement_curator_screen",
        "read:change_statement_curator_screen",
    ],

    ("GET", "/statements/chunk/<int:chunk_id>"): [
        "read:subtopic_curator_screen",
        "read:statement_curator_screen",
        "read:change_statement_curator_screen",
    ],

    ("GET", "/subtopics"): [
        "update:subtopic_association",
        "read:requirement_curator_screen",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    ("GET", "/subtopics/business/<int:business_id>"): ["update:subtopic_association"],

    ("GET", "/subtopics/document/<int:document_id>"): [
        "read:requirement_curator_screen",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    # POST
    ("POST", "/acceptance-criteria/<int:acceptance_criteria_id>/generate-test-cases"): [
        "create:individual_tc",
    ],

    ("POST", "/change-statements/generate-from-citation-api"): [
        "create:identify_changes_from_federal_register_job",
    ],

    ("POST", "/chunk/<int:chunk_id>/subtopics"): ["update:subtopic_association"],

    ("POST", "/chunks/regenerate-statements"): ["create:regenerate_statements"],

    ("POST", "/document/generate-acceptance-criteria"): ["create:bulk_ac"],

    ("POST", "/document/generate-test-cases"): ["create:bulk_tc"],

    ("POST", "/document/generate-unique-requirements"): [
        "create:requirements_from_statements",
        "create:requirements_from_change_statements",
    ],

    ("POST", "/document/generate-user-stories"): ["create:user_stories"],

    ("POST", "/file/change-statement-curator-excel-export"): [
        "create:export_change_statements_to_excel",
    ],

    ("POST", "/file/excel-export"): [
        "create:export_requirements_to_excel",
        "create:export_artifacts_to_excel",
    ],

    ("POST", "/file/requirements-explorer-excel-export"): [
        "create:export_curated_artifacts",
    ],

    ("POST", "/file/upload-file"): [
        "create:artifact_job",
        "create:procedure_optimizer_job",
        "create:new_procedure_job",
        "create:identify_changes_from_markup_job",
        "create:identify_changes_from_pdf_comparison_job",
    ],

    ("POST", "/jobs/artifact-curator/<int:job_id>"): [
        "update:artifact",
        "update:rate_artifact",
    ],

    ("POST", "/jobs/filter"): [
        "read:jobs_tab",
        "read:curated_artifacts_tab",
        "read:procedure_library_tab",
        "read:subtopic_curator_screen",
        "read:statement_curator_screen",
        "read:change_statement_curator_screen",
        "read:requirement_curator_screen",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    ("POST", "/statement"): [
        "create:new_statement",
        "create:new_change_statement",
    ],

    ("POST", "/unique-statement"): [
        "read:requirement_curator_screen",
        "create:new_requirement",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    ("POST", "/unique-statements/filter"): [
        "read:requirement_curator_screen",
        "read:artifact_curator_screen",
        "read:artifact_viewer_screen",
    ],

    ("POST", "/user-story/<int:user_story_id>/generate-acceptance-criteria"): [
        "create:individual_ac",
    ],

    # PUT
    ("PUT", "/acceptance-criteria/<int:acceptance_criteria_id>"): [
        "update:artifact",
        "update:rate_artifact",
    ],

    ("PUT", "/job/<int:job_id>"): [
        "update:curation_complete_from_requirements",
        "update:curation_complete_from_artifacts",
        "update:continue_curation",
    ],

    ("PUT", "/statement/<int:statement_id>"): [
        "update:statement",
        "update:rate_statement",
        "update:change_statement",
        "update:rate_change_statement",
        "update:rate_subtopic",
    ],

    ("PUT", "/test-case/<int:test_case_id>"): [
        "update:artifact",
        "update:rate_artifact",
    ],

    ("PUT", "/unique-statement/<int:unique_statement_id>"): [
        "update:requirement",
        "update:rate_requirement",
        "update:artifact",
        "update:rate_artifact",
    ],
}
