from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from util.config import (
    AWS_POSTGRES_USERNAME,
    AWS_POSTGRES_PASSWORD,
    AWS_POSTGRES_HOST,
    AWS_POSTGRES_PORT,
    AWS_POSTGRES_DATABASE,
)

# SQLAlchemy engine
engine = create_engine(
    f"postgresql+psycopg2://{AWS_POSTGRES_USERNAME}:{AWS_POSTGRES_PASSWORD}@{AWS_POSTGRES_HOST}:\
  {AWS_POSTGRES_PORT}/{AWS_POSTGRES_DATABASE}",
    pool_size=20,  # Increase pool size
    max_overflow=30,  # Allow additional overflow connections
    pool_timeout=30,  # Timeout for getting a connection from the pool
    pool_recycle=1800,  # Recycle connections after 1800 seconds (30 minutes)
)

Session = sessionmaker(bind=engine)
