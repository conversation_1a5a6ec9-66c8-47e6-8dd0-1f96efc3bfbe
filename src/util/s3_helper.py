"""
s3_helper.py

This module provides the S3Helper class to facilitate interaction with Amazon S3.
It includes methods to retrieve and upload documents to an S3 bucket, using
AWS access credentials and the boto3 library.
"""

import boto3


class S3Helper:
    """
    A helper class for interacting with Amazon S3. Provides methods for retrieving
    and uploading documents in an S3 bucket.

    Attributes:
        s3_client (boto3.client): The boto3 S3 client used for S3 operations.
        bucket_name (str, optional): The default name of the S3 bucket.
    """

    def __init__(
        self, aws_access_key: str, aws_secret_key: str, region: str, bucket_name: str = None
    ):
        """
        Initializes the S3Helper with AWS credentials and optional bucket configuration.

        Parameters:
            aws_access_key (str): The AWS access key ID.
            aws_secret_key (str): The AWS secret access key.
            region (str): The AWS region where the S3 bucket is located.
            bucket_name (str, optional): The default name of the S3 bucket.
                                         Can be specified later in individual methods.
        """
        self.s3_client = boto3.client(
            "s3",
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key,
            region_name=region,
        )
        self.bucket_name = bucket_name

    def get_document_from_s3(self, s3_location: str, bucket_name: str = None) -> bytes:
        """
        Retrieves a document from S3 given its S3 URI location, with an option
        to specify a different bucket.

        Parameters:
            s3_location (str): The full S3 URI of the document
              (e.g., "s3://bucket_name/path/to/file").
            bucket_name (str, optional): The name of the S3 bucket.
                                         Defaults to the instance's bucket_name if provided.

        Returns:
            bytes: The content of the document in bytes.

        Raises:
            RuntimeError: If the document retrieval fails.
        """
        if s3_location.startswith("s3://"):
            parsed_bucket_name, document_key = s3_location[5:].split("/", 1)
        else:
            parsed_bucket_name = bucket_name or self.bucket_name
            document_key = s3_location

        if not parsed_bucket_name:
            raise ValueError(
                "Bucket name must be provided either in the s3_location or as a parameter."
            )

        try:
            s3_response = self.s3_client.get_object(Bucket=parsed_bucket_name, Key=document_key)
            document_bytes = s3_response["Body"].read()
            return document_bytes
        except Exception as e:
            raise RuntimeError(f"Failed to retrieve document from S3: {str(e)}") from e

    def upload_document_to_s3(
        self, file_content: bytes, s3_key: str, bucket_name: str = None
    ) -> None:
        """
        Uploads a document to S3, with an option to specify a different bucket.

        Parameters:
            file_content (bytes): The content of the file to upload.
            s3_key (str): The S3 key (path) where the file will be stored.
            bucket_name (str, optional): The name of the S3 bucket.
                                         Defaults to the instance's bucket_name if provided.

        Raises:
            RuntimeError: If the upload fails.
        """
        target_bucket = bucket_name if bucket_name else self.bucket_name
        if not target_bucket:
            raise ValueError(
                "Bucket name must be provided either in the method or during initialization."
            )

        try:
            self.s3_client.put_object(Bucket=target_bucket, Key=s3_key, Body=file_content)
        except Exception as e:
            raise RuntimeError(f"Failed to upload document to S3: {str(e)}") from e

    def delete_document_from_s3(self, s3_key: str, bucket_name: str = None) -> None:
        """
        Deletes a document from S3.

        Parameters:
            s3_key (str): The S3 key (path) of the document to delete.
            bucket_name (str, optional): The name of the S3 bucket.
                                         Defaults to the instance's bucket_name if provided.

        Raises:
            RuntimeError: If the deletion fails.
        """
        target_bucket = bucket_name if bucket_name else self.bucket_name
        if not target_bucket:
            raise ValueError(
                "Bucket name must be provided either in the method or during initialization."
            )

        try:
            self.s3_client.delete_object(Bucket=target_bucket, Key=s3_key)
        except Exception as e:
            raise RuntimeError(f"Failed to delete document from S3: {str(e)}") from e

    def generate_presigned_url(self, s3_key: str, expires_in: int = 300, bucket_name: str = None) -> str:
        target_bucket = bucket_name if bucket_name else self.bucket_name
        if not target_bucket:
            raise ValueError("Bucket name must be provided either in the method or during initialization.")
        try:
            return self.s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": target_bucket, "Key": s3_key},
                ExpiresIn=expires_in,
            )
        except Exception as e:
            raise RuntimeError(f"Failed to generate pre-signed URL: {str(e)}") from e
