import logging
from typing import Union

from util.audit.dto import AuditActivityInsertDTO
from shared.models import AuditActivity
from util.audit.exceptions import AuditActivitySaveException
from util.audit.user_ctx import get_user_ctx

logger = logging.getLogger(__name__)


class Auditor:
    def __init__(self, *, session_factory):
        self.session_factory = session_factory

    def record(
        self,
        dto: AuditActivityInsertDTO,
    ) -> None:
        """
        Records an audit activity.

        Args:
            audit_object_type_id (int): ID representing the object type being audited.
            object_key (Union[int, str]): Unique ID of the audited object.
            snapshot (dict): Snapshot of the object after the change.
            audit_operation_type_id (int): ID representing the audit operation type.
        """

        user = get_user_ctx()

        email = getattr(user, "email", None) or getattr(user, "email_address", "unknown")

        logger.info(
            f"Recording audit activity | object={dto.object_key} | operation={dto.operation_type} | user={email}"
        )

        entry = AuditActivity(
            audit_object_type_id=dto.object_type.value,
            object_key=dto.object_key,  # or str if DB schema allows
            data_snapshot=dto.snapshot,
            user_id=user.user_id,
            audit_operation_type_id=dto.operation_type.value,
        )

        self._save_audit_activity(entry)

    def _save_audit_activity(self, new_audit_activity: AuditActivity) -> None:
        with self.session_factory() as session:
            try:

                session.add(new_audit_activity)
                session.commit()

            except Exception as e:
                session.rollback()
                raise AuditActivitySaveException(f"Error saving audit activity: {str(e)}") from e
