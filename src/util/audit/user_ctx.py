import logging
from dataclasses import dataclass
from typing import Optional
from contextvars import ContextVar

# Configure logger for this module
logger = logging.getLogger(__name__)


@dataclass
class UserCtx:
    auth0_id: Optional[str]
    user_id: Optional[int]
    email: Optional[str]
    name: Optional[str]
    enterprise_id: Optional[int]
    domain_id: Optional[int]


# Holds the current request's user context
_current_user_ctx: ContextVar[Optional[UserCtx]] = ContextVar("_current_user_ctx", default=None)


def set_user_ctx(ctx: UserCtx):
    logger.debug(f"Setting user context: id={ctx.auth0_id}, email={ctx.email}, name={ctx.name}")
    _current_user_ctx.set(ctx)


def get_user_ctx() -> Optional[UserCtx]:
    ctx = _current_user_ctx.get()
    if ctx:
        logger.debug(
            f"Retrieved user context: id={ctx.auth0_id}, email={ctx.email}, name={ctx.name}"
        )
    else:
        logger.debug("No user context set")
    return ctx


def clear_user_ctx():
    logger.debug("Clearing user context")
    _current_user_ctx.set(None)
