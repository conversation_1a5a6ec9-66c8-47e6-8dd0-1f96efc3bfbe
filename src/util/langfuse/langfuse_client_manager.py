"""
This module provides a singleton class `LangfuseClientManager` for managing the Langfuse client
instance.

The `LangfuseClientManager` ensures that only one instance of the Langfuse client is created and 
provides a thread-safe way to access it. The client instance is initialized with environment 
variables for the public key, 

Classes:
    LangfuseClientManager: A singleton class responsible for managing the Langfuse client instance.
"""

import os
import threading

from langfuse import Langfuse


class LangfuseClientManager:
    """
    LangfuseClientManager is a singleton class responsible for managing the Langfuse client
    instance.

    This class ensures that only one instance of the Langfuse client is created and provides
    a thread-safe way to access it. The client instance is initialized with environment
    variables for the public key, secret key, host, and timeout.

    Methods:
        get_instance(): Returns the singleton instance of the Langfuse client. If the
                        instance does not exist, it initializes it using environment variables.

    Raises:
        EnvironmentError: If any of the required environment variables (LANGFUSE_SECRET_KEY,
                        LANGFUSE_PUBLIC_KEY, LANGFUSE_HOST) are missing.
    """

    _instance = None
    _lock = threading.Lock()

    @classmethod
    def get_instance(cls) -> Langfuse:
        """
        Retrieves the singleton instance of the Langfuse client.
        This method ensures that only one instance of the Langfuse client is created
        and reused throughout the application. It initializes the instance with
        environment variables for the secret key, public key, and host if it does not
        already exist.
        Returns:
            Langfuse: The singleton instance of the Langfuse client.
        Raises:
            EnvironmentError: If any of the required Langfuse environment variables
                              (LANGFUSE_SECRET_KEY, LANGFUSE_PUBLIC_KEY, LANGFUSE_HOST)
                              are missing.
        """
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    langfuse_secret_key = os.getenv("LANGFUSE_SECRET_KEY")
                    langfuse_public_key = os.getenv("LANGFUSE_PUBLIC_KEY")
                    langfuse_host = os.getenv("LANGFUSE_HOST")

                    if not all([langfuse_secret_key, langfuse_public_key, langfuse_host]):
                        raise EnvironmentError("Missing required Langfuse environment variables.")

                    cls._instance = Langfuse(
                        public_key=langfuse_public_key,
                        secret_key=langfuse_secret_key,
                        host=langfuse_host,
                        timeout=10,
                    )
        return cls._instance
