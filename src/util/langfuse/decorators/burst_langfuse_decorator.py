"""
This module contains the BurstLangfuseDecorators class which provides
decorators for observing functions with Lang<PERSON>.
"""

import inspect
import logging
import uuid
from functools import wraps
from typing import Optional

from langfuse.decorators import langfuse_context

from util.config import ENVIRONMENT_NAME


class BurstLangfuseDecorator:
    """
    A class to provide decorators for observing functions with Lang<PERSON>.

    Attributes:
        _log (logging.Logger): Logger for the class.
    """

    _log = logging.getLogger("phoenix-burst")

    def trace_context(self, param_names: Optional[list[str]] = None):
        """
        Decorator to add additional context to langfuse trace.

        Args:
            param_names (Optional[list[str]]): A list of parameter names to observe.

        Returns:
            function: The wrapped function with observation.
        """

        def decorator(fn):
            @wraps(fn)
            def wrapper(*args, **kwargs):
                session_id_parts = []
                if param_names:
                    signature = inspect.signature(fn)
                    bound_args = signature.bind(*args, **kwargs)
                    bound_args.apply_defaults()

                    for param_name in param_names:
                        param_value = bound_args.arguments.get(param_name)
                        if param_value is None:
                            # Check args for an attribute match if param_name is not found directly
                            for arg in args:
                                if hasattr(arg, param_name):
                                    param_value = getattr(arg, param_name, None)
                                    break

                        if param_value is not None:
                            session_id_parts.append(f"{param_name}-{param_value}")

                    if session_id_parts:
                        session_uuid = uuid.uuid4()
                        session_id = f"{'-'.join(session_id_parts)}-{session_uuid}"
                        self._log.debug("Generated session_id: %s", session_id)
                    else:
                        session_id = None
                else:
                    session_id = None
                    self._log.debug("No param_names provided, session_id will not be set.")

                tags = [ENVIRONMENT_NAME]
                self._log.debug("Adding tags: %s", tags)

                langfuse_context.update_current_trace(session_id=session_id, tags=tags)
                self._log.debug(
                    "Updated langfuse context with session_id: %s and tags: %s", session_id, tags
                )

                return fn(*args, **kwargs)

            return wrapper

        return decorator


# Example usage
burst_langfuse_decorators = BurstLangfuseDecorator()
trace_context = burst_langfuse_decorators.trace_context
