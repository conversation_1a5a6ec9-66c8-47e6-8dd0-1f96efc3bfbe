"""
This module provides helper functions for interacting with the Langfuse API.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Tuple, TypeVar, Union

import requests
from langfuse import Langfuse
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from util.config import (
    ENVIRONMENT_NAME,
    LANGFUSE_HOST,
    LANGFUSE_PUBLIC_KEY,
    LANGFUSE_SECRET_KEY,
)
from util.langfuse.langfuse_client_manager import LangfuseClientManager

# Type definitions for better type checking
PromptT = TypeVar('PromptT')
FallbackPromptT = TypeVar('FallbackPromptT')

# Default model configuration
MODEL_DEFAULT = "gpt-4o-mini"

class BurstLangfuseHelpers:
    """
    Helper class for interacting with the Langfuse API.
    Provides zero-latency prompt management with background refresh capabilities.
    """

    # Langfuse API Constants
    LANGFUSE_PROMPTS_ENDPOINT = LANGFUSE_HOST + "/api/public/v2/prompts"
    DEFAULT_TIMEOUT = 5  # Timeout in seconds
    DEFAULT_LIMIT = 100  # Number of items per page
    DEFAULT_CACHE_TTL = 60  # Default cache TTL in seconds
    VALID_LABELS = {"dev", "sit", "client-demo", "demo", "prod", "latest"}

    _log = logging.getLogger("phoenix-burst")
    _shared_session: Optional[requests.Session] = None
    _prompt_cache: Dict[str, Tuple[Any, float]] = {}  # Our own cache dictionary
    _refresh_tasks: Dict[str, asyncio.Task] = {}  # Background refresh tasks

    def __init__(self, session: Optional[requests.Session] = None, default_cache_ttl: int = DEFAULT_CACHE_TTL):
        """
        Initialize the BurstLangfuseHelpers.

        Args:
            session: Optional requests.Session to use for HTTP requests
            default_cache_ttl: Default cache TTL in seconds (default: 60)
        """
        self.langfuse_client: Langfuse = LangfuseClientManager.get_instance()
        # KA:Choosing not to extract methods or variables, or inject due to simplicity and a
        #   very low rate of anticipated change to this code.
        self.default_cache_ttl = max(0, default_cache_ttl)  # Ensure non-negative TTL
        if session is None:
            if BurstLangfuseHelpers._shared_session is None:
                session = requests.Session()
                retries = Retry(
                    total=3,
                    backoff_factor=0.3,
                    status_forcelist=[500, 502, 503, 504],
                    allowed_methods=["GET"],
                )
                adapter = HTTPAdapter(max_retries=retries)
                session.mount("https://", adapter)
                BurstLangfuseHelpers._shared_session = session
            self.session = BurstLangfuseHelpers._shared_session
        else:
            self.session = session

    def force_cache_invalidation(self, prompt_name: Optional[str] = None) -> None:
        """
        Forces cache invalidation for the current process.
        If prompt_name is provided, only invalidates that specific prompt's cache.
        Otherwise, invalidates the entire cache.

        Args:
            prompt_name: Optional name of the prompt to invalidate. If None, invalidate all prompts.
        """
        try:
            if prompt_name is not None:
                # Invalidate specific prompt for all labels
                keys_to_remove = [
                    key for key in self._prompt_cache if key.startswith(f"{prompt_name}:")
                ]
                for key in keys_to_remove:
                    del self._prompt_cache[key]
                    # Cancel any pending refresh tasks for this prompt
                    if key in self._refresh_tasks and not self._refresh_tasks[key].done():
                        self._refresh_tasks[key].cancel()
                        del self._refresh_tasks[key]
                self._log.info("Forced cache invalidation for prompt '%s' (cleared %d items)",
                             prompt_name, len(keys_to_remove))
            else:
                # Invalidate entire cache
                cache_size = len(BurstLangfuseHelpers._prompt_cache)
                # Clear the entire cache
                BurstLangfuseHelpers._prompt_cache.clear()
                # Cancel any pending refresh tasks
                for task in BurstLangfuseHelpers._refresh_tasks.values():
                    if not task.done():
                        task.cancel()
                BurstLangfuseHelpers._refresh_tasks.clear()
                # Force a new session to ensure no connection pooling issues
                if BurstLangfuseHelpers._shared_session:
                    BurstLangfuseHelpers._shared_session.close()
                    BurstLangfuseHelpers._shared_session = None
                self._log.info("Forced complete cache invalidation (cleared %d items)", cache_size)
        except (KeyError, RuntimeError, asyncio.CancelledError) as e:
            self._log.error("Error during cache invalidation: %s", str(e))
            raise RuntimeError(f"Failed to invalidate cache: {str(e)}") from e

    def _start_background_refresh(self, prompt_name: str, label: str) -> None:
        """
        Start a background refresh task for the prompt.

        Args:
            prompt_name: Name of the prompt to refresh
            label: Environment label for the prompt
        """
        cache_key = f"{prompt_name}:{label}"
        if cache_key in self._refresh_tasks and not self._refresh_tasks[cache_key].done():
            return

        try:
            self._log.info(
                "Starting background refresh for prompt '%s' with label '%s'",
                prompt_name, label
            )
            # Get a fresh client instance
            self.langfuse_client = LangfuseClientManager.get_instance()
            current_prompt = self.langfuse_client.get_prompt(prompt_name, label=label)
            current_time = time.time()

            # Update cache with new version
            BurstLangfuseHelpers._prompt_cache[cache_key] = (current_prompt, current_time)
            self._log.info("Background refresh completed for '%s' (version %s)",
                         prompt_name, current_prompt.version)
        except (requests.RequestException, ValueError) as e:
            self._log.error("Error during background refresh for '%s': %s", prompt_name, e)
        finally:
            # Clean up the task reference
            if cache_key in self._refresh_tasks:
                del self._refresh_tasks[cache_key]

    def fetch_langfuse_prompt(self, prompt_name: str, cache_ttl_seconds: Optional[int] = None,
                            fallback: Optional[Union[str, List[Dict[str, str]]]] = None,
                            **variables) -> Tuple[Any, Optional[str]]:
        """
        Fetches the specified prompt content from Langfuse API using the Langfuse SDK.
        Implements zero-latency access with background refresh for stale cache entries.

        Args:
            prompt_name: The name of the prompt to retrieve
            cache_ttl_seconds: Custom cache TTL in seconds. If None, uses default
            fallback: Fallback prompt to use if fetch fails
            **variables: Variables to compile into the prompt template

        Returns:
            Tuple containing:
            - The raw prompt object
            - The compiled prompt content (if variables provided) or None

        Raises:
            ValueError: If the prompt content is empty or not found
            RuntimeError: If there is an issue with the Langfuse SDK call
        """
        if not prompt_name:
            raise ValueError("Prompt name cannot be empty")

        # Ensure label is valid; otherwise, default to "latest"
        label = ENVIRONMENT_NAME if ENVIRONMENT_NAME in self.VALID_LABELS else "latest"

        # Create cache key
        cache_key = f"{prompt_name}:{label}"
        current_time = time.time()
        cache_ttl = max(0, cache_ttl_seconds if cache_ttl_seconds is not None else self.default_cache_ttl)

        # Check our cache first
        if cache_key in self._prompt_cache:
            cached_prompt, cached_time = self._prompt_cache[cache_key]
            cache_age = current_time - cached_time

            self._log.info("Cache check for '%s': found version %s (age: %.1fs, TTL: %ds)",
                          prompt_name, cached_prompt.version, cache_age, cache_ttl)

            # If cache is stale, start background refresh but return cached version immediately
            if cache_age >= cache_ttl:
                self._log.info("Cache stale for '%s', starting background refresh", prompt_name)
                self._start_background_refresh(prompt_name, label)

            # Return cached version immediately
            if variables:
                try:
                    compiled_prompt = cached_prompt.compile(**variables)
                    return cached_prompt, compiled_prompt
                except KeyError as e:
                    raise ValueError(f"Missing variable for prompt '{prompt_name}': {e}") from e
            return cached_prompt, None

        # Cache miss - fetch synchronously
        try:
            self._log.info("Cache miss for '%s', fetching synchronously", prompt_name)
            self.langfuse_client = LangfuseClientManager.get_instance()
            prompt = self.langfuse_client.get_prompt(prompt_name, label=label)

            # Cache the result
            self._prompt_cache[cache_key] = (prompt, current_time)
            self._log.info("Fetched fresh prompt '%s' (version %s, label %s)",
                          prompt_name, prompt.version, label)

            if not prompt:
                raise ValueError(f"Prompt '{prompt_name}' content is empty or not found")

            if variables:
                try:
                    compiled_prompt = prompt.compile(**variables)
                    return prompt, compiled_prompt
                except KeyError as e:
                    raise ValueError(f"Missing variable for prompt '{prompt_name}': {e}") from e
            return prompt, None

        except (requests.RequestException, ValueError) as e:
            self._log.error(
                "Error retrieving prompt '%s' with label '%s': %s", prompt_name, label, e
            )
            if fallback is not None:
                self._log.info("Using fallback prompt for '%s'", prompt_name)
                # Create a fallback prompt object
                fallback_prompt = type('FallbackPrompt', (), {
                    'version': 'fallback',
                    'compile': lambda **kwargs: fallback if isinstance(fallback, str) else fallback,
                    'is_fallback': True
                })()
                return fallback_prompt, fallback
            raise RuntimeError(
                f"Failed to retrieve prompt '{prompt_name}' with label '{label}': {e}"
            ) from e

    @staticmethod
    def get_model_config(prompt):
        """
        Extracts LLM configuration from Langfuse prompt.

        Args:
            prompt (object): Langfuse prompt object containing config.

        Returns:
            dict: Dictionary containing LLM API parameters.
        """
        model_config = getattr(prompt, "config", {})
        BurstLangfuseHelpers._log.info("Raw Langfuse config for prompt version %s: %s",
                                      prompt.version, model_config)

        config = {
            "model": model_config.get("model", MODEL_DEFAULT),
            "temperature": model_config.get("temperature", 0.7),
            "max_tokens": model_config.get("max_tokens", 2048),
            "top_p": model_config.get("top_p", 1.0),
            "frequency_penalty": model_config.get("frequency_penalty", 0.0),
            "presence_penalty": model_config.get("presence_penalty", 0.0),
        }

        BurstLangfuseHelpers._log.info("Processed model config: %s", config)
        return config

    def _retrieve_prompt_page(self, label: str, page: int) -> Tuple[List[object], int]:
        """
        Fetch a single page of prompt items from the Langfuse API.

        Returns:
            A tuple (items, total_pages) where 'items' is the list of prompt items
            and 'total_pages' is extracted from the response metadata (or None if not provided).
        """
        params = {"label": label, "page": page, "limit": self.DEFAULT_LIMIT}
        try:
            response = self.session.get(
                self.LANGFUSE_PROMPTS_ENDPOINT,
                params=params,
                auth=(LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY),
                timeout=self.DEFAULT_TIMEOUT,
            )
            response.raise_for_status()
        except Exception as e:
            self._log.error("Failed to retrieve prompt list on page %d: %s", page, e)
            raise RuntimeError(f"Failed to retrieve prompt list on page {page}: {e}") from e

        data = response.json()
        items = data.get("data", [])
        total_pages = data.get("meta", {}).get("totalPages")
        return items, total_pages

    def _retrieve_prompt_list(self, label: str) -> List[object]:
        """
        Retrieves all prompt items from the API via pagination.

        Returns:
            A complete list of prompt items.
        """
        all_items = []
        page = 1
        total_pages = None

        while True:
            items, current_total_pages = self._retrieve_prompt_page(label, page)
            if total_pages is None and current_total_pages is not None:
                total_pages = current_total_pages
            all_items.extend(items)
            if (total_pages is not None and page >= total_pages) or (
                len(items) < self.DEFAULT_LIMIT
            ):
                break
            page += 1

        return all_items

    def _retrieve_prompt_objects(self, items: List[object], label: str) -> List[object]:
        """
        Converts prompt items into prompt objects using the Langfuse client.

        Raises:
            Exception: If a prompt item is malformed or prompt retrieval fails.

        Returns:
            A list of prompt objects.
        """
        result = []
        for item in items:
            if isinstance(item, dict):
                prompt_name = item.get("name")
            elif isinstance(item, str):
                prompt_name = item
            else:
                prompt_name = None

            if not prompt_name:
                self._log.critical("Malformed prompt item encountered: %s", item)
                raise ValueError(f"Malformed prompt item encountered: {item}")

            try:
                result.append(self.langfuse_client.get_prompt(prompt_name, label=label))
            except Exception as e:
                self._log.error("Error retrieving prompt '%s': %s", prompt_name, e)
                raise RuntimeError(f"Error retrieving prompt '{prompt_name}': {e}") from e
        return result

    def prefetch_prompts(self, label: str) -> List[object]:
        """
        Prefetches a list of prompts whose versions match the provided environment label.

        Args:
            label (str): The environment label to filter by.

        Returns:
            list: A list of Langfuse prompt objects.

        Raises:
            Exception: If any API requests or individual SDK prompt retrievals fail.
        """
        items = self._retrieve_prompt_list(label)
        return self._retrieve_prompt_objects(items, label)
