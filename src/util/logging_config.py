import logging
import logging.handlers
import os
import sys
import traceback
import json
from datetime import datetime
from typing import Optional, Dict, Any

class EnhancedLogger:
    """Enhanced logger with structured logging and error tracking capabilities."""

    def __init__(self, name: str, log_level: int = logging.INFO):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(log_level)
        self.logger.propagate = False
        self._setup_handlers()

    def _setup_handlers(self):
        """Setup logging handlers for console and file output."""
        # Only add handlers if none exist
        if not self.logger.handlers:
            # Console handler with colored output
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(ColoredFormatter())
            self.logger.addHandler(console_handler)

            # File handler with rotation
            log_dir = "logs"
            try:
                os.makedirs(log_dir, exist_ok=True)
            except Exception as e:
                self.logger.error("Failed to create log directory: %s", str(e))
                raise

            file_handler = logging.handlers.RotatingFileHandler(
                filename=os.path.join(log_dir, "app.log"),
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5
            )
            file_handler.setFormatter(StructuredFormatter())
            self.logger.addHandler(file_handler)

    def _get_context(self, **kwargs) -> Dict[str, Any]:
        """Get default context for logging."""
        return {
            'timestamp': datetime.now().isoformat(),
            'pid': os.getpid(),
            **kwargs
        }

    def info(self, message: str, *args, **kwargs):
        """Log an info message with context."""
        if args:
            # Handle old-style string formatting
            self.logger.info(message, *args)
        else:
            # Handle structured logging
            self.logger.info(message, extra=self._get_context(**kwargs))

    def error(self, message: str, *args, exc_info: Optional[Exception] = None, **kwargs):
        """Log an error message with context and exception details."""
        context = self._get_context(**kwargs)
        if exc_info:
            context.update({
                'exception_type': exc_info.__class__.__name__,
                'exception_message': str(exc_info),
                'stack_trace': traceback.format_exc()
            })

        # Build a detailed message that includes all context
        detailed_message = message

        # Add the original error message if available
        if 'original_error' in context:
            detailed_message = f"{message} - {context['original_error']}"

        # Add other context fields
        for key, value in context.items():
            if key not in ['exception_type', 'exception_message', 'stack_trace', 'original_error']:
                detailed_message += f" | {key}: {value}"

        if args:
            # Handle old-style string formatting
            self.logger.error(detailed_message, *args, exc_info=exc_info)
        else:
            # Handle structured logging
            self.logger.error(detailed_message, extra=context)

    def warning(self, message: str, *args, **kwargs):
        """Log a warning message with context."""
        if args:
            # Handle old-style string formatting
            self.logger.warning(message, *args)
        else:
            # Handle structured logging
            self.logger.warning(message, extra=self._get_context(**kwargs))

    def debug(self, message: str, *args, **kwargs):
        """Log a debug message with context."""
        if args:
            # Handle old-style string formatting
            self.logger.debug(message, *args)
        else:
            # Handle structured logging
            self.logger.debug(message, extra=self._get_context(**kwargs))

    def critical(self, message: str, *args, exc_info: Optional[Exception] = None, **kwargs):
        """Log a critical message with context and exception details."""
        context = self._get_context(**kwargs)
        if exc_info:
            context.update({
                'exception_type': exc_info.__class__.__name__,
                'exception_message': str(exc_info),
                'stack_trace': traceback.format_exc()
            })
        if args:
            # Handle old-style string formatting
            self.logger.critical(message, *args, exc_info=exc_info)
        else:
            # Handle structured logging
            self.logger.critical(message, extra=context)

    def exception(self, message: str, *args, **kwargs):
        """Log an exception message with context and stack trace."""
        if args:
            # Handle old-style string formatting
            self.logger.exception(message, *args)
        else:
            # Handle structured logging
            self.logger.exception(message, extra=self._get_context(**kwargs))

class ColoredFormatter(logging.Formatter):
    """Custom formatter with colored output for console."""

    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }

    def format(self, record):
        """Format the log record with colors."""
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{self.COLORS['RESET']}"
        return super().format(record)

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging output."""

    def _serialize_value(self, value: Any) -> Any:
        """Convert non-serializable values to serializable format."""
        if isinstance(value, Exception):
            return {
                'type': value.__class__.__name__,
                'message': str(value),
                'args': value.args
            }
        if hasattr(value, '__dict__'):
            return {k: self._serialize_value(v) for k, v in value.__dict__.items()}
        if isinstance(value, (list, tuple)):
            return [self._serialize_value(item) for item in value]
        if isinstance(value, dict):
            return {k: self._serialize_value(v) for k, v in value.items()}
        return value

    def format(self, record):
        """Format the log record as structured JSON."""
        log_data = {
            'timestamp': record.timestamp if hasattr(record, 'timestamp') else datetime.now().isoformat(),
            'level': record.levelname,
            'message': record.getMessage(),
            'pid': record.pid if hasattr(record, 'pid') else os.getpid(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # Add extra context
        for key, value in record.__dict__.items():
            if key not in log_data and not key.startswith('_'):
                try:
                    log_data[key] = self._serialize_value(value)
                except (TypeError, ValueError) as e:
                    log_data[key] = f"<unserializable: {str(e)}>"

        return json.dumps(log_data)

def setup_logging(name: str, log_level: int = logging.INFO) -> EnhancedLogger:
    """
    Setup and return an enhanced logger instance.
    
    Args:
        name (str): Name of the logger
        log_level (int): Logging level (default: logging.INFO)
        
    Returns:
        EnhancedLogger: Configured logger instance
    """
    return EnhancedLogger(name, log_level)
