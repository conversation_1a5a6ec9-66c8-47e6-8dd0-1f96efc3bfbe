import json
from sqlalchemy import exists

from util.database import Session
from shared.models import Statement


def has_change_statements(chunk_id: int) -> bool:
    """Check if a chunk has one or more statements with statement_type_id = 13."""
    session = Session()
    try:
        return session.query(
            exists().where(Statement.chunk_id == chunk_id).where(Statement.statement_type_id == 13)
        ).scalar()
    except Exception as e:
        print(f"Error checking for change statements in chunk {chunk_id}: {e}")
        return False
    finally:
        session.close()


def _parse_json_field(field, error_message):
    """Helper to parse a JSON field and return the result or an error."""
    try:
        return json.loads(field)
    except json.JSONDecodeError:
        return {"error": error_message}


def _validate_optional_fields(data, fields):
    """Helper to validate optional fields as strings."""
    for field in fields:
        if field in data and not isinstance(data[field], str):
            raise ValueError(f"Field '{field}' must be a string if provided.")


def _normalize_impact_field(data):
    """Normalize the 'impact' field to capitalize valid values or default to 'Unknown'."""
    if "impact" in data:
        valid_impact_values = {"high", "medium", "low", "none"}
        impact_value = data["impact"].lower()
        data["impact"] = (
            impact_value.capitalize() if impact_value in valid_impact_values else "Unknown"
        )


def merge_change_statement_text_data(statement, updated_fields):
    """Merge and validate JSON data for 'text' field in a 'Change Statement'."""
    existing_text_data = _parse_json_field(
        statement.text, "Existing 'text' field contains invalid JSON."
    )
    if isinstance(existing_text_data, dict) and "error" in existing_text_data:
        return existing_text_data, 400

    updated_text_data = (
        updated_fields["text"]
        if isinstance(updated_fields["text"], dict)
        else _parse_json_field(
            updated_fields["text"], "Updated 'text' field contains invalid JSON."
        )
    )
    if isinstance(updated_text_data, dict) and "error" in updated_text_data:
        return updated_text_data, 400

    merged_text_data = {**existing_text_data, **updated_text_data}

    # Validate and normalize fields
    _validate_optional_fields(
        merged_text_data,
        ["original_text", "updated_text", "changed_text", "change_statement", "impact"],
    )
    _normalize_impact_field(merged_text_data)

    return json.dumps(merged_text_data)


def format_change_statement_text(text):
    """Parses, validates, and normalizes the 'text' field for a 'Change Statement'."""
    text_data = _parse_json_field(text, "Existing 'text' field contains invalid JSON.")
    if isinstance(text_data, dict) and "error" in text_data:
        return text_data

    text_data = {key.lower(): value for key, value in text_data.items()}

    # Validate and normalize fields
    _validate_optional_fields(
        text_data, ["original_text", "updated_text", "changed_text", "change_statement", "impact"]
    )
    _normalize_impact_field(text_data)

    return text_data
