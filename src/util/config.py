"""
This module handles the initialization of environment variables required for the application. 
It provides a temporary solution to support both the older version, which relies on a .env file 
being present, and the newer version, which initializes environment variables differently.

Functions:
    are_env_vars_set(var_names): Checks if the specified environment variables are set.
    initialize_env_vars(var_names): Ensures all environment variables are set, 
        optionally loading them from a .env file.

    get_env_var(key): Retrieves the environment variable and raises an exception if it is not set.
"""

import os

from dotenv import load_dotenv


def are_env_vars_set(var_names):
    """
    Check if the specified environment variables are set.

    Args:
        var_names (list of str): List of environment variable names to check.

    Returns:
        bool: True if all specified environment variables are set, False otherwise.
    """
    return all(os.getenv(var_name) is not None for var_name in var_names)


def initialize_env_vars(var_names, dotenv_path=None):
    """
    Ensure all environment variables are set. If any are missing,
    attempt to load them from a .env file.

    Args:
        var_names (list of str): List of environment variable names to check.
        dotenv_path (str, optional): Path to the .env file. If not provided,
        it defaults to the current directory.

    Raises:
        EnvironmentError: If any required environment variables are still
        missing after loading the .env file.
    """
    if not are_env_vars_set(var_names):
        if dotenv_path is None:
            cwd = os.getcwd()
            dotenv_path = os.path.join(cwd, ".env")

        load_dotenv(dotenv_path)

    # After loading the .env file, check if all variables are set
    missing_vars = [var for var in var_names if os.getenv(var) is None]

    if missing_vars:
        raise EnvironmentError(f"Missing required environment variables: {', '.join(missing_vars)}")


def get_env_var(key: str) -> str:
    """
    Retrieve the environment variable and raise an exception if it is not set.

    Args:
        key (str): The environment variable name.

    Returns:
        str: The value of the environment variable.

    Raises:
        ValueError: If the environment variable is not set (i.e., None).
    """
    value = os.getenv(key)
    if value is None:
        raise ValueError(f"Environment variable '{key}' is required but not set.")
    return value


# List of required environment variables
env_vars = [
    "OPENAI_API_KEY",
    "PINECONE_API_KEY",
    "FEDERAL_REGISTER_API",
    "CODE_OF_FEDERAL_REGULATIONS_API",
    "AWS_S3_BUCKET_NAME",
    "AWS_REGION",
    "AWS_S3_ACCESS_KEY",
    "AWS_S3_SECRET_KEY",
    "AWS_POSTGRES_USERNAME",
    "AWS_POSTGRES_PASSWORD",
    "AWS_POSTGRES_HOST",
    "AWS_POSTGRES_PORT",
    "AWS_POSTGRES_DATABASE",
    "AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN",
    "AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN",
    "AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN",
    "AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN",
    "AWS_SNS_JOB_GENERATE_USER_STORIES_ARN",
    "AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN",
    "AWS_SNS_JOB_GENERATE_TEST_CASES_ARN",
    "AWS_SNS_JOB_ARN",
    "AWS_FEED_MANAGER_REGION",
    "AWS_FEED_MANAGER_ACCESS_KEY",
    "AWS_FEED_MANAGER_SECRET_KEY",
    "AWS_FEED_MANAGER_QUEUE",
    "AUTH0_DOMAIN",
    "AUTH0_CLIENT_ID",
    "AUTH0_CLIENT_SECRET",
    "LOG_LEVEL",
    "THIRD_PARTY_LOG_LEVEL",
    "LANGFUSE_SECRET_KEY",
    "LANGFUSE_PUBLIC_KEY",
    "LANGFUSE_HOST",
    "FEED_MANAGER_API_URL",
    "ENVIRONMENT_NAME",
]

# Ensure all environment variables are initialized
initialize_env_vars(env_vars)

# Access the environment variables using get_env_var to ensure they are always strings
OPENAI_API_KEY = get_env_var("OPENAI_API_KEY")
PINECONE_API_KEY = get_env_var("PINECONE_API_KEY")

FEDERAL_REGISTER_API = get_env_var("FEDERAL_REGISTER_API")
CODE_OF_FEDERAL_REGULATIONS_API = get_env_var("CODE_OF_FEDERAL_REGULATIONS_API")

AWS_REGION = get_env_var("AWS_REGION")
AWS_S3_BUCKET_NAME = get_env_var("AWS_S3_BUCKET_NAME")
AWS_S3_ACCESS_KEY = get_env_var("AWS_S3_ACCESS_KEY")
AWS_S3_SECRET_KEY = get_env_var("AWS_S3_SECRET_KEY")

AWS_POSTGRES_USERNAME = get_env_var("AWS_POSTGRES_USERNAME")
AWS_POSTGRES_PASSWORD = get_env_var("AWS_POSTGRES_PASSWORD")
AWS_POSTGRES_HOST = get_env_var("AWS_POSTGRES_HOST")
AWS_POSTGRES_PORT = get_env_var("AWS_POSTGRES_PORT")
AWS_POSTGRES_DATABASE = get_env_var("AWS_POSTGRES_DATABASE")

AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN = os.getenv("AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN")
AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN = get_env_var(
    "AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN"
)
AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN = os.getenv(
    "AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN"
)
AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN = os.getenv(
    "AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN"
)
AWS_SNS_JOB_GENERATE_USER_STORIES_ARN = os.getenv("AWS_SNS_JOB_GENERATE_USER_STORIES_ARN")
AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN = os.getenv(
    "AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN"
)
AWS_SNS_JOB_GENERATE_TEST_CASES_ARN = os.getenv("AWS_SNS_JOB_GENERATE_TEST_CASES_ARN")
AWS_SNS_JOB_REGENERATE_STATEMENTS_ARN = os.getenv("AWS_SNS_JOB_REGENERATE_STATEMENTS_ARN")
AWS_SNS_JOB_ARN = os.getenv("AWS_SNS_JOB_ARN")

AWS_FEED_MANAGER_REGION = get_env_var("AWS_FEED_MANAGER_REGION")
AWS_FEED_MANAGER_ACCESS_KEY = get_env_var("AWS_FEED_MANAGER_ACCESS_KEY")
AWS_FEED_MANAGER_SECRET_KEY = get_env_var("AWS_FEED_MANAGER_SECRET_KEY")
AWS_FEED_MANAGER_QUEUE = get_env_var("AWS_FEED_MANAGER_QUEUE")

LOG_LEVEL = get_env_var("LOG_LEVEL").upper()
THIRD_PARTY_LOG_LEVEL = get_env_var("THIRD_PARTY_LOG_LEVEL").upper()

LANGFUSE_SECRET_KEY = get_env_var("LANGFUSE_SECRET_KEY")
LANGFUSE_PUBLIC_KEY = get_env_var("LANGFUSE_PUBLIC_KEY")
LANGFUSE_HOST = get_env_var("LANGFUSE_HOST")

FEED_MANAGER_API_URL = get_env_var("FEED_MANAGER_API_URL")
ENVIRONMENT_NAME = get_env_var("ENVIRONMENT_NAME")

AUTH0_DOMAIN = get_env_var("AUTH0_DOMAIN")
AUTH0_CLIENT_ID = get_env_var("AUTH0_CLIENT_ID")
AUTH0_CLIENT_SECRET = get_env_var("AUTH0_CLIENT_SECRET")

AWS_REGION_NAME = get_env_var("AWS_REGION_NAME")
AWS_ACCESS_KEY_ID = get_env_var("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = get_env_var("AWS_SECRET_ACCESS_KEY")
