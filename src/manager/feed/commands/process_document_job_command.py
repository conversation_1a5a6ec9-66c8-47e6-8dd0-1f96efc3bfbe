from accessor.document.dto.document_job_dto import DocumentJobDTO
from manager.feed.commands.command import Command
from shared.configs.job_configs import JOB_CONFIGS
from shared.enums.job_enums import JobMessageK<PERSON>, JobTypeEnum
from util.config import AWS_SNS_JOB_ARN, AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN


class ProcessDocumentJobCommand(Command):

    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.document_job_processor = self.dependency_factory.get_document_job_processor()

    def execute(self):
        self.logger.info(
            "Starting document job initial with document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )
        dto = self.document_accessor.prepare_document_in_initial_processing(
            self.document_id, self.job_id
        )
        if not dto:
            raise ValueError(
                f"Unable to prepare document for processing document_id: \
                             {self.document_id} and job_id: {self.job_id}"
            )
        response = self._process_document_job_initial(dto)

        if response.status == "success":
            self.logger.info("Publishing next step message based on job type.")
            if not self._publish_next_step_message(dto):
                self.logger.error("Failed to publish next step message for job %s", self.job_id)
                self.logger.info("Moving job %s to error status.", self.job_id)
                self.document_accessor.job_error(self.job_id)
        else:
            self.logger.info("Moving job %s to error status.", self.job_id)
            self.document_accessor.job_error(self.job_id)
        self._delete_message(receipt_handle=self.receipt_handle)

    def _process_document_job_initial(self, dto: DocumentJobDTO):

        response = self.document_job_processor.chunk_document(dto)
        self.logger.info(
            "Completed document job initial. document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )

        return response

    def _publish_next_step_message(self, dto: DocumentJobDTO):
        """
        Publishes the next step's SNS message based on the job type.
        For an optimized_document, it kicks off the metrics generation flow.
        Otherwise, it uses the default chunks-created flow.
        """
        if dto.job_type_id == JobTypeEnum.OPTIMIZE_PROCEDURE.value:
            message_config = JOB_CONFIGS.get(JobMessageKey.OPTIMIZE_PROCEDURE)
            message = {
                "header": {"type": message_config["message_type"].value},
                "body": {"document_id": self.document_id, "job_id": self.job_id},
            }
            sns_topic = message_config["sns_topic_arn"]
        else:
            message_config = JOB_CONFIGS.get(JobMessageKey.CHUNKS_GENERATED)
            message = {
                "header": {"type": message_config["message_type"].value},
                "body": {"document_id": self.document_id, "job_id": self.job_id},
            }
            sns_topic = message_config["sns_topic_arn"]

        return self._publish_sns_message(sns_topic, message)
