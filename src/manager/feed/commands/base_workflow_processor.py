"""Base class for workflow processing commands."""

from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Event, Thread
from typing import Optional, Tuple
import time

from accessor.document.document_accessor import DocumentAccessor
from manager.feed.commands.command import Command
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.enums.job_enums import JobWorkflowStatusEnum
from shared.models.job_workflow import JobWorkflow
from shared.models.workflow_type_enum import WorkflowTypeEnum
from util.logging_config import get_logger


class BaseWorkflowProcessor(Command, ABC):
    """Template method base class for processing job workflows."""

    MAX_WORKERS = 5  # Configurable thread pool size
    VISIBILITY_TIMEOUT = 4 * 3600  # 4 hours in seconds
    EXTENDER_INTERVAL = 3600  # 1 hour in seconds

    @property
    @abstractmethod
    def workflow_type(self) -> WorkflowTypeEnum:
        """The type of workflow this processor handles."""
        ...

    def __init__(
        self,
        dependency_factory: DependencyFactory,
        document_id: int,
        job_id: int,
        receipt_handle: str,
        sqs_client,
        sns_client,
        logger=None,
    ):
        super().__init__(
            dependency_factory,
            document_id,
            job_id,
            receipt_handle,
            sqs_client,
            sns_client,
            logger,
        )
        self.document_accessor: DocumentAccessor = (
            self.dependency_factory.get_document_accessor()
        )
        self.job_workflow: Optional[JobWorkflow] = None
        self.job_workflow_id: Optional[int] = None
        self._visibility_stop_event: Optional[Event] = None
        self._visibility_thread: Optional[Thread] = None

    def pre_workflow(self):
        """Optional hook called before processing items.
        
        Override this method to perform any setup needed before processing items.
        """
        pass

    def post_workflow(self):
        """Optional hook called after processing items.
        
        Override this method to perform any cleanup needed after processing items.
        """
        pass

    def _extend_visibility(self, stop_event: Event):
        """Extend message visibility timeout periodically.
        
        Args:
            stop_event: Event to signal thread should stop
        """
        while not stop_event.is_set():
            try:
                self.sqs_client.change_message_visibility(
                    QueueUrl=self.sqs_client.get_queue_url(QueueName="your-queue-name")["QueueUrl"],
                    ReceiptHandle=self.receipt_handle,
                    VisibilityTimeout=self.VISIBILITY_TIMEOUT,
                )
                self.logger.debug(
                    "Extended visibility timeout for message %s to %d seconds",
                    self.receipt_handle,
                    self.VISIBILITY_TIMEOUT,
                )
            except Exception as e:
                self.logger.error(
                    "Failed to extend visibility timeout: %s",
                    e,
                )
            
            # Wait for next interval or stop signal
            stop_event.wait(self.EXTENDER_INTERVAL)

    def _start_visibility_extender(self):
        """Start the visibility timeout extender thread."""
        self._visibility_stop_event = Event()
        self._visibility_thread = Thread(
            target=self._extend_visibility,
            args=(self._visibility_stop_event,),
            daemon=True,
        )
        self._visibility_thread.start()
        self.logger.debug("Started visibility timeout extender thread")

    def _stop_visibility_extender(self):
        """Stop the visibility timeout extender thread."""
        if self._visibility_stop_event and self._visibility_thread:
            self._visibility_stop_event.set()
            self._visibility_thread.join(timeout=5)  # Wait up to 5 seconds
            if self._visibility_thread.is_alive():
                self.logger.warning("Visibility extender thread did not stop cleanly")
            self._visibility_stop_event = None
            self._visibility_thread = None
            self.logger.debug("Stopped visibility timeout extender thread")

    def _get_or_create_job_workflow(self) -> Tuple[JobWorkflow, bool]:
        """Get or create the job workflow for this job and workflow type.
        
        Returns:
            Tuple[JobWorkflow, bool]: The job workflow and whether it was just created
        """
        job_workflow = self.document_accessor.get_job_workflow(self.job_id, self.workflow_type)
        was_created = False
        
        if not job_workflow:
            self.logger.info("Creating new job workflow for job %s", self.job_id)
            job_workflow = self.document_accessor.create_job_workflow(
                self.job_id, self.workflow_type, run_count=1
            )
            was_created = True
        else:
            self.logger.info(
                "Resuming existing job workflow with status %s", job_workflow.status
            )

        self.job_workflow = job_workflow
        self.job_workflow_id = job_workflow.job_workflow_id
        return job_workflow, was_created

    def execute(self):
        """Execute the workflow processing."""
        self._start_visibility_extender()
        try:
            job_workflow, was_created = self._get_or_create_job_workflow()

            if job_workflow.status == JobWorkflowStatusEnum.COMPLETED:
                self.logger.info("Job workflow %s already completed", self.job_id)
                return

            # Seed items only on first run
            if was_created:
                self.logger.info("Seeding items for new workflow %s", self.job_id)
                self._seed_item_rows()

            # Detect resume and increment run count
            if job_workflow.status in [
                JobWorkflowStatusEnum.IN_PROGRESS,
                JobWorkflowStatusEnum.FAILED,
                JobWorkflowStatusEnum.RETRYING,
            ]:
                self.logger.info(
                    "Resuming workflow %s (run %d)", 
                    self.job_id, 
                    job_workflow.run_count + 1
                )
                job_workflow.run_count += 1
                self.document_accessor.update_job_workflow(
                    job_workflow, 
                    JobWorkflowStatusEnum.RETRYING
                )

            if job_workflow.status == JobWorkflowStatusEnum.FAILED:
                self._cleanup_partial()

            # Mark the workflow overall in-progress
            self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.IN_PROGRESS)

            try:
                # Call pre-workflow hook
                self.pre_workflow()
                
                self._process_all_items()
                
                # Call post-workflow hook
                self.post_workflow()
                
                self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.COMPLETED)
            except Exception as e:
                self.logger.exception("Error processing workflow %s", self.job_id)
                self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.FAILED)
                raise
        finally:
            self._stop_visibility_extender()
            self._delete_message(self.receipt_handle)

    def _safe_process(self, item_key: str) -> None:
        """Safely process a single item with timing and status updates.
        
        Args:
            item_key: The key of the item to process
        """
        start_time = time.time()
        try:
            self.process_item(item_key)
            self.document_accessor.mark_done(self.job_workflow_id, item_key)
            duration = time.time() - start_time
            self.logger.info(
                "Item %s processed successfully in %.2fs", 
                item_key, 
                duration
            )
        except Exception as e:
            duration = time.time() - start_time
            self.logger.exception(
                "Item %s failed after %.2fs: %s", 
                item_key, 
                duration, 
                str(e)
            )
            self.document_accessor.mark_failed(self.job_workflow_id, item_key, str(e))

    def _process_all_items(self):
        """Process all items in the workflow using a thread pool."""
        with ThreadPoolExecutor(max_workers=self.MAX_WORKERS) as executor:
            futures = []
            
            # Submit initial batch of items
            while len(futures) < self.MAX_WORKERS:
                item_key = self.document_accessor.reserve_next_pending(self.job_workflow_id)
                if not item_key:
                    break
                futures.append(executor.submit(self._safe_process, item_key))
            
            # Process completed futures and submit new items
            for future in as_completed(futures):
                try:
                    future.result()  # Re-raise any exceptions
                except Exception as e:
                    self.logger.exception("Error in thread pool: %s", e)
                
                # Submit next item if available
                item_key = self.document_accessor.reserve_next_pending(self.job_workflow_id)
                if item_key:
                    futures.append(executor.submit(self._safe_process, item_key))

    def _cleanup_partial(self):
        """Clean up any failed items from a previous run."""
        incomplete_items = self.document_accessor.list_incomplete(self.job_workflow_id)
        for item_key in incomplete_items:
            self.cleanup_item(item_key)

    @abstractmethod
    def _seed_item_rows(self) -> None:
        """Seed the job item workflow rows for this workflow.
        
        This is called only on the first run of a workflow.
        Subclasses should implement this to create the necessary job item rows
        using the document accessor.
        """
        ...

    @abstractmethod
    def process_item(self, item_key: str):
        """Process a single item in the workflow."""
        ...

    @abstractmethod
    def cleanup_item(self, item_key: str):
        """Clean up a failed item."""
        ... 