import datetime
from typing import List

from accessor.content import content_accessor
from langfuse.decorators import observe

from manager.feed.commands.command import Command
from shared.enums.job_enums import JobStatusEnum
from shared.models import JobWorkflowStatusEnum, WorkflowTypeEnum, Subtopic, UniqueStatement
from util.config import AWS_FEED_MANAGER_QUEUE
from util.langfuse.decorators import trace_context


class GeneratePolicyRequirementComparisonCommand(Command):

    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        domain_id,
        chunk_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.domain_id = domain_id

        self.logger.debug("self.domain_id = %s", self.domain_id)

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.content_accessor = self.dependency_factory.get_content_accessor()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()

    @observe(name="generate_requirements_from_policy_requirement_comparison")
    @trace_context(["document_id", "job_id"])
    def execute(self):
        self._process_policy_requirement_comparison()
        self.logger.debug(
            "Generating policy requirement comparison for document_id: %s, job_id: %s and domain_id: %s",
            self.document_id,
            self.job_id,
            self.domain_id,
        )
        self.document_accessor.update_job_status(self.job_id, JobStatusEnum.ARTIFACTS_ARE_READY)
        self._delete_message(
            self.receipt_handle,
            "Processed generate requirements from policy requirement comparison message.",
        )

    def _process_policy_requirement_comparison(self):

        self.logger.info(
            "Starting process of generating policy requirement comparison for document_id: %s, job_id: %s, and domain_id: %s",
            self.document_id,
            self.job_id,
            self.domain_id,
        )

        self.document_accessor.update_job_status(
            self.job_id, JobStatusEnum.IDENTIFYING_POLICY_IMPACTS
        )
        visibility_timeout_seconds = 3600  # 1-hour iteration

        job_workflow = self._get_job_workflow()

        if job_workflow.status == JobWorkflowStatusEnum.COMPLETED:
            return

        try:
            resume_from_ids = self._handle_resume_conditions(job_workflow)

            ordered_subtopics = self.content_accessor.get_all_subtopics_ordered(self.document_id)

            start_index = self._find_procedure_change_start_index_for_processing(
                ordered_subtopics, resume_from_ids
            )

            ordered_statement_types_to_process = [
                1,  # Requirement
                6,  # Authorization
            ]

            self._process_all_statement_types(
                ordered_statement_types_to_process,
                start_index,
                resume_from_ids,
                visibility_timeout_seconds,
            )

            self.document_accessor.update_job_workflow(
                job_workflow, JobWorkflowStatusEnum.COMPLETED
            )

        except Exception:
            # self._handle_job_failure(job_workflow)
            raise

    # Workflow helper methods
    def _get_job_workflow(self):
        # Find the job workflow if one exists, if not create a new one
        job_workflow = self.document_accessor.get_job_workflow(
            self.job_id, WorkflowTypeEnum.IDENTIFYING_POLICY_IMPACTS
        )
        if job_workflow:
            self.logger.info(
                "Resuming existing job with Job Workflow status of %s.", job_workflow.status
            )
        else:
            job_workflow = self.document_accessor.create_job_workflow(
                self.job_id, WorkflowTypeEnum.IDENTIFYING_POLICY_IMPACTS, run_count=1
            )
        return job_workflow

    def _handle_resume_conditions(self, job_workflow):
        if job_workflow.status in [
            JobWorkflowStatusEnum.IN_PROGRESS,
            JobWorkflowStatusEnum.FAILED,
            JobWorkflowStatusEnum.RETRYING,
        ]:
            return self._find_resume_point_and_delete_artifacts()
        else:
            if job_workflow.status == JobWorkflowStatusEnum.STARTING:
                self.document_accessor.update_job_workflow(
                    job_workflow, JobWorkflowStatusEnum.IN_PROGRESS
                )
            return None

    # The strategy to delete partial artifacts is the following:
    # 1. Determine the maximum subtopic_id
    # 2. Use the max subtopic_id and obtain the maximum unique statement id
    # 3. Delete the max unique statement because it could be partially completed
    # 4. Return back the last
    def _find_resume_point_and_delete_artifacts(self):
        (max_statement_type_id, max_subtopic_id, max_unique_statement_id) = (
            self.content_accessor.find_policy_requirement_comparison_generation_resume_point(
                self.document_id
            )
        )
        if max_unique_statement_id is not None:
            self.content_accessor.delete_procedure_change_artifacts(
                max_unique_statement_id, max_statement_type_id, max_subtopic_id
            )
            return {
                "statement_type_id": max_statement_type_id,
                "subtopic_id": max_subtopic_id,
                "unique_statement_id": max_unique_statement_id,
            }
        return None

    def _find_procedure_change_start_index_for_processing(
        self, ordered_subtopics: List[Subtopic], resume_from_ids
    ):
        if resume_from_ids:
            return self._find_procedure_change_start_index(ordered_subtopics, resume_from_ids)
        return 0

    def _find_procedure_change_start_index(
        self, ordered_subtopics: List[Subtopic], resume_from_ids
    ) -> int:
        return next(
            (
                i
                for i, subtopic in enumerate(ordered_subtopics)
                if subtopic.subtopic_id >= resume_from_ids["subtopic_id"]
            ),
            len(ordered_subtopics),
        )

    def _handle_job_failure(self, job_workflow):
        self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.FAILED)
        if job_workflow.run_count == Command.MAX_RUN_COUNT:
            self.document_accessor.job_error(self.job_id)

    def _process_all_statement_types(
        self,
        ordered_statement_types: List[int],
        start_index: int,
        resume_from_ids,
        visibility_timeout_seconds: int,
    ):
        for statement_type_id in ordered_statement_types[start_index:]:
            ordered_subtopics = self.content_accessor.get_subtopics_ordered(
                self.document_id, statement_type_id
            )
            subtopic_start_index = self._find_subtopic_start_index(
                ordered_subtopics, resume_from_ids
            )
            for subtopic in ordered_subtopics[subtopic_start_index:]:
                self._process_document_requirements_per_subtopic(
                    statement_type_id,
                    subtopic,
                    resume_from_ids,
                    visibility_timeout_seconds,
                )

    def _find_subtopic_start_index(self, ordered_subtopics, resume_from_ids):
        if resume_from_ids:
            return next(
                (
                    i
                    for i, subtopic in enumerate(ordered_subtopics)
                    if subtopic.subtopic_id >= resume_from_ids["subtopic_id"]
                ),
                len(ordered_subtopics),
            )
        return 0

    def _process_document_requirements_per_subtopic(
        self,
        statement_type_id: int,
        subtopic: Subtopic,
        resume_from_ids,
        visibility_timeout_seconds: int,
    ):
        start_time = datetime.datetime.now(datetime.timezone.utc)
        self.sqs_client.change_message_visibility(
            QueueUrl=AWS_FEED_MANAGER_QUEUE,
            ReceiptHandle=self.receipt_handle,
            VisibilityTimeout=visibility_timeout_seconds,
        )

        requirements = self._get_requirements_per_subtopic(
            statement_type_id, subtopic, resume_from_ids
        )

        if len(requirements) > 0:

            for requirement in requirements:

                dto = self.document_accessor.build_policy_requirement_comparison_requirement_dto(
                    self.document_id,
                    self.domain_id,
                    requirement.unique_statement_id,
                    subtopic.subtopic_id,
                    self.job_id,
                )

                self.synthesis_engine.generate_procedure_changes_for_requirement(dto)

        end_time = datetime.datetime.now(datetime.timezone.utc)
        self.logger.debug(
            "Time taken to process subtopic_id %s for document_id %s: %s seconds",
            subtopic.subtopic_id,
            self.document_id,
            (end_time - start_time).total_seconds(),
        )

    def _get_requirements_per_subtopic(
        self, statement_type_id: int, subtopic: Subtopic, resume_from_ids
    ) -> List[UniqueStatement]:
        requirements = self.content_accessor.get_unique_statements_ordered(
            self.document_id,
            subtopic.subtopic_id,  # type: ignore
            statement_type_id,
        )
        if resume_from_ids and subtopic.subtopic_id == resume_from_ids["subtopic_id"]:
            start_index = next(
                (
                    i
                    for i, requirement in enumerate(requirements)
                    if requirement.unique_statement_id >= resume_from_ids["unique_statement_id"]
                ),
                len(requirements),
            )
            return requirements[start_index:]
        return requirements
