"""
Module responsible for optimizing procedure document.
"""

import os
from datetime import datetime
from io import BytesIO

from docx import Document

from accessor.content.interfaces.i_document_statistics_accessor import (
    IDocumentStatisticsAccessor,
)
from accessor.document.dto.document_job_create_dto import (
    DocumentCreateDTO,
    DocumentJobCreateDTO,
    JobCreateDTO,
)
from accessor.document.dto.document_job_dto import DocumentJobDTO
from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO
from engine.analysis.dto.readability_dto import ReadabilityDTO
from manager.feed.commands.command import Command
from shared.configs.job_configs import JOB_CONFIGS
from shared.enums.job_enums import JobMessage<PERSON><PERSON>, JobStatusEnum, JobTypeEnum


class OptimizeDocumentCommand(Command):
    """
    Class responsible for optimizing a procedure document.
    """

    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )
        self.content_accessor = self.dependency_factory.get_content_accessor()
        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.analysis_engine = self.dependency_factory.get_analysis_engine()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()
        self.transform_engine = self.dependency_factory.get_transform_engine()

    def execute(self):
        try:
            self.logger.info(
                "Starting optimize document command for document_id: %s, job_id: %s.",
                self.document_id,
                self.job_id,
            )

            self.logger.info("Retrieving document: %s", self.document_id)
            original_document = self.document_accessor.prepare_document_in_initial_processing(
                self.document_id, self.job_id
            )
            if not original_document:
                raise ValueError(f"Document not found for document_id: {self.document_id}.")

            self.logger.info("Updating job status.")
            self.document_accessor.update_job_status(
                self.job_id, JobStatusEnum.OPTIMIZING_PROCEDURE
            )

            self.logger.info("Processing document statistics for original document.")
            self._process_document_statistics(
                original_document.document_bytes, self.document_id, doc_label="original"
            )

            self.logger.info("Generating optimized text.")
            optimized_text = self.synthesis_engine.generate_optimized_text(
                original_document.document_id
            )

            preview = optimized_text[:1000] if len(optimized_text) > 1000 else optimized_text
            self.logger.debug("Optimized markdown preview (first 1000 chars): %s", preview)

            if "https://tmp" in optimized_text:
                self.logger.warning(
                    "Optimized markdown contains 'https://tmp', which may cause "
                    "Pandoc to misinterpret local paths."
                )

            self.logger.info("Converting optimized markdown to Word document.")
            word_document = self.transform_engine.transform_markdown_to_docx(optimized_text)

            self.logger.info("Mapping document job dto to create document dto.")
            optimized_document = self._map_document_job_to_create_dto(original_document)
            optimized_document.name = original_document.document_name + " - Optimized Procedure"
            optimized_document.document_bytes = word_document
            optimized_document.filename = "optimized-" + original_document.document_filename
            optimized_document.document_end_page = 1
            optimized_document.document_end_page = -1
            optimized_document.original_document_id = original_document.document_id

            self.logger.info("Saving optimized document.")
            response = self.document_accessor.create_document(optimized_document)
            new_document_id = response["document_id"]
            self.logger.info("Optimized document saved with new document_id: %s", new_document_id)

            self.logger.info("Processing document statistics for optimized document.")
            self._process_document_statistics(
                optimized_document.document_bytes, new_document_id, doc_label="optimized"
            )

            self.logger.info("Converting optimized document to PDF.")
            pdf_document = self.transform_engine.transform_docx_to_pdf(
                optimized_document.document_bytes
            )
            self.logger.info("Converted optimized document to PDF.")

            self.logger.info("Creating document DTO for new PDF.")
            new_document_dto = DocumentCreateDTO(
                optimized_document.domain_id,
                optimized_document.name,
                self._replace_or_add_extension(
                    optimized_document.filename,
                    ".pdf",
                ),
                pdf_document,
                optimized_document.business_id,
                optimized_document.effective_datetime,
                1,
                -1,
                new_document_id,
            )

            self.logger.info("Creating job DTO for new PDF.")
            job = JobCreateDTO(JobTypeEnum.PROCEDURE_IMPORT.value)
            document_job = DocumentJobCreateDTO(new_document_dto, job)

            self.logger.info("Saving document and job for new PDF.")
            response = self.document_accessor.create_document_and_job(document_job)
            new_document_id = response["document_id"]
            new_job_id = response["job_id"]
            self.logger.info("New document_id: %s, new_job_id: %s", new_document_id, new_job_id)

            if not self._publish_document_job_created(new_document_id, new_job_id):
                self.logger.error(
                    "Failed to publish document job message for document_id: %s",
                    new_document_id,
                )
                self.logger.info("Moving job %s to error status.", self.job_id)
                self.document_accessor.job_error(self.job_id)
            else:
                self.logger.info(
                    "Published document job message for document_id: %s",
                    new_document_id,
                )

            self.logger.info("Marking job complete for job_id: %s.", self.job_id)
            self.document_accessor.job_complete(self.job_id)
            self.logger.info("Deleting message from queue.")
            self._delete_message(receipt_handle=self.receipt_handle)
        except Exception as e:
            self.logger.exception("Unexpected error in OptimizeDocumentPipelineCommand: %s", str(e))
            self.document_accessor.job_error(self.job_id)
            self._delete_message(receipt_handle=self.receipt_handle)

    def _publish_document_job_created(self, document_id, job_id):
        message_config = JOB_CONFIGS.get(JobMessageKey.SUBMIT_INITIAL_DOCUMENT)
        message = {
            "header": {"type": message_config["message_type"].value},
            "body": {"document_id": document_id, "job_id": job_id},
        }
        self.logger.info("Publishing new job message.")
        return self._publish_sns_message(message_config["sns_topic_arn"], message)

    def _process_document_statistics(
        self, document_bytes: bytes, document_id: str, doc_label: str = "document"
    ):
        """
        Process document statistics by extracting text from a docx file,
        analyzing readability, mapping the readability DTO to document statistics,
        and storing those statistics. Detailed logging is added for debugging purposes.

        Args:
            document_bytes (bytes): The byte content of the docx file.
            document_id (str): The identifier of the document for which statistics are being stored.
            doc_label (str, optional): A label to distinguish the document (e.g.,
            "original" or "optimized"). Defaults to "document".

        Returns:
            tuple: A tuple containing the raw statistics and the mapped statistics DTO.
        """
        self.logger.info("Extracting text from %s docx.", doc_label)
        text = self._extract_text_from_docx(document_bytes)

        self.logger.info("Analyzing readability of %s text.", doc_label)
        stats = self.analysis_engine.analyze_readability(text)

        self.logger.info("Mapping readability DTO to document statistics DTO for %s.", doc_label)
        mapped_stats = self._map_readability_to_document_stats(stats)

        self.logger.info(
            "Storing %s document statistics for document_id: %s", doc_label, document_id
        )
        document_statistics_accessor: IDocumentStatisticsAccessor = self.content_accessor
        document_statistics_accessor.create_document_statistics(document_id, mapped_stats)

        return stats, mapped_stats

    # KA: These don't belong here. Find a better home.
    def _map_readability_to_document_stats(
        self, readability: ReadabilityDTO
    ) -> DocumentStatisticsDTO:
        """
        Maps a ReadabilityDTO to a DocumentStatisticsDTO.

        Parameters:
            readability (ReadabilityDTO): The DTO containing readability metrics.

        Returns:
            DocumentStatisticsDTO: The DTO with document statistics.
        """
        now = datetime.now()
        return DocumentStatisticsDTO(
            word_count=readability.word_count,
            difficult_word_count=readability.difficult_words,
            average_sentence_length=readability.avg_sentence_length,
            average_word_length=readability.avg_word_length,
            flesch_grade_level=int(round(readability.flesch_kincaid_grade)),
            flesch_score=int(round(readability.flesch_reading_ease)),
            flesch_reading_ease=readability.reading_ease_descriptor,
            created_at=now,
            updated_at=now,
        )

    def _map_document_job_to_create_dto(
        self, document_job_dto: DocumentJobDTO
    ) -> DocumentCreateDTO:
        """
        Maps a DocumentJobDTO to a DocumentCreateDTO.

        :param document_job_dto: The source DTO containing both document and job properties.
        :return: A DocumentCreateDTO with fields copied from DocumentJobDTO.
        """
        return DocumentCreateDTO(
            domain_id=document_job_dto.domain_id,
            name=document_job_dto.document_name,
            filename=document_job_dto.document_filename,
            document_bytes=document_job_dto.document_bytes,
            business_id=document_job_dto.document_business_id,
            start_page=document_job_dto.document_start_page,
            end_page=document_job_dto.document_end_page,
        )

    def _replace_or_add_extension(self, filename: str, new_extension: str) -> str:
        """
        Replaces the current file extension with a new one, or adds the extension if none exists.

        Args:
            filename (str): The original filename.
            new_extension (str): The new extension to use (with or without the leading dot).

        Returns:
            str: The filename with the new extension.
        """
        if not new_extension.startswith("."):
            new_extension = "." + new_extension

        base, _ = os.path.splitext(filename)
        return base + new_extension

    def _extract_text_from_docx(self, docx_bytes: bytes) -> str:
        """
        Extract text from a DOCX file given as bytes.
        """
        with BytesIO(docx_bytes) as file_obj:
            doc = Document(file_obj)
            text = "\n".join(para.text for para in doc.paragraphs)
        return text
