from dependency_injector import containers, providers

from accessor.content.content_accessor import ContentAccessor
from accessor.document.document_accessor import Document<PERSON>ccessor
from accessor.image import ImageAccessor
from accessor.reference import ReferenceAccessor
from engine.analysis.analysis_engine import AnalysisEngine
from engine.synthesis.config import InitServices
from engine.synthesis.data_access import DataAccess
from engine.synthesis.document_processor import DocumentProcessor
from engine.synthesis.synthesis_engine import SynthesisEngine
from engine.synthesis.text_extractor import TextExtractor
from engine.transformer import TransformEngine
from manager.feed.document_job_processor import DocumentJobProcessor
from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY


class CommandContainer(containers.DeclarativeContainer):
    # Define the shared services
    document_accessor = providers.Singleton(
        DocumentAccessor,
        access_key=AWS_FEED_MANAGER_ACCESS_KEY,
        secret_key=AWS_FEED_MANAGER_SECRET_KEY,
    )
    document_job_processor = providers.Singleton(DocumentJobProcessor)

    # Register `data_access` and `text_extractor` as singletons
    data_access = providers.Singleton(DataAccess)
    text_extractor = providers.Singleton(
        TextExtractor,
        logger_name=providers.Dependency(),  # Make `logger_name` a dependency
    )

    document_processor = providers.Factory(
        DocumentProcessor,
        pinecone_client=lambda: InitServices.init_pinecone(),
        data_access=data_access,  # Inject `data_access`
        text_extractor=text_extractor,  # Inject `text_extractor`
    )
    analysis_engine = providers.Singleton(AnalysisEngine)
    synthesis_engine = providers.Singleton(SynthesisEngine)
    content_accessor = providers.Singleton(ContentAccessor)
    transform_engine = providers.Singleton(TransformEngine)
    image_accessor = providers.Singleton(ImageAccessor)
    reference_accessor = providers.Singleton(ReferenceAccessor)
