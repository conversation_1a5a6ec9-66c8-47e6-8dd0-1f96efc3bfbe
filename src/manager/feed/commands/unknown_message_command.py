from manager.feed.commands.command import Command


class UnknownMessageCommand(Command):
    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

    def execute(self):
        # Log the unknown message type
        self.logger.warning(
            f"Received unknown message type. document_id: {self.document_id}, job_id: {self.job_id}"
        )
        # Optionally perform further action, like alerting or tracking
        self._delete_message(self.receipt_handle)  # Delete the message after logging
