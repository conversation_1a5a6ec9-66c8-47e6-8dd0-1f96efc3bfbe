# Unused imports are required for dynamic loading.
from manager.feed.commands.generate_acceptance_criteria_command import (
    GenerateAcceptanceCriteriaCommand,
)
from manager.feed.commands.generate_federal_register_change_statements_command import (
    GenerateFederalRegisterChangeStatementsCommand,
)
from manager.feed.commands.generate_policy_requirement_comparison_command import (
    GeneratePolicyRequirementComparisonCommand,
)
from manager.feed.commands.generate_test_cases_command import GenerateTestCasesCommand
from manager.feed.commands.generate_unique_requirements_command import (
    GenerateUniqueRequirementsCommand,
)
from manager.feed.commands.generate_user_stories_command import (
    GenerateUserStoriesCommand,
)
from manager.feed.commands.optimize_document_command import OptimizeDocumentCommand
from manager.feed.commands.process_document_chunks_created_command import (
    ProcessDocumentChunksCreatedCommand,
)
from manager.feed.commands.process_document_generate_chunk_images_command import (
    ProcessDocumentGenerateChunkImagesCommand,
)
from manager.feed.commands.process_document_job_command import ProcessDocumentJobCommand
from manager.feed.commands.regenerate_statements_command import (
    RegenerateStatementsCommand,
)
from shared.configs.command_map import COMMAND_MAP


class CommandFactory:
    def __init__(self, dependency_factory, logger):
        self.dependency_factory = dependency_factory
        self.logger = logger

    def get_command(
        self,
        message_type,
        # Optional params
        document_id=None,
        job_id=None,
        receipt_handle=None,
        sqs_client=None,
        sns_client=None,
        chunk_id=None,
        domain_id=None,
    ):
        command_class_name = COMMAND_MAP.get(message_type)
        if not command_class_name:
            raise ValueError(f"Unsupported message type: {message_type}")

        self.logger.debug(f"Resolved command class name: {command_class_name}")

        command_class = globals().get(command_class_name)
        if not command_class:
            raise ValueError(f"Command class not found for: {command_class_name}")

        self.logger.debug(f"Resolved command class: {command_class}")

        # Add this log to inspect parameters being passed to the command
        self.logger.debug(
            f"Instantiating {command_class_name} with parameters: "
            f"document_id={document_id}, job_id={job_id}, receipt_handle={receipt_handle}, "
            f"sqs_client={sqs_client}, sns_client={sns_client}, chunk_id={chunk_id}, domain_id={domain_id}"
        )

        # Pass in constructor args to the derived class
        return command_class(
            dependency_factory=self.dependency_factory,
            document_id=document_id,
            job_id=job_id,
            receipt_handle=receipt_handle,
            sqs_client=sqs_client,
            sns_client=sns_client,
            logger=self.logger,
            chunk_id=chunk_id,
            domain_id=domain_id,
        )
