from langfuse.decorators import observe

from manager.feed.commands.command import Command
from util.langfuse.decorators import trace_context


class GenerateUniqueRequirementsCommand(Command):
    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()

    @observe(name="generate_unique_statements_bulk")
    @trace_context(["document_id", "job_id"])
    def execute(self):
        try:
            self._process_generate_unique_statements()
            self.logger.debug(
                "Starting process of generating requirement identifiers for "
                "document_id: %s and job_id: %s",
                self.document_id,
                self.job_id,
            )
            self.document_accessor.generate_requirement_identifiers(self.document_id)
            self.document_accessor.job_ready_for_requirement_curation(self.job_id)
        except Exception as e:
            self.logger.exception(
                "Error generating unique statements. Setting job to error status: %s", e
            )
            self.document_accessor.job_error(self.job_id)
        finally:
            self._delete_message(self.receipt_handle)

    def _process_generate_unique_statements(self):
        self.logger.info(
            "Starting process of generating unique requirements for document_id: %s \
                         and job_id: %s",
            self.document_id,
            self.job_id,
        )
        dto = self.document_accessor.prepare_document_in_chunk_curation(
            self.document_id, self.job_id
        )
        if not dto:
            raise ValueError(
                f"Unable to prepare document in chunk curation. document_id: \
                {self.document_id} and job_id: {self.job_id}"
            )
        self.document_accessor.job_generating_requirements(self.job_id)

        self.synthesis_engine.generate_unique_statements(dto)
