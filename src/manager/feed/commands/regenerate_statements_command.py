from langfuse.decorators import observe

from manager.feed.commands.command import Command
from util.langfuse.decorators import trace_context
from shared.enums.statement_type_enum import StatementTypeEnum


class RegenerateStatementsCommand(Command):
    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.chunk_id = chunk_id

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.content_accessor = self.dependency_factory.get_content_accessor()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()

    @observe(name="regenerate_chunk_statements_bulk")
    @trace_context(["chunk_id"])
    def execute(self):
        try:
            self._process_regenerate_statements()
            self.document_accessor.job_ready_for_chunk_curation(self.job_id)
        except Exception as e:
            self.logger.exception(
                "Error regenerating statements. Setting job to error status: %s", e
            )
            self.document_accessor.job_error(self.job_id)
        finally:
            self._delete_message(self.receipt_handle)

    def _process_regenerate_statements(self):
        self.logger.info(
            "Starting processing of regenerating statements for chunk_id: %s", self.chunk_id
        )
        self.content_accessor.remove_chunk_statements(self.chunk_id)

        # Retrieve the job_type_id
        job_type_id = self.document_accessor.get_job_type_id_by_document(self.document_id)
        
        self.synthesis_engine.generate_chunk_statements(
            document_id=self.document_id, chunk_id=self.chunk_id, job_type_id=job_type_id
        )
