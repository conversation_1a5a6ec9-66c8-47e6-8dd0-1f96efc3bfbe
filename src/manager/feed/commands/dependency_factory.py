from manager.feed.commands.command_container import CommandContainer


class DependencyFactory:
    def __init__(self, logger_name):
        self._analysis_engine = None
        self._synthesis_engine = None
        self._document_accessor = None
        self._document_job_processor = None
        self._document_processor = None
        self._content_accessor = None
        self._image_accessor = None
        self._reference_accessor = None
        self._transform_engine = None
        self.logger_name = logger_name
        self._service_container = CommandContainer()

    def get_analysis_engine(self):
        if self._analysis_engine is None:
            self._analysis_engine = self._service_container.analysis_engine(
                logger_name=self.logger_name
            )
        return self._analysis_engine

    def get_synthesis_engine(self):
        if self._synthesis_engine is None:
            self._synthesis_engine = self._service_container.synthesis_engine(
                logger_name=self.logger_name
            )
        return self._synthesis_engine

    def get_transform_engine(self):
        if self._transform_engine is None:
            self._transform_engine = self._service_container.transform_engine(
                logger_name=self.logger_name
            )
        return self._transform_engine

    def get_document_accessor(self):
        if self._document_accessor is None:
            self._document_accessor = self._service_container.document_accessor(
                logger_name=self.logger_name
            )
        return self._document_accessor

    def get_document_job_processor(self):
        if self._document_job_processor is None:
            self._document_job_processor = self._service_container.document_job_processor(
                logger_name=self.logger_name
            )
        return self._document_job_processor

    def get_document_processor(self):
        if self._document_processor is None:
            self._document_processor = self._service_container.document_processor(
                logger_name=self.logger_name,  # Pass `logger_name` to `text_extractor`
                text_extractor=self._service_container.text_extractor(logger_name=self.logger_name),
                data_access=self._service_container.data_access(),
            )
        return self._document_processor

    def get_content_accessor(self):
        if self._content_accessor is None:
            self._content_accessor = self._service_container.content_accessor(
                logger_name=self.logger_name
            )
        return self._content_accessor

    def get_image_accessor(self):
        if self._image_accessor is None:
            self._image_accessor = self._service_container.image_accessor(
                logger_name=self.logger_name
            )
        return self._image_accessor

    def get_reference_accessor(self):
        if self._reference_accessor is None:
            self._reference_accessor = self._service_container.reference_accessor(
                logger_name=self.logger_name
            )
        return self._reference_accessor
