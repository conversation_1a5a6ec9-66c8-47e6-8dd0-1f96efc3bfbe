import json
import datetime
from typing import Any, Dict

from langfuse.decorators import observe

from accessor.reference.dto import (
    OriginalRegulationSectionDTO,
    RegulationSectionComparisonDTO,
    UpdatedRegulationSectionDTO,
)
from manager.feed.commands.command import Command
from shared.enums import JobStatusEnum, StatementTypeEnum
from shared.models import Chun<PERSON>, JobWorkflowStatusEnum, WorkflowTypeEnum
from util.config import AWS_FEED_MANAGER_QUEUE
from util.langfuse.decorators import trace_context


class GenerateFederalRegisterChangeStatementsCommand(Command):

    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        domain_id,
        chunk_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.domain_id = domain_id

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.content_accessor = self.dependency_factory.get_content_accessor()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()
        self.reference_accessor = self.dependency_factory.get_reference_accessor()

    @observe(name="generate_federal_register_change_statements")
    @trace_context(["document_id", "job_id"])
    def execute(self):

        self._process_federal_register_changes()
        self.document_accessor.update_job_status(
            self.job_id, JobStatusEnum.SUBTOPICS_READY_FOR_CURATION
        )
        self._delete_message(
            self.receipt_handle,
            "Processed generate federal register change statements message.",
        )

    def _process_federal_register_changes(self):

        self.logger.info(
            "Starting process of generating federal register change statements for document_id: %s, job_id: %s, and domain_id: %s",
            self.document_id,
            self.job_id,
            self.domain_id,
        )

        self.document_accessor.update_job_status(self.job_id, JobStatusEnum.INITIAL_PROCESSING)
        visibility_timeout_seconds = 600  # 10 min iteration

        job_workflow = self._get_job_workflow()

        try:
            self.logger.debug(
                "Generating federal register change statements for document_id: %s, job_id: %s and domain_id: %s",
                self.document_id,
                self.job_id,
                self.domain_id,
            )

            document = self.content_accessor.get_document_by_id(self.document_id)
            updated_sections = self.reference_accessor.get_updated_sections_from_federal_register(
                document.s3_location
            )
            original_sections = self.reference_accessor.get_original_sections_from_federal_register(
                updated_sections
            )

            # In case the job failed in progress, clear out all chunks and start over
            # There isn't a good way of "resuming" this job because you can't create the change statements
            # without pulling from the Federal Register api again so might as well run the whole process again
            self.content_accessor.delete_all_chunks_for_document(self.document_id)
            chunks = self._save_updated_sections_as_document_chunks(updated_sections)

            # This process will generate the following types of change statements:
            # - sections that are the exact same (marked with 'No changes detected')
            # - sections in updated version but not in original version (marked with 'New addition')
            # - sections that have different verbiage between original and updated versions
            #   and the updated sections have different text
            # - sections in original version but not in updated version (deleted requirements)
            self._compare_updated_sections_to_original_sections(
                chunks, updated_sections, original_sections, visibility_timeout_seconds
            )

            self.document_accessor.update_job_workflow(
                job_workflow, JobWorkflowStatusEnum.COMPLETED
            )

        except Exception:
            self._handle_job_failure(job_workflow)
            raise

    def _get_job_workflow(self):
        # Find the job workflow if one exists, if not create a new one
        job_workflow = self.document_accessor.get_job_workflow(
            self.job_id, WorkflowTypeEnum.IDENTIFYING_CHANGE_STATEMENTS
        )
        if job_workflow:
            self.logger.info(
                "Resuming existing job with Job Workflow status of %s.", job_workflow.status
            )
        else:
            job_workflow = self.document_accessor.create_job_workflow(
                self.job_id, WorkflowTypeEnum.IDENTIFYING_CHANGE_STATEMENTS, run_count=1
            )
        return job_workflow

    def _save_updated_sections_as_document_chunks(
        self, sections: list[UpdatedRegulationSectionDTO]
    ) -> list[Chunk]:
        for section in sections:
            self.content_accessor.save_chunk(
                document_id=self.document_id,
                formatted_text=section.content,
                text_content=section.content,
                vector_id=None,  # we aren't storing this information in Pinecone
            )

        chunks = self.content_accessor.get_chunks_by_document(self.document_id)

        for chunk in chunks:
            self._save_identified_subtopics(chunk)

        return chunks

    def _save_identified_subtopics(self, chunk: Chunk):
        # 1. send in the list of subtopics + the chunk text to openAI
        # 2. get back a list of subtopics and parse them
        # 3. save them as Identified Subtopic statements

        business_id = self.content_accessor.get_document_by_id(chunk.document_id).business_id
        subtopics = self.synthesis_engine.get_chunk_subtopics(chunk, business_id)
        self.synthesis_engine.save_identified_subtopics(chunk.chunk_id, subtopics)

    def _compare_updated_sections_to_original_sections(
        self,
        chunks: list[Chunk],
        updated_sections: list[UpdatedRegulationSectionDTO],
        original_sections: list[OriginalRegulationSectionDTO],
        visibility_timeout_seconds: int,
    ):
        start_time = datetime.datetime.now(datetime.timezone.utc)
        self._extend_message_visibility(visibility_timeout_seconds)

        combined_original_sections_text = " ".join(section.content for section in original_sections)

        all_original_requirements = self.synthesis_engine.extract_all_original_requirements(
            combined_original_sections_text
        )

        # Now compare each updated section against the complete original text
        self.logger.info("Analyzing changes for each section...")
        for chunk, section in zip(chunks, updated_sections):  # Start from the second item
            self._generate_change_statements_for_updated_section(
                section, all_original_requirements, chunk.chunk_id
            )

        self._identify_deleted_requirements(all_original_requirements, original_sections)

        end_time = datetime.datetime.now(datetime.timezone.utc)
        self.logger.debug(
            "Time taken to generate change statements for document_id %s: %s seconds",
            self.document_id,
            (end_time - start_time).total_seconds(),
        )

    def _extend_message_visibility(self, timeout_seconds: int):
        self.sqs_client.change_message_visibility(
            QueueUrl=AWS_FEED_MANAGER_QUEUE,
            ReceiptHandle=self.receipt_handle,
            VisibilityTimeout=timeout_seconds,
        )

    def _generate_change_statements_for_updated_section(
        self,
        updated_section: UpdatedRegulationSectionDTO,
        all_original_requirements: list,
        chunk_id: int,
    ):

        self.logger.info(f"Analyzing changes in section {updated_section.section}...")

        section_dto = RegulationSectionComparisonDTO(
            document_id=self.document_id,
            chunk_id=chunk_id,
            updated_section_title=updated_section.title,
            updated_section_name=updated_section.section,
            all_original_requirements=all_original_requirements,
            updated_section_info=updated_section,
        )

        self.synthesis_engine.generate_change_statements_for_document(section_dto)

    def _identify_deleted_requirements(
        self, all_original_requirements: list, original_sections: list[OriginalRegulationSectionDTO]
    ):
        self.synthesis_engine.generate_deleted_requirements_for_document(
            self.document_id, all_original_requirements, original_sections
        )

    def _handle_job_failure(self, job_workflow):
        self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.FAILED)
        if job_workflow.run_count == Command.MAX_RUN_COUNT:
            self.document_accessor.job_error(self.job_id)
