# This isn't required, but good idea to have it to centralize execution logic, ensure consistency, or
# easily add other common behavior (logging, error handling, retries) in one place.
from manager.feed.commands.command import Command


class CommandHandler:
    def __init__(self, logger):
        self.logger = logger

    def handle(self, command: Command):
        try:
            self.logger.info("Executing command...")
            command.execute()  # The command handles message-specific logic
        except Exception as e:
            self.logger.exception(f"Error executing command: {e}")
