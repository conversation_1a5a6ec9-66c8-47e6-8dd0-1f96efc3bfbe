import datetime

from langfuse.decorators import observe

from manager.feed.commands.command import Command
from shared.enums.job_enums import JobStatusEnum
from shared.models import JobWorkflowStatusEnum, WorkflowTypeEnum
from util.config import AWS_FEED_MANAGER_QUEUE
from util.langfuse.decorators import trace_context


class GenerateTestCasesCommand(Command):

    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.content_accessor = self.dependency_factory.get_content_accessor()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()

    @observe(name="generate_test_cases_bulk")
    @trace_context(["document_id", "job_id"])
    def execute(self):
        self._process_generate_bulk_test_cases()
        self.logger.debug(
            "Generating test cases for document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )
        self.document_accessor.generate_test_case_identifiers(self.document_id)
        self.document_accessor.update_job_status(self.job_id, JobStatusEnum.ARTIFACTS_ARE_READY)
        self._delete_message(self.receipt_handle, "Processed generate test cases message.")

    def _process_generate_bulk_test_cases(self):

        self.logger.info(
            "Starting process of generating test cases for document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )

        self.document_accessor.update_job_status(self.job_id, JobStatusEnum.GENERATING_TEST_CASES)
        visibility_timeout_seconds = 14400  # 4-hour iteration

        job_workflow = self._get_job_workflow()

        if job_workflow.status == JobWorkflowStatusEnum.COMPLETED:
            return

        try:
            resume_from_ids = self._handle_resume_conditions(job_workflow)

            ordered_statement_types = self.content_accessor.get_ordered_statement_types(
                self.document_id
            )
            start_index = self._find_tc_start_index_for_processing(
                ordered_statement_types, resume_from_ids
            )

            self._process_all_statement_types(
                ordered_statement_types,
                start_index,
                resume_from_ids,
                visibility_timeout_seconds,
            )

            self.document_accessor.update_job_workflow(
                job_workflow, JobWorkflowStatusEnum.COMPLETED
            )

        except Exception:
            self._handle_job_failure(job_workflow)
            raise

    def _prepare_document_for_test_case_generation(self, document_id, job_id):
        document = self.document_accessor.prepare_document_in_requirement_curation(
            document_id, job_id
        )
        if not document:
            raise ValueError(
                "Unable to prepare document in requirement curation. "
                f"document_id: {document_id} and job_id: {job_id}"
            )
        self.document_accessor.update_job_status(job_id, JobStatusEnum.GENERATING_TEST_CASES)
        return document

    # Workflow helper methods
    def _get_job_workflow(self):
        job_workflow = self.document_accessor.get_job_workflow(
            self.job_id, WorkflowTypeEnum.GENERATE_TEST_CASES
        )
        if job_workflow:
            self.logger.info(
                "Resuming existing job with Job Workflow status of %s.", job_workflow.status
            )
        else:
            job_workflow = self.document_accessor.create_job_workflow(
                self.job_id, WorkflowTypeEnum.GENERATE_TEST_CASES, run_count=1
            )
        return job_workflow

    def _handle_resume_conditions(self, job_workflow):
        if job_workflow.status in [
            JobWorkflowStatusEnum.IN_PROGRESS,
            JobWorkflowStatusEnum.FAILED,
            JobWorkflowStatusEnum.RETRYING,
        ]:
            return self._find_resume_point_and_delete_artifacts()
        else:
            if job_workflow.status == JobWorkflowStatusEnum.STARTING:
                self.document_accessor.update_job_workflow(
                    job_workflow, JobWorkflowStatusEnum.IN_PROGRESS
                )
            return None

    def _find_resume_point_and_delete_artifacts(self):
        (max_statement_type_id, max_subtopic_id, max_acceptance_criteria_id) = (
            self.content_accessor.find_test_case_generation_resume_point(self.document_id)
        )
        if max_acceptance_criteria_id is not None:
            self.content_accessor.delete_test_case_artifacts(
                max_acceptance_criteria_id, max_statement_type_id, max_subtopic_id
            )
            return {
                "statement_type_id": max_statement_type_id,
                "subtopic_id": max_subtopic_id,
                "acceptance_criteria_id": max_acceptance_criteria_id,
            }
        return None

    def _find_tc_start_index_for_processing(self, ordered_statement_types, resume_from_ids):
        if resume_from_ids:
            return self.find_tc_start_index(ordered_statement_types, resume_from_ids)
        return 0

    def find_tc_start_index(self, ordered_statement_types, resume_from_ids):
        """
        Finds the starting index in the ordered_statement_types list where the statement_type_id
        is greater than or equal to the statement_type_id in resume_from_ids.

        :param ordered_statement_types: List of statement types ordered by some criteria.
        :param resume_from_ids: Dictionary containing the 'statement_type_id' to resume from.
        :return: The index to start from.
        """
        return next(
            (
                i
                for i, statement_type in enumerate(ordered_statement_types)
                if statement_type.statement_type_id >= resume_from_ids["statement_type_id"]
            ),
            len(ordered_statement_types),
        )

    def _handle_job_failure(self, job_workflow):
        self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.FAILED)
        if job_workflow.run_count == Command.MAX_RUN_COUNT:
            self.document_accessor.job_error(self.job_id)

    # Artifact generation helper methods
    def _process_all_statement_types(
        self,
        ordered_statement_types,
        start_index,
        resume_from_ids,
        visibility_timeout_seconds,
    ):
        for statement_type in ordered_statement_types[start_index:]:
            ordered_subtopics = self.content_accessor.get_subtopics_ordered(
                self.document_id, statement_type.statement_type_id  # type: ignore
            )
            subtopic_start_index = self._find_subtopic_start_index(
                ordered_subtopics, resume_from_ids
            )
            for subtopic in ordered_subtopics[subtopic_start_index:]:
                self._process_subtopic(
                    statement_type,
                    subtopic,
                    resume_from_ids,
                    visibility_timeout_seconds,
                )

    def _find_subtopic_start_index(self, ordered_subtopics, resume_from_ids):
        if resume_from_ids:
            return next(
                (
                    i
                    for i, subtopic in enumerate(ordered_subtopics)
                    if subtopic.subtopic_id >= resume_from_ids["subtopic_id"]
                ),
                len(ordered_subtopics),
            )
        return 0

    def _process_subtopic(
        self,
        statement_type,
        subtopic,
        resume_from_ids,
        visibility_timeout_seconds,
    ):
        start_time = datetime.datetime.now(datetime.timezone.utc)
        self.sqs_client.change_message_visibility(
            QueueUrl=AWS_FEED_MANAGER_QUEUE,
            ReceiptHandle=self.receipt_handle,
            VisibilityTimeout=visibility_timeout_seconds,
        )

        acceptance_criteria_list = self._get_acceptance_criteria(
            subtopic, statement_type, resume_from_ids
        )

        if len(acceptance_criteria_list) > 0:

            for acceptance_criteria in acceptance_criteria_list:

                # Check if there are any existing TC for this acceptance criteria
                # If so, skip this acceptance criteria to avoid dupes
                if self.content_accessor.check_acceptance_criteria_test_cases_exists(
                    acceptance_criteria.acceptance_criteria_id
                ):
                    continue

                test_case_generation_dto = self.document_accessor.build_test_case_generation_dto(
                    acceptance_criteria.acceptance_criteria_id
                )

                self.synthesis_engine.generate_test_cases_for_acceptance_criteria(
                    test_case_generation_dto,
                )

        end_time = datetime.datetime.now(datetime.timezone.utc)
        self.logger.debug(
            "Time taken to process subtopic_id %s for document_id %s: %s seconds",
            subtopic.subtopic_id,
            self.document_id,
            (end_time - start_time).total_seconds(),
        )

    def _get_acceptance_criteria(self, subtopic, statement_type, resume_from_ids):
        acceptance_criteria = self.content_accessor.get_acceptance_criteria_ordered(
            self.document_id,
            subtopic.subtopic_id,  # type: ignore
            statement_type.statement_type_id,  # type: ignore
        )
        if resume_from_ids and subtopic.subtopic_id == resume_from_ids["subtopic_id"]:
            start_index = next(
                (
                    i
                    for i, acceptance_criteria in enumerate(acceptance_criteria)
                    if acceptance_criteria.acceptance_criteria_id
                    >= resume_from_ids["acceptance_criteria_id"]
                ),
                len(acceptance_criteria),
            )
            return acceptance_criteria[start_index:]
        return acceptance_criteria
