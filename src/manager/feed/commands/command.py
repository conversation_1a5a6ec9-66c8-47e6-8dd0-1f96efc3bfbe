import json
import uuid
from abc import ABC, abstractmethod
from logging import Logger

from manager.feed.commands.dependency_factory import DependencyFactory
from util.config import AWS_FEED_MANAGER_QUEUE


class Command(ABC):

    @abstractmethod
    def execute(self):
        """All commands must implement this method."""

    MAX_RUN_COUNT = 5

    def __init__(
        self,
        dependency_factory: DependencyFactory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger: Logger,
    ):
        self.dependency_factory = dependency_factory
        self.document_id = document_id
        self.job_id = job_id
        self.receipt_handle = receipt_handle
        self.sqs_client = sqs_client
        self.sns_client = sns_client
        self.logger = logger

    def _delete_message(self, receipt_handle, log_message=None):
        self.sqs_client.delete_message(
            QueueUrl=AWS_FEED_MANAGER_QUEUE, ReceiptHandle=receipt_handle
        )

        if log_message:
            self.logger.debug(log_message)
        else:
            self.logger.debug(f"Deleting message from queue {receipt_handle}")

    def _publish_sns_message(self, topic_arn, message):
        message_deduplication_id = str(uuid.uuid4())
        try:
            response = self.sns_client.publish(
                TopicArn=topic_arn,
                Message=json.dumps(message),
                MessageGroupId=str(uuid.uuid4()),
                MessageDeduplicationId=message_deduplication_id,
            )

            http_status = response["ResponseMetadata"]["HTTPStatusCode"]
            message_id = response.get("MessageId", "")

            if http_status == 200 and message_id:
                self.logger.info("Message published successfully with MessageId: %s", message_id)
                return True
            self.logger.error("Failed to publish message: %s", response)
            return False
        except Exception as e:
            self.logger.exception("Error when attempting to publish message: %s", e, exc_info=True)
            return False
