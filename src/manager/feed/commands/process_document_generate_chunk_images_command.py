"""
Module for processing document chunks and generating images.

This module defines a command to process document chunks by generating images for each chunk.
The command uses various dependencies to access document data, transform PDFs,
and store generated images. It sets the job status based on the success or failure of processing.
"""

import fitz

from accessor.content import ContentAccessor
from accessor.document.document_accessor import DocumentAccessor
from accessor.document.dto.document_job_dto import DocumentJobDTO
from accessor.image import IChunkImageAccessor
from engine.transformer import IPdfTransformer
from engine.transformer.dto import TransformConfig
from manager.feed.commands.command import Command
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.enums.job_enums import JobTypeEnum


class ProcessDocumentGenerateChunkImagesCommand(Command):
    """
    Command to generate images from document chunks.
    This command processes document chunks by creating images for each chunk
    based on bounding boxes. Images are stored in the designated storage, and
    job status is updated accordingly.
    """

    def __init__(
        self,
        dependency_factory: DependencyFactory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        """Initialize the ProcessDocumentGenerateChunkImagesCommand with required dependencies."""

        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.content_accessor: ContentAccessor = self.dependency_factory.get_content_accessor()
        self.document_accessor: DocumentAccessor = self.dependency_factory.get_document_accessor()
        self.transform_engine: IPdfTransformer = self.dependency_factory.get_transform_engine()
        self.image_accessor: IChunkImageAccessor = self.dependency_factory.get_image_accessor()

    def execute(self):
        """
        Execute the command to process document chunks and generate images.

        This method:
        - Generates document images
        - Updates the job status to ready for chunk curation if successful.
        - Sets the job status to error if an exception occurs.
        """
        try:
            document = self.document_accessor.prepare_document_in_initial_processing(
                self.document_id, self.job_id
            )
            if not document or not document.document_bytes:
                raise ValueError(
                    f"Document with ID {self.document_id} not found or missing PDF bytes."
                )
            self._generate_document_images(document)
            if document.job_type_id == JobTypeEnum.PROCEDURE_IMPORT.value:
                self.document_accessor.job_complete(self.job_id)
            else:
                self.document_accessor.job_ready_for_subtopic_curation(self.job_id)
        except Exception as e:
            self.logger.exception(
                "Error processing document chunks. Setting job to error status: %s", e
            )
            self.document_accessor.job_error(self.job_id)
        finally:
            self._delete_message(self.receipt_handle)

    # Check what type of job type and then execute different code for creating 1 page images

    def _generate_document_images(self, document: DocumentJobDTO):
        """
        Generate images for each chunk of the document.

        This method processes each chunk, retrieving bounding box data for creating images.
        It transforms each chunk of the PDF into an image and stores the image with
        associated metadata.

        Raises:
            ValueError: If the document or its PDF bytes are missing.
        """
        self.logger.info(
            "Starting process to generate chunk images for document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )
        # Retrieve document data and PDF bytes

        with fitz.open(stream=document.document_bytes, filetype="pdf") as pdf_document:

            chunks = self.content_accessor.get_chunks_by_document(self.document_id)

            for chunk in chunks:
                self.logger.info("Processing chunk_id: %s", chunk.chunk_id)

                document_blocks = chunk.chunk_metadata.get("blocks", [])
                if not document_blocks:
                    self.logger.warning("No bounding boxes found for chunk_id: %s", chunk.chunk_id)
                    continue

                try:
                    image_data = self.transform_engine.transform_pdf_to_cropped_image(
                        doc=pdf_document, document_blocks=document_blocks, config=TransformConfig()
                    )

                    image_id = self.image_accessor.store_chunk_image(
                        file_content=image_data.image_bytes,
                        file_name=f"{document.document_id}_chunk_{chunk.chunk_id}.png",
                        metadata={
                            "chunk_id": chunk.chunk_id,
                            "page_numbers": list(
                                set(block["page_number"] for block in document_blocks)
                            ),
                        },
                        chunk_id=chunk.chunk_id,
                    )

                    self.logger.info(
                        "Stored image_id: %s for chunk_id: %s", image_id, chunk.chunk_id
                    )

                except Exception as e:
                    self.logger.exception(
                        "Failed to process chunk_id: %s due to error: %s", chunk.chunk_id, e
                    )
                    continue
