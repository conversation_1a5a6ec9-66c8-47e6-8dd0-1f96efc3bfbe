import datetime
from typing import List

from langfuse.decorators import observe

from manager.feed.commands.command import Command
from shared.models import JobWorkflowStatusEnum, WorkflowTypeEnum
from shared.models.unique_statement import UniqueStatement
from shared.models.workflow_type_enum import WorkflowTypeEnum
from util.config import AWS_FEED_MANAGER_QUEUE
from util.langfuse.decorators import trace_context


class GenerateRequirementsBundleCommand(Command):
    def __init__(
        self,
        dependency_factory,
        document_id,
        job_id,
        receipt_handle,
        sqs_client,
        sns_client,
        logger,
        chunk_id=None,
        domain_id=None,
    ):
        super().__init__(
            dependency_factory, document_id, job_id, receipt_handle, sqs_client, sns_client, logger
        )

        self.document_accessor = self.dependency_factory.get_document_accessor()
        self.content_accessor = self.dependency_factory.get_content_accessor()
        self.synthesis_engine = self.dependency_factory.get_synthesis_engine()

    @observe(name="generate_requirements_bundle_bulk")
    @trace_context(["document_id", "job_id"])
    def execute(self):
        self._process_generate_requirements_bundle()
        self.logger.debug(
            "Generating artifact identifiers for document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )
        self.document_accessor.generate_artifact_identifiers(self.document_id)
        self.document_accessor.job_complete(self.job_id)
        self._delete_message(self.receipt_handle, "Processed generate requirements bundle message.")

    def _process_generate_requirements_bundle(self):
        self.logger.info(
            "Starting process of generating requirements bundle for document_id: %s and job_id: %s",
            self.document_id,
            self.job_id,
        )

        dto = self.document_accessor.prepare_document_in_requirement_curation(
            self.document_id, self.job_id
        )
        if not dto:
            raise ValueError(
                "Unable to prepare document in requirement curation. "
                f"document_id: {self.document_id} and job_id: {self.job_id}"
            )

        visibility_timeout_seconds = 14400  # 4-hour iteration

        job_workflow = self.document_accessor.get_job_workflow(
            self.job_id, WorkflowTypeEnum.GENERATE_REQUIREMENTS_BUNDLE
        )

        if job_workflow:
            self.logger.info(
                "Resuming existing job with Job Workflow status of %s.", job_workflow.status
            )
        else:
            job_workflow = self.document_accessor.create_job_workflow(
                self.job_id, WorkflowTypeEnum.GENERATE_REQUIREMENTS_BUNDLE, run_count=1
            )

        if job_workflow.status == JobWorkflowStatusEnum.COMPLETED:
            return
        try:
            resume_from_ids = None
            new_status = None
            if job_workflow.status in [
                JobWorkflowStatusEnum.IN_PROGRESS,
                JobWorkflowStatusEnum.FAILED,
                JobWorkflowStatusEnum.RETRYING,
            ]:
                (max_statement_type_id, max_subtopic_id, max_unique_statement_id) = (
                    self.content_accessor.find_bundle_resume_point(self.document_id)
                )

                if (
                    max_unique_statement_id is not None
                    and max_statement_type_id is not None
                    and max_subtopic_id is not None
                ):
                    self.content_accessor.delete_artifacts(
                        max_unique_statement_id, max_statement_type_id, max_subtopic_id
                    )
                    resume_from_ids = {
                        "statement_type_id": max_statement_type_id,
                        "subtopic_id": max_subtopic_id,
                        "unique_statement_id": max_unique_statement_id,
                    }

                job_workflow.run_count += 1
                new_status = JobWorkflowStatusEnum.RETRYING

            if job_workflow.status == JobWorkflowStatusEnum.STARTING:
                new_status = JobWorkflowStatusEnum.IN_PROGRESS

            self.document_accessor.update_job_workflow(job_workflow, new_status)

            ordered_statement_types = self.content_accessor.get_ordered_statement_types(
                self.document_id
            )
            if resume_from_ids:
                start_index = next(
                    (
                        i
                        for i, statement_type in enumerate(ordered_statement_types)
                        if statement_type.statement_type_id >= resume_from_ids["statement_type_id"]
                    ),
                    len(ordered_statement_types),
                )
            else:
                start_index = 0
            for statement_type in ordered_statement_types[start_index:]:
                ordered_subtopics = self.content_accessor.get_subtopics_ordered(
                    self.document_id, statement_type.statement_type_id  # type: ignore
                )
                if resume_from_ids:
                    subtopic_start_index = next(
                        (
                            i
                            for i, subtopic in enumerate(ordered_subtopics)
                            if subtopic.subtopic_id >= resume_from_ids["subtopic_id"]
                        ),  # type: ignore
                        len(ordered_subtopics),
                    )
                else:
                    subtopic_start_index = 0
                for subtopic in ordered_subtopics[subtopic_start_index:]:
                    start_time = datetime.datetime.now(datetime.timezone.utc)
                    self.sqs_client.change_message_visibility(
                        QueueUrl=AWS_FEED_MANAGER_QUEUE,
                        ReceiptHandle=self.receipt_handle,
                        VisibilityTimeout=visibility_timeout_seconds,
                    )

                    unique_statements: List[UniqueStatement] = (
                        self.content_accessor.get_unique_statements_ordered(
                            self.document_id,
                            subtopic.subtopic_id,  # type: ignore
                            statement_type.statement_type_id,  # type: ignore
                        )
                    )
                    if resume_from_ids and subtopic.subtopic_id == resume_from_ids["subtopic_id"]:
                        start_index = next(
                            (
                                i
                                for i, statement in enumerate(unique_statements)
                                if statement.unique_statement_id
                                >= resume_from_ids["unique_statement_id"]
                            ),  # type: ignore
                            len(unique_statements),
                        )
                        unique_statements = unique_statements[start_index:]
                        resume_from_ids = None

                    if len(unique_statements) > 0:
                        self.synthesis_engine.generate_requirements_bundle(
                            document_id=self.document_id,
                            business_id=dto.document_business_id,
                            statement_type_id=statement_type.statement_type_id,  # type: ignore
                            subtopic_id=subtopic.subtopic_id,  # type: ignore
                            unique_statements=unique_statements,
                        )
                    end_time = datetime.datetime.now(datetime.timezone.utc)
                    time_taken = end_time - start_time
                    self.logger.debug(
                        "Time taken to process subtopic_id %s for document_id %s: %s seconds",
                        subtopic.subtopic_id,
                        self.document_id,
                        time_taken.total_seconds(),
                    )
            self.document_accessor.update_job_workflow(
                job_workflow, JobWorkflowStatusEnum.COMPLETED
            )
        except Exception:
            self.document_accessor.update_job_workflow(job_workflow, JobWorkflowStatusEnum.FAILED)
            raise
