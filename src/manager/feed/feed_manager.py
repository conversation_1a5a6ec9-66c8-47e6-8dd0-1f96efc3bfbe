"""
Feed Manager Module

This module defines the FeedManager class, which is responsible for managing
document processing jobs. The FeedManager handles the entire lifecycle of
document processing, including initial processing, chunk creation, and
requirements generation. It interacts with AWS services (SNS and SQS) for
message publishing and queue polling and coordinates with various components
such as DocumentJobProcessor, DocumentAccessor, and SynthesisEngine to
perform the necessary operations.
"""

import json
import logging
import sys
import threading

import boto3
from flask import Flask, jsonify
from langfuse.decorators import observe

from accessor.content import ContentAccessor
from accessor.document.document_accessor import DocumentAccessor
from accessor.identity import IdentityAccessor
from engine.synthesis.synthesis_engine import SynthesisEngine
from manager.feed.commands.command_factory import CommandFactory
from manager.feed.commands.command_handler import CommandHandler
from manager.feed.commands.dependency_factory import DependencyFactory
from shared.exceptions import AuthError
from shared.models.acceptance_criteria import AcceptanceCriteria
from shared.models.test_case import TestCase
from util.audit.user_ctx import UserCtx, set_user_ctx
from util.config import (
    AWS_FEED_MANAGER_ACCESS_KEY,
    AWS_FEED_MANAGER_QUEUE,
    AWS_FEED_MANAGER_REGION,
    AWS_FEED_MANAGER_SECRET_KEY,
    LOG_LEVEL,
    THIRD_PARTY_LOG_LEVEL,
)
from util.database import Session
from util.langfuse.decorators import trace_context

app = Flask(__name__)

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")
identity_accessor = IdentityAccessor(logger_name="burst-api")


class FeedManager:
    """
    FeedManager is responsible for managing document processing jobs, including
    initial processing, chunk creation, and requirements generation. It utilizes
    AWS SNS for publishing messages and AWS SQS for queue polling. The manager
    coordinates with DocumentJobProcessor, DocumentAccessor, and SynthesisEngine
    to handle various processing stages and ensures proper logging throughout
    its operations.
    """

    def __init__(self, logger_name, sqs_client=None):
        self._setup_logging(logger_name)
        self.logger = logging.getLogger(logger_name)
        self.stop_event = threading.Event()
        self.queue_thread = None
        self.document_accessor = DocumentAccessor(
            AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY, logger_name
        )
        self.synthesis_engine = SynthesisEngine(logger_name)

        self.sns_client = boto3.client(
            "sns",
            region_name=AWS_FEED_MANAGER_REGION,
            aws_access_key_id=AWS_FEED_MANAGER_ACCESS_KEY,
            aws_secret_access_key=AWS_FEED_MANAGER_SECRET_KEY,
        )

        self.sqs_client = sqs_client or boto3.client(
            "sqs",
            region_name=AWS_FEED_MANAGER_REGION,
            aws_access_key_id=AWS_FEED_MANAGER_ACCESS_KEY,
            aws_secret_access_key=AWS_FEED_MANAGER_SECRET_KEY,
        )

        self.system_user_ctx = self._set_system_user_context()

    def _set_system_user_context(self):
        system_user = identity_accessor.get_system_user()

        if not system_user or not system_user.enterprise:
            self.logger.error("System user or enterprise not found")
            raise AuthError(
                {
                    "code": "invalid_system_user",
                    "description": "System user or enterprise not properly configured",
                },
                401,
            )

        # Set user context for use across the feed manager
        user_ctx = UserCtx(
            auth0_id=system_user.auth0_id,
            user_id=system_user.user_id,
            email=system_user.email_address,
            name=system_user.first_name + " " + system_user.last_name,
            enterprise_id=system_user.enterprise_id,
            domain_id=system_user.enterprise.domain.domain_id,
        )

        set_user_ctx(user_ctx)

        return user_ctx

    def _setup_logging(self, logger_name):
        log_level = LOG_LEVEL
        third_party_log_level = THIRD_PARTY_LOG_LEVEL

        logging.basicConfig(level=third_party_log_level)

        shared_logger = logging.getLogger(logger_name)
        shared_logger.setLevel(getattr(logging, log_level))

        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level))

        stderr_handler = logging.StreamHandler(sys.stderr)
        stderr_handler.setLevel(logging.ERROR)

        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(lineno)d - %(message)s"
        )
        console_handler.setFormatter(formatter)
        stderr_handler.setFormatter(formatter)

        if shared_logger.hasHandlers():
            shared_logger.handlers.clear()

        shared_logger.addHandler(console_handler)
        shared_logger.addHandler(stderr_handler)

        shared_logger.propagate = False
        shared_logger.debug("Logging setup complete - DEBUG level enabled")

    def _poll_queue_with_user_ctx(self):
        # Reset system user context inside the thread
        set_user_ctx(self.system_user_ctx)
        self._poll_queue()

    def _poll_queue(self):
        self.logger.info("Starting to poll queue.")
        while not self.stop_event.is_set():
            try:
                response = self.sqs_client.receive_message(
                    QueueUrl=AWS_FEED_MANAGER_QUEUE,
                    MaxNumberOfMessages=1,
                    WaitTimeSeconds=20,
                    VisibilityTimeout=43200,  # 12 hours
                )

                messages = response.get("Messages", [])

                if not messages:
                    self.logger.info("No messages found in the queue.")
                    continue

                for message in messages:
                    body = json.loads(message["Body"])
                    self.logger.info("Received message: %s", body)
                    # Direct call for single-threaded processing
                    self._process_message(message)
            except Exception as e:
                self.logger.exception("Error receiving messages: %s", e, exc_info=True)

    def _process_message(self, message):
        try:
            self.logger.debug("Starting message processing")
            sqs_message = json.loads(message["Body"])
            receipt_handle = message["ReceiptHandle"]

            body = json.loads(sqs_message.get("Message", "{}"))
            message_type = body.get("header", {}).get("type")
            document_id = body.get("body", {}).get("document_id")
            job_id = body.get("body", {}).get("job_id")
            chunk_id = body.get("body", {}).get("chunk_id")
            domain_id = body.get("body", {}).get("domain_id")

            if not ((job_id and document_id) or chunk_id):
                self._handle_malformed_message(body, receipt_handle)
                return

            self.logger.info(
                "Processing message. Message type: %s, document_id: %s, job_id: %s",
                message_type,
                document_id,
                job_id,
            )

            dependency_factory = DependencyFactory(self.logger.name)
            command_factory = CommandFactory(dependency_factory, self.logger)
            command_handler = CommandHandler(self.logger)

            try:
                self.logger.debug(f"Received message type: {message_type}")

                command = command_factory.get_command(
                    message_type,
                    document_id,
                    job_id,
                    receipt_handle,
                    self.sqs_client,
                    self.sns_client,
                    chunk_id,
                    domain_id,
                )
                command_handler.handle(command)

            except Exception as e:
                self.logger.exception("Error processing message: %s", e, exc_info=True)
                self._handle_failed_message(receipt_handle)
            self.logger.debug("Completed message processing")
        except Exception as e:
            self.logger.exception(e, exc_info=True)

    def _handle_malformed_message(self, body, receipt_handle):
        self.logger.error("Malformed document-job message. Deleting: %s", body)
        self.sqs_client.delete_message(
            QueueUrl=AWS_FEED_MANAGER_QUEUE, ReceiptHandle=receipt_handle
        )

    observe(name="generate_acceptance_criteria_user_story")
    trace_context(["user_story_id"])

    def _process_generate_acceptance_criteria_for_user_story(self, user_story_id):
        session = Session()

        dto = self.document_accessor.build_acceptance_criteria_generation_dto(user_story_id)

        if not dto:
            raise Exception(f"Unable to create dto for user story {user_story_id}")

        # TODO: Check if AC already exists for this user story
        #       If so, return a message about AC already existing and don't generate
        #       any new AC

        self.logger.debug(
            "Generating acceptance criteria for document_id: %s and job_id: %s",
            dto.document_id,
            dto.job_id,
        )
        self.synthesis_engine.generate_acceptance_criteria(dto)
        self.document_accessor.generate_acceptance_criteria_identifiers(dto.document_id)

        # Fetch all acceptance criterias for the given user_story_id
        criterias = (
            session.query(AcceptanceCriteria)
            .filter_by(user_story_id=user_story_id)
            .order_by(AcceptanceCriteria.acceptance_criteria_id)
            .all()
        )

        # If no criteria found, return a 404 response
        # This means the generation failed
        if not criterias:
            return {
                "error": "Generation failed. No acceptance criteria found for the given user_story_id."
            }, 404

        # Get aggregated rating counts based on document_id
        counts_all_levels = content_accessor.get_document_counts_all_levels(dto.document_id)

        # Add the `count_all_levels` property to each criteria
        criteria_list = []
        for criteria in criterias:
            criteria_dict = {
                "acceptance_criteria_id": criteria.acceptance_criteria_id,
                "user_story_id": criteria.user_story_id,
                "description": criteria.description,
                "identifier": criteria.identifier,
                "rating": criteria.rating.value,
                "counts_all_levels": counts_all_levels,
            }
            criteria_list.append(criteria_dict)

        return criteria_list, 200

    @observe(name="generate_test_cases_acceptance_criteria")
    @trace_context(["acceptance_criteria_id"])
    def _process_generate_test_cases_for_acceptance_criteria(self, acceptance_criteria_id):
        session = Session()

        dto = self.document_accessor.build_test_case_generation_dto(acceptance_criteria_id)

        self.logger.debug(
            "Generating test case for document_id: %s and job_id: %s",
            dto.document_id,
            dto.job_id,
        )
        self.synthesis_engine.generate_test_cases_for_acceptance_criteria(dto)
        self.document_accessor.generate_test_case_identifiers(dto.document_id)
        # Fetch all test cases for the given acceptance criteria id
        test_cases = (
            session.query(TestCase)
            .filter_by(acceptance_criteria_id=acceptance_criteria_id)
            .order_by(TestCase.test_case_id)
            .all()
        )

        # If no criteria found, return a 404 response
        # This means the generation failed
        if not test_cases:
            return {
                "error": "Generation failed. No test cases found for the given acceptance_criteria_id."
            }, 404

        # Get aggregated rating counts based on document_id
        counts_all_levels = content_accessor.get_document_counts_all_levels(dto.document_id)

        # Add the `count_all_levels` property to each criteria
        test_case_list = []
        for test_case in test_cases:
            test_case_dict = {
                "test_case_id": test_case.test_case_id,
                "acceptance_criteria_id": test_case.acceptance_criteria_id,
                "description": test_case.description,
                "identifier": test_case.identifier,
                "rating": test_case.rating.value,
                "counts_all_levels": counts_all_levels,
            }
            test_case_list.append(test_case_dict)

        return test_case_list, 200

    def _handle_unknown_message_type(self, message_type, receipt_handle):
        self.logger.error("Unknown message type: %s. Skipping.", message_type)
        self._delete_message(receipt_handle, "Unknown message type.")

    def _handle_failed_message(self, receipt_handle):
        self.sqs_client.change_message_visibility(
            QueueUrl=AWS_FEED_MANAGER_QUEUE, ReceiptHandle=receipt_handle, VisibilityTimeout=15
        )
        self.logger.debug("Message visibility timeout changed to 15 seconds.")

    def _delete_message(self, receipt_handle, log_message=None):
        self.sqs_client.delete_message(
            QueueUrl=AWS_FEED_MANAGER_QUEUE, ReceiptHandle=receipt_handle
        )

        if log_message:
            self.logger.debug(log_message)
        else:
            self.logger.debug(f"Deleting message from queue {receipt_handle}")

    def start(self):
        """
        Starts the FeedManager.

        This method logs the start of the FeedManager and begins polling the SQS queue
        for messages to process. It sets up the necessary environment for continuous
        message polling and processing.
        """
        self.logger.info("Starting FeedManager.")
        self.stop_event.clear()
        self.queue_thread = threading.Thread(target=self._poll_queue_with_user_ctx)
        self.queue_thread.start()

    def stop(self):
        """
        Stops the FeedManager.

        This method logs the stopping of the FeedManager. It is intended to be used to
        safely shut down any ongoing processes and stop the message polling.
        Currently, the implementation includes placeholders for stopping events and
        threads, which can be uncommented and used as needed.
        """
        self.logger.info("Stopping FeedManager.")
        self.stop_event.set()
        if self.queue_thread:
            self.queue_thread.join()  # Wait for the thread to finish
        self.logger.info("FeedManager stopped.")


@app.route("/api/user-story/<int:user_story_id>/generate-acceptance-criteria", methods=["POST"])
def begin_document_generate_acceptance_criteria(user_story_id: int):

    try:

        if user_story_id <= 0:
            return {"error": "Invalid user_story_id. It must be a positive integer."}, 400

        app.logger.info("begin_document_generate_acceptance_criteria called")
        return feed_manager_instance._process_generate_acceptance_criteria_for_user_story(
            user_story_id
        )

    except Exception as e:
        app.logger.exception("Error generating acceptance criteria: %s", e)
        return {"error": str(e)}, 500


@app.route(
    "/api/acceptance-criteria/<int:acceptance_criteria_id>/generate-test-cases", methods=["POST"]
)
def begin_document_generate_test_cases_for_acceptance_criteria(acceptance_criteria_id: int):

    try:

        if acceptance_criteria_id <= 0:
            return {"error": "Invalid acceptance_criteria_id. It must be a positive integer."}, 400

        app.logger.info("begin_document_generate_test_cases_for_acceptance_criteria called")
        return feed_manager_instance._process_generate_test_cases_for_acceptance_criteria(
            acceptance_criteria_id
        )

    except Exception as e:
        app.logger.exception("Error generating test case: %s", e)
        return {"error": str(e)}, 500


@app.route("/health", methods=["GET"])
def health():
    return jsonify(status="UP"), 200


feed_manager_instance = FeedManager("phoenix-burst")
