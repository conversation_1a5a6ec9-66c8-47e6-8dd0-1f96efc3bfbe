import os
import sys

from manager.feed.feed_manager import feed_manager_instance
from util.langfuse.helpers.burst_langfuse_helpers import BurstLangfuseHelpers

# Logging configuration
accesslog = "/var/log/gunicorn.access.log"
errorlog = "/var/log/gunicorn.err.log"
loglevel = "info"
workers = 3
timeout = 30  # Timeout after 30 seconds to avoid long-running workers
max_requests = 1000  # Restart workers after 1000 requests
max_requests_jitter = 50  # Jitter to avoid all workers restarting at once
worker_class = "sync"


def on_starting(server):
    server.log.info("Starting Gunicorn server.")


def when_ready(server):
    server.log.info("Gunicorn server is ready.")
    try:
        env_name = os.environ.get("ENVIRONMENT_NAME", "")

        helpers = BurstLangfuseHelpers()
        helpers.prefetch_prompts(env_name)
        server.log.info("Langfuse prompts preloaded successfully using environment '%s'.", env_name)
    except Exception as e:
        server.log.critical("Error preloading Langfuse prompts: %s. Exiting.", e)
        sys.exit(1)
    server.log.info("Starting Feed Manager.")
    feed_manager_instance.start()
    server.log.info("Feed Manager ready.")


def on_exit(server):
    server.log.info("Stopping Gunicorn server.")
    feed_manager_instance.stop()
