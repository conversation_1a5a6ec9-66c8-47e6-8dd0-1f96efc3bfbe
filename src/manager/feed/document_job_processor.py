import copy
import logging
import os
from typing import List, <PERSON><PERSON>

from llama_index.core.ingestion import IngestionPipeline
from llama_index.core.schema import TextNode
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.pinecone import PineconeVectorStore
from pinecone import Pinecone, ServerlessSpec

from accessor.content.content_accessor import ContentAccessor
from accessor.document.dto.document_job_dto import DocumentJobDTO
from engine.parsing.llama_index_extensions.node_preprocessor import NodePreprocessor
from engine.parsing.llama_index_extensions.parser.document_comparison_parser.document_comparison_parser import (
    DocumentComparisonParser,
)
from engine.parsing.llama_index_extensions.parser.markdown.markdown_parser import MarkdownParser
from engine.parsing.llama_index_extensions.parser.pdf_markup_parser.pdf_markup_parser import (
    PdfMarkupParser,
)
from engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser import (
    PdfPageParser,
)
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_node_parser import PDFNodeParser
from engine.parsing.llama_index_extensions.parser.vtt_parser.vtt_node_parser import VTTNodeParser
from engine.transformer.transform_engine import TransformEngine
from manager.feed.document_job_processor_interface import DocumentJobProcessorInterface
from manager.feed.dto.job_response import JobResponse
from shared.enums.job_enums import JobTypeEnum

OPEN_AI_MODEL_NAME = "gpt-4o-mini"
EMBEDDING_MODEL_NAME = "text-embedding-3-large"
DEFAULT_DOCUMENTS_PATH = "./data"
CHUNK_SIZE = 2000
CHUNK_OVERLAP = 200
MAX_TOKENS = 512
BATCH_SIZE = 50


class DocumentJobProcessor(DocumentJobProcessorInterface):
    def __init__(self, logger_name):
        self.logger_name = logger_name
        self.logger = logging.getLogger(logger_name)
        self.content_accessor = ContentAccessor(logger_name)
        self.transform_engine: TransformEngine = TransformEngine(logger_name)

    def _is_valid_document_job(self, document_name: str, job_type_id: int) -> bool:
        """
        Returns True if the document filename and job type combination is valid for processing.
        """
        if document_name.endswith(".pdf"):
            return True
        if document_name.endswith(".vtt"):
            return True
        if document_name.endswith(".docx"):
            return job_type_id == JobTypeEnum.OPTIMIZE_PROCEDURE.value
        return False

    def _validate_document_dto(self, document_job_dto: DocumentJobDTO) -> Tuple[bool, str]:
        """
        Validates that the DocumentJobDTO contains all required values
        and that the file/job type combination is valid.

        Returns:
        - (True, "") if valid.
        - (False, error_message) if invalid.
        """
        if not document_job_dto.document_filename:
            return False, "Document filename is missing."
        if not document_job_dto.document_bytes:
            return False, "Document bytes are missing."
        if not document_job_dto.job_type_id:
            return False, "Job type is missing."
        if (
            document_job_dto.document_start_page is None
            or document_job_dto.document_end_page is None
        ):
            return False, "Document start and end pages must be provided."
        if not document_job_dto.domain_vector_index_name:
            return False, "Domain vector index name is missing."
        if not document_job_dto.domain_name:
            return False, "Domain name is missing."
        if not self._is_valid_document_job(
            document_job_dto.document_filename, document_job_dto.job_type_id
        ):
            return False, (
                f"Unsupported document type '{document_job_dto.document_filename}' "
                f"for job type '{document_job_dto.job_type_id}'."
            )
        return True, ""

    def chunk_document(self, document_job_dto: DocumentJobDTO) -> JobResponse:
        valid, error_msg = self._validate_document_dto(document_job_dto)
        if not valid:
            self.logger.error("Validation error: %s", error_msg)
            return JobResponse(f"Failed to chunk document. {error_msg}", "failure")

        try:
            self.logger.info(
                "Started chunking document id %s, filename %s.",
                document_job_dto.document_id,
                document_job_dto.document_filename,
            )
            pinecone_index = self._initialize_vector_index(
                document_job_dto.domain_vector_index_name
            )
            nodes = self._parse_document(document_job_dto)

            self.logger.info("Preprocessing nodes.")
            node_preprocessor = NodePreprocessor()
            nodes = node_preprocessor.preprocess_nodes(nodes)
            self._persist_to_rdb(document_job_dto.document_id, nodes)
            nodes_for_vector = copy.deepcopy(nodes)
            for node in nodes_for_vector:
                if "blocks" in node.metadata:
                    del node.metadata["blocks"]
            self._run_vector_pipeline(pinecone_index, nodes_for_vector)
            self.logger.info(
                "Completed chunking document %s with Llama Index.", document_job_dto.document_id
            )
            return JobResponse("Successfully chunked document.", "success")

        except Exception as e:
            self.logger.exception("Error processing job: %s", e)
            return JobResponse("Failed to chunk document.", "failure")

    def _parse_pdf_document(self, document_job_dto: DocumentJobDTO) -> list:
        """
        Parses a PDF document into nodes.
        If the job type is markup_change_identification, uses PdfMarkupParser.
        Otherwise, uses PDFNodeParser with a fallback to PdfPageParser.
        """
        document_name = document_job_dto.document_filename
        if document_job_dto.job_type_id == JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value:
            self.logger.info("Parsing PDF file into nodes using PdfMarkupParser.")
            parser = PdfMarkupParser(self.logger_name)
            return parser.parse_nodes(
                document_job_dto.document_bytes,
                document_job_dto.document_start_page,
                document_job_dto.document_end_page,
            )
        elif document_job_dto.job_type_id == JobTypeEnum.VERSION_COMPARISON.value:
            self.logger.info("Parsing PDF file into nodes using DocumentComparisonParser.")
            parser = DocumentComparisonParser(self.logger_name)
            return parser.generate_change_statement_chunks(document_job_dto)
        else:
            try:
                self.logger.info("Parsing PDF file into nodes using PDFNodeParser.")
                parser = PDFNodeParser()
                nodes = parser._parse_nodes(
                    document=document_job_dto.document_bytes,
                    document_name=document_name,
                    domain=document_job_dto.domain_name,
                    start_page=document_job_dto.document_start_page,
                    end_page=document_job_dto.document_end_page,
                    show_progress=True,
                )
                if not nodes:
                    raise ValueError("Parsed nodes are None or empty.")
                return nodes
            except Exception as e:
                self.logger.exception("Error parsing PDF file: %s", e)
                self.logger.info("Attempting fallback parse with PdfPageParser.")
                parser = PdfPageParser()
                return parser.parse_nodes(
                    document_job_dto.document_bytes,
                    document_job_dto.document_start_page,
                    document_job_dto.document_end_page,
                )

    def _parse_docx_document(self, document_job_dto: DocumentJobDTO) -> list:
        """
        Parses a DOCX document.
        """
        self.logger.info("Converting docx to markdown.")
        markdown_text = self.transform_engine.transform_docx_to_markdown(
            document_job_dto.document_bytes, document_job_dto.document_id
        )

        self.logger.info("Parsing markdown into nodes.")
        markdown_parser = MarkdownParser()
        return markdown_parser.parse_nodes(markdown_text)

    def _parse_vtt_document(self, document_job_dto: DocumentJobDTO) -> list:
        """
        Parses a VTT document into nodes.
        """
        self.logger.info("Parsing VTT file into nodes.")
        parser = VTTNodeParser()
        return parser.parse_nodes(document=document_job_dto.document_bytes, show_progress=True)

    def _parse_document(self, document_job_dto: DocumentJobDTO) -> list:
        """
        Extracts nodes from the document based on its type and job type.
        Dispatches to specific helper methods based on file extension.
        Raises ValueError if the document type is unsupported.
        """
        document_name = document_job_dto.document_filename
        if document_name.endswith(".pdf"):
            return self._parse_pdf_document(document_job_dto)
        elif document_name.endswith(".docx"):
            return self._parse_docx_document(document_job_dto)
        elif document_name.endswith(".vtt"):
            return self._parse_vtt_document(document_job_dto)
        else:
            self.logger.error("Unsupported document type: %s", document_name)
            raise ValueError(f"Unsupported document type: {document_name}")

    def _initialize_vector_index(self, index_name):
        logging.info("Initializing vector database index.")
        pc = Pinecone(os.environ["PINECONE_API_KEY"])
        index_list_response = pc.list_indexes()
        index_names = [index_info["name"] for index_info in index_list_response.get("indexes", [])]
        if index_name not in index_names:
            logging.info("Creating new vector database index: %s.", index_name)
            pc.create_index(
                name=index_name,
                dimension=3072,
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-west-2"),
            )
        pinecone_index = pc.Index(index_name)
        logging.info("Vector database index initialized.")
        return pinecone_index

    def _persist_to_rdb(self, document_id: str, nodes: List[TextNode], batch_size: int = 200):
        logging.info("Persisting to RDB.")
        chunks = []
        for node in nodes:
            metadata = node.metadata
            chunks.append(
                {
                    "document_id": document_id,
                    "formatted_text": node.text,
                    "text_content": node.text,
                    "vector_id": node.node_id,
                    "chunk_metadata": metadata,
                }
            )
            if len(chunks) >= batch_size:
                self.content_accessor.save_chunks(chunks)
                chunks.clear()

        # Save any remaining chunks
        if chunks:
            self.content_accessor.save_chunks(chunks)

    def _run_vector_pipeline(self, pinecone_index, nodes):
        logging.info("Building the vector store pipeline.")
        vector_store = PineconeVectorStore(pinecone_index=pinecone_index, batch_size=BATCH_SIZE)
        pipeline = IngestionPipeline(
            transformations=[OpenAIEmbedding(model=EMBEDDING_MODEL_NAME)], vector_store=vector_store
        )
        logging.info("Running the vector store pipeline.")
        nodes = pipeline.run(nodes=nodes, show_progress=True)
