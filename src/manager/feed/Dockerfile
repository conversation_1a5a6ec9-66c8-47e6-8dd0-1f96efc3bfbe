# syntax=docker/dockerfile:1.4

###########################
#   1) BASE IMAGE SETUP  #
###########################
FROM python:3.12.4-slim-bookworm AS base
# — install system deps up front
RUN apt-get update \
 && DEBIAN_FRONTEND=noninteractive apt-get install -y \
      curl supervisor procps \
      texlive-latex-base texlive-latex-extra \
      texlive-fonts-recommended texlive-luatex \
      fonts-lmodern texlive-plain-generic \
 && rm -rf /var/lib/apt/lists/*

##############################
# 2) BUILDER: POETRY & DEPS   #
##############################
FROM base AS builder

# 2.1 Pin and install Poetry via official installer
ARG POETRY_VERSION=2.1.3
ENV POETRY_HOME=/opt/poetry
ENV PATH="/opt/poetry/bin:$PATH"
ENV POETRY_VIRTUALENVS_IN_PROJECT=true
ENV POETRY_NO_INTERACTION=1
RUN curl -sSL https://install.python-poetry.org \
    | python3 - --version ${POETRY_VERSION} \
    && poetry --version \
    | grep "$POETRY_VERSION"        # verify install

# 2.2 Leverage BuildKit cache for Poetry's download cache
ENV POETRY_CACHE_DIR=/root/.cache/pypoetry
WORKDIR /app

# 2.3 Copy only lockfiles to maximize layer reuse
COPY src/manager/feed/pyproject.toml \
     src/manager/feed/poetry.lock    /app/

# 2.4 Enforce lockfile consistency before install
RUN --mount=type=cache,target=${POETRY_CACHE_DIR} \
    poetry lock || (echo "❌ pyproject.toml/poetry.lock mismatch—run poetry lock" >&2; exit 1)  # lock consistency

# 2.5 Install **only** runtime deps, in‐project venv, binary‐only
RUN --mount=type=cache,target=${POETRY_CACHE_DIR} \
    poetry install \
      --no-root \
      --no-interaction \
      --no-ansi

# 2.6 Download any NLTK data now, so it's cached in builder
RUN poetry run python -m nltk.downloader punkt punkt_tab


###########################
# 3) FINAL RUNTIME IMAGE  #
###########################
FROM base AS final

# 3.1 Copy system‐wide Pandoc
ARG PANDOC_VERSION=3.6
WORKDIR /tmp
ADD https://github.com/jgm/pandoc/releases/download/${PANDOC_VERSION}/pandoc-${PANDOC_VERSION}-linux-amd64.tar.gz pandoc.tar.gz
RUN tar -xzf pandoc.tar.gz --strip-components=1 -C /usr/local \
 && rm pandoc.tar.gz \
 && luaotfload-tool --update --force

# 3.2 Pull in the cached virtualenv
COPY --from=builder /app/.venv /app/.venv
ENV PATH="/app/.venv/bin:$PATH"

# 3.3 Copy only your application code
WORKDIR /app
ENV PYTHONPATH=/app
ENV PORT=5000

COPY src/manager/feed /app/manager/feed
COPY src/manager/__init__.py /app/manager/__init__.py
COPY src/accessor/__init__.py /app/accessor/__init__.py
COPY src/accessor/content /app/accessor/content
COPY src/accessor/document /app/accessor/document
COPY src/accessor/image /app/accessor/image
COPY src/accessor/reference /app/accessor/reference
COPY src/accessor/ai /app/accessor/ai
COPY src/accessor/identity /app/accessor/identity

COPY src/engine/__init__.py /app/engine/__init__.py
COPY src/engine/analysis /app/engine/analysis
COPY src/engine/parsing /app/engine/parsing
COPY src/engine/synthesis /app/engine/synthesis
COPY src/engine/transformer /app/engine/transformer

COPY src/util/__init__.py /app/util/__init__.py
COPY src/util /app/util
COPY src/shared /app/shared

# 3.4 Supervisor config & permissions
COPY src/manager/feed/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
RUN chmod +x /app/manager/feed/feed_manager.py

EXPOSE 5000

# 3.5 Run under supervisor
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
