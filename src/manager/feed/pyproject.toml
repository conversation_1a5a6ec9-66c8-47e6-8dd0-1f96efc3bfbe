[project]
name = "feed_manager_service"
version = "0.1.0"
description = "Feed Manager Service"
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<3.13"

[tool.poetry.dependencies]
blinker = "^1.9.0"
boto3 = "^1.35.90"
dependency-injector = "^4.45.0"
flask = { version = "^2.2.5", extras = ["async"] }
flask-restx = "^1.3.0"
gunicorn = "^22.0.0"
langfuse = "^2.53.9"
litellm = "^1.66.0"
llama-index = "^0.12.35"
llama-index-core = "^0.12.35"
llama-index-vector-stores-pinecone = "^0.4.4"
matplotlib = "^3.9.2"
numpy = "^1.26.4"
openai = "^1.78.1"
pandas = "^2.2.3"
pillow = "^11.0.0"
pinecone = "^5.4.2"
pinecone-client = "^3.1.0"
platformdirs = "^3.0.0"
psycopg2-binary = "^2.9.9"
py-readability-metrics = "^1.4.5"
pydantic = "^2.9.2"
pymupdf = "^1.24.13"
pypandoc = "^1.15"
python-docx = "^1.1.2"
python-dotenv = "^1.0.1"
spacy = "^3.7.5"
sqlalchemy = "^2.0.29"
textstat = "^0.7.5"
webvtt-py = "^0.5.1"
werkzeug = "^3.0.2"
xlsxwriter = "^3.2.0"
en-core-web-sm = { url = "https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl" }
llama-index-embeddings-bedrock = "^0.5.0"
llama-index-vector-stores-opensearch = "^0.5.4"
opensearch-py = "^2.8.0"
requests-aws4auth = "^1.3.1"

[tool.poetry.group.test.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.7"
pytest-mock = "^3.12.0"
reportlab = "^4.4.1"

[tool.poetry.group.dev.dependencies]
isort = "^6.0.1"
black = "^25.1.0"
pytest = "^8.3.5"
alembic = "^1.16.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
package-mode = false  

