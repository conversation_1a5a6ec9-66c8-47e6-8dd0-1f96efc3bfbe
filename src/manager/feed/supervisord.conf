[supervisord]
nodaemon=true
loglevel=info

[program:feed_manager]
command=python /app/manager/feed/feed_manager.py
autostart=true
autorestart=true
startretries=10
stderr_logfile=/var/log/feed_manager.err.log
stdout_logfile=/var/log/feed_manager.out.log

[program:gunicorn]
command=gunicorn -c manager/feed/gunicorn_config.py manager.feed.wsgi:app
directory=/app
autostart=true
autorestart=true
startretries=10
stderr_logfile=/var/log/gunicorn.err.log
stdout_logfile=/var/log/gunicorn.out.log