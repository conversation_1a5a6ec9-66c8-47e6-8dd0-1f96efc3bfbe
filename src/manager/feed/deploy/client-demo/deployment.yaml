apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: phoenix-burst-feed-manager
  name: phoenix-burst-feed-manager-deployment
  namespace: client-demo-phoenix-burst
spec:
  replicas: 3
  selector:
    matchLabels:
      app: phoenix-burst-feed-manager
  template:
    metadata:
      annotations:
        dapr.io/app-id: phoenix-burst-feed-manager
        dapr.io/app-port: '5000'
        dapr.io/enabled: 'true'
      labels:
        app: phoenix-burst-feed-manager
    spec:
      containers:
      - env:
        # General Configuration
        - name: ENVIRONMENT_NAME
          value: client-demo
        - name: LOG_LEVEL
          value: DEBUG
        - name: THIRD_PARTY_LOG_LEVEL
          value: INFO
        # APIs and External Endpoints
        - name: LANGFUSE_HOST
          value: https://us.cloud.langfuse.com
        - name: FEDERAL_REGISTER_API
          value: https://www.federalregister.gov/api/v1/documents/
        - name: CODE_OF_FEDERAL_REGULATIONS_API
          value: https://www.ecfr.gov/api/versioner/v1/full/
        # Auth0 Configuration
        - name: AUTH0_DOMAIN
          valueFrom:
            secretKeyRef:
              key: AUTH0_DOMAIN
              name: phoenix-burst-secrets
        - name: AUTH0_CLIENT_ID
          valueFrom:
            secretKeyRef:
              key: AUTH0_CLIENT_ID
              name: phoenix-burst-secrets
        - name: AUTH0_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              key: AUTH0_CLIENT_SECRET
              name: phoenix-burst-secrets
        # Secret API Keys
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              key: OPENAI_API_KEY
              name: phoenix-burst-secrets
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              key: PINECONE_API_KEY
              name: phoenix-burst-secrets
        # Database Configuration
        - name: AWS_POSTGRES_USERNAME
          valueFrom:
            secretKeyRef:
              key: AWS_POSTGRES_USERNAME
              name: phoenix-burst-secrets
        - name: AWS_POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              key: AWS_POSTGRES_PASSWORD
              name: phoenix-burst-secrets
        - name: AWS_POSTGRES_HOST
          valueFrom:
            secretKeyRef:
              key: AWS_POSTGRES_HOST
              name: phoenix-burst-secrets
        - name: AWS_POSTGRES_PORT
          valueFrom:
            secretKeyRef:
              key: AWS_POSTGRES_PORT
              name: phoenix-burst-secrets
        - name: AWS_POSTGRES_DATABASE
          valueFrom:
            secretKeyRef:
              key: AWS_POSTGRES_DATABASE
              name: phoenix-burst-secrets
        # AWS S3 and Region Configuration
        - name: AWS_S3_BUCKET_NAME
          valueFrom:
            secretKeyRef:
              key: AWS_S3_BUCKET_NAME
              name: phoenix-burst-secrets
        - name: AWS_REGION
          valueFrom:
            secretKeyRef:
              key: AWS_REGION
              name: phoenix-burst-secrets
        - name: AWS_S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              key: AWS_S3_ACCESS_KEY
              name: phoenix-burst-secrets
        - name: AWS_S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              key: AWS_S3_SECRET_KEY
              name: phoenix-burst-secrets
        # Feed Manager Configuration
        - name: AWS_FEED_MANAGER_REGION
          valueFrom:
            secretKeyRef:
              key: AWS_FEED_MANAGER_REGION
              name: phoenix-burst-secrets
        - name: FEED_MANAGER_API_URL
          valueFrom:
            secretKeyRef:
              key: FEED_MANAGER_API_URL
              name: phoenix-burst-secrets
        - name: AWS_FEED_MANAGER_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              key: AWS_FEED_MANAGER_ACCESS_KEY
              name: phoenix-burst-secrets
        - name: AWS_FEED_MANAGER_SECRET_KEY
          valueFrom:
            secretKeyRef:
              key: AWS_FEED_MANAGER_SECRET_KEY
              name: phoenix-burst-secrets
        - name: AWS_FEED_MANAGER_QUEUE
          valueFrom:
            secretKeyRef:
              key: AWS_FEED_MANAGER_QUEUE
              name: phoenix-burst-secrets
        # AWS SNS Configuration
        - name: AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_GENERATE_USER_STORIES_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_GENERATE_USER_STORIES_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_GENERATE_TEST_CASES_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_GENERATE_TEST_CASES_ARN
              name: phoenix-burst-secrets
        - name: AWS_SNS_JOB_ARN
          valueFrom:
            secretKeyRef:
              key: AWS_SNS_JOB_ARN
              name: phoenix-burst-secrets
        # Langfuse Configuration
        - name: LANGFUSE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              key: LANGFUSE_SECRET_KEY
              name: phoenix-burst-secrets
        - name: LANGFUSE_PUBLIC_KEY
          valueFrom:
            secretKeyRef:
              key: LANGFUSE_PUBLIC_KEY
              name: phoenix-burst-secrets
        # AWS configuration for liteLLM/bedrock
        - name: AWS_REGION_NAME
          valueFrom:
            secretKeyRef:
              key: AWS_REGION_NAME
              name: phoenix-burst-secrets
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              key: AWS_ACCESS_KEY_ID
              name: phoenix-burst-secrets
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              key: AWS_SECRET_ACCESS_KEY
              name: phoenix-burst-secrets
        image: 581701393095.dkr.ecr.us-east-1.amazonaws.com/feed-manager:client-demo-latest
        imagePullPolicy: Always
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 20
          periodSeconds: 20
        name: phoenix-burst-feed-manager
        ports:
        - containerPort: 5000
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 20
          periodSeconds: 20
        resources:
          limits:
            cpu: '2'
            memory: 4Gi
          requests:
            cpu: '1'
            memory: 2Gi
        volumeMounts:
        - mountPath: /var/log/
          name: log-volume

      - name: fluent-bit
        image: amazon/aws-for-fluent-bit:latest
        env:
        - name: FLUENT-BIT-LOG-LEVEL
          value: debug
        resources:
          limits:
            cpu: 200m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
        volumeMounts:
        - mountPath: /fluent-bit/etc/fluent-bit.conf
          name: fluent-bit-config
          subPath: fluent-bit.conf
        - mountPath: /var/log/
          name: log-volume

      volumes:
      - name: log-volume
        emptyDir: {}

      - name: fluent-bit-config
        configMap:
          name: feed-manager-logging
