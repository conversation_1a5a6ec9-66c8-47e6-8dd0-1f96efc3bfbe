apiVersion: v1
kind: ConfigMap
metadata:
  name: feed-manager-logging
  namespace: prod-phoenix-burst
  labels:
    app: phoenix-burst-feed-manager
    component: logging
    type: fluent-bit
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush        1
        Log_Level    debug
        Parsers_File parsers.conf

    # Capture Feed Manager logs from files
    [INPUT]
        Name              tail
        Path              /var/log/gunicorn.err.log
        Tag               feed-manager.error
        Parser            json
        Refresh_Interval  10
        Mem_Buf_Limit     10MB

    [INPUT]
        Name              tail
        Path              /var/log/gunicorn.out.log
        Tag               feed-manager.output
        Parser            json
        Refresh_Interval  10
        Mem_Buf_Limit     10MB

    # Ensure Kubernetes metadata is attached
    [FILTER]
        Name              kubernetes
        Match             feed-manager.*
        Merge_Log         On

    # Send Feed Manager logs to CloudWatch
    [OUTPUT]
        Name              cloudwatch
        Match             feed-manager.error
        region            us-east-2
        log_group_name    /aws/eks/phoenix-burst/prod
        log_stream_prefix feed-manager-error/
        auto_create_group true

    # Send Feed Manager logs to CloudWatch
    [OUTPUT]
        Name              cloudwatch
        Match             feed-manager.output
        region            us-east-2
        log_group_name    /aws/eks/phoenix-burst/prod
        log_stream_prefix feed-manager-output/
        auto_create_group true

  parsers.conf: |
    [PARSER]
        Name   json
        Format json