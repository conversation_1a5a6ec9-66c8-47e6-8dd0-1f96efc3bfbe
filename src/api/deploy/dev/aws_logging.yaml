apiVersion: v1
kind: ConfigMap
metadata:
  name: api-logging
  namespace: dev-phoenix-burst
  labels:
    app: phoenix-burst-api
    component: logging
    type: fluent-bit
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush        1
        Log_Level    debug
        Parsers_File parsers.conf

    # Capture API logs from stdout/stderr
    [INPUT]
        Name              forward
        Listen            0.0.0.0
        Port              24224
        Tag               api
        Mem_Buf_Limit     10MB

    # Add Kubernetes metadata
    [FILTER]
        Name              kubernetes
        Match             api
        Merge_Log         On
        Keep_Log          On

    # Send API logs to CloudWatch
    [OUTPUT]
        Name              cloudwatch
        Match             api
        region            us-east-2
        log_group_name    /aws/eks/phoenix-burst/dev
        log_stream_prefix api/
        auto_create_group true

    # Debugging (Optional - remove after verification)
    [OUTPUT]
        Name              stdout
        Match             api
        Format            json_lines

  parsers.conf: |
    [PARSER]
        Name   json
        Format json
