apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: phoenix-burst-api-ingress
  namespace: prod-phoenix-burst
  annotations:
    alb.ingress.kubernetes.io/healthcheck-path: /health
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: "traffic-port"
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "60"
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: "20"
    alb.ingress.kubernetes.io/healthy-threshold-count: "2"
    alb.ingress.kubernetes.io/unhealthy-threshold-count: "2"
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    kubernetes.io/ingress.class: "alb"
    alb.ingress.kubernetes.io/group.name: "phoenix-burst-ingress-group"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP":80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-2:581701393095:certificate/2577098e-c760-4148-9cbb-5abf8c80a096
    alb.ingress.kubernetes.io/actions.redirect-to-https: >
      {"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}
spec:
  ingressClassName: alb
  rules:
    # HTTP to HTTPS redirection for all paths
    - http:
        paths:
          - path: /*
            pathType: ImplementationSpecific
            backend:
              service:
                name: "redirect-to-https"
                port:
                  name: use-annotation
    # HTTPS traffic handling
    - host: api.phoenixburst.ai
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: phoenix-burst-api-service
                port:
                  number: 80
