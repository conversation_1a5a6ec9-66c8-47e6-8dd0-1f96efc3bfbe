[project]
name = "phoenix_burst_api"
version = "0.1.0"
description = "Phoenix Burst API Service"
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<3.13"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
alembic = "^1.7.7"
anthropic = "^0.21.3"
blinker = "^1.9.0"
boto3 = "^1.35.90"
botocore = "^1.35.90"
cohere = "^5.2.5"
cryptography = "^44.0.1"
dependency-injector = "^4.45.0"
flask-cors = "^4.0.1"
flask = { version = "^3.0.3", extras = ["async"] }
flask-restx = "^1.3.0"
gunicorn = "^22.0.0"
langfuse = "^2.53.9"
nest-asyncio = "^1.6.0"
openai = "^1.14.3"
psycopg2-binary = "^2.9.9"
PyJWT = "^2.10.1"
pypandoc = "^1.15"
python-dotenv = "^1.0.1"
requests = "^2.31.0"
six = "^1.17.0"
sqlalchemy = "^2.0.36"
werkzeug = "^3.0.2"
xlsxwriter = "^3.2.0"
pymupdf = "^1.24.13"
pandas = "^2.2.3"
pillow = "^11.0.0"

[tool.poetry.group.dev.dependencies]
mypy-extensions = "^1.1.0"
typing-inspect = "^0.9.0"
typing-inspection = "^0.4.0"
types-requests = "^2.32.0.20250328"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
