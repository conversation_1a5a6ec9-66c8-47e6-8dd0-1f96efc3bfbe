"""
Provides authentication and authorization functionality for the API.
It integrates with Auth0 to handle JWT validation, including token signature verification,
extraction of user claims, and handling of user permissions.

The module supports both API key-based and JWT-based authentication, as well as cookie-based
authentication for extracting the user ID.

Also provides a decorator for securing endpoints with permission and scope validation.
"""

import base64
import logging
import time
from functools import wraps
from typing import Dict, List

import jwt
import requests
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from flask import current_app, g, request

from accessor.identity import IdentityAccessor

from shared.exceptions import AuthError

from util.audit import AuditServiceContainer
from util.audit.user_ctx import UserCtx, set_user_ctx
from util.auth0.permissions_map import PERMISSIONS_MAP
from util.config import AUTH0_CLIENT_ID, AUTH0_CLIENT_SECRET, AUTH0_DOMAIN

identity_accessor = IdentityAccessor(logger_name="burst-api")


# Caches
JWKS = None
JWKS_TIMESTAMP = 0  # Track when <PERSON><PERSON><PERSON> was last updated
MGMT_TOKEN_CACHE = {"token": None, "expires_at": 0}
PERMISSIONS_CACHE = None

# Vars
# Keeping API_KEY here for local API testing
API_KEY = "79dc1e341d91d6a40384bf737b6c89e3f4da2a907218a0e25fd53ffa3d27e6f8"
ALGORITHMS = "RS256"


# Scope decorator
def requires_scope(required_scope: str):
    """
    A decorator that ensures the request has the required scope in the JWT token.

    This decorator extracts the JWT from the request header, decodes it, and checks
    if the required scope is present in the decoded token. If the scope is missing,
    it raises an AuthError.

    Args:
        required_scope (str): The scope that is required to access the endpoint.

    Returns:
        function: A wrapped function that verifies the scope and calls the decorated function.

    Raises:
        AuthError: If the token is missing the required scope.
    """

    def wrapper(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            token = _get_token_auth_header()
            claims = jwt.decode(token, options={"verify_signature": False})
            scopes = claims.get("scope", "").split()
            logging.info(f"Decoded token scopes: {scopes}")
            if required_scope not in scopes:
                logging.warning(f"Missing required scope: {required_scope}")
                raise AuthError(
                    {"code": "insufficient_scope", "description": "Missing required scope"}, 403
                )
            return f(*args, **kwargs)

        return decorated

    return wrapper


# Permissions decorator
def requires_permissions(required_permissions: List[str]):
    """
    A decorator that ensures the request has the required permissions in the JWT token.

    This decorator extracts the JWT from the request header, decodes it, and checks
    if the required permissions are present in the user's permissions. If the permissions
    are missing, it raises an AuthError.

    Args:
        required_permissions (List[str]): A list of permission names required to access the endpoint.

    Returns:
        function: A wrapped function that verifies the permissions and calls the decorated function.

    Raises:
        AuthError: If the user does not have the required permissions.
    """

    def wrapper(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            token = _get_token_auth_header()
            claims = jwt.decode(token, options={"verify_signature": False})
            user_sub = claims.get("sub")

            user_permissions = _get_user_permissions(user_sub)
            logging.info(f"User permissions: {user_permissions}")

            if not any(permission in user_permissions for permission in required_permissions):
                logging.warning(f"User lacks required permissions: {required_permissions}")
                raise AuthError(
                    {"code": "forbidden", "description": "Insufficient permissions"}, 403
                )
            return f(*args, **kwargs)

        return decorated

    return wrapper


# Secure Endpoint decorator
def secure_endpoint(required_permissions: List[str] = None, required_scopes: List[str] = None):
    """
    A decorator that secures an endpoint by validating the JWT token, required permissions,
    and required scopes.

    This decorator:
    1. Extracts the JWT token from the request header.
    2. Decodes the token and verifies its signature using the Auth0 RSA key.
    3. Checks if the user has the required permissions (from `required_permissions`) and/or scopes (from `required_scopes`).
    4. If any check fails, it raises an `AuthError`.

    Args:
        required_permissions (List[str], optional): A list of permissions required for access. Defaults to None.
        required_scopes (List[str], optional): A list of scopes required for access. Defaults to None.

    Returns:
        function: A wrapped function that performs the security checks and calls the decorated function.

    Raises:
        AuthError: If the token is invalid, expired, or the user lacks required permissions or scopes.
    """

    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            try:
                # Check for JWT token in Authorization header
                auth_header = request.headers.get("Authorization")
                if auth_header:
                    logging.info("JWT authorization header found")
                    token = _get_token_auth_header()
                    rsa_key = _get_rsa_key(token)
                    try:
                        payload = jwt.decode(
                            token,
                            rsa_key,
                            algorithms=[ALGORITHMS],
                            audience=AUTH0_CLIENT_ID,
                            issuer=f"https://{AUTH0_DOMAIN}/",
                        )
                        g.current_user = payload

                        user = identity_accessor.get_user_by_auth0_id(payload.get("sub"))

                        if user.user_id is None:
                            logging.error("User ID not found for Auth0 ID: %s", payload.get("sub"))
                            raise AuthError(
                                {"code": "invalid_token", "description": "User ID is None"}, 401
                            )

                        if not user.enterprise:
                            logging.error(
                                "User has no associated enterprise for Auth0 ID: %s",
                                payload.get("sub"),
                            )
                            raise AuthError(
                                {
                                    "code": "invalid_user_config",
                                    "description": "User has no associated enterprise",
                                },
                                401,
                            )

                        # Set user context for use across the app
                        user_ctx = UserCtx(
                            auth0_id=payload.get("sub"),
                            user_id=user.user_id,
                            email=payload.get("email"),
                            name=payload.get("name"),
                            enterprise_id=user.enterprise_id,
                            domain_id=user.enterprise.domain.domain_id,
                        )

                        if user_ctx.user_id is None:
                            logging.error("User ID is None for Auth0 ID: %s", payload.get("sub"))
                            raise AuthError(
                                {"code": "invalid_token", "description": "User ID is None"}, 401
                            )

                        set_user_ctx(user_ctx)

                    except jwt.ExpiredSignatureError:
                        raise AuthError(
                            {"code": "token_expired", "description": "Token is expired"}, 401
                        )
                    except jwt.InvalidAudienceError:
                        raise AuthError(
                            {"code": "invalid_claims", "description": "Invalid audience"}, 401
                        )
                    except jwt.InvalidIssuerError:
                        raise AuthError(
                            {"code": "invalid_claims", "description": "Invalid issuer"}, 401
                        )
                    except Exception as e:
                        raise AuthError(
                            {"code": "invalid_token", "description": f"Unable to decode token {e}"},
                            401,
                        )

                    # Perform scope and permissions checks
                    user_sub = payload.get("sub")
                    if not user_sub:
                        logging.error("User 'sub' (subject) is missing from the JWT payload")
                        raise AuthError(
                            {"code": "invalid_token", "description": "Missing 'sub' in token"}, 401
                        )

                    token_scopes = payload.get("scope", "").split()
                    logging.info(f"Token scopes: {token_scopes}")

                    # Check permissions if provided
                    if required_permissions:
                        _check_permissions(payload.get("permissions", []), required_permissions)

                    # Check scopes if provided
                    if required_scopes and not any(
                        scope in token_scopes for scope in required_scopes
                    ):
                        logging.warning(
                            f"Insufficient scopes. Required: {required_scopes}, Found: {token_scopes}"
                        )
                        raise AuthError(
                            {"code": "forbidden", "description": "Insufficient scopes"}, 403
                        )

                    logging.info("Authorization successful with JWT")
                    return f(*args, **kwargs)

                else:
                    logging.info("No Authorization header found")
                    raise AuthError(
                        {"code": "invalid_token", "description": "Missing auth token"}, 401
                    )

            except Exception as e:
                logging.error(f"Authorization failed: {str(e)}")
                raise e

        return wrapper

    return decorator


# JWKS
def _get_jwks() -> List[Dict]:
    """Fetch and cache JWKS (JSON Web Key Set) keys from Auth0 with expiration handling.

    JWKS is a JSON object containing a set of public keys used to verify JSON Web Tokens (JWTs).
    These keys are typically provided by an identity provider (e.g., Auth0, Okta, AWS Cognito).
    Using JWKS allows services to validate JWTs dynamically without storing secret keys locally.

    This function:
    - Checks if JWKS is already cached in Flask's `current_app.config`.
    - If cached keys have expired (default: 1 hour), it fetches fresh JWKS from Auth0.
    - Caches the new keys to avoid unnecessary network requests.
    """
    now = time.time()

    # Retrieve JWKS cache and expiration time from Flask config
    jwks_cache = current_app.config.get("JWKS_CACHE") or {}
    expiration_time = current_app.config.get(
        "JWKS_EXPIRATION_TIME_IN_SECONDS", 3600
    )  # Default: 1 hour

    # If JWKS is missing or expired, fetch fresh keys from Auth0
    if not jwks_cache or (now - jwks_cache.get("timestamp", 0)) > expiration_time:
        try:
            auth0_jwks_url = f"https://{AUTH0_DOMAIN}/.well-known/jwks.json"

            # Fetch JWKS keys with a timeout of 5 seconds
            response = requests.get(auth0_jwks_url, timeout=5)
            response.raise_for_status()

            # Store new JWKS keys along with a timestamp in Flask config
            jwks_cache = {"keys": response.json(), "timestamp": now}
            current_app.config["JWKS_CACHE"] = jwks_cache
            logging.info("JWKS keys updated successfully")

        except requests.RequestException as e:
            logging.error("Failed to fetch JWKS from Auth0: %s", str(e))
            raise AuthError(
                {"code": "jwks_fetch_failed", "description": "Unable to fetch JWKS keys"}, 500
            ) from e

    return jwks_cache["keys"]


# JWT Header Extraction
def _get_token_auth_header() -> str:
    """Extracts the Access Token from the Authorization Header.

    This function:
    - Retrieves the `Authorization` header from the incoming request.
    - Validates that the header is present and correctly formatted.
    - Ensures the token follows the "Bearer <token>" format.
    - Extracts and returns the token.

    Returns:
        str: The extracted JWT access token.

    Raises:
        AuthError: If the `Authorization` header is missing or improperly formatted.
    """
    # Step 1: Retrieve the Authorization header from the request
    auth = request.headers.get("Authorization", None)
    logging.info("Authorization header: %s", auth)

    # Step 2: Validate that the Authorization header is present
    if not auth:
        logging.error("Authorization header is missing")
        raise AuthError(
            {
                "code": "authorization_header_missing",
                "description": "Authorization header is expected",
            },
            401,
        )

    # Step 3: Split the Authorization header into parts (expecting "Bearer <token>")
    parts = auth.split()

    # Log different invalid formats
    if parts[0].lower() != "bearer":
        logging.error("Invalid header format. Expected 'Bearer', got '%s'", parts[0])
        raise AuthError(
            {"code": "invalid_header", "description": "Authorization must start with Bearer"}, 401
        )
    if len(parts) == 1:
        logging.error("Token not found in the Authorization header")
        raise AuthError({"code": "invalid_header", "description": "Token not found"}, 401)
    if len(parts) > 2:
        logging.error("Authorization header must be Bearer token")
        raise AuthError(
            {"code": "invalid_header", "description": "Authorization header must be Bearer token"},
            401,
        )

    # Step 5: Return the extracted JWT access token
    token = parts[1]
    logging.info(
        "Extracted token: %s...", token[:10]
    )  # Log a truncated version of the token for security
    return token


# RSA Public Key
def _get_rsa_key(token: str) -> bytes:
    """Fetch the correct RSA public key from JWKS and convert it to PEM format.

    This function:
    - Extracts the "kid" (key ID) from the JWT header.
    - Retrieves the JWKS (JSON Web Key Set) from the Auth0 server.
    - Finds the matching key from the JWKS based on the "kid" value.
    - Ensures the required RSA key components ("kty", "n", "e") are present.
    - Converts the public key components (`n`, `e`) into an RSA public key object.
    - Serializes the RSA public key to PEM format and returns it.

    Args:
        token (str): The JWT token from which to extract the "kid" value.

    Returns:
        bytes: The PEM-encoded RSA public key.

    Raises:
        AuthError: If the token header is missing "kid", the key is not found in JWKS,
                   or the JWKS key format is invalid.
    """
    logging.info(
        "Extracting RSA public key from token: %s...", token[:10]
    )  # Truncated token for logging
    try:
        # Step 1: Retrieve the JSON Web Key Set (JWKS) from Auth0
        jwks = _get_jwks()

        # Step 2: Extract the JWT header without verifying the signature
        unverified_header = jwt.get_unverified_header(token)

        # Log the unverified JWT header
        logging.info("Unverified JWT header: %s", unverified_header)

        # Step 3: Extract the "kid" (key ID) from the token header
        key_id = unverified_header.get("kid")
        if not key_id:
            logging.error("No 'kid' (key ID) found in token header")
            raise AuthError(
                {
                    "code": "invalid_header",
                    "description": "No 'kid' (key ID) found in token header",
                },
                401,
            )
        logging.info("Extracted kid: %s", key_id)

        # Step 4: Find the matching key in the JWKS using the "kid" value
        rsa_key = next((key for key in jwks["keys"] if key.get("kid") == key_id), None)
        if not rsa_key:
            logging.error("No matching key found for kid: %s", key_id)
            raise AuthError(
                {"code": "invalid_jwks", "description": "Unable to find appropriate JWKS key"}, 401
            )
        logging.info("Found RSA key: %s", rsa_key)

        # Step 5: Ensure the JWKS key contains required fields for RSA verification
        if not all(k in rsa_key for k in ("kty", "n", "e")):
            logging.error("JWKS key is missing required fields: 'kty', 'n', 'e'")
            raise AuthError(
                {"code": "invalid_jwks_key", "description": "Invalid JWKS key format"}, 500
            )

        # Step 6: Convert `n` (modulus) and `e` (exponent) from Base64 URL encoding to integers
        n = int.from_bytes(base64.urlsafe_b64decode(rsa_key["n"] + "==="), "big")
        e = int.from_bytes(base64.urlsafe_b64decode(rsa_key["e"] + "==="), "big")

        # Step 7: Construct the RSA public key
        public_key = rsa.RSAPublicNumbers(e, n).public_key()

        # Step 8: Serialize the RSA public key to PEM format
        pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )
        logging.info("Generated PEM-encoded RSA key")
        return pem
    except Exception as e:
        logging.error("Error occurred during RSA key extraction: %s", str(e))
        raise AuthError(
            {"code": "invalid_jwks_key", "description": "Error extracting RSA key"}, 500
        )


# Management Token
def _get_management_token() -> str:
    """
    Retrieves the management token from Auth0 for API interactions.

    This method fetches a new management token if one is not already cached
    or if the cached token has expired. The token is used to interact with
    Auth0's management API to fetch user permissions.

    Returns:
        str: The management access token for Auth0 API calls.

    Raises:
        AuthError: If there is a problem retrieving the token from Auth0.
    """
    logging.info("Fetching management token")
    now = time.time()
    if MGMT_TOKEN_CACHE["token"] and now < MGMT_TOKEN_CACHE["expires_at"]:
        logging.info("Returning cached management token")
        return MGMT_TOKEN_CACHE["token"]

    url = f"https://{AUTH0_DOMAIN}/oauth/token"
    payload = {
        "client_id": AUTH0_CLIENT_ID,
        "client_secret": AUTH0_CLIENT_SECRET,
        "audience": AUTH0_CLIENT_ID,
        "grant_type": "client_credentials",
    }
    headers = {"Content-Type": "application/json"}
    response = requests.post(url, json=payload, headers=headers)
    response.raise_for_status()
    data = response.json()
    MGMT_TOKEN_CACHE["token"] = data["access_token"]
    MGMT_TOKEN_CACHE["expires_at"] = now + data.get("expires_in", 3600)
    logging.info("Management token fetched successfully")
    return data["access_token"]


# Get User Permissions
def _get_user_permissions(user_id: str) -> List[str]:
    """
    Retrieves the permissions for a given user from Auth0.

    This method fetches the user permissions by calling the Auth0 API with
    the provided user ID. If the permissions are already cached, it returns
    the cached permissions.

    Args:
        user_id (str): The unique identifier for the user in Auth0.

    Returns:
        List[str]: A list of permission names for the user.

    Raises:
        AuthError: If there is a problem retrieving the permissions from Auth0.
    """
    logging.info("Fetching permissions for user ID: %2", user_id)
    if user_id in PERMISSIONS_CACHE:
        logging.info("Returning cached permissions")
        return PERMISSIONS_CACHE[user_id]

    token = _get_management_token()
    url = f"https://{AUTH0_DOMAIN}/api/v2/users/{user_id}/permissions"
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    permissions = [p["permission_name"] for p in response.json()]
    PERMISSIONS_CACHE[user_id] = permissions
    logging.info("Fetched permissions: %s", permissions)
    return permissions


# Check Permissions Function
def _check_permissions(user_permissions: List[str], required_permissions: List[str]):
    """Checks whether the user has the required permissions."""
    required_permission_keys = [
        PERMISSIONS_MAP.get(permission, permission) for permission in required_permissions
    ]

    logging.info(
        "Checking user permissions: %s against required permissions: %s",
        user_permissions,
        required_permission_keys,
    )

    # Check if any of the user's permissions match the required ones
    if not any(permission in user_permissions for permission in required_permission_keys):
        logging.warning("User lacks required permissions: %s", required_permission_keys)
        raise AuthError({"code": "forbidden", "description": "Insufficient permissions"}, 403)

    logging.info("User has the required permissions: %s", required_permission_keys)
