# syntax=docker/dockerfile:1.4

################################
# 1) BASE: OS + System Tools  #
################################
FROM python:3.10.12-slim AS base  
# Install only minimal OS packages
RUN apt-get update \
 && DEBIAN_FRONTEND=noninteractive apt-get install -y \
    pandoc \
    curl \
 && rm -rf /var/lib/apt/lists/*         

####################################
# 2) BUILDER: Poetry + Dependencies #
####################################
FROM base AS builder

# 2.1 Pin Poetry version and install via official script
ARG POETRY_VERSION=2.1.3
ENV POETRY_HOME=/opt/poetry \
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    POETRY_NO_INTERACTION=1 \
    PATH="/opt/poetry/bin:$PATH"
RUN curl -sSL https://install.python-poetry.org \
    | python3 - --version $POETRY_VERSION \
 && poetry --version \
    | grep "$POETRY_VERSION"        # verify install

WORKDIR /app

# 2.2 Copy only the manifest files to leverage Docker caching
COPY src/api/pyproject.toml src/api/poetry.lock /app/  

# 2.3 Enforce that lockfile is in sync before proceeding
RUN poetry lock || (echo "❌ pyproject.toml/poetry.lock mismatch—run poetry lock" >&2; exit 1)  # lock consistency

# 2.4 Install runtime deps: no dev, no project root, wheels only
RUN --mount=type=cache,target=/root/.cache/pypoetry \
    poetry install \
      --no-root \
      --no-interaction \
      --no-ansi

###################################
# 3) FINAL: Runtime-only Image   #
###################################
FROM base AS final

# 3.1 Copy Pandoc from builder (or install here if preferred)
# (Already in base via apt-get)

# 3.2 Copy the virtualenv from builder
COPY --from=builder /app/.venv /app/.venv
ENV PATH="/app/.venv/bin:$PATH"

WORKDIR /app
ENV PYTHONPATH=/app

# 3.3 Copy app source
COPY src/api    /app/api
COPY src/engine /app/engine
COPY src/shared /app/shared
COPY src/accessor /app/accessor
COPY src/accessor/__init__.py /app/accessor/__init__.py
COPY src/loader   /app/loader
COPY src/manager  /app/manager
COPY src/util     /app/util

# Set the PYTHONPATH environment variable to include all relevant directories
ENV PYTHONPATH=/app/api:/app/shared:/app/engine:/app/accessor:/app/loader:/app/manager

# Expose the port Gunicorn will run on
EXPOSE 5000

# Use Gunicorn to serve the application
ENTRYPOINT ["gunicorn"]

# Command to run the application with Gunicorn and the configuration file
CMD ["-w", "2", "-b", "0.0.0.0:5000", "-c", "/app/api/gunicorn_config.py", "wsgi:app"]
