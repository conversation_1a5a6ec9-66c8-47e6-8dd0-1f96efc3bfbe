from flask import request
from flask_restx import Resource, Namespace, reqparse, marshal, fields, abort

from sqlalchemy.exc import SQLAlchemyError

from shared.models.keyword import Keyword

from util.database import Session
from api.middleware.auth import secure_endpoint


# Create a namespace to be used for the swagger file
keyword_namespace = Namespace("keywords", description="Keyword related operations")

# Define how to marshal keyword data
keyword_fields = {
    "keyword_id": fields.Integer,
    "domain_id": fields.Integer,
    "subtopic_id": fields.Integer,
    "text": fields.String,
}


@keyword_namespace.route("keyword", methods=["POST"])
class CreateNewKeyword(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("domain_id", type=int, required=True, help="Domain ID cannot be blank.")
        parser.add_argument(
            "subtopic_id", type=int, required=True, help="Subtopic ID cannot be blank."
        )
        parser.add_argument("text", type=str, required=True, help="Keyword text cannot be blank.")
        args = parser.parse_args()

        session = Session()
        new_keyword = Keyword(
            domain_id=args["domain_id"], subtopic_id=args["subtopic_id"], text=args["text"]
        )

        try:
            session.add(new_keyword)
            session.commit()
            return marshal(new_keyword, keyword_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating keyword: " + str(e))
        finally:
            session.close()


@keyword_namespace.route("keyword/<int:keyword_id>")
class SingleKeyword(Resource):
    @secure_endpoint()
    def put(self, keyword_id):
        parser = reqparse.RequestParser()
        parser.add_argument("text", type=str, required=True, help="Keyword text cannot be blank.")
        args = parser.parse_args()

        session = Session()
        keyword = session.query(Keyword).filter_by(keyword_id=keyword_id).one_or_none()
        if not keyword:
            abort(404, message="Keyword not found")

        keyword.text = args["text"]

        try:
            session.commit()
            return marshal(keyword, keyword_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating keyword: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, keyword_id):
        session = Session()
        keyword = session.query(Keyword).filter_by(keyword_id=keyword_id).one_or_none()
        if not keyword:
            abort(404, message="Keyword not found")
        return marshal(keyword, keyword_fields), 200


@keyword_namespace.route("keywords/filter", methods=["POST"])
class GetKeywordsByFilter(Resource):
    @secure_endpoint()
    def post(self):
        session = Session()
        filter_data = request.json

        query = session.query(Keyword)

        # Apply filters based on the presence of keys in the filter_data
        if "domain_id" in filter_data:
            query = query.filter_by(domain_id=filter_data["domain_id"])
        if "topic_id" in filter_data:
            query = query.filter_by(topic_id=filter_data["topic_id"])
        if "subtopic_id" in filter_data:
            query = query.filter_by(subtopic_id=filter_data["subtopic_id"])

        keywords = query.all()
        return marshal(keywords, keyword_fields), 200
