from flask_restx import Namespace, Resource, fields, reqparse, marshal, abort
from sqlalchemy.exc import SQLAlchemyError

from shared.models.job_type import JobType
from shared.enums.job_enums import JobTypeEnum
from util.database import Session
from api.middleware.auth import secure_endpoint

# Create a namespace for job type related operations
job_type_namespace = Namespace("job types", description="Job type related operations")

# Define how to marshal job type data
job_type_fields = {
    "job_type_id": fields.Integer,
    "name": fields.String,
    "description": fields.String,
}


@job_type_namespace.route("job-type/<int:job_type_id>")
class SingleJobType(Resource):
    @secure_endpoint()
    def get(self, job_type_id):
        with Session() as session:
            job_type = session.query(JobType).filter_by(job_type_id=job_type_id).one_or_none()
            if not job_type:
                abort(404, message="Job type not found")

            job_type_dict = marshal(job_type, job_type_fields)
            job_type_dict["name"] = JobTypeEnum.from_id(job_type.job_type_id)

            return job_type_dict, 200

    @secure_endpoint()
    def put(self, job_type_id):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=False)
        parser.add_argument("description", type=str, required=False)
        args = parser.parse_args()

        with Session() as session:
            job_type = session.query(JobType).filter_by(job_type_id=job_type_id).one_or_none()
            if not job_type:
                abort(404, message="Job type not found")

            job_type.name = args.get("name", job_type.name)
            job_type.description = args.get("description", job_type.description)

            try:
                session.commit()
                return marshal(job_type, job_type_fields), 200
            except SQLAlchemyError as e:
                session.rollback()
                abort(500, message="Error updating job type: " + str(e))

    @secure_endpoint()
    def delete(self, job_type_id):
        with Session() as session:
            job_type = session.query(JobType).filter_by(job_type_id=job_type_id).one_or_none()
            if not job_type:
                abort(404, message="Job type not found")

            try:
                session.delete(job_type)
                session.commit()
                return {"message": "Job type deleted successfully"}, 200
            except SQLAlchemyError as e:
                session.rollback()
                abort(500, message="Error deleting job type: " + str(e))


@job_type_namespace.route("job-types", methods=["GET"])
class JobTypeList(Resource):
    @secure_endpoint()
    def get(self):
        with Session() as session:
            job_types = session.query(JobType).all()
            return marshal(job_types, job_type_fields), 200
