from flask_restx import Namespace, Resource, fields, marshal

from accessor.document.document_accessor import DocumentAccessor
from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY

from api.middleware.auth import secure_endpoint

# Create a namespace for the job status operations
job_status_namespace = Namespace("job-statuses", description="Job Status operations")

# Define the fields for the response model
job_status_model = job_status_namespace.model(
    "JobStatus",
    {
        "job_status_id": fields.Integer(description="The ID of the job status"),
        "name": fields.String(description="The name of the job status"),
        "description": fields.String(description="The description of the job status"),
        "state": fields.String(description="The state of the job status"),
        "sort_order": fields.Integer(description="The sort order of the job status"),
    },
)

LOGGER_NAME = "burst-api"
document_accessor = DocumentAccessor(
    access_key=AWS_FEED_MANAGER_ACCESS_KEY,
    secret_key=AWS_FEED_MANAGER_SECRET_KEY,
    logger_name=LOGGER_NAME,
)


@job_status_namespace.route("job-statuses")
class JobStatusResource(Resource):
    """
    Resource for retrieving job statuses with their associated sort order.

    This endpoint provides a list of all available job statuses, including
    details such as the job status ID, name, description, state, and sort order.
    The results are sorted based on a predefined sort order to maintain consistency
    across the application.

    Endpoint:
        - GET /job-statuses: Retrieve all job statuses with their sort order.

    Responses:
        - 200: Successfully returns a list of job statuses with their details.
        - 500: An internal server error occurred while processing the request.

    Example Usage:
        - GET /job-statuses: Returns a list of job statuses sorted in the specified order.
    """

    @secure_endpoint()
    def get(self):
        """
        Get all job statuses with their sort order.
        """
        try:
            job_statuses = document_accessor.get_job_statuses_with_sort_order()
            return marshal(job_statuses, job_status_model), 200
        except Exception:
            job_status_namespace.abort(500, "Error retrieving job statuses.")
