from flask import request
from flask_restx import Namespace, abort, fields, marshal
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource
from api.schemas.audit_activity_fields import audit_activity_fields
from shared.enums import AuditObjectType, AuditOperationType
from shared.models import RatingEnum  # Needed to deserialize the UserStory object
from shared.models import UserStory
from util.database import Session

# Create a namespace to be used for the swagger file
user_story_namespace = Namespace("user-stories", description="User story related operations")

# Define how to marshal user story data
user_story_fields = {
    "user_story_id": fields.Integer,
    "unique_statement_id": fields.Integer,
    "description": fields.String,
    "identifier": fields.String,
    "rating": fields.String(
        attribute=lambda x: (
            x.rating if isinstance(x.rating, str) else x.rating.value if x.rating else None
        )
    ),
    "user_entered": fields.Boolean,
    "edited": fields.Boolean,
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@user_story_namespace.route("user-story", methods=["POST"])
class CreateUserStory(BaseResource):
    @secure_endpoint()
    def post(self):
        data = request.get_json()

        # Validate main fields and handle errors early
        if field_validation_error := self.validate_required_fields(
            data, ["unique_statement_id", "description"]
        ):
            return field_validation_error
        if field_validation_error := self.validate_integer("unique_statement_id", data):
            return field_validation_error
        if field_validation_error := self.validate_string("description", data):
            return field_validation_error

        # Prepare fields for saving
        insert_fields = {k: v for k, v in data.items() if v is not None}

        # Any user-created user story is automatically given a postive rating
        insert_fields["user_entered"] = True
        insert_fields["rating"] = RatingEnum.positive

        try:
            new_user_story = content_accessor.save_user_story(insert_fields)
            if not new_user_story:
                abort(500, message="Error creating user story")

            document_id = content_accessor.get_document_id_by_user_story_id(
                new_user_story.user_story_id
            )
            counts_all_levels = content_accessor.get_document_counts_all_levels(document_id)

            result = marshal(new_user_story, user_story_fields)
            result["counts_all_levels"] = counts_all_levels

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating acceptance criteria: {e}")


@user_story_namespace.route("user-story/<int:user_story_id>")
class SingleUserStory(BaseResource):
    @secure_endpoint()
    def put(self, user_story_id: int):
        # Get the JSON payload
        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["description", "rating"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Validate each updated field using helper methods
        if "description" in updated_fields:
            if error := self.validate_text(updated_fields["description"]):
                return error
        if "rating" in updated_fields:
            if error := self.validate_rating(updated_fields["rating"]):
                return error

        # Automatically set `rating` to `positive` if `description` is updated and `rating` is not provided
        if ("description" in updated_fields) and "rating" not in updated_fields:
            updated_fields["rating"] = RatingEnum.positive

        try:
            updated_user_story = content_accessor.update_user_story(user_story_id, updated_fields)

            if not updated_user_story:
                abort(500, message="Error updating user story")

            result = marshal(updated_user_story, user_story_fields)

            document_id = content_accessor.get_document_id_by_user_story_id(user_story_id)
            counts = content_accessor.get_document_counts_all_levels(document_id)
            result["counts_all_levels"] = counts

            has_children = content_accessor.check_user_story_has_children(user_story_id)
            result["has_children"] = has_children

            return result, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating user story: {e}")

    @secure_endpoint()
    def delete(self, user_story_id):
        session = Session()
        user_story_to_delete = (
            session.query(UserStory).filter_by(user_story_id=user_story_id).one_or_none()
        )
        if not user_story_to_delete:
            abort(404, message="User story not found")

        try:
            content_accessor.delete_user_story(user_story_id)

            return {"message": "User story deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting user story: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, user_story_id):
        session = Session()
        user_story = session.query(UserStory).filter_by(user_story_id=user_story_id).one_or_none()
        if not user_story:
            abort(404, message="User story not found")
        result = marshal(user_story, user_story_fields)

        document_id = content_accessor.get_document_id_by_user_story_id(user_story_id)
        counts = content_accessor.get_document_counts_all_levels(document_id)

        result["counts_all_levels"] = counts

        return result, 200


@user_story_namespace.route(
    "user-stories/unique-statement/<int:unique_statement_id>", methods=["GET"]
)
class UserStoriesByUniqueStatementResource(BaseResource):
    @secure_endpoint()
    def get(self, unique_statement_id):
        user_stories = content_accessor.get_user_stories_for_unique_statement(unique_statement_id)

        if not user_stories:
            return {}, 200

        try:
            # Build the response with the has_children field
            user_stories_list = []
            for story in user_stories:
                user_story_dict = {
                    "user_story_id": story[0],  # user_story_id
                    "unique_statement_id": story[1],  # unique_statement_id
                    "description": story[2],  # description
                    "identifier": story[3],  # identifier
                    "rating": story[
                        4
                    ].value,  # rating (assuming it's an enum or object with a value)
                    "edited": story[5],
                    "user_entered": story[6],
                    "has_children": story[7]
                    > 0,  # criteria_count > 0 means there are acceptance_criteria
                }
                user_stories_list.append(user_story_dict)

            # Return the final response
            return user_stories_list, 200

        except Exception as e:
            return {"message": f"An internal error occurred: {e}"}, 500


@user_story_namespace.route("user-story/audit-activity/<int:user_story_id>", methods=["GET"])
class UserStoryAuditActivity(BaseResource):
    @secure_endpoint()
    def get(self, user_story_id: int):
        user_story_audit_activity = content_accessor.get_all_audit_activity_per_object(
            AuditObjectType.USER_STORY.value, user_story_id
        )

        if not user_story_audit_activity:
            return {
                "message": "No user story audit activity found for the given user_story_id"
            }, 404

        result = marshal(user_story_audit_activity, audit_activity_fields)

        return result, 200
