from flask_restx import Resource, Namespace, abort
from flask import request

from shared.models.enterprise import Enterprise

from util.database import Session
from api.middleware.auth import secure_endpoint


# Create a namespace to be used for the swagger file
enterprise_namespace = Namespace("enterprises", description="Enterprise related operations")


@enterprise_namespace.route("enterprises")
class MultipleEnterprises(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()

        try:
            enterprises = session.query(Enterprise).all()
            enterprise_list = [
                {"enterprise_id": enterprise.enterprise_id, "name": enterprise.name}
                for enterprise in enterprises
            ]
            return enterprise_list
        finally:
            session.close()


@enterprise_namespace.route("enterprise/<int:enterprise_id>")
class SingleEnterprise(Resource):
    @secure_endpoint()
    def get(self, enterprise_id):
        session = Session()

        try:
            enterprise = session.query(Enterprise).get(enterprise_id)
            if enterprise:
                enterprise_data = {
                    "enterprise_id": enterprise.enterprise_id,
                    "name": enterprise.name,
                    "businesses": [
                        {"business_id": business.business_id, "name": business.name}
                        for business in enterprise.businesses
                    ],
                }
                return enterprise_data
            return {"message": "Enterprise not found"}, 404
        finally:
            session.close()

    @secure_endpoint()
    def put(self, enterprise_id):
        session = Session()

        try:
            enterprise = session.query(Enterprise).get(enterprise_id)
            if not enterprise:
                return {"message": "Enterprise not found"}, 404

            data = request.json
            enterprise.name = data.get("name", enterprise.name)
            session.commit()

            enterprise_data = {"enterprise_id": enterprise.enterprise_id, "name": enterprise.name}
            return enterprise_data
        finally:
            session.close()
