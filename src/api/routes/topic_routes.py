from flask_restx import Namespace, Resource, abort, fields, marshal, reqparse
from sqlalchemy.exc import SQLAlchemyError

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware import auth
from shared.models.topic import Topic
from util.database import Session
from api.middleware.auth import secure_endpoint

# Create a namespace to be used for the swagger file
topic_namespace = Namespace("topics", description="Topics related operations")

# Define how to marshal topic data
topic_fields = {
    "topic_id": fields.Integer,
    "name": fields.String,
    "description": fields.String,
    "business_id": fields.Integer,
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@topic_namespace.route("topic", methods=["POST"])
class CreateTopic(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, help="Name cannot be blank.")
        parser.add_argument(
            "description", type=str, required=True, help="Description cannot be blank."
        )
        parser.add_argument("business_id", type=int, required=False, default=1, help="Business ID")
        args = parser.parse_args()

        session = Session()
        new_topic = Topic(
            name=args["name"], description=args["description"], business_id=args["business_id"]
        )

        try:
            session.add(new_topic)
            session.commit()
            return marshal(new_topic, topic_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating topic: " + str(e))
        finally:
            session.close()


@topic_namespace.route("topic/<int:topic_id>")
class SingleTopic(Resource):
    @secure_endpoint()
    def put(self, topic_id):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=False)
        parser.add_argument("description", type=str, required=False)
        parser.add_argument("business_id", type=int, required=False)
        args = parser.parse_args()

        session = Session()
        topic = session.query(Topic).filter_by(topic_id=topic_id).one_or_none()
        if not topic:
            abort(404, message="Topic not found")

        topic.name = args.get("name", topic.name)
        topic.description = args.get("description", topic.description)
        topic.business_id = args.get("business_id", topic.business_id)

        try:
            session.commit()
            return marshal(topic, topic_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating topic: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def delete(self, topic_id):
        session = Session()
        topic = session.query(Topic).filter_by(topic_id=topic_id).one_or_none()
        if not topic:
            abort(404, message="Topic not found")

        try:
            session.delete(topic)
            session.commit()
            return {"message": "Topic deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting topic: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, topic_id):
        session = Session()
        topic = session.query(Topic).filter_by(topic_id=topic_id).one_or_none()
        if not topic:
            abort(404, message="Topic not found")
        return marshal(topic, topic_fields), 200


@topic_namespace.route("topics", methods=["GET"])
class MultipleTopics(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()
        topics = session.query(Topic).all()
        return marshal(topics, topic_fields), 200


@topic_namespace.route("topics/business/<int:business_id>", methods=["GET"])
class TopicsByBusiness(Resource):
    @secure_endpoint()
    def get(self, business_id):
        session = Session()
        try:
            topics = session.query(Topic).filter_by(business_id=business_id).all()
            if not topics:
                abort(404, message="No topics found for the given business ID")
            return marshal(topics, topic_fields), 200
        except SQLAlchemyError as e:
            abort(500, message="Error retrieving topics: " + str(e))
        finally:
            session.close()


@topic_namespace.route("topics/enterprise/<int:enterprise_id>", methods=["GET"])
class TopicsByEnterprise(Resource):
    @secure_endpoint()
    def get(self, enterprise_id):
        try:
            topics = content_accessor.get_topics_by_enterprise(enterprise_id)
            if not topics:
                abort(404, message="No subtopics found for the given enterprise ID")
            return marshal(topics, topic_fields), 200

        except ContentAccessorException as e:
            abort(500, message=f"Error retrieving topics: {e}")
