from flask_restx import Resource, Namespace, reqparse, marshal, fields, abort
from sqlalchemy.exc import SQLAlchemyError

from shared.models.domain_group import DomainGroup
from util.database import Session
from api.middleware.auth import secure_endpoint


# Create a namespace to be used for the swagger file
domain_group_namespace = Namespace("domains-groups", description="Domain group related operations")

# Define how to marshal domain group data
domain_group_fields = {
    "domain_group_id": fields.Integer,
    "name": fields.String,
    "display_name": fields.String,
}


@domain_group_namespace.route("domain-group", methods=["POST"])
class CreateDomainGroup(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=True, help="Name cannot be blank.")
        parser.add_argument(
            "display_name", type=str, required=True, help="Display name cannot be blank."
        )
        args = parser.parse_args()

        session = Session()
        new_domain_group = DomainGroup(name=args["name"], display_name=args["display_name"])

        try:
            session.add(new_domain_group)
            session.commit()
            return marshal(new_domain_group, domain_group_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating domain group: " + str(e))
        finally:
            session.close()


@domain_group_namespace.route("domain-group/<int:domain_group_id>")
class SingleDomainGroup(Resource):
    @secure_endpoint()
    def put(self, domain_group_id):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=False)
        parser.add_argument("display_name", type=str, required=False)
        args = parser.parse_args()

        session = Session()
        domain_group = (
            session.query(DomainGroup).filter_by(domain_group_id=domain_group_id).one_or_none()
        )
        if not domain_group:
            abort(404, message="Domain group not found")

        domain_group.name = args.get("name", domain_group.name)
        domain_group.display_name = args.get("display_name", domain_group.display_name)

        try:
            session.commit()
            return marshal(domain_group, domain_group_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating domain group: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def delete(self, domain_group_id):
        session = Session()
        domain_group = (
            session.query(DomainGroup).filter_by(domain_group_id=domain_group_id).one_or_none()
        )
        if not domain_group:
            abort(404, message="Domain group not found")

        try:
            session.delete(domain_group)
            session.commit()
            return {"message": "Domain group deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting domain group: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, domain_group_id):
        session = Session()
        domain_group = (
            session.query(DomainGroup).filter_by(domain_group_id=domain_group_id).one_or_none()
        )
        if not domain_group:
            abort(404, message="Domain group not found")
        return marshal(domain_group, domain_group_fields), 200


@domain_group_namespace.route("domain-groups", methods=["GET"])
class DomainGroupsResource(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()
        domain_groups = session.query(DomainGroup).all()
        return marshal(domain_groups, domain_group_fields), 200
