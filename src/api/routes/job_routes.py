from flask import request
from flask_restx import Namespace, Resource, abort, fields, marshal, reqparse
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.document.document_accessor import DocumentAccessor
from accessor.document.enum.job_sort_order import JobSortOrder
from accessor.reference import ReferenceAccessor
from accessor.reference.exceptions.federal_citation_exceptions import (
    CitationServiceInternalError,
    CitationNumberNotFoundException,
    MalformedCitationNumberException,
    MissingRequiredTagsException,
)
from api.routes.base_resource import BaseResource
from shared.enums import JobStatusEnum, JobTypeEnum
from shared.models.business import Business
from shared.models.job import Job
from shared.models.job_enums import VALID_JOB_TYPES
from shared.models.job_status import JobStatus
from api.middleware.auth import secure_endpoint
from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY
from util.database import Session

# Create a namespace to be used for the swagger file
job_namespace = Namespace("jobs", description="Job related operations")


# Define the fields for marshalling
job_fields = {
    "job_id": fields.Integer,
    "reference_id": fields.Integer,
    "job_type_id": fields.Integer,
    "job_type": fields.String(attribute="job_type.name"),
    "job_status_id": fields.Integer,
    "job_status_name": fields.String(attribute="job_status.name"),
    "job_status_description": fields.String,
    "job_state": fields.String(attribute="job_status.state"),
    "submitted_datetime": fields.DateTime(dt_format="iso8601"),
    "job_name": fields.String,
    "deleted": fields.Boolean,
    "global_knowledge": fields.Boolean,
}

# Define the fields for marshalling document statistics
document_statistics_fields = {
    "word_count": fields.Integer,
    "difficult_word_count": fields.Integer,
    "average_sentence_length": fields.Float,
    "average_word_length": fields.Float,
    "flesch_grade_level": fields.Integer,
    "flesch_score": fields.Integer,
    "flesch_reading_ease": fields.String,
}

# Initialize ContentAccessor
LOGGER_NAME = "burst-api"
content_accessor = ContentAccessor(logger_name=LOGGER_NAME)
document_accessor = DocumentAccessor(
    access_key=AWS_FEED_MANAGER_ACCESS_KEY,
    secret_key=AWS_FEED_MANAGER_SECRET_KEY,
    logger_name="burst-api",
)
reference_accessor = ReferenceAccessor(logger_name=LOGGER_NAME)


def validate_enum(value, valid_values):
    if value not in valid_values:
        raise ValueError(f"Invalid value '{value}'. Valid values are: {', '.join(valid_values)}")
    return value


@job_namespace.route("job", methods=["POST"])
class CreateJob(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "reference_id",
            type=int,
            required=True,
            help="Reference ID cannot be blank.",
        )
        parser.add_argument(
            "job_type_id",
            type=lambda x: validate_enum(x, VALID_JOB_TYPES),
            required=True,
            help="Job type id",
        )
        parser.add_argument("job_status_id", type=int, required=False, help="Job status ID")
        parser.add_argument("global_knowledge", type=bool, required=False, default=False)
        args = parser.parse_args()

        session = Session()

        job_status_id = args.get("job_status_id")
        if job_status_id:
            job_status = (
                session.query(JobStatus).filter_by(job_status_id=job_status_id).one_or_none()
            )
            if not job_status:
                abort(400, message="Invalid job status ID provided.")
        else:
            job_status = (
                session.query(JobStatus)
                .filter_by(job_status_id=JobStatusEnum.INITIAL_PROCESSING.value)
                .one_or_none()
            )

        new_job = Job(
            reference_id=args["reference_id"],
            job_type_id=args.get("job_type_id", VALID_JOB_TYPES[0]),
            job_status_id=job_status.job_status_id,
            global_knowledge=args.get("global_knowledge", False),
        )

        try:
            session.add(new_job)
            session.commit()
            return marshal(new_job, job_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating job: " + str(e))
        finally:
            session.close()


@job_namespace.route("job/<int:job_id>")
class SingleJob(BaseResource):
    @secure_endpoint()
    def get(self, job_id):
        session = Session()
        try:
            job = (
                session.query(Job)
                .options(joinedload(Job.document), joinedload(Job.job_status))
                .filter_by(job_id=job_id)
                .one_or_none()
            )

            if not job:
                abort(404, message="Job not found")

            return marshal(job, job_fields), 200
        except Exception as e:
            abort(500, message="Error fetching job: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def put(self, job_id):
        # Get the JSON payload
        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["job_status_id"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Validate each updated field using helper methods
        if "job_status_id" in updated_fields:
            if error := self.validate_integer("job_status_id", data):
                return error

        try:
            updated_job = content_accessor.update_job(job_id, updated_fields)

            if not updated_job:
                abort(500, message="Error updating job")

            return marshal(updated_job, job_fields), 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating job: {e}")

    @secure_endpoint()
    def delete(self, job_id):
        session = Session()
        job = session.query(Job).filter_by(job_id=job_id).one_or_none()
        if not job:
            abort(404, message="Job not found")

        if job.global_knowledge:
            abort(403, message="Job cannot be deleted")

        try:
            job.deleted = True
            session.commit()
            return {"message": "Job marked as deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error marking job as deleted: " + str(e))
        finally:
            session.close()


@job_namespace.route("jobs/filter", methods=["POST"])
class JobFilterResource(BaseResource):
    """
    Resource for filtering and retrieving jobs based on various criteria.

    This endpoint allows users to filter jobs by business ID, domain ID, job status ID,
    and sort the results based on different fields such as creation date, name, or status.
    Sorting can be performed in either ascending or descending order.

    Request Parameters:
        - business_id (int, optional): The ID of the business to filter jobs by.
        - domain_id (int, optional): The ID of the domain to filter jobs by.
        - job_status_id (int, optional): The ID of the job status to filter jobs by.
        - sort_by (str, optional): The field to sort the results by. Defaults to "Default".
          Allowed values include "Default", "Created", "Name", and "Status".
        - ascending (bool, optional): Whether to sort the results in ascending order.
          Defaults to True.

    Responses:
        - 200: Successfully returns a list of jobs matching the criteria.
        - 400: Invalid input for sorting or other parameters.
        - 500: An internal server error occurred while retrieving the jobs.

    Example Usage:
        - POST /jobs/filter with a JSON body containing the desired filter parameters.
    """

    def get_all_business_id_to_filter_on(self, business_id: int) -> int | None:
        """
        Handles the special case of the "All" business ID by returning None if the provided
        business_id matches the "All" business ID.

        Args:
            business_id (int): The business ID to check

        Returns:
            int | None: Returns None if the business_id matches the "All" business ID,
                       otherwise returns the original business_id
        """
        all_business = content_accessor.get_all_business()
        all_business_id = all_business.business_id

        if business_id and business_id == all_business_id:
            # Set business_id to None to indicate no filtering for business_id
            # for backwards compatibility
            return None
        return business_id

    def get_sort_by_enum(self, sort_by: str) -> JobSortOrder:
        """
        Converts a string sort_by value to a JobSortOrder enum.

        Args:
            sort_by (str): The sort_by value to convert

        Returns:
            JobSortOrder: The corresponding JobSortOrder enum value

        Raises:
            ValueError: If the sort_by value is invalid
        """
        try:
            return JobSortOrder(sort_by)
        except ValueError:
            abort(400, message="Invalid sort_by value")

    def _format_job_status_description(self, job) -> str:
        """
        Formats the job status description based on job completion status and counts.

        Args:
            job: The job object containing status and count information

        Returns:
            str: A formatted description of the job status and counts
        """
        if job.job_status_id != JobStatusEnum.JOB_COMPLETE.value:
            return job.job_status_description

        return (
            f"{job.unique_statement_count:,} Requirements, "
            f"{job.user_story_count:,} US, "
            f"{job.acceptance_criteria_count:,} AC, "
            f"{job.test_case_count:,} TC"
        )

    def _prepare_jobs_for_response(self, jobs: list[Job]) -> None:
        """
        Prepares jobs for the API response by adding available versions, formatting status descriptions,
        and checking for change statements.

        Args:
            jobs (list[Job]): List of job objects to prepare
        """
        document_ids = [job.reference_id for job in jobs]
        version_map = content_accessor.get_available_document_versions_by_ids(document_ids)
        change_statements_map = content_accessor.get_documents_with_change_statements(document_ids)

        for job in jobs:
            job.job_status_description = self._format_job_status_description(job)
            job.available_versions = version_map.get(job.reference_id, [])
            job.has_change_statements = change_statements_map.get(job.reference_id, False)

    @secure_endpoint()
    def post(self):
        data = request.json

        # Optional fields with validation only if provided
        business_id_to_filter_on = data.get("business_id")
        if "business_id" in data and (error := self.validate_integer("business_id", data)):
            return error

        domain_id = data.get("domain_id")
        if "domain_id" in data and (error := self.validate_integer("domain_id", data)):
            return error

        job_status_id = data.get("job_status_id")
        if "job_status_id" in data and (error := self.validate_integer("job_status_id", data)):
            return error

        sort_by = data.get("sort_by", JobSortOrder.DEFAULT.value)
        if "sort_by" in data and (error := self.validate_string("sort_by", data)):
            return error

        ascending = data.get("ascending", True)
        if "ascending" in data and (error := self.validate_boolean("ascending", data)):
            return error

        business_id_to_filter_on = self.get_all_business_id_to_filter_on(business_id_to_filter_on)
        sort_by_enum = self.get_sort_by_enum(sort_by)

        try:
            jobs = document_accessor.get_jobs(
                business_id=business_id_to_filter_on,
                domain_id=domain_id,
                job_status_id=job_status_id,
                sort_by=sort_by_enum,
                ascending=ascending,
            )

            self._prepare_jobs_for_response(jobs)

            job_dto_fields = {
                "job_id": fields.Integer,
                "reference_id": fields.Integer,
                "job_type_id": fields.Integer,
                "job_status_id": fields.Integer,
                "job_status_name": fields.String,
                "job_status_description": fields.String,
                "job_state": fields.String(attribute=lambda obj: obj.job_state.value),
                "submitted_datetime": fields.DateTime(dt_format="iso8601"),
                "job_name": fields.String,
                "deleted": fields.Boolean,
                "global_knowledge": fields.Boolean,
                "curation_complete_datetime": fields.DateTime(dt_format="iso8601"),
                "domain_id": fields.Integer,
                "business_id": fields.Integer,
                "business_name": fields.String,
                "domain_name": fields.String,
                "has_change_statements": fields.Boolean,
                "document_statistics": fields.Nested(
                    document_statistics_fields, allow_null=False, skip_none=True
                ),
                "available_versions": fields.List(
                    fields.Nested(
                        {"label": fields.String, "type": fields.String, "version": fields.String}
                    ),
                    attribute=lambda obj: getattr(obj, "available_versions", []),
                ),
            }
            return marshal(jobs, job_dto_fields), 200
        except Exception:
            abort(500, message="Error filtering jobs.")


@job_namespace.route("jobs/validate-fed-citation-number", methods=["POST"])
class ValidateFedCitationNumberResource(Resource):

    @secure_endpoint()
    def post(self):
        data = request.json
        citation_number = data.get("citation_number")

        try:
            reference_accessor.validate_citation_number(citation_number)
            return {
                "status": "success",
                "message": "Federal Register citation number validated successfully",
            }, 200

        except (MalformedCitationNumberException, CitationNumberNotFoundException):
            return {"status": "error", "message": "Invalid Federal Register citation number"}, 400

        except MissingRequiredTagsException:
            return {
                "status": "error",
                "message": "The Federal Register does not provide the necessary details to evaluate this citation",
            }, 422

        except (CitationServiceInternalError, Exception) as e:
            reference_accessor.logger.exception("Unhandled exception during validation: %s", e)
            return {
                "status": "error",
                "message": "Burst is unable to process your request. Please try again later",
            }, 500
