import logging

from flask import request
from flask_restx import Resource

from util import file_upload


class FileValidationException(Exception):
    pass


class BaseResource(Resource):
    MAX_INT_VALUE = 2147483647

    def validate_integer(self, field_name: str, data):
        try:
            value = int(data[field_name])  # Attempt to convert the value to an integer
        except (ValueError, TypeError):
            return {"error": f"{field_name} is not a valid integer."}, 400

        if not (0 <= value <= self.MAX_INT_VALUE):
            return {
                "error": f"{field_name} must be a non-negative integer and <= {self.MAX_INT_VALUE}."
            }, 400

        return None

    def validate_string(self, field_name, data):
        value = data[field_name]
        if value is None or not isinstance(value, str) or not value.strip():
            return {"error": f"{field_name} cannot be blank and must be a string."}, 400
        return None

    def validate_required_fields(self, data, fields):
        missing = [f for f in fields if f not in data]
        if missing:
            return {"error": f"Missing required field(s): {', '.join(missing)}"}, 400
        return None

    def validate_text(self, text):
        if text is None or not isinstance(text, str) or not text.strip():
            return {"error": "Text cannot be blank and must be a string."}, 400
        return None

    def validate_rating(self, rating):
        if rating and rating not in ("negative", "positive", "unrated"):
            return {"error": "Rating must be 'negative', 'positive', or 'unrated'."}, 400
        return None

    def validate_boolean(self, field_name: str, data):
        """
        Validates that a field is a boolean value.

        Args:
            field_name (str): The name of the field to validate
            data (dict): The data dictionary containing the field

        Returns:
            tuple or None: Returns a tuple of (error_dict, status_code) if validation fails,
                          None if validation passes
        """
        value = data.get(field_name)
        if value is not None and not isinstance(value, bool):
            return {"error": f"{field_name} must be a boolean value."}, 400
        return None

    def validate_file_for_job_type(self, file_list, job_type_id: int):
        # Check if a file is in the request
        if "file" not in file_list:
            logging.error("No file part in request")
            raise FileValidationException("No file part in request")

        file = request.files["file"]
        logging.debug("File received: %s", file.filename)

        if file.filename == "":
            logging.error("No selected file")
            raise FileValidationException("No selected file")

        # Check if the file extension is allowed
        if not file_upload.is_extension_allowed(file.filename, job_type_id):
            file_extension = file_upload.get_file_extension(file.filename)
            logging.error("File extension '%s' not allowed", file_extension)
            raise FileValidationException(
                f"File extension '{file_extension}' not allowed for job_type_id of {job_type_id}"
            )

        return file
