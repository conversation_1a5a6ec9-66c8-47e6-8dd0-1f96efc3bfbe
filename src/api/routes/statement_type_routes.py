from flask_restx import Namespace, Resource, fields, reqparse, marshal, abort
from sqlalchemy.exc import SQLAlchemyError

from shared.models.statement_type import StatementType
from util.database import Session
from api.middleware.auth import secure_endpoint


# Create a namespace to be used for the swagger file
statement_type_namespace = Namespace(
    "statement types", description="Statement type related operations"
)

# Define how to marshal statement data
statement_type_fields = {
    "statement_type_id": fields.Integer,
    "name": fields.String,
    "description": fields.String,
}


@statement_type_namespace.route("statement-type", methods=["POST"])
class CreateStatement(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "statement_type_id", type=int, required=True, help="Statement Type ID cannot be blank."
        )
        parser.add_argument("name", type=str, required=True, help="Name cannot be blank.")
        parser.add_argument(
            "description", type=str, required=True, help="Description cannot be blank."
        )
        args = parser.parse_args()

        session = Session()
        new_statement_type = StatementType(
            statement_type_id=args["statement_type_id"],
            name=args["name"],
            description=args["description"],
        )

        try:
            session.add(new_statement_type)
            session.commit()
            return marshal(new_statement_type, statement_type_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating statement type: " + str(e))
        finally:
            session.close()


@statement_type_namespace.route("statement-type/<int:statement_type_id>")
class SingleStatementType(Resource):
    @secure_endpoint()
    def get(self, statement_type_id):
        session = Session()
        statement_type = (
            session.query(StatementType)
            .filter_by(statement_type_id=statement_type_id)
            .one_or_none()
        )
        if not statement_type:
            abort(404, message="Statement type not found")
        return marshal(statement_type, statement_type_fields), 200

    @secure_endpoint()
    def put(self, statement_type_id):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=False)
        parser.add_argument("description", type=str, required=False)
        args = parser.parse_args()

        session = Session()
        statement_type = (
            session.query(StatementType)
            .filter_by(statement_type_id=statement_type_id)
            .one_or_none()
        )
        if not statement_type:
            abort(404, message="Statement type not found")

        statement_type.name = args.get("name", statement_type.name)
        statement_type.description = args.get("description", statement_type.description)

        try:
            session.commit()
            return marshal(statement_type, statement_type_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating statement type: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def delete(self, statement_type_id):
        session = Session()
        statement_type = (
            session.query(StatementType)
            .filter_by(statement_type_id=statement_type_id)
            .one_or_none()
        )
        if not statement_type:
            abort(404, message="Statement type not found")

        try:
            session.delete(statement_type)
            session.commit()
            return {"message": "Statement type deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting statement type: " + str(e))
        finally:
            session.close()


@statement_type_namespace.route("statement-types", methods=["GET"])
class StatementTypeList(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()
        statement_types = session.query(StatementType).all()
        return marshal(statement_types, statement_type_fields), 200
