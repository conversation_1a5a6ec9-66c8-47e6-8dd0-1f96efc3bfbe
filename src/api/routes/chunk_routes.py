from flask_restx import Namespace, Resource, abort, fields, marshal, reqparse
from sqlalchemy import cast, func, case
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content.accessors.job_accessor import <PERSON>Accessor
from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException
from accessor.image import IChunkImageAccessor, ImageAccessor

from api.services.feed_manager_service import FeedManagerService
from shared.enums import JobStatusEnum
from shared.enums.statement_type_enum import StatementTypeEnum
from shared.models import Chunk, RatingEnum, Statement, StatementType, Subtopic, Topic
from util.database import Session
from api.middleware.auth import secure_endpoint

# Create a namespace to be used for the swagger file
chunk_namespace = Namespace("chunks", description="Chunk related operations")
LOGGER_NAME = "phoenix-burst-api"


# Define a custom marshalling function for statement type
class StatementTypeField(fields.Raw):
    def format(self, value):
        return value.name if value else None


# Define how to marshal statement data
statement_fields = {
    "statement_id": fields.Integer,
    "chunk_id": fields.Integer,
    # Use custom field
    "statement_type": StatementTypeField(attribute="statement_type"),
    "text": fields.String,
    "rating": fields.String(attribute=lambda x: x.rating.value if x.rating else None),
}

# Define how to marshal chunk data
chunk_fields_slim = {
    "chunk_id": fields.Integer,
    "document_id": fields.Integer,
    "vector_id": fields.String,
    "chunk_metadata": fields.Raw,
}

chunk_fields = {
    "chunk_id": fields.Integer,
    "document_id": fields.Integer,
    "formatted_text": fields.Raw,
    "text_content": fields.Raw,
    "chunk_metadata": fields.Raw,
    "statements": fields.List(fields.Nested(statement_fields)),
    "identifier": fields.String,
}

# Instantiate the FeedManagerService
feed_manager_service = FeedManagerService()

content_accessor = ContentAccessor(logger_name="burst-api")


@chunk_namespace.route("chunk", methods=["POST"])
class CreateChunk(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "document_id", type=int, required=True, help="Document ID cannot be blank."
        )
        parser.add_argument("formatted_text", type=dict, location="json", required=True)
        parser.add_argument("text_content", type=dict, location="json", required=True)
        parser.add_argument("identifier", type=str, required=True, default="", help="Identifier")
        args = parser.parse_args()

        session = Session()
        new_chunk = Chunk(
            document_id=args["document_id"],
            formatted_text=args["formatted_text"],
            text_content=args["text_content"],
            identifier=args["identifier"],
        )

        try:
            session.add(new_chunk)
            session.commit()
            return marshal(new_chunk, chunk_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating chunk: " + str(e))
        finally:
            session.close()


@chunk_namespace.route("chunk/<int:chunk_id>")
class SingleChunk(Resource):
    @secure_endpoint()
    def put(self, chunk_id):
        parser = reqparse.RequestParser()
        parser.add_argument("formatted_text", type=dict, location="json", required=False)
        parser.add_argument("text_content", type=dict, location="json", required=False)
        parser.add_argument("identifier", type=str, required=False)
        args = parser.parse_args()

        session = Session()
        chunk = session.query(Chunk).filter_by(chunk_id=chunk_id).one_or_none()
        if not chunk:
            abort(404, message="Chunk not found")

        if args.get("formatted_text") is not None:
            chunk.formatted_text = args.get("formatted_text")
        if args.get("text_content") is not None:
            chunk.text_content = args.get("text_content")
        if args.get("identifier") is not None:
            chunk.identifier = args.get("identifier")

        try:
            session.commit()
            return marshal(chunk, chunk_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating chunk: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def delete(self, chunk_id):
        session = Session()
        chunk = session.query(Chunk).filter_by(chunk_id=chunk_id).one_or_none()
        if not chunk:
            abort(404, message="Chunk not found")

        try:
            session.delete(chunk)
            session.commit()
            return {"message": "Chunk deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting chunk: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, chunk_id):
        session = Session()
        try:
            chunk = (
                session.query(Chunk)
                .options(joinedload(Chunk.statements).joinedload(Statement.statement_type))
                .filter_by(chunk_id=chunk_id)
                .one_or_none()
            )
            if not chunk:
                abort(404, message="Chunk not found")

            document_id = session.query(Chunk.document_id).filter_by(chunk_id=chunk_id).scalar()

            # Use helper function for subtopic and non-subtopic filtering
            filters = JobAccessor.get_job_status_statement_filters(
                document_id=document_id, chunk_id=chunk_id
            )

            # Retrieve statements with potential associated subtopic and topic information
            statements_data = (
                session.query(
                    Statement.statement_id,
                    Statement.text,
                    Statement.statement_type_id,
                    Statement.rating,
                    Statement.user_entered,
                    Statement.edited,
                    StatementType.name.label("type_name"),
                    Subtopic.subtopic_id,
                    Subtopic.name.label("subtopic_name"),
                    Subtopic.description,
                    Topic.name.label("topic_name"),
                )
                .outerjoin(
                    StatementType, Statement.statement_type_id == StatementType.statement_type_id
                )
                .outerjoin(
                    # Join based on text matching subtopic name
                    Subtopic,
                    Statement.text == Subtopic.name,
                )
                .outerjoin(Topic, Subtopic.topic_id == Topic.topic_id)
                .filter(*filters)
                .all()
            )

            grouped_statements = {
                "requirements": [],
                "opportunities": [],
                "definitions": [],
                "unassigned": [],
                "references": [],
                "authorizations": [],
                "discretions": [],
                "subtopics": [],
                "procedure_changes": [],
                "organizational_changes": [],
            }

            type_map = {
                "requirement": "requirements",
                "opportunity": "opportunities",
                "definition": "definitions",
                "unassigned": "unassigned",
                "reference information": "references",
                "authorization": "authorizations",
                "discretion": "discretions",
                "identified subtopic": "subtopics",
                "procedure change": "procedure_changes",
                "organizational change": "organizational_changes",
            }

            # Process each statement and group by type
            for (
                statement_id,
                text,
                statement_type_id,
                rating,
                user_entered,
                edited,
                type_name,
                subtopic_id,
                subtopic_name,
                description,
                topic_name,
            ) in statements_data:
                group_key = type_map.get(type_name.lower(), "unassigned")
                entry = {
                    "statement_id": statement_id,
                    "text": text,
                    "type": type_name,
                    "statement_type_id": statement_type_id,
                    "rating": rating.value if rating else None,
                    "user_entered": user_entered,
                    "edited": edited,
                }
                if group_key == "subtopics" and subtopic_id:
                    # Special formatting for 'subtopics'
                    entry = {
                        "statement_id": statement_id,
                        "text": description,
                        "rating": rating.value if rating else None,
                        "topic_name": topic_name,
                        "subtopic_name": subtopic_name,
                        "subtopic_id": subtopic_id,
                        "type": type_name,
                    }
                grouped_statements[group_key].append(entry)

            # Query the counts of ratings for the given chunk_id with conditional filters
            counts_data = (
                session.query(
                    func.count(Statement.statement_id)  # pylint: disable=not-callable
                    .filter(Statement.rating == RatingEnum.unrated.value)
                    .label("unrated_count"),
                    func.count(Statement.statement_id)  # pylint: disable=not-callable
                    .filter(Statement.rating == RatingEnum.positive.value)
                    .label("positive_count"),
                    func.count(Statement.statement_id)  # pylint: disable=not-callable
                    .filter(Statement.rating == RatingEnum.negative.value)
                    .label("negative_count"),
                )
                .filter(*filters)
                .one_or_none()
            )

            chunk_data = marshal(chunk, chunk_fields)
            chunk_data["statements"] = grouped_statements
            chunk_data["counts"] = {
                "unrated_count": counts_data.unrated_count,
                "positive_count": counts_data.positive_count,
                "negative_count": counts_data.negative_count,
            }

            image_accessor: IChunkImageAccessor = ImageAccessor(LOGGER_NAME)
            chunk_images = image_accessor.get_images_by_chunk(chunk_id)
            chunk_data["images"] = [
                {
                    "image_id": image.image_id,
                    "file_name": image.file_name,
                }
                for image in chunk_images
            ]
            return chunk_data, 200
        except SQLAlchemyError as e:
            abort(500, message="Error retrieving chunk: " + str(e))
        finally:
            session.close()


@chunk_namespace.route("chunks/regenerate-statements", methods=["POST"])
class GenerateUniqueRequirements(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("chunk_id", type=int, required=True, help="chunk_id cannot be blank.")
        args = parser.parse_args()

        chunk_id = args["chunk_id"]

        try:
            session = Session()

            # Retrieve the Chunk object using the chunk_id
            chunk = session.query(Chunk).filter_by(chunk_id=chunk_id).first()
            if not chunk:
                return {"message": "Chunk not found"}, 404

            # Retrieve the associated Document through the Chunk object
            document = chunk.document
            if not document:
                return {"message": "Document not found for the given chunk"}, 404

            # Retrieve the Job associated with the Document
            job = document.job
            if not job:
                return {"message": "Job not found for the given document"}, 404

            document_id = document.document_id
            job_id = job.job_id

            # Call the feed manager service and get the result and status code
            feed_manager_service.logger.info(
                "Received request to generate statements for document_id: %s, "
                "job_id: %s, chunk_id: %s",
                document_id,
                job_id,
                chunk_id,
            )

            result, status_code = feed_manager_service.regenerate_statements(
                document_id, job_id, chunk_id
            )

            # If the status code indicates success, proceed with the updates
            if status_code == 200:
                # Update the job's status to "Initial Processing"
                # Replace with the actual status ID for "Initial Processing"
                job.job_status_id = JobStatusEnum.INITIAL_PROCESSING.value

                # Commit the transaction
                session.commit()

            return result, status_code

        except Exception as e:
            session.rollback()
            feed_manager_service.logger.error("Error processing request: %s", str(e))
            return {"message": "An error occurred while processing the request."}, 500

        finally:
            session.close()


@chunk_namespace.route("chunks/document/<int:document_id>", methods=["GET"])
class MultipleChunks(Resource):
    @secure_endpoint()
    def get(self, document_id):
        session = Session()
        try:
            # Retrieve all chunks for the given document
            all_chunks_query = (
                session.query(
                    Chunk.chunk_id,
                    Chunk.document_id,
                    cast(Chunk.vector_id, UUID).label("vector_id"),
                    Chunk.chunk_metadata,
                )
                .filter(Chunk.document_id == document_id)
                .order_by(Chunk.chunk_id)
                .all()
            )
            # Use helper function for subtopic and non-subtopic filtering
            filters = JobAccessor.get_job_status_statement_filters(document_id)

            # Query unrated statement counts based on filters
            unrated_counts_query = (
                session.query(
                    Chunk.chunk_id,
                    func.count(case((Statement.rating == RatingEnum.unrated.value, 1))).label(
                        "unrated_statements_count"
                    ),
                )
                .outerjoin(Statement, Chunk.chunk_id == Statement.chunk_id)
                .filter(*filters)
                .group_by(Chunk.chunk_id)
                .all()
            )
            unrated_counts_map = {
                row.chunk_id: int(row.unrated_statements_count) for row in unrated_counts_query
            }

            # Query for subtopic counts (independent of job status)
            subtopic_counts_query = (
                session.query(
                    Chunk.chunk_id,
                    func.count(case((Statement.rating == RatingEnum.unrated.value, 1))).label(
                        "unrated_count"
                    ),
                    func.count(case((Statement.rating == RatingEnum.positive.value, 1))).label(
                        "positive_count"
                    ),
                    func.count(case((Statement.rating == RatingEnum.negative.value, 1))).label(
                        "negative_count"
                    ),
                )
                .outerjoin(Statement, Chunk.chunk_id == Statement.chunk_id)
                .filter(Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value)
                .group_by(Chunk.chunk_id)
                .all()
            )
            subtopic_counts_map = {
                row.chunk_id: {
                    "unrated_count": int(row.unrated_count),
                    "positive_count": int(row.positive_count),
                    "negative_count": int(row.negative_count),
                }
                for row in subtopic_counts_query
            }

            # Assemble chunk data for response
            chunk_data_list = []
            for chunk_data in all_chunks_query:
                chunk_dict = {
                    "chunk_id": chunk_data.chunk_id,
                    "document_id": chunk_data.document_id,
                    "vector_id": str(chunk_data.vector_id),  # Convert UUID to string
                    "chunk_metadata": chunk_data.chunk_metadata,
                    "subtopic_counts": subtopic_counts_map.get(
                        chunk_data.chunk_id,
                        {"unrated_count": 0, "positive_count": 0, "negative_count": 0},
                    ),
                    "unrated_count": unrated_counts_map.get(chunk_data.chunk_id, 0),
                }
                chunk_data_list.append(chunk_dict)
            return chunk_data_list, 200
        except SQLAlchemyError as e:
            abort(500, message="Error retrieving chunks: " + str(e))
        finally:
            session.close()


@chunk_namespace.route("chunk/<int:chunk_id>/subtopics", methods=["POST"])
class UpdateChunkSubtopics(Resource):
    @secure_endpoint()
    def post(self, chunk_id):
        parser = reqparse.RequestParser()
        parser.add_argument("subtopic_ids", type=list, location="json", required=True)
        args = parser.parse_args()
        subtopic_ids = args.get("subtopic_ids", [])

        try:
            chunk = content_accessor.update_chunk_subtopics(chunk_id, subtopic_ids)

            return marshal(chunk, chunk_fields), 200
        except ContentAccessorException as e:
            return {"message": f"An internal error occurred: {e}"}, 500
