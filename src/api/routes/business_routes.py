from flask_restx import Resource, Namespace, abort
from flask import request

from shared.models import Business

from util.database import Session
from api.middleware.auth import secure_endpoint

# Create a namespace to be used for the swagger file
business_namespace = Namespace("business", description="Business related operations")


@business_namespace.route("businesses")
class MultipleBusinesses(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()
        try:
            # Retrieve query parameter to determine whether to include "All"
            include_all = request.args.get("include_all", "false").lower() == "true"

            # Filter businesses based on the 'include_all' parameter
            if include_all:
                businesses = session.query(Business).all()
            else:
                businesses = session.query(Business).filter(Business.name != "All").all()

            business_list = [
                {"business_id": business.business_id, "name": business.name}
                for business in businesses
            ]
            return business_list
        finally:
            session.close()


@business_namespace.route("business/<int:business_id>")
class SingleBusiness(Resource):
    @secure_endpoint()
    def get(self, business_id):
        session = Session()

        try:
            business = session.query(Business).get(business_id)
            if business:
                business_data = {"business_id": business.business_id, "name": business.name}
                return business_data
            return {"message": "Business not found"}, 404
        finally:
            session.close()

    @secure_endpoint()
    def put(self, business_id):
        session = Session()

        try:
            business = session.query(Business).get(business_id)
            if not business:
                return {"message": "Business not found"}, 404

            data = request.json
            business.name = data.get("name", business.name)
            session.commit()

            business_data = {"business_id": business.business_id, "name": business.name}
            return business_data
        finally:
            session.close()
