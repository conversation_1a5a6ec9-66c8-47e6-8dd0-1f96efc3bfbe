import json
import logging

from accessor.content.accessors.dto import DocumentInsertDTO
from flask_restx import Namespace, Resource, abort, fields, marshal_with, reqparse

from accessor.content import ContentAccessor
from api.services.feed_manager_service import FeedManagerService
from shared.enums import JobStatusEnum
from shared.models.domain_comparison import DomainComparison
from shared.enums.job_enums import JobType<PERSON>num
from util.database import Session
from api.middleware.auth import secure_endpoint

logger = logging.getLogger(__name__)


# Create a namespace to be used for the swagger file
change_statement_namespace = Namespace(
    "change statements", description="Change Statement related operations"
)

# Instantiate the FeedManagerService
feed_manager_service = FeedManagerService()

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@change_statement_namespace.route("change-statements/generate-from-citation-api", methods=["POST"])
class FederalRegisterChangeStatementResource(Resource):
    @secure_endpoint()
    def post(self):

        parser = reqparse.RequestParser()
        parser.add_argument(
            "federal_register_document_number",
            type=str,
            required=True,
            help="federal_register_document_number cannot be blank",
        )
        parser.add_argument(
            "domain_id",
            type=int,
            required=True,
            help="domain_id cannot be blank",
        )
        parser.add_argument(
            "job_name",
            type=str,
            required=True,
            help="job_name cannot be blank",
        )
        parser.add_argument(
            "business_id",
            type=int,
            required=True,
            help="business_id cannot be blank",
        )
        args = parser.parse_args()

        federal_register_document_number = args["federal_register_document_number"]
        domain_id = args["domain_id"]
        job_name = args["job_name"]
        business_id = args["business_id"]

        # Leave s3_location blank for now because there isn't a physical file to be stored
        new_document_dto = DocumentInsertDTO(
            domain_id=domain_id,
            name=job_name,
            s3_location=federal_register_document_number,
            business_id=business_id,
        )
        new_document = content_accessor.save_document(new_document_dto)

        job_insert_fields = {}
        job_insert_fields["reference_id"] = new_document.document_id
        job_insert_fields["job_status_id"] = JobStatusEnum.INITIAL_PROCESSING.value
        job_insert_fields["job_type_id"] = JobTypeEnum.FEDERAL_REGISTER_CHANGE_IDENTIFICATION.value

        new_change_statement_job = content_accessor.save_job(job_insert_fields)

        feed_manager_service.logger.info(
            f"Received request to generate federal register change statements. domain_id: {domain_id}, document_id: {new_document.document_id}, job_id {new_change_statement_job.job_id}"
        )

        result, status_code = feed_manager_service.generate_federal_register_change_statements(
            new_document.document_id, new_change_statement_job.job_id, domain_id
        )

        return result, status_code
