"""
This module provides an API endpoint for image operations, including
retrieving image content by image ID.
"""

import base64

from flask import jsonify
from flask_restx import Namespace, Resource, fields

from accessor.image import IImageAccessor, ImageAccessor, ImageContentDTO

from api.middleware.auth import secure_endpoint

images_namespace = Namespace("images", description="Image Operations")
LOGGER_NAME = "phoenix-burst-api"
image_accessor: IImageAccessor = ImageAccessor(LOGGER_NAME)

image_metadata_model = images_namespace.model(
    "ImageMetadata",
    {
        "image_id": fields.Integer(required=True, description="The image identifier"),
        "file_name": fields.String(required=True, description="The name of the image file"),
        "metadata": fields.Raw(description="Additional metadata for the image"),
        "created_at": fields.DateTime(description="Image creation timestamp"),
        "updated_at": fields.DateTime(description="Image last update timestamp"),
    },
)


@images_namespace.route("images/<int:image_id>")
class ImageResource(Resource):
    """
    A RESTful resource for retrieving image content by ID.

    This resource allows users to fetch an image from storage by specifying its ID in the URL.
    The image is returned as a JSON object with a base64-encoded byte array.
    """

    @secure_endpoint()
    @images_namespace.response(200, "Success", image_metadata_model)
    @images_namespace.response(404, "Image not found")
    def get(self, image_id):
        """
        Retrieve an image by ID and return it as a JSON object
        with base64-encoded content and metadata.
        """
        try:
            image: ImageContentDTO = image_accessor.get_image_content(image_id)
            if not image or not image.content:
                images_namespace.abort(404, "Image not found")

            encoded_content = base64.b64encode(image.content).decode("utf-8")

            response = {
                "image_id": image.image_id,
                "file_name": image.file_name,
                "content_type": "image/png",
                "byte_array": encoded_content,
            }

            # Return as JSON
            return jsonify(response)

        except Exception as e:
            images_namespace.abort(500, f"Error retrieving image: {str(e)}")
