from flask_restx import Resource, Namespace, fields, marshal, abort
from flask import request

from sqlalchemy.orm import joinedload
from sqlalchemy import func, distinct, case

from shared.models import (
    Domain,
    UniqueStatementSubtopicAssociation,
    UniqueStatement,
    Subtopic,
    StatementType,
    Keyword,
)

from util.database import Session
from api.middleware.auth import secure_endpoint


# Create a namespace to be used for the swagger file
domain_namespace = Namespace("domains", description="Domain related operations")


domain_summary_fields = {
    "domain_name": fields.String,
    "domain_id": fields.Integer,
    "topic_count": fields.Integer,
    "subtopic_count": fields.Integer,
    "requirement_count": fields.Integer,
    "opportunity_count": fields.Integer,
    "authorization_count": fields.Integer,
    "procedure_change_count": fields.Integer,
    "organizational_change_count": fields.Integer,
    "discretion_count": fields.Integer,
    "keyword_count": fields.Integer,
    "is_active": fields.<PERSON>an,
}


@domain_namespace.route("domains")
class MultipleDomains(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()

        try:
            # Only return active domains for now
            domains = session.query(Domain).filter_by(is_active=True).all()

            domain_list = [
                {
                    "domain_id": domain.domain_id,
                    "name": domain.name,
                    "display_name": domain.display_name,
                    "is_active": domain.is_active,
                }
                for domain in domains
            ]
            return domain_list
        finally:
            session.close()


@domain_namespace.route("domain/<int:domain_id>")
class SingleDomain(Resource):
    @secure_endpoint()
    def get(self, domain_id):
        session = Session()

        try:
            # Fetch domain with all related unique statements, subtopics, and topics
            domain = (
                session.query(Domain)
                .options(
                    joinedload(Domain.unique_statements)
                    .joinedload(UniqueStatement.subtopic)
                    .joinedload(Subtopic.topic)
                )
                .get(domain_id)
            )
            if domain is not None:
                # Extract domain details
                domain_details = {
                    "domain_id": domain.domain_id,
                    "name": domain.name,
                    "display_name": domain.display_name,
                    "vector_database": domain.vector_database_name,
                }

                # Initialize containers for topics, subtopics, and unique statements
                topics = {}
                subtopics = {}
                unique_statements = []

                # Extract unique statements, topics, and subtopics from the domain
                for us in domain.unique_statements:
                    topic = us.subtopic.topic
                    subtopic = us.subtopic

                    # Aggregate unique statement details with topic and subtopic names
                    unique_statements.append(
                        {
                            "unique_statement_id": us.unique_statement_id,
                            "text": us.text,
                            "identifier": us.identifier,
                            "rating": us.rating,
                            "subtopic_id": subtopic.subtopic_id,
                            # Add topic and subtopic names
                            "topic_subtopic": f"Topic: {topic.name} > {subtopic.name}",
                        }
                    )

                    # Add topics to the dictionary if not already present
                    if topic.topic_id not in topics:
                        topics[topic.topic_id] = {
                            "topic_id": topic.topic_id,
                            "name": topic.name,
                            "description": topic.description,
                        }

                    # Add subtopics to the dictionary if not already present
                    if subtopic.subtopic_id not in subtopics:
                        subtopics[subtopic.subtopic_id] = {
                            "subtopic_id": subtopic.subtopic_id,
                            "name": subtopic.name,
                            "description": subtopic.description,
                            "topic_id": topic.topic_id,  # Link subtopic to its topic
                        }

                # Organize subtopics under their topics
                for subtopic in subtopics.values():
                    # Remove topic_id from subtopic details
                    topic_id = subtopic.pop("topic_id")
                    if "subtopics" not in topics[topic_id]:
                        topics[topic_id]["subtopics"] = []
                    topics[topic_id]["subtopics"].append(subtopic)

                # Add topics and unique statements list to domain details
                domain_details["topics"] = list(topics.values())
                domain_details["unique_statements"] = unique_statements

                return domain_details
            else:
                return {"message": "Domain not found"}, 404
        finally:
            session.close()

    @secure_endpoint()
    def put(self, domain_id):
        session = Session()

        try:
            domain = session.query(Domain).filter(Domain.domain_id == domain_id).one_or_none()
            if domain is not None:
                data = request.get_json()
                domain.name = data.get("name", domain.name)
                domain.display_name = data.get("display_name", domain.display_name)
                domain.vector_database_name = data.get(
                    "vector_database_name", domain.vector_database_name
                )
                session.commit()
                return {"message": "Domain updated successfully"}
            else:
                return {"message": "Domain not found"}, 404
        finally:
            session.close()


@domain_namespace.route("domains/subtopic_id/<int:subtopic_id>", methods=["GET"])
class MultipleDomainsWithCounts(Resource):
    @secure_endpoint()
    def get(self, subtopic_id):
        session = Session()

        try:
            results = (
                session.query(
                    Domain.name.label("domain_name"),
                    Domain.domain_id.label("domain_id"),
                    func.coalesce(func.count(distinct(Subtopic.topic_id)), 0).label("topic_count"),
                    func.coalesce(func.count(distinct(Subtopic.subtopic_id)), 0).label(
                        "subtopic_count"
                    ),
                    func.coalesce(
                        func.sum(case((StatementType.name == "requirement", 1), else_=0)), 0
                    ).label("requirement_count"),
                    func.coalesce(
                        func.sum(case((StatementType.name == "opportunity", 1), else_=0)), 0
                    ).label("opportunity_count"),
                    func.coalesce(
                        func.sum(case((StatementType.name == "authorization", 1), else_=0)), 0
                    ).label("authorization_count"),
                    func.coalesce(
                        func.sum(case((StatementType.name == "procedure_change", 1), else_=0)), 0
                    ).label("procedure_change_count"),
                    func.coalesce(
                        func.sum(case((StatementType.name == "organizational_change", 1), else_=0)),
                        0,
                    ).label("organizational_change_count"),
                    func.coalesce(
                        func.sum(case((StatementType.name == "discretion", 1), else_=0)), 0
                    ).label("discretion_count"),
                    func.coalesce(func.count(Keyword.keyword_id), 0).label("keyword_count"),
                )
                .outerjoin(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                )
                .outerjoin(
                    UniqueStatement,
                    (Domain.domain_id == UniqueStatement.domain_id)
                    & (
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id
                    ),
                )
                .outerjoin(
                    Subtopic, UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id
                )
                .outerjoin(
                    StatementType,
                    UniqueStatement.statement_type_id == StatementType.statement_type_id,
                )
                .outerjoin(
                    Keyword,
                    (Keyword.domain_id == Domain.domain_id) & (Keyword.subtopic_id == subtopic_id),
                )
                .filter(Domain.is_active.is_(True))
                .group_by(Domain.name, Domain.domain_id)
                .all()
            )

            return marshal(results, domain_summary_fields), 200
        finally:
            session.close()
