from datetime import datetime

from flask import request
from flask_restx import Namespace, Resource, abort, fields, marshal, reqparse
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content import ContentAccessor
from accessor.content.accessors.dto import DocumentInsertDTO, DocumentUpdateDTO
from accessor.content.exceptions import ContentAccessorException
from accessor.content.accessors.document_accessor import (
    VERSION_ORIGINAL_WORD,
    VERSION_OPTIMIZED_WORD,
    VERSION_OPTIMIZED_PDF,
    VERSION_ORIGINAL_PDF,
)
from api.services.feed_manager_service import FeedManagerService
from shared.enums import JobStatusEnum
from shared.enums.job_enums import JobTypeEnum
from shared.models import Document, Job
from api.middleware.auth import secure_endpoint
from util.config import (
    AWS_FEED_MANAGER_ACCESS_KEY,
    AWS_FEED_MANAGER_SECRET_KEY,
    AWS_REGION,
    AWS_S3_BUCKET_NAME,
)
from util.database import Session
from util.s3_helper import S3H<PERSON>per

# Create a namespace to be used for the swagger file
document_namespace = Namespace("documents", description="Document related operations")

# Define how to marshal document data
document_fields = {
    "document_id": fields.Integer,
    "domain_id": fields.Integer,
    "name": fields.String,
    "s3_location": fields.String,
    "business_id": fields.Integer,
    "effective_datetime": fields.DateTime,
    "start_page": fields.Integer,
    "end_page": fields.Integer,
    "job_id": fields.Integer(attribute="job.job_id"),
}

# Instantiate the FeedManagerService
feed_manager_service = FeedManagerService()

LOGGER_NAME = "burst-api"

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name=LOGGER_NAME)


@document_namespace.route("document", methods=["POST"])
class CreateDocument(Resource):
    @secure_endpoint()
    def post(self):
        try:
            # Get the JSON payload
            data = request.get_json()

            # Validate required fields
            required_fields = ["domain_id", "name", "s3_location", "effective_datetime"]
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return {"error": f"Missing required fields: {', '.join(missing_fields)}"}, 400

            # Field validation
            if not isinstance(data.get("domain_id"), int) or data.get("domain_id") < 0:
                return {"error": "Domain ID must be a non-negative integer."}, 400

            if not isinstance(data.get("name"), str):
                return {"error": "Name must be a string."}, 400

            if not isinstance(data.get("s3_location"), str):
                return {"error": "S3 location must be a string."}, 400

            if not isinstance(data.get("business_id", 1), int) or data.get("business_id", 1) < 0:
                return {"error": "Business ID must be a non-negative integer."}, 400

            if data.get("start_page") is not None:
                if not isinstance(data["start_page"], int) or data["start_page"] < 1:
                    return {
                        "error": "Start page must be an integer greater than or equal to 1."
                    }, 400

            if data.get("end_page") is not None:
                if not isinstance(data["end_page"], int) or (
                    data["end_page"] != -1 and data["end_page"] < 1
                ):
                    return {
                        "error": "End page must be -1 or an integer greater than or equal to 1."
                    }, 400

            # Validate effective_datetime format
            try:
                effective_datetime = datetime.fromisoformat(
                    data["effective_datetime"].replace("Z", "+00:00")
                )
            except ValueError:
                return {"error": "Invalid datetime format for effective_datetime"}, 400

            # Use defaults if optional fields are not provided
            start_page = data.get("start_page", 1)
            end_page = data.get("end_page", -1)
            business_id = data.get("business_id", 1)

            # Map validated data to the DTO
            document_dto = DocumentInsertDTO(
                domain_id=data["domain_id"],
                name=data["name"],
                s3_location=data["s3_location"],
                business_id=business_id,
                effective_datetime=effective_datetime,
                start_page=start_page,
                end_page=end_page,
            )

            # Pass the DTO to the accessor
            new_document = content_accessor.save_document(document_dto)

            print("Marshaled Response:", marshal(new_document, document_fields))

            return marshal(new_document, document_fields), 201

        except ContentAccessorException as e:
            return {"error": f"Error saving document: {e}"}, 500
        except Exception as e:
            return {"error": f"Unexpected error: {e}"}, 500


@document_namespace.route("document/<int:document_id>")
class SingleDocument(Resource):
    @secure_endpoint()
    def put(self, document_id):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=False)
        parser.add_argument("s3_location", type=str, required=False)
        parser.add_argument("business_id", type=int, required=False)
        parser.add_argument("effective_datetime", type=str, required=False)
        parser.add_argument("start_page", type=int, required=False)
        parser.add_argument("end_page", type=int, required=False)
        args = parser.parse_args()

        # Fetch the existing document
        document = content_accessor.get_document_by_id(document_id)
        if not document:
            abort(404, message="Document not found")

        # Parse and validate the effective_datetime if provided
        effective_datetime = None
        if args.get("effective_datetime"):
            try:
                effective_datetime = datetime.fromisoformat(
                    args["effective_datetime"].replace("Z", "+00:00")
                )
            except ValueError:
                return {"error": "Invalid datetime format for effective_datetime"}, 400

        # Create the DTO with updated values
        update_dto = DocumentUpdateDTO(
            name=args.get("name"),
            s3_location=args.get("s3_location"),
            business_id=args.get("business_id"),
            effective_datetime=effective_datetime,
            start_page=args.get("start_page"),
            end_page=args.get("end_page"),
        )

        # Update the document using the DTO
        updated_document = content_accessor.update_document(document_id, update_dto)

        return marshal(updated_document, document_fields), 200

    @secure_endpoint()
    def delete(self, document_id):
        document = content_accessor.get_document_by_id(document_id)

        if not document:
            abort(404, message="Document not found")

        return content_accessor.delete_document(document_id), 200

    @secure_endpoint()
    def get(self, document_id):
        document = content_accessor.get_document_by_id(document_id)

        if not document:
            abort(404, message="Document not found")

        global_knowledge = document.job.global_knowledge if document.job else None
        statement_counts = content_accessor.get_document_statement_counts(document_id) or {}
        unique_requirement_counts = (
            content_accessor.get_document_unique_requirement_counts(document_id) or {}
        )
        chunks_remaining = content_accessor.get_document_count_chunks_with_unrated_statements(
            document_id
        )
        bulk_generation_button_statuses = (
            content_accessor.get_document_bulk_generation_button_statuses(document_id)
        )
        counts_all_levels = content_accessor.get_document_counts_all_levels(document_id=document_id)

        marshaled_document = marshal(document, document_fields)

        # Add the additional fields manually
        result = {
            **marshaled_document,  # Unpack the marshaled document fields
            "global_knowledge": global_knowledge,
            "statement_counts": statement_counts,
            "unique_requirement_counts": unique_requirement_counts,
            "chunks_remaining": chunks_remaining,
            "counts_all_levels": counts_all_levels,
            **bulk_generation_button_statuses,
        }

        return result, 200


@document_namespace.route("document/<int:document_id>/statistics", methods=["GET"])
class DocumentStatistics(Resource):
    @secure_endpoint()
    def get(self, document_id):
        try:
            optimized_stats, original_stats = (
                content_accessor.get_document_statistics_by_document_id(document_id)
            )

            stats_fields = {
                "word_count": fields.Integer,
                "difficult_word_count": fields.Integer,
                "average_sentence_length": fields.Float,
                "average_word_length": fields.Float,
                "flesch_grade_level": fields.Integer,
                "flesch_score": fields.Integer,
                "flesch_reading_ease": fields.String,
                "created_at": fields.DateTime,
                "updated_at": fields.DateTime,
            }

            response = {}
            if optimized_stats:
                response["document_statistics"] = marshal(optimized_stats, stats_fields)
            if original_stats:
                response["original_document_statistics"] = marshal(original_stats, stats_fields)

            return response, 200

        except Exception as e:
            return {"error": f"Error getting document statistics: {str(e)}"}, 500


@document_namespace.route("documents/domain/<int:domain_id>", methods=["GET"])
class DocumentsByDomainResource(Resource):
    @secure_endpoint()
    def get(self, domain_id):
        session = Session()
        documents = session.query(Document).filter_by(domain_id=domain_id).all()
        return marshal(documents, document_fields), 200


@document_namespace.route("documents/global_knowledge", methods=["GET"])
class GlobalKnowledgeDocuments(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()
        jobs_with_global_knowledge = (
            session.query(Job)
            .filter_by(global_knowledge=True)
            .options(joinedload(Job.document))
            .all()
        )
        documents = [job.document for job in jobs_with_global_knowledge if job.document]
        return marshal(documents, document_fields), 200


@document_namespace.route("documents/global_knowledge_explorer", methods=["GET"])
class GlobalGlobalKnowledgeExplorerDocuments(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()

        status_ids = [
            JobStatusEnum.READY_FOR_REQUIREMENT_CURATION.value,
            JobStatusEnum.JOB_COMPLETE.value,
        ]

        explorer_jobs = (
            session.query(Job)
            .filter(Job.global_knowledge.is_(True), Job.job_status_id.in_(status_ids))
            .options(joinedload(Job.document))
            .all()
        )
        documents = [job.document for job in explorer_jobs if job.document]
        return marshal(documents, document_fields), 200


@document_namespace.route("documents/procedure-library", methods=["GET"])
class ReferenceDocumentProcedureLibraryDocuments(Resource):
    @secure_endpoint()
    def get(self):

        try:
            job_types = [JobTypeEnum.PROCEDURE_IMPORT.value, JobTypeEnum.OPTIMIZE_PROCEDURE.value]
            documents = content_accessor.get_documents_by_job_type(job_types)

            return marshal(documents, document_fields), 200

        except ContentAccessorException as e:
            return {"message": str(e)}, 500


# Shared functionality goes inside of a base class
class BaseDocumentResource(Resource):
    @staticmethod
    def parse_args():
        parser = reqparse.RequestParser()
        parser.add_argument(
            "document_id", type=int, required=True, help="Document ID cannot be blank."
        )
        parser.add_argument("job_id", type=int, required=True, help="Job ID cannot be blank.")
        return parser.parse_args()


@document_namespace.route("document/generate-unique-requirements", methods=["POST"])
class GenerateUniqueRequirements(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()
        document_id = args["document_id"]
        job_id = args["job_id"]

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to generate unique requirements for document_id: {document_id}, job_id: {job_id}",
            service_function=feed_manager_service.generate_unique_requirements,
        )


@document_namespace.route("document/generate-requirements-bundle", methods=["POST"])
class GenerateRequirementsBundle(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()
        document_id = args["document_id"]
        job_id = args["job_id"]

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to generate requirements bundle for document_id: {document_id}, job_id: {job_id}",
            service_function=feed_manager_service.generate_requirements_bundle,
        )


@document_namespace.route("document/generate-user-stories", methods=["POST"])
class GenerateUserStories(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()

        try:
            document_id = int(args["document_id"])
            job_id = int(args["job_id"])
        except KeyError as e:
            return {"error": f"Missing required parameter: {str(e)}"}, 400
        except ValueError:
            return {"error": "document_id and job_id must be integers."}, 400

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to generate user stories for document_id: {document_id}, job_id: {job_id}",
            service_function=feed_manager_service.generate_user_stories,
        )


@document_namespace.route("document/generate-acceptance-criteria", methods=["POST"])
class GenerateBulkAcceptanceCriteria(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()

        try:
            document_id = int(args["document_id"])
            job_id = int(args["job_id"])
        except KeyError as e:
            return {"error": f"Missing required parameter: {str(e)}"}, 400
        except ValueError:
            return {"error": "document_id and job_id must be integers."}, 400

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to generate acceptance criteria for document_id: {document_id}, job_id: {job_id}",
            service_function=feed_manager_service.generate_bulk_acceptance_criteria,
        )


@document_namespace.route("document/generate-test-cases", methods=["POST"])
class GenerateBulkTestCases(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()

        try:
            document_id = int(args["document_id"])
            job_id = int(args["job_id"])
        except KeyError as e:
            return {"error": f"Missing required parameter: {str(e)}"}, 400
        except ValueError:
            return {"error": "document_id and job_id must be integers."}, 400

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to generate test cases for document_id: {document_id}, job_id: {job_id}",
            service_function=feed_manager_service.generate_bulk_test_cases,
        )


@document_namespace.route(
    "user-story/<int:user_story_id>/generate-acceptance-criteria", methods=["POST"]
)
class GenerateAcceptanceCriteria(Resource):
    @secure_endpoint()
    def post(self, user_story_id: int):
        session = Session()

        if user_story_id <= 0:
            return {"error": "Invalid user_story_id. It must be a positive integer."}, 400

        try:
            return feed_manager_service.generate_acceptance_criteria(user_story_id)

        except Exception as e:
            # Handle unexpected errors
            return {"error": str(e)}, 500

        finally:
            # Ensure the session is properly closed
            session.close()


@document_namespace.route(
    "acceptance-criteria/<int:acceptance_criteria_id>/generate-test-cases", methods=["POST"]
)
class GenerateTestCases(Resource):
    @secure_endpoint()
    def post(self, acceptance_criteria_id: int):
        session = Session()

        if acceptance_criteria_id <= 0:
            return {"error": "Invalid acceptance_criteria_id. It must be a positive integer."}, 400

        try:
            return feed_manager_service.generate_test_cases_for_acceptance_criteria(
                acceptance_criteria_id
            )

        except Exception as e:
            # Handle unexpected errors
            return {"error": str(e)}, 500

        finally:
            # Ensure the session is properly closed
            session.close()


@document_namespace.route("document/skip-artifacts", methods=["POST"])
class SkipArtifacts(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()
        document_id = args["document_id"]
        job_id = args["job_id"]

        session = Session()

        try:
            # Fetch the job
            job = (
                session.query(Job).filter_by(job_id=job_id, reference_id=document_id).one_or_none()
            )
            if not job:
                abort(404, message="Job not found for the given document_id and job_id.")

            # Update the job status
            job.job_status_id = JobStatusEnum.JOB_COMPLETE.value

            # Commit the changes
            session.commit()
            return {"message": "Job status updated successfully."}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating job status: " + str(e))
        finally:
            session.close()

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to skip artifacts for document_id: {document_id}, job_id: {job_id}",
        )


@document_namespace.route("document/complete-document", methods=["POST"])
class CompleteDocument(BaseDocumentResource):
    @secure_endpoint()
    def post(self):
        args = self.parse_args()
        document_id = args["document_id"]
        job_id = args["job_id"]

        session = Session()

        try:
            # Fetch the job
            job = (
                session.query(Job).filter_by(job_id=job_id, reference_id=document_id).one_or_none()
            )
            if not job:
                abort(404, message="Job not found for the given document_id and job_id.")

            # Update the job status
            job.job_status_id = JobStatusEnum.JOB_COMPLETE.value

            # Commit the changes
            session.commit()
            return {"message": "Job status updated successfully."}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating job status: " + str(e))
        finally:
            session.close()

        return handle_request_and_update_status(
            document_id=document_id,
            job_id=job_id,
            log_message=f"Received request to complete document_id: {document_id}, job_id: {job_id}",
        )


@document_namespace.route("document/<int:document_id>/download", methods=["GET"])
class DownloadDocument(BaseDocumentResource):
    @secure_endpoint()
    def get(self, document_id):
        version = request.args.get("version")
        if not version:
            return {"error": "Missing required query param 'version'"}, 400

        try:
            doc_info = content_accessor.get_document_download_info(document_id, version)
            s3_helper = S3Helper(
                aws_access_key=AWS_FEED_MANAGER_ACCESS_KEY,
                aws_secret_key=AWS_FEED_MANAGER_SECRET_KEY,
                region=AWS_REGION,
                bucket_name=AWS_S3_BUCKET_NAME,
            )

            # Extract just the object key from the full S3 location
            s3_location = doc_info["s3_location"]
            if s3_location.startswith("s3://"):
                # Remove s3:// prefix and bucket name to get just the key
                s3_key = "/".join(s3_location.replace("s3://", "").split("/")[1:])
            else:
                s3_key = s3_location

            url = s3_helper.generate_presigned_url(s3_key)

            # Get the document to check if it has an original_document_id
            document = content_accessor.get_document_by_id(document_id)
            if not document:
                return {"error": "Document not found"}, 404

            # Determine the file name from the S3 location
            file_name = s3_location.split("/")[-1]

            # Determine the version label and type based on the version
            version_info = {
                VERSION_ORIGINAL_WORD: {"label": "Original Word", "type": ".docx"},
                VERSION_OPTIMIZED_WORD: {"label": "Optimized Word", "type": ".docx"},
                VERSION_OPTIMIZED_PDF: {"label": "Optimized PDF", "type": ".pdf"},
                VERSION_ORIGINAL_PDF: {"label": "Original PDF", "type": ".pdf"},
            }

            return_data = {
                "document_id": doc_info["document_id"],
                "version": version,
                "label": version_info[version]["label"],
                "file_name": file_name,
                "download_url": url,
            }

            return return_data, 200

        except ContentAccessorException as e:
            return {"error": str(e)}, 500


def handle_request_and_update_status(document_id, job_id, log_message, service_function=None):
    feed_manager_service.logger.info(log_message)

    if service_function:
        result, status_code = service_function(document_id, job_id)
    else:
        result, status_code = None, 200

    return result, status_code
