from flask_restx import Namespace, abort, fields, marshal
from flask import request


from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource

# Create a namespace to be used for the swagger file
department_namespace = Namespace("departments", description="Department related operations")

# Define how to marshal department data
department_fields = {
    "department_id": fields.Integer,
    "business_id": fields.Integer,
    "name": fields.String(attribute="name"),
    "description": fields.String(attribute="description"),
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@department_namespace.route("department", methods=["POST"])
class CreateDepartment(BaseResource):
    @secure_endpoint
    def post(self):
        data = request.get_json()

        # Validate main fields and handle errors early
        if field_validation_error := self.validate_required_fields(
            data, ["business_id", "name", "description"]
        ):
            return field_validation_error

        if field_validation_error := self.validate_integer("business_id", data):
            return field_validation_error
        if field_validation_error := self.validate_text(data.get("name")):
            return field_validation_error
        if field_validation_error := self.validate_text(data.get("description")):
            return field_validation_error

        # Prepare fields for saving
        insert_fields = {k: v for k, v in data.items() if v is not None}

        try:
            new_department = content_accessor.save_department(insert_fields)

            if not new_department:
                abort(500, message="Error creating department")

            result = marshal(new_department, department_fields)

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating department: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@department_namespace.route("department/<int:department_id>")
class SingleDepartment(BaseResource):
    @secure_endpoint()
    def put(self, department_id):
        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["name", "description"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Fetch the existing statement for context
        try:
            department = content_accessor.get_department_by_id(department_id)

            if not department:
                abort(404, message="Department not found")
        except ContentAccessorException as e:
            abort(500, message=f"Error fetching department: {e}")

        # Validate each updated field using helper methods
        if "name" in updated_fields:
            if error := self.validate_string("name", updated_fields):
                return error

        if "description" in updated_fields:
            if error := self.validate_string("description", updated_fields):
                return error

        try:
            updated_department = content_accessor.update_department(department_id, updated_fields)

            if not updated_department:
                abort(500, message="Error updating department")

            result = marshal(updated_department, department_fields)

            return result, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating department: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")

    @secure_endpoint()
    def delete(self, department_id):
        try:
            if department_id is None:
                abort(400, message="department_id is a required field")

            content_accessor.delete_department(department_id)

            return {"message": "Department deleted successfully"}, 200
        except ContentAccessorException as e:
            abort(500, message=f"Error deleting department: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")

    @secure_endpoint()
    def get(self, department_id):

        department = content_accessor.get_department_by_id(department_id)

        if not department:
            abort(404, message="Department not found")

        return marshal(department, department_fields), 200


@department_namespace.route("departments/business/<int:business_id>", methods=["GET"])
class DepartmentsByBusiness(BaseResource):
    @secure_endpoint()
    def get(self, business_id):
        try:
            if business_id is None:
                abort(400, message="business_id is a required field")

            departments = content_accessor.get_departments_by_business(business_id)

            return marshal(departments, department_fields), 200

        except ContentAccessorException as e:
            abort(500, message=f"Error getting departments: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@department_namespace.route("departments/enterprise/<int:enterprise_id>", methods=["GET"])
class DepartmentsByEnterprise(BaseResource):
    @secure_endpoint()
    def get(self, enterprise_id):
        try:
            if enterprise_id is None:
                abort(400, message="enterprise_id is a required field")

            departments = content_accessor.get_departments_by_enterprise(enterprise_id)

            return marshal(departments, department_fields), 200

        except ContentAccessorException as e:
            abort(500, message=f"Error getting departments: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")
