from api.middleware import auth
from flask_restx import Namespace, Resource, abort, fields, marshal, reqparse
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from shared.models import Subtopic
from util.database import Session
from api.middleware.auth import secure_endpoint

# Create a namespace to be used for the swagger file
subtopic_namespace = Namespace("subtopics", description="Subtopic related operations")

# Define how to marshal subtopic data
subtopic_fields = {
    "subtopic_id": fields.Integer,
    "subtopic_name": fields.String(attribute="name"),
    "topic_id": fields.Integer,
    "subtopic_description": fields.String(attribute="description"),
    "topic_name": fields.String(attribute="topic.name"),
    "business_id": fields.Integer,
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@subtopic_namespace.route("subtopic", methods=["POST"])
class CreateSubtopic(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument("topic_id", type=int, required=True, help="Topic ID cannot be blank.")
        parser.add_argument("name", type=str, required=True, help="Name cannot be blank.")
        parser.add_argument(
            "description", type=str, required=True, help="Description cannot be blank."
        )
        parser.add_argument(
            "business_id", type=int, required=True, help="Business ID cannot be blank."
        )
        args = parser.parse_args()

        session = Session()
        new_subtopic = Subtopic(
            topic_id=args["topic_id"],
            name=args["name"],
            description=args["description"],
            business_id=args["business_id"],
        )

        try:
            session.add(new_subtopic)
            session.commit()
            return marshal(new_subtopic, subtopic_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating subtopic: " + str(e))
        finally:
            session.close()


@subtopic_namespace.route("subtopic/<int:subtopic_id>")
class SingleSubtopic(Resource):
    @secure_endpoint()
    def put(self, subtopic_id):
        parser = reqparse.RequestParser()
        parser.add_argument("name", type=str, required=False)
        parser.add_argument("description", type=str, required=False)
        parser.add_argument("business_id", type=int, required=False)
        args = parser.parse_args()

        session = Session()
        subtopic = session.query(Subtopic).filter_by(subtopic_id=subtopic_id).one_or_none()
        if not subtopic:
            abort(404, message="Subtopic not found")

        subtopic.name = args.get("name", subtopic.name)
        subtopic.description = args.get("description", subtopic.description)
        subtopic.business_id = args.get("business_id", subtopic.business_id)

        try:
            session.commit()
            return marshal(subtopic, subtopic_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating subtopic: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def delete(self, subtopic_id):
        session = Session()
        subtopic = session.query(Subtopic).filter_by(subtopic_id=subtopic_id).one_or_none()
        if not subtopic:
            abort(404, message="Subtopic not found")

        try:
            session.delete(subtopic)
            session.commit()
            return {"message": "Subtopic deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting subtopic: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, subtopic_id):
        session = Session()
        subtopic = session.query(Subtopic).filter_by(subtopic_id=subtopic_id).one_or_none()
        if not subtopic:
            abort(404, message="Subtopic not found")
        return marshal(subtopic, subtopic_fields), 200


@subtopic_namespace.route("subtopics", methods=["GET"])
class SubtopicsResource(Resource):
    @secure_endpoint()
    def get(self):
        try:
            subtopics = content_accessor.get_all_subtopics()
            return marshal(subtopics, subtopic_fields), 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error retrieving subtopics: {e}")


@subtopic_namespace.route("subtopics/business/<int:business_id>", methods=["GET"])
class SubtopicsByBusiness(Resource):
    @secure_endpoint()
    def get(self, business_id):
        session = Session()
        try:
            subtopics = (
                session.query(Subtopic)
                .filter_by(business_id=business_id)
                .options(joinedload(Subtopic.topic))
                .all()
            )
            if not subtopics:
                abort(404, message="No subtopics found for the given business ID")
            return marshal(subtopics, subtopic_fields), 200
        except SQLAlchemyError as e:
            abort(500, message="Error retrieving subtopics: " + str(e))
        finally:
            session.close()


@subtopic_namespace.route("subtopics/document/<int:document_id>", methods=["GET"])
class SubtopicsByDocument(Resource):
    @secure_endpoint()
    def get(self, document_id):
        try:
            # Validate input
            if document_id is None:
                abort(400, message="document_id is a required field")

            subtopics_with_counts = content_accessor.get_document_subtopic_counts(document_id)

            if not subtopics_with_counts:
                abort(404, message="No subtopics found for the given criteria")

            # Manually structure the output, including unrated_count
            subtopic_data_list = []
            for subtopic_data in subtopics_with_counts:
                subtopic_dict = {
                    "subtopic_id": subtopic_data.subtopic_id,
                    "topic_id": subtopic_data.topic_id,
                    "subtopic_name": subtopic_data.subtopic_name,
                    "subtopic_description": subtopic_data.subtopic_description,
                    "business_id": subtopic_data.business_id,
                    "topic_name": subtopic_data.topic_name,
                    "unrated_requirements_count": subtopic_data.unrated_requirements_count,
                    "unrated_artifact_counts": subtopic_data.unrated_artifact_counts,
                }
                subtopic_data_list.append(subtopic_dict)

            return subtopic_data_list, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error retrieving subtopics: {e}")


@subtopic_namespace.route("subtopics/enterprise/<int:enterprise_id>", methods=["GET"])
class SubtopicsByEnterprise(Resource):
    @secure_endpoint()
    def get(self, enterprise_id):
        try:
            subtopics = content_accessor.get_subtopics_by_enterprise(enterprise_id)
            if not subtopics:
                abort(404, message="No subtopics found for the given enterprise ID")
            return marshal(subtopics, subtopic_fields), 200

        except ContentAccessorException as e:
            abort(500, message=f"Error retrieving subtopics: {e}")
