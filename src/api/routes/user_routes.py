from flask_restx import Resource, Namespace, reqparse, marshal, fields, abort
from sqlalchemy.exc import SQLAlchemyError

from shared.models.user import User

from util.database import Session
from api.middleware.auth import secure_endpoint

# Create a namespace to be used for the Swagger file
user_namespace = Namespace("users", description="User related operations")

# Define how to marshal user data
user_fields = {
    "user_id": fields.Integer,
    "first_name": fields.String,
    "last_name": fields.String,
    "email_address": fields.String,
    "enterprise_id": fields.Integer,
    "role_id": fields.Integer,
}


@user_namespace.route("user", methods=["POST"])
class CreateUser(Resource):
    @secure_endpoint()
    def post(self):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "first_name", type=str, required=True, help="First name cannot be blank."
        )
        parser.add_argument("last_name", type=str, required=True, help="Last name cannot be blank.")
        parser.add_argument(
            "email_address", type=str, required=True, help="Email address cannot be blank."
        )
        parser.add_argument(
            "enterprise_id", type=int, required=True, help="Enterprise ID cannot be blank."
        )

        args = parser.parse_args()

        session = Session()
        new_user = User(
            first_name=args["first_name"],
            last_name=args["last_name"],
            email_address=args["email_address"],
            enterprise_id=args["enterprise_id"],
        )

        try:
            session.add(new_user)
            session.commit()
            return marshal(new_user, user_fields), 201
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error creating user: " + str(e))
        finally:
            session.close()


@user_namespace.route("user/<int:user_id>")
class SingleUser(Resource):
    @secure_endpoint()
    def put(self, user_id):
        parser = reqparse.RequestParser()
        parser.add_argument(
            "first_name", type=str, required=False, help="First name cannot be blank."
        )
        parser.add_argument(
            "last_name", type=str, required=False, help="Last name cannot be blank."
        )
        parser.add_argument(
            "email_address", type=str, required=False, help="Email address cannot be blank."
        )
        parser.add_argument(
            "enterprise_id", type=int, required=False, help="Enterprise ID cannot be blank."
        )

        args = parser.parse_args()

        session = Session()
        try:
            user = session.query(User).filter_by(user_id=user_id).one_or_none()
            if not user:
                abort(404, message="User not found")

            # Update fields if provided
            if args["first_name"] is not None:
                user.first_name = args["first_name"]
            if args["last_name"] is not None:
                user.last_name = args["last_name"]
            if args["email_address"] is not None:
                user.email_address = args["email_address"]
            if args["enterprise_id"] is not None:
                user.enterprise_id = args["enterprise_id"]

            session.commit()

            return marshal(user, user_fields), 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating user: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def delete(self, user_id):
        session = Session()
        user = session.query(User).filter_by(user_id=user_id).one_or_none()
        if not user:
            abort(404, message="User not found")

        try:
            session.delete(user)
            session.commit()
            return {"message": "User deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting user: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, user_id):
        session = Session()
        user = session.query(User).filter_by(user_id=user_id).one_or_none()
        if not user:
            abort(404, message="User not found")
        return marshal(user, user_fields), 200


@user_namespace.route("users", methods=["GET"])
class AllUsers(Resource):
    @secure_endpoint()
    def get(self):
        session = Session()
        try:
            # Query the User table for all users
            users = session.query(User).order_by(User.user_id).all()

            # Marshal the results to match the output fields
            return marshal(users, user_fields), 200
        except Exception as e:
            return {"message": f"An internal error occurred: {e}"}, 500
        finally:
            session.close()


@user_namespace.route("users/enterprise/<int:enterprise_id>", methods=["GET"])
class UsersByEnterprise(Resource):
    @secure_endpoint()
    def get(self, enterprise_id):
        session = Session()
        try:
            # Query the User table and filter by enterprise_id, then sort by user_id
            users = (
                session.query(User)
                .filter_by(enterprise_id=enterprise_id)
                .order_by(User.user_id)
                .all()
            )

            # Marshal the results to match the output fields
            return marshal(users, user_fields), 200
        except Exception as e:
            return {"message": f"An internal error occurred: {e}"}, 500
        finally:
            session.close()
