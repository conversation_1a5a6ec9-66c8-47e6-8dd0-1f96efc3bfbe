from flask import request
from flask_restx import Namespace, abort, fields, marshal
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource
from api.schemas.audit_activity_fields import audit_activity_fields
from shared.enums import AuditObjectType, AuditOperationType
from shared.models import RatingEnum  # Needed to deserialize the UserStory object
from shared.models import AcceptanceCriteria, TestCase, UserStory
from util.audit.auditor import Auditor
from util.audit.dto import AuditActivityInsertDTO
from util.database import Session

# Create a namespace to be used for the swagger file
test_case_namespace = Namespace("test-cases", description="Test case related operations")

# Define how to marshal test case data
test_case_fields = {
    "test_case_id": fields.Integer,
    "acceptance_criteria_id": fields.Integer,
    "description": fields.String,
    "identifier": fields.String,
    "rating": fields.String(
        attribute=lambda x: (
            x.rating if isinstance(x.rating, str) else x.rating.value if x.rating else None
        )
    ),
    "user_entered": fields.Boolean,
    "edited": fields.Boolean,
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")
auditor = Auditor(session_factory=Session)


@test_case_namespace.route("test-case", methods=["POST"])
class CreateTestCase(BaseResource):
    @secure_endpoint()
    def post(self):

        data = request.get_json()

        # Validate main fields and handle errors early
        if field_validation_error := self.validate_required_fields(
            data, ["acceptance_criteria_id", "description"]
        ):
            return field_validation_error
        if field_validation_error := self.validate_integer("acceptance_criteria_id", data):
            return field_validation_error
        if field_validation_error := self.validate_string("description", data):
            return field_validation_error

        # Prepare fields for saving
        insert_fields = {k: v for k, v in data.items() if v is not None}

        # Any user-created test case is automatically given a postive rating
        insert_fields["user_entered"] = True
        insert_fields["rating"] = RatingEnum.positive

        try:
            new_test_case = content_accessor.save_test_case(insert_fields)
            if not new_test_case:
                abort(500, message="Error creating test case")

            document_id = content_accessor.get_document_id_by_test_case_id(
                new_test_case.test_case_id
            )

            counts_all_levels = content_accessor.get_document_counts_all_levels(document_id)

            result = marshal(new_test_case, test_case_fields)
            result["counts_all_levels"] = counts_all_levels

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating acceptance criteria: {e}")


@test_case_namespace.route("test-case/<int:test_case_id>")
class SingleTestCase(BaseResource):
    @secure_endpoint()
    def put(self, test_case_id):

        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["description", "rating"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Validate each updated field using helper methods
        if "description" in updated_fields:
            if error := self.validate_text(updated_fields["description"]):
                return error
        if "rating" in updated_fields:
            if error := self.validate_rating(updated_fields["rating"]):
                return error

        # Automatically set `rating` to `positive` if `description` is updated and `rating` is not provided
        if ("description" in updated_fields) and "rating" not in updated_fields:
            updated_fields["rating"] = RatingEnum.positive

        try:
            updated_test_case = content_accessor.update_test_case(test_case_id, updated_fields)
            if not updated_test_case:
                abort(500, message="Error updating test case")

            result = marshal(updated_test_case, test_case_fields)

            document_id = content_accessor.get_document_id_by_test_case_id(test_case_id)
            counts = content_accessor.get_document_counts_all_levels(document_id)
            result["counts_all_levels"] = counts

            return result, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating test case: {e}")

    @secure_endpoint()
    def delete(self, test_case_id):
        session = Session()
        test_case_to_delete = (
            session.query(TestCase).filter_by(test_case_id=test_case_id).one_or_none()
        )
        if not test_case_to_delete:
            abort(404, message="Test case not found")

        try:
            content_accessor.delete_test_case(test_case_id)

            return {"message": "Test case deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting test case: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, test_case_id):
        session = Session()
        test_case = session.query(TestCase).filter_by(test_case_id=test_case_id).one_or_none()
        if not test_case:
            abort(404, message="Test case not found")

        document_id = content_accessor.get_document_id_by_test_case_id(test_case_id)
        counts = content_accessor.get_document_counts_all_levels(document_id)

        result = marshal(test_case, test_case_fields)

        result["counts_all_levels"] = counts

        return result, 200


@test_case_namespace.route(
    "test-cases/acceptance-criteria/<int:acceptance_criteria_id>", methods=["GET"]
)
class MultipleTestCases(BaseResource):
    @secure_endpoint()
    def get(self, acceptance_criteria_id):
        session = Session()
        try:
            test_cases = (
                session.query(TestCase)
                .options(
                    joinedload(TestCase.acceptance_criteria)
                    .joinedload(AcceptanceCriteria.user_story)
                    .joinedload(UserStory.unique_statement)
                )
                .filter(TestCase.acceptance_criteria_id == acceptance_criteria_id)
                .order_by(TestCase.test_case_id)
                .all()
            )

            if not test_cases:
                return {"message": "No test cases found for the given acceptance_criteria_id"}, 404

            # Extract document_id from the first test_case
            first_test_case = test_cases[0]
            if (
                first_test_case.acceptance_criteria
                and first_test_case.acceptance_criteria.user_story
                and first_test_case.acceptance_criteria.user_story.unique_statement
            ):
                document_id = (
                    first_test_case.acceptance_criteria.user_story.unique_statement.document_id
                )
            else:
                return {
                    "message": "Document_id could not be found for the given user_story_id"
                }, 404

            # Get aggregated rating counts based on document_id
            counts_all_levels = content_accessor.get_document_counts_all_levels(document_id)

            # Build the list of acceptance_criterias with counts_all_levels
            test_cases_list = []
            for test_case in test_cases:
                test_case_dict = {
                    "test_case_id": test_case.test_case_id,
                    "acceptance_criteria_id": test_case.acceptance_criteria_id,
                    "description": test_case.description,
                    "identifier": test_case.identifier,
                    "rating": test_case.rating.value,
                    "edited": test_case.edited,
                    "user_entered": test_case.user_entered,
                    "counts_all_levels": counts_all_levels,
                }
                test_cases_list.append(test_case_dict)

            # Build the final response dictionary
            response = test_cases_list

            # Return the JSON response with a 200 status code
            return response, 200
        except Exception as e:
            return {"message": f"An internal error occurred: {e}"}, 500
        finally:
            session.close()


@test_case_namespace.route("test-case/audit-activity/<int:test_case_id>", methods=["GET"])
class TestCaseAuditActivity(BaseResource):
    @secure_endpoint()
    def get(self, test_case_id: int):
        test_case_audit_activity = content_accessor.get_all_audit_activity_per_object(
            AuditObjectType.TEST_CASE.value, test_case_id
        )

        if not test_case_audit_activity:
            return {"message": "No test case audit activity found for the given test_case_id"}, 404

        result = marshal(test_case_audit_activity, audit_activity_fields)

        return result, 200
