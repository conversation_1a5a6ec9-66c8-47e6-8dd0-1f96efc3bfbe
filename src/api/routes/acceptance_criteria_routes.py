from flask import request
from flask_restx import Namespace, abort, fields, marshal
from sqlalchemy.exc import SQLAlchemyError

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource
from api.schemas.audit_activity_fields import audit_activity_fields
from shared.enums import AuditObjectType, AuditOperationType
from shared.models import AcceptanceCriteria
from shared.models.rating_enum import (
    RatingEnum,
)  # Needed to deserialize the AcceptanceCriteria object
from util.audit.auditor import Auditor
from util.audit.dto import AuditActivityInsertDTO
from util.database import Session

# Create a namespace to be used for the swagger file
acceptance_criteria_namespace = Namespace(
    "acceptance criteria", description="Acceptance criteria related operations"
)

# Define how to marshal acceptance criteria data
acceptance_criteria_fields = {
    "acceptance_criteria_id": fields.Integer,
    "user_story_id": fields.Integer,
    "description": fields.String,
    "identifier": fields.String,
    "rating": fields.String(
        attribute=lambda x: (
            x.rating if isinstance(x.rating, str) else x.rating.value if x.rating else None
        )
    ),
    "user_entered": fields.Boolean,
    "edited": fields.Boolean,
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")
auditor = Auditor(session_factory=Session)


@acceptance_criteria_namespace.route("acceptance-criteria", methods=["POST"])
class CreateAcceptanceCriteria(BaseResource):
    @secure_endpoint()
    def post(self):

        data = request.get_json()

        # Validate main fields and handle errors early
        if field_validation_error := self.validate_required_fields(
            data, ["user_story_id", "description"]
        ):
            return field_validation_error
        if field_validation_error := self.validate_integer("user_story_id", data):
            return field_validation_error
        if field_validation_error := self.validate_string("description", data):
            return field_validation_error

        # Prepare fields for saving
        insert_fields = {k: v for k, v in data.items() if v is not None}

        # Any user-created acceptance criteria is automatically given a postive rating
        insert_fields["user_entered"] = True
        insert_fields["rating"] = RatingEnum.positive

        try:
            new_acceptance_criteria = content_accessor.save_acceptance_criteria(insert_fields)
            if not new_acceptance_criteria:
                abort(500, message="Error creating acceptance criteria")

            document_id = content_accessor.get_document_id_by_acceptance_criteria_id(
                new_acceptance_criteria.acceptance_criteria_id
            )
            counts_all_levels = content_accessor.get_document_counts_all_levels(document_id)

            result = marshal(new_acceptance_criteria, acceptance_criteria_fields)
            result["counts_all_levels"] = counts_all_levels

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating acceptance criteria: {e}")


@acceptance_criteria_namespace.route("acceptance-criteria/<int:acceptance_criteria_id>")
class AcceptanceCriteriaResource(BaseResource):
    @secure_endpoint()
    def put(self, acceptance_criteria_id: int):

        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["description", "rating"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Validate each updated field using helper methods
        if "description" in updated_fields:
            if error := self.validate_text(updated_fields["description"]):
                return error
        if "rating" in updated_fields:
            if error := self.validate_rating(updated_fields["rating"]):
                return error

        # Automatically set `rating` to `positive` if `description` is updated and `rating` is not provided
        if ("description" in updated_fields) and "rating" not in updated_fields:
            updated_fields["rating"] = RatingEnum.positive

        try:
            updated_acceptance_criteria = content_accessor.update_acceptance_criteria(
                acceptance_criteria_id, updated_fields
            )
            if not updated_acceptance_criteria:
                abort(500, message="Error updating acceptance criteria")

            result = marshal(updated_acceptance_criteria, acceptance_criteria_fields)

            document_id = content_accessor.get_document_id_by_acceptance_criteria_id(
                acceptance_criteria_id
            )
            counts = content_accessor.get_document_counts_all_levels(document_id)
            result["counts_all_levels"] = counts

            has_children = content_accessor.check_acceptance_criteria_has_children(
                acceptance_criteria_id
            )
            result["has_children"] = has_children

            return result, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating acceptance criteria: {e}")

    @secure_endpoint()
    def get(self, acceptance_criteria_id):
        acceptance_criteria = content_accessor.get_acceptance_criteria_by_id(acceptance_criteria_id)
        if not acceptance_criteria:
            abort(404, message="Acceptance criterion not found")

        result = marshal(acceptance_criteria, acceptance_criteria_fields)

        document_id = content_accessor.get_document_id_by_acceptance_criteria_id(
            acceptance_criteria_id
        )
        counts = content_accessor.get_document_counts_all_levels(document_id)
        result["counts_all_levels"] = counts

        has_children = content_accessor.check_acceptance_criteria_has_children(
            acceptance_criteria_id
        )
        result["has_children"] = has_children

        return result, 200

    @secure_endpoint()
    def delete(self, acceptance_criteria_id):
        session = Session()
        acceptance_criteria_to_delete = (
            session.query(AcceptanceCriteria)
            .filter_by(acceptance_criteria_id=acceptance_criteria_id)
            .one_or_none()
        )
        if not acceptance_criteria_to_delete:
            abort(404, message="Acceptance criterion not found")

        try:
            content_accessor.delete_acceptance_criteria(acceptance_criteria_id)

            return {"message": "Acceptance criterion deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting acceptance criterion: " + str(e))
        finally:
            session.close()


@acceptance_criteria_namespace.route(
    "acceptance-criterias/user-story/<int:user_story_id>", methods=["GET"]
)
class AcceptanceCriteriaByUserStoryResource(BaseResource):
    @secure_endpoint()
    def get(self, user_story_id):
        acceptance_criterias = content_accessor.get_all_acceptance_criteria_per_user_story(
            user_story_id
        )

        if not acceptance_criterias:
            return {"message": "No acceptance criteria found for the given user_story_id"}, 404

        try:
            # Extract document_id from the first criteria tuple
            first_criteria = acceptance_criterias[0]
            document_id = first_criteria[7]  # Index for document_id in the result tuple

            # Get aggregated rating counts based on document_id
            counts_all_levels = content_accessor.get_document_counts_all_levels(document_id)

            # Build the list of acceptance_criterias with counts_all_levels
            acceptance_criterias_list = []
            for criteria in acceptance_criterias:
                acceptance_criteria_dict = {
                    "acceptance_criteria_id": criteria[0],  # acceptance_criteria_id
                    "user_story_id": criteria[1],  # user_story_id
                    "description": criteria[2],  # description
                    "identifier": criteria[3],  # identifier
                    "rating": criteria[
                        4
                    ].value,  # rating (assuming it's an enum or object with a value)
                    "edited": criteria[5],
                    "user_entered": criteria[6],
                    "counts_all_levels": counts_all_levels,  # counts_all_levels
                    "has_children": criteria[7] > 0,  # test_case_count > 0
                }
                acceptance_criterias_list.append(acceptance_criteria_dict)

            # Build the final response dictionary
            response = acceptance_criterias_list

            # Return the JSON response with a 200 status code
            return response, 200

        except Exception as e:
            return {"message": f"An internal error occurred: {e}"}, 500


@acceptance_criteria_namespace.route(
    "acceptance-criteria/audit-activity/<int:acceptance_criteria_id>", methods=["GET"]
)
class AcceptanceCriteriaAuditActivity(BaseResource):
    @secure_endpoint()
    def get(self, acceptance_criteria_id: int):
        acceptance_criteria_audit_activity = content_accessor.get_all_audit_activity_per_object(
            AuditObjectType.ACCEPTANCE_CRITERIA.value, acceptance_criteria_id
        )

        if not acceptance_criteria_audit_activity:
            return {
                "message": "No acceptance criteria audit activity found for the given acceptance_criteria_id"
            }, 404

        result = marshal(acceptance_criteria_audit_activity, audit_activity_fields)

        return result, 200
