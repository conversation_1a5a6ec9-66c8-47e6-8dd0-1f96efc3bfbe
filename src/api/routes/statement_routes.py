import json

from flask import request
from flask_restx import Namespace, Resource, abort, fields, marshal
from sqlalchemy import func, text
from sqlalchemy.exc import SQLAlchemyError

from accessor.content import ContentAccessor
from accessor.content.accessors.job_accessor import JobAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource
from api.schemas.audit_activity_fields import audit_activity_fields
from shared.enums import AuditObjectType, AuditOperationType
from shared.enums.statement_type_enum import StatementTypeEnum
from shared.models import Chunk, RatingEnum, Statement, Subtopic
from util.audit.auditor import Auditor
from util.audit.dto import AuditActivityInsertDTO
from util.database import Session
from util.statement_type_helper import (
    has_change_statements,
    merge_change_statement_text_data,
    format_change_statement_text,
)

# Create a namespace to be used for the swagger file
statement_namespace = Namespace("statements", description="Statement related operations")

# Define how to marshal statement data
statement_fields = {
    "statement_id": fields.Integer,
    "chunk_id": fields.Integer,
    "statement_type_id": fields.Integer,
    "text": fields.String,
    "rating": fields.String(attribute=lambda x: x.rating.value if x.rating else None),
    "user_entered": fields.Boolean,
    "edited": fields.Boolean,
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")
auditor = Auditor(session_factory=Session)


@statement_namespace.route("statement", methods=["POST"])
class CreateStatement(BaseResource):
    @secure_endpoint()
    def post(self):

        data = request.get_json()

        # Validate main fields and handle errors early
        if field_validation_error := self.validate_required_fields(
            data, ["chunk_id", "statement_type_id", "text"]
        ):
            return field_validation_error

        if field_validation_error := self.validate_integer("chunk_id", data):
            return field_validation_error
        if field_validation_error := self.validate_integer("statement_type_id", data):
            return field_validation_error

        if data["statement_type_id"] == 13:
            # If `text` is a dictionary, validate it directly
            if isinstance(data["text"], dict):
                result = format_change_statement_text(json.dumps(data["text"]))
            else:
                result = format_change_statement_text(data["text"])

            if isinstance(result, dict) and "error" in result:
                return result, 400
            # Serialize the validated data back into JSON format for storage
            data["text"] = json.dumps(result)
        else:
            # For other statement types, validate text as a plain string
            if field_validation_error := self.validate_text(data.get("text")):
                return field_validation_error

        # Prepare fields for saving
        insert_fields = {k: v for k, v in data.items() if v is not None}
        insert_fields["user_entered"] = True

        # Any call through API is coming from the UI
        insert_fields["user_entered"] = True

        # Any user-created statement is automatically given a postive rating
        insert_fields["rating"] = RatingEnum.positive

        try:
            new_statement = content_accessor.save_statement(insert_fields)
            if not new_statement:
                abort(500, message="Error creating statement")

            document_id = content_accessor.get_document_id_by_chunk_id(new_statement.chunk_id)
            grouped_data = content_accessor.get_statement_counts(document_id)

            if grouped_data is None:
                abort(404, message="Statement counts not found")

            result = marshal(new_statement, statement_fields)
            result["counts"] = {
                "unrated_count": grouped_data["unrated_count"],
                "positive_count": grouped_data["positive_count"],
                "negative_count": grouped_data["negative_count"],
            }

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating statement: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@statement_namespace.route("statement/<int:statement_id>")
class SingleStatement(BaseResource):
    @secure_endpoint()
    def put(self, statement_id):

        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["text", "statement_type_id", "rating"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Fetch the existing statement for context
        try:
            statement = content_accessor.get_statement_by_id(statement_id)
            if not statement:
                abort(404, message="Statement not found")
        except ContentAccessorException as e:
            abort(500, message=f"Error fetching statement: {e}")

        # Validate each updated field using helper methods
        if "text" in updated_fields:
            # Handle text updates for 'change statement' statement type
            try:
                if updated_fields.get("statement_type_id", statement.statement_type_id) == 13:
                    # Check if the text is raw JSON or serialized
                    if isinstance(updated_fields["text"], dict):
                        # Convert raw JSON to a serialized string
                        updated_fields["text"] = json.dumps(updated_fields["text"])
                    elif isinstance(updated_fields["text"], str):
                        # Validate if the serialized string is valid JSON
                        try:
                            json.loads(updated_fields["text"])  # Check if it's valid JSON
                        except json.JSONDecodeError:
                            abort(400, message="Invalid serialized JSON in 'text' field")
                    else:
                        abort(
                            400, message="'text' must be a JSON object or a serialized JSON string"
                        )

                    merged_text_result = merge_change_statement_text_data(statement, updated_fields)
                    if isinstance(merged_text_result, dict) and "error" in merged_text_result:
                        return merged_text_result, 400  # Return the error immediately
                    updated_fields["text"] = merged_text_result
                else:
                    # Use existing validation for non-"Change Statement" types
                    if error := self.validate_text(updated_fields["text"]):
                        return error
                    if isinstance(updated_fields["text"], dict):
                        updated_fields["text"] = json.dumps(
                            updated_fields["text"]
                        )  # Serialize JSON
            except Exception as e:
                return {"error": f"Error processing 'text' field: {e}"}, 500
        if "statement_type_id" in updated_fields:
            if error := self.validate_integer("statement_type_id", updated_fields):
                return error
        if "rating" in updated_fields:
            if error := self.validate_rating(updated_fields["rating"]):
                return error

        # Automatically set `rating` to `positive` if `statement_type_id` or `text` is updated and `rating` is not provided
        if (
            "statement_type_id" in updated_fields or "text" in updated_fields
        ) and "rating" not in updated_fields:
            updated_fields["rating"] = RatingEnum.positive

        # Set edited to True only if more than just 'rating' was updated
        if len(updated_fields) > 1 or "rating" not in updated_fields:
            updated_fields["edited"] = True

        try:
            # Update the unique statement, including subtopics if provided
            updated_statement = content_accessor.update_statement(statement_id, updated_fields)
            if not updated_statement:
                abort(500, message="Error updating statement")

            result = marshal(updated_statement, statement_fields)

            document_id = content_accessor.get_document_id_by_chunk_id(updated_statement.chunk_id)
            grouped_data = content_accessor.get_statement_counts(document_id)

            if grouped_data is None:
                abort(404, message="Statement counts not found")

            result["counts"] = {
                "unrated_count": grouped_data["unrated_count"],
                "positive_count": grouped_data["positive_count"],
                "negative_count": grouped_data["negative_count"],
            }
            result["unrated_statement_count"] = grouped_data[
                "unrated_statement_count"
            ]  # For parent chunk
            result["remaining_chunks"] = grouped_data[
                "remaining_chunks"
            ]  # For document's remaining chunks

            return result, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating statement: {e}")

    @secure_endpoint()
    def delete(self, statement_id):
        session = Session()
        statement_to_delete = (
            session.query(Statement).filter_by(statement_id=statement_id).one_or_none()
        )
        if not statement_to_delete:
            abort(404, message="Statement not found")

        try:
            content_accessor.delete_statement(statement_id)

            return {"message": "Statement deleted successfully"}, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error deleting statement: " + str(e))
        finally:
            session.close()

    @secure_endpoint()
    def get(self, statement_id):
        session = Session()
        statement_data = (
            session.query(Statement, Subtopic.description)
            .outerjoin(
                Subtopic, (Statement.statement_type_id == 8) & (Statement.text == Subtopic.name)
            )
            .filter(Statement.statement_id == statement_id)
            .one_or_none()
        )

        if not statement_data:
            abort(404, message="Statement not found")

        statement, subtopic_description = statement_data
        if statement.statement_type_id == 8 and subtopic_description:
            full_text = f"{statement.text} - {subtopic_description}"
        elif statement.statement_type_id == 13:
            full_text = format_change_statement_text(statement.text)
        else:
            full_text = statement.text

        response_data = {
            "statement_id": statement.statement_id,
            "chunk_id": statement.chunk_id,
            "statement_type_id": statement.statement_type_id,
            "text": full_text,
            "rating": statement.rating.value if statement.rating else None,
            "user_entered": statement.user_entered,
            "edited": statement.edited,
        }

        return response_data, 200


@statement_namespace.route("statements/chunk/<int:chunk_id>", methods=["GET"])
class StatementsByChunkResource(Resource):
    @secure_endpoint()
    def get(self, chunk_id):
        session = Session()
        try:
            document_id = session.query(Chunk.document_id).filter_by(chunk_id=chunk_id).scalar()

            # Use helper function for subtopic and non-subtopic filtering
            filters = JobAccessor.get_job_status_statement_filters(
                document_id=document_id, chunk_id=chunk_id
            )

            change_statements_present = False
            change_statements_present = has_change_statements(chunk_id)

            query = (
                session.query(Statement, Subtopic.description)
                .outerjoin(
                    Subtopic,
                    (Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value)
                    & (Statement.text == Subtopic.name),
                )
                .filter(*filters)
            )

            if change_statements_present:
                query = query.filter(
                    Statement.statement_type_id == StatementTypeEnum.CHANGE_STATEMENT.value
                )

            # Add sorting by statement_id ascending
            query = query.order_by(Statement.statement_id.asc())

            statement_data = query.all()
            results = []

            for statement, subtopic_description in statement_data:
                if (
                    statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value
                    and subtopic_description
                ):
                    full_text = f"{statement.text} - {subtopic_description}"
                elif statement.statement_type_id == StatementTypeEnum.CHANGE_STATEMENT.value:
                    full_text = format_change_statement_text(statement.text)
                else:
                    full_text = statement.text

                result = {
                    "statement_id": statement.statement_id,
                    "chunk_id": statement.chunk_id,
                    "statement_type_id": statement.statement_type_id,
                    "text": full_text,
                    "rating": statement.rating.value if statement.rating else None,
                    "user_entered": statement.user_entered,
                    "edited": statement.edited,
                }
                results.append(result)

            return results, 200

        except Exception as e:
            abort(500, message=f"Error fetching statements: {str(e)}")


@statement_namespace.route("statements/document/<int:document_id>/approve", methods=["PUT"])
class ApproveStatementsByDocument(Resource):
    @secure_endpoint()
    def put(self, document_id):
        session = Session()
        try:
            # Execute a raw SQL update statement to update the rating of all statements to 'positive'
            update_query = text(
                """
                UPDATE statement
                SET rating = 'positive'
                WHERE chunk_id IN (
                    SELECT chunk_id
                    FROM chunk
                    WHERE document_id = :document_id
                )
            """
            )
            session.execute(update_query, {"document_id": document_id})
            session.commit()

            # Query to get the counts of each rating type
            counts_query = (
                session.query(
                    func.count()
                    .filter(Statement.rating == RatingEnum.positive.value)
                    .label(RatingEnum.positive.value),
                    func.count()
                    .filter(Statement.rating == RatingEnum.negative.value)
                    .label(RatingEnum.negative.value),
                    func.count()
                    .filter(Statement.rating == RatingEnum.unrated.value)
                    .label(RatingEnum.unrated.value),
                )
                .join(Chunk)
                .filter(Chunk.document_id == document_id)
                .group_by(Chunk.document_id)
                .one_or_none()
            )

            if counts_query is None:
                counts = {"positive_count": 0, "negative_count": 0, "unrated_count": 0}
            else:
                counts = {
                    "positive_count": counts_query.positive or 0,
                    "negative_count": counts_query.negative or 0,
                    "unrated_count": counts_query.unrated or 0,
                }

            chunks_remaining = content_accessor.get_document_count_chunks_with_unrated_statements(
                document_id
            )

            return {
                "message": "All statements associated with the document updated to 'positive' rating successfully",
                "counts": counts,
                "chunks_remaining": chunks_remaining,
            }, 200
        except SQLAlchemyError as e:
            session.rollback()
            abort(500, message="Error updating statements: " + str(e))
        finally:
            session.close()


@statement_namespace.route("statement/audit-activity/<int:statement_id>", methods=["GET"])
class StatementAuditActivity(BaseResource):
    @secure_endpoint()
    def get(self, statement_id: int):
        statement_audit_activity = content_accessor.get_all_audit_activity_per_object(
            AuditObjectType.STATEMENT.value, statement_id
        )

        if not statement_audit_activity:
            return {"message": "No statement audit activity found for the given statement_id"}, 404

        # Process each audit activity record
        for activity in statement_audit_activity:
            if activity.data_snapshot and isinstance(activity.data_snapshot, dict):
                if (
                    activity.data_snapshot.get("statement_type_id")
                    == StatementTypeEnum.CHANGE_STATEMENT.value
                    and "text" in activity.data_snapshot
                ):
                    activity.data_snapshot["text"] = format_change_statement_text(
                        activity.data_snapshot["text"]
                    )

        result = marshal(statement_audit_activity, audit_activity_fields)

        return result, 200
