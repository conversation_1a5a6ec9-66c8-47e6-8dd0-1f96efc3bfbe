from flask import request
from flask_restx import Namespace, abort, fields, marshal, marshal_with

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource
from api.schemas.audit_activity_fields import audit_activity_fields
from shared.enums import AuditObjectType, AuditOperationType
from shared.models import RatingEnum  # Needed to deserialize the UserStory object
from util.audit.auditor import Auditor
from util.audit.dto import AuditActivityInsertDTO
from util.database import Session

# Create a namespace to be used for the swagger file
unique_statement_namespace = Namespace(
    "unique-statement", description="Unique statement related operations"
)

# Define output fields
unique_statement_fields = {
    "unique_statement_id": fields.Integer,
    "domain_id": fields.Integer,
    "document_id": fields.Integer,
    "statement_type_id": fields.Integer,
    "subtopics": fields.List(
        fields.Nested(
            {
                "subtopic_id": fields.Integer,
                "name": fields.String,
                "description": fields.String,
                "topic_name": fields.String(attribute=lambda s: s.topic.name if s.topic else None),
                # "is_primary": fields.<PERSON><PERSON><PERSON>, Added manually in each of the responses to avoid relationship issues
            }
        )
    ),
    "text": fields.String,
    "identifier": fields.String,
    "rating": fields.String(
        attribute=lambda x: (
            x.rating if isinstance(x.rating, str) else x.rating.value if x.rating else None
        )
    ),
    "user_entered": fields.Boolean,
    "edited": fields.Boolean,
}

content_accessor = ContentAccessor(logger_name="burst-api")
auditor = Auditor(session_factory=Session)


class UniqueStatementBase(BaseResource):
    # Shared helper methods

    def _validate_subtopics(self, subtopics):
        MAX_INT_VALUE = 2147483647  # Define max integer threshold

        if not isinstance(subtopics, list) or not subtopics:
            return {"error": "subtopics must be a non-empty list of objects."}, 400

        # Count primary subtopics
        primary_count = sum(1 for st in subtopics if st.get("is_primary") == True)
        if primary_count == 0:
            return {"error": "At least one subtopic must be marked as primary."}, 400
        if primary_count > 1:
            return {"error": "Only one subtopic can be marked as primary."}, 400

        # Track seen subtopic_ids
        seen_subtopic_ids = set()

        for st in subtopics:
            subtopic_id = st.get("subtopic_id")
            if not isinstance(subtopic_id, int) or subtopic_id < 0:
                return {
                    "error": "Each subtopic must have a valid non-negative integer subtopic_id."
                }, 400
            if subtopic_id > MAX_INT_VALUE:
                return {"error": f"subtopic_id cannot exceed {MAX_INT_VALUE}."}, 400
            if "is_primary" not in st or not isinstance(st["is_primary"], bool):
                return {"error": "Each subtopic must specify is_primary as a boolean."}, 400

            if subtopic_id in seen_subtopic_ids:
                return {"error": f"Duplicate subtopic_id found: {subtopic_id}"}, 400
            seen_subtopic_ids.add(subtopic_id)

        return None

    def _get_primary_topic_id(self, subtopics):
        for subtopic in subtopics:
            if subtopic["is_primary"]:
                subtopic_data = content_accessor.get_subtopic_by_id(subtopic["subtopic_id"])
                if subtopic_data is None:
                    raise NotFoundException(
                        f'Subtopic with ID {subtopic["subtopic_id"]} does not exist.'
                    )
                return subtopic_data.topic_id
        return None

    def _validate_document_and_type_ids(self, data):
        MAX_INT_VALUE = 2147483647
        for field in ["document_id", "statement_type_id"]:
            if field in data:
                value = data[field]
                if not isinstance(value, int) or not (0 <= value <= MAX_INT_VALUE):
                    return {
                        "error": f"{field} must be a non-negative integer and <= {MAX_INT_VALUE}."
                    }, 400
        return None


# TODO Check if this is being used. I don't think it is.
@unique_statement_namespace.route("unique-statements/domain/<int:domain_id>")
class MultipleUniqueStatements(UniqueStatementBase):
    @secure_endpoint()
    @marshal_with(unique_statement_fields)
    def get(self, domain_id):
        return content_accessor.get_all_unique_statements_by_domain_id(domain_id)


@unique_statement_namespace.route("unique-statement", methods=["POST"])
class CreateUniqueStatement(UniqueStatementBase):
    @secure_endpoint()
    def post(self):

        try:

            data = request.get_json()

            # Validate main fields and handle errors early
            if error := self.validate_required_fields(
                data, ["document_id", "statement_type_id", "text", "subtopics"]
            ):
                return error
            if error := self.validate_integer("document_id", data):
                return error
            if error := self.validate_integer("statement_type_id", data):
                return error
            if error := self.validate_text(data.get("text")):
                return error
            if error := self.validate_rating(data.get("rating")):
                return error
            if error := self._validate_subtopics(data["subtopics"]):
                return error

            # Prepare fields for saving, including subtopics
            insert_fields = {k: v for k, v in data.items() if v is not None}
            insert_fields["subtopics"] = data["subtopics"]

            # Any user-created statement is automatically given a postive rating
            insert_fields["user_entered"] = True
            insert_fields["rating"] = RatingEnum.positive

            new_unique_statement = content_accessor.create_unique_statement_with_subtopics(
                insert_fields
            )

            if not new_unique_statement:
                abort(500, message="Error creating unique statement")

            result = marshal(new_unique_statement, unique_statement_fields)

            # Add `is_primary` manually in the subtopics
            for subtopic_data in result["subtopics"]:
                subtopic_data["is_primary"] = next(
                    (
                        assoc.is_primary
                        for assoc in new_unique_statement.unique_statement_subtopic_associations
                        if assoc.subtopic_id == subtopic_data["subtopic_id"]
                    ),
                    None,
                )

            # Not sure if we still need these counts and the following counts
            result["counts"] = content_accessor.get_document_unique_requirement_counts(
                new_unique_statement.document_id
            )
            result["counts_all_levels"] = content_accessor.get_document_counts_all_levels(
                new_unique_statement.document_id
            )

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating unique statement: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@unique_statement_namespace.route("unique-statement/<int:unique_statement_id>")
class SingleUniqueStatement(UniqueStatementBase):
    @secure_endpoint()
    @marshal_with(unique_statement_fields)
    def get(self, unique_statement_id):

        try:
            unique_statement = content_accessor.get_unique_statement_by_id(unique_statement_id)

            if unique_statement is None:
                abort(404, message="Unique statement not found")

            return unique_statement, 200
        except ContentAccessorException as e:
            abort(500, message=f"Error fetching unique statement: {e}")

    @secure_endpoint()
    def put(self, unique_statement_id):
        # Get the JSON payload
        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["text", "statement_type_id", "subtopics", "rating"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Validate each updated field using helper methods
        if "text" in updated_fields:
            if error := self.validate_text(updated_fields["text"]):
                return error
        if "rating" in updated_fields:
            if error := self.validate_rating(updated_fields["rating"]):
                return error
        if "statement_type_id" in updated_fields:
            if error := self._validate_document_and_type_ids(
                {"statement_type_id": updated_fields["statement_type_id"]}
            ):
                return error

        # Validate subtopics if provided
        if "subtopics" in updated_fields:
            if error := self._validate_subtopics(updated_fields["subtopics"]):
                return error

        # Automatically set `rating` to `positive` if `statement_type_id` or `text` is updated and `rating` is not provided
        if (
            "statement_type_id" in updated_fields or "text" in updated_fields
        ) and "rating" not in updated_fields:
            updated_fields["rating"] = RatingEnum.positive

        try:
            updated_unique_statement = content_accessor.update_unique_statement(
                unique_statement_id, updated_fields
            )
            if not updated_unique_statement:
                abort(500, message="Error updating unique statement")

            result = marshal(updated_unique_statement, unique_statement_fields)

            # Add `is_primary` manually in the subtopics
            for subtopic_data in result["subtopics"]:
                subtopic_data["is_primary"] = next(
                    (
                        assoc.is_primary
                        for assoc in updated_unique_statement.unique_statement_subtopic_associations
                        if assoc.subtopic_id == subtopic_data["subtopic_id"]
                    ),
                    None,
                )

            # Include counts data in the response
            result["counts"] = content_accessor.get_document_unique_requirement_counts(
                updated_unique_statement.document_id
            )
            result["counts_all_levels"] = content_accessor.get_document_counts_all_levels(
                updated_unique_statement.document_id
            )

            return result

        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating unique statement: {e}")

    @secure_endpoint()
    def delete(self, unique_statement_id):
        try:

            unique_statement_exists = content_accessor.verify_unique_statement_exists(
                unique_statement_id
            )

            if not unique_statement_exists:
                abort(404, message="Unique statement not found")

            content_accessor.delete_unique_statement(unique_statement_id)

            return {"message": "Unique statement deleted successfully"}, 200
        except ContentAccessorException as e:
            return {"message": f"An internal error occurred: {e}"}, 500


@unique_statement_namespace.route("unique-statements/filter", methods=["POST"])
class FilterUniqueStatements(UniqueStatementBase):
    @secure_endpoint()
    def post(self):
        data = request.get_json()
        domain_id = data.get("domain_id")
        document_id = data.get("document_id")
        topic_id = data.get("topic_id")
        subtopic_id = data.get("subtopic_id")

        if not domain_id and not document_id:
            abort(400, message="Either domain_id or document_id is required.")

        try:
            unique_statements = content_accessor.get_unique_statements_using_filter(
                domain_id, document_id, topic_id, subtopic_id
            )

            if not unique_statements:
                return {"message": "No unique statements found"}, 404

            # Manually marshal the response
            result = [
                marshal(statement, unique_statement_fields) for statement in unique_statements
            ]

            # Add `is_primary` manually in each unique statement's subtopics
            for statement, statement_data in zip(unique_statements, result):
                for subtopic_data in statement_data["subtopics"]:
                    subtopic_data["is_primary"] = next(
                        (
                            assoc.is_primary
                            for assoc in statement.unique_statement_subtopic_associations
                            if assoc.subtopic_id == subtopic_data["subtopic_id"]
                        ),
                        None,
                    )
                    # Rename `name` to `subtopic_name`
                    subtopic_data["subtopic_name"] = subtopic_data.pop("name", None)

            return result, 200
        except ContentAccessorException as e:
            return {"message": f"An internal error occurred: {e}"}, 500


@unique_statement_namespace.route(
    "unique-statements/document/<int:document_id>/approve", methods=["PUT"]
)
class UpdateUniqueStatementsRating(UniqueStatementBase):
    @secure_endpoint()
    def put(self, document_id):
        try:
            result = content_accessor.update_unique_statements_to_positive_rating_by_document_id(
                document_id
            )

            return result, 200
        except ContentAccessorException as e:
            return {"message": f"An internal error occurred: {e}"}, 500


@unique_statement_namespace.route(
    "unique-statement/audit-activity/<int:unique_statement_id>", methods=["GET"]
)
class UniqueStatementAuditActivity(BaseResource):
    @secure_endpoint()
    def get(self, unique_statement_id: int):
        unique_statement_audit_activity = content_accessor.get_all_audit_activity_per_object(
            AuditObjectType.UNIQUE_STATEMENT.value, unique_statement_id
        )

        unique_statement_subtopic_association_audit_activity = (
            content_accessor.get_all_audit_activity_per_object(
                AuditObjectType.UNIQUE_STATEMENT_SUBTOPIC_ASSOCIATION.value, unique_statement_id
            )
        )

        combined_audit_activity = (
            unique_statement_audit_activity + unique_statement_subtopic_association_audit_activity
        )

        if not combined_audit_activity:
            return {
                "message": "No unique statement audit activity found for the given unique_statement_id"
            }, 404

        result = marshal(combined_audit_activity, audit_activity_fields)

        return result, 200
