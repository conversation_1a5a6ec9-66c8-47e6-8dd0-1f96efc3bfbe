from flask import request
from flask_restx import Namespace, abort, fields, marshal

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from api.middleware.auth import secure_endpoint
from api.routes.base_resource import BaseResource

# Create a namespace to be used for the swagger file
persona_namespace = Namespace("personas", description="Persona related operations")

# Define how to marshal persona data
persona_fields = {
    "persona_id": fields.Integer,
    "department_id": fields.Integer,
    "name": fields.String(attribute="name"),
    "description": fields.String(attribute="description"),
}

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@persona_namespace.route("persona", methods=["POST"])
class CreatePersona(BaseResource):
    @secure_endpoint()
    def post(self):
        data = request.get_json()

        # Validate main fields and handle errors early
        if field_validation_error := self.validate_required_fields(
            data, ["department_id", "name", "description"]
        ):
            return field_validation_error

        if field_validation_error := self.validate_integer("department_id", data):
            return field_validation_error
        if field_validation_error := self.validate_text(data.get("name")):
            return field_validation_error
        if field_validation_error := self.validate_text(data.get("description")):
            return field_validation_error

        # Prepare fields for saving
        insert_fields = {k: v for k, v in data.items() if v is not None}

        try:
            new_persona = content_accessor.save_persona(insert_fields)

            if not new_persona:
                abort(500, message="Error creating persona")

            result = marshal(new_persona, persona_fields)

            return result, 201
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error creating persona: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@persona_namespace.route("persona/<int:persona_id>")
class SinglePersona(BaseResource):
    @secure_endpoint()
    def put(self, persona_id):
        data = request.get_json()

        # Define allowed fields for update
        allowed_fields = ["name", "description"]

        # Filter out fields that are provided, allowed, and not None
        updated_fields = {k: v for k, v in data.items() if k in allowed_fields and v is not None}
        if not updated_fields:
            abort(400, message="No fields to update")

        # Fetch the existing statement for context
        try:
            persona = content_accessor.get_persona_by_id(persona_id)

            if not persona:
                abort(404, message="Persona not found")
        except ContentAccessorException as e:
            abort(500, message=f"Error fetching persona: {e}")

        # Validate each updated field using helper methods
        if "name" in updated_fields:
            if error := self.validate_string("name", updated_fields):
                return error

        if "description" in updated_fields:
            if error := self.validate_string("description", updated_fields):
                return error

        try:
            updated_persona = content_accessor.update_persona(persona_id, updated_fields)

            if not updated_persona:
                abort(500, message="Error updating persona")

            result = marshal(updated_persona, persona_fields)

            return result, 200
        except NotFoundException as e:
            return {"message": str(e)}, 404
        except ContentAccessorException as e:
            abort(500, message=f"Error updating persona: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")

    @secure_endpoint()
    def delete(self, persona_id):
        try:
            if persona_id is None:
                abort(400, message="persona_id is a required field")

            content_accessor.delete_persona(persona_id)

            return {"message": "Persona deleted successfully"}, 200
        except ContentAccessorException as e:
            abort(500, message=f"Error deleting persona: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")

    @secure_endpoint()
    def get(self, persona_id):

        try:
            persona = content_accessor.get_persona_by_id(persona_id)

            if not persona:
                abort(404, message="Persona not found")

            return marshal(persona, persona_fields), 200
        except ContentAccessorException as e:
            abort(500, message=f"Error getting persona: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@persona_namespace.route("personas/department/<int:department_id>", methods=["GET"])
class PersonasByDepartment(BaseResource):
    @secure_endpoint()
    def get(self, department_id):
        try:
            if department_id is None:
                abort(400, message="department_id is a required field")

            personas = content_accessor.get_personas_by_department(department_id)

            return marshal(personas, persona_fields), 200

        except ContentAccessorException as e:
            abort(500, message=f"Error getting personas: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@persona_namespace.route("personas/enterprise/<int:enterprise_id>", methods=["GET"])
class PersonasByEnterprise(BaseResource):
    @secure_endpoint()
    def get(self, enterprise_id):
        try:
            if enterprise_id is None:
                abort(400, message="enterprise_id is a required field")

            personas = content_accessor.get_personas_by_enterprise(enterprise_id)

            return marshal(personas, persona_fields), 200

        except ContentAccessorException as e:
            abort(500, message=f"Error getting personas: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")
