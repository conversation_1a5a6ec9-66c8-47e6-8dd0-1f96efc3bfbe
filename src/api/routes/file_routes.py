from datetime import datetime
import json
import io, re
import logging

from accessor.content.accessors.dto import DocumentInsertDTO
from accessor.document.document_accessor import DocumentAccessor
from accessor.content.exceptions import ContentAccessorException
from accessor.document.dto import DocumentCreateDTO
from accessor.document.dto.document_job_create_dto import DocumentJobCreateDTO, JobCreateDTO
from flask import jsonify, request, send_file, make_response
from flask_restx import Namespace, Resource, abort
from sqlalchemy import cast
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import joinedload
from sqlalchemy.sql import text, literal
from shared.enums.job_enums import JobTypeEnum
from werkzeug.utils import secure_filename

from accessor.content import ContentAccessor
from api.routes.base_resource import BaseResource, FileValidationException
from api.services import aws_service
from api.services.feed_manager_service import FeedManagerService
from engine.transformer.transform_engine import TransformEngine
from shared.enums.job_enums import JobStatusEnum
from shared.models import (
    Business,
    Chunk,
    Document,
    Job,
    Statement,
    Subtopic,
    Topic,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
)
from util import file_upload
from util.config import AWS_FEED_MANAGER_ACCESS_KEY, AWS_FEED_MANAGER_SECRET_KEY
from util.database import Session
from api.middleware.auth import secure_endpoint
from util.audit.user_ctx import get_user_ctx

logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

# Create a namespace to be used for the swagger file
file_namespace = Namespace("file", description="File related operations")

# Instantiate the FeedManagerService
feed_manager_service = FeedManagerService()

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")
document_accessor = DocumentAccessor(
    access_key=AWS_FEED_MANAGER_ACCESS_KEY,
    secret_key=AWS_FEED_MANAGER_SECRET_KEY,
    logger_name="burst-api",
)


@file_namespace.route("/upload-file")
class FileUpload(BaseResource):

    def _get_start_page(self, start_page) -> int:
        if start_page is None or int(start_page) < 1:
            start_page = 1
        else:
            start_page = int(start_page)

        return start_page

    def _get_end_page(self, end_page) -> int:
        if end_page is None or int(end_page) < 1:
            end_page = -1
        else:
            end_page = int(end_page)

        return end_page

    def _get_domain_id(self, job_type_id: int, json_payload: dict) -> int:
        """
        Determines the domain ID based on the job type and request payload.

        Args:
            job_type_id (int): The type of job being processed
            json_payload (dict): The request payload containing form data

        Returns:
            int: The determined domain ID

        Raises:
            HTTPException: If user context is not found or validation fails
        """
        if (
            job_type_id == JobTypeEnum.PROCEDURE_IMPORT.value
            or job_type_id == JobTypeEnum.OPTIMIZE_PROCEDURE.value
        ):
            # Get user context to get enterprise_id
            user_ctx = get_user_ctx()
            if not user_ctx or not user_ctx.user_id:
                abort(401, message="User context not found")

            return user_ctx.domain_id
        else:
            if field_validation_error := self.validate_required_fields(
                json_payload, ["domain_id", "name"]
            ):
                return field_validation_error
            if field_validation_error := self.validate_integer("domain_id", json_payload):
                return field_validation_error

            return int(json_payload["domain_id"])

    def _validate_job_type(self, json_payload: dict) -> int:
        """Validates and returns the job type ID from the payload."""
        if "job_type_id" in json_payload and json_payload["job_type_id"]:
            if field_validation_error := self.validate_integer("job_type_id", json_payload):
                return field_validation_error

        return int(json_payload["job_type_id"])

    def _validate_required_fields(self, json_payload: dict) -> None:
        """Validates all required fields in the payload."""
        if field_validation_error := self.validate_required_fields(json_payload, ["name"]):
            return field_validation_error
        if field_validation_error := self.validate_string("name", json_payload):
            return field_validation_error

    def _validate_optional_fields(self, json_payload: dict) -> None:
        """Validates all optional fields in the payload."""
        if "business_id" in json_payload and json_payload["business_id"]:
            if field_validation_error := self.validate_integer("business_id", json_payload):
                return field_validation_error
        if "start_page" in json_payload and json_payload["start_page"]:
            if field_validation_error := self.validate_integer("start_page", json_payload):
                return field_validation_error
        if "end_page" in json_payload and json_payload["end_page"]:
            if field_validation_error := self.validate_integer("end_page", json_payload):
                return field_validation_error

    def _create_document_dto(self, domain_id: int, json_payload: dict) -> DocumentInsertDTO:
        """Creates and returns a DocumentInsertDTO from the validated payload."""
        return DocumentInsertDTO(
            domain_id=domain_id,
            name=json_payload["name"],
            business_id=int(json_payload.get("business_id", 1)),
            start_page=self._get_start_page(json_payload.get("start_page", 0)),
            end_page=self._get_end_page(json_payload.get("end_page", 0)),
            effective_datetime=json_payload.get("effective_datetime", datetime.now()),
            s3_location="",  # To be updated after file upload
        )

    def _upload_to_s3(self, filename: str, uploaded_file) -> tuple[str, str]:
        """Uploads a file to S3 and returns the location and any error."""
        return aws_service.upload_file_to_s3(filename, uploaded_file)

    def _create_job(self, document_id: int, job_type_id: int) -> Job:
        """Creates and returns a new job for the document."""
        job_insert_fields = {
            "reference_id": document_id,
            "job_status_id": JobStatusEnum.INITIAL_PROCESSING.value,
            "job_type_id": job_type_id,
        }
        return content_accessor.save_job(job_insert_fields)

    def _prepare_response(
        self, document_id: int, job_id: int, feed_manager_response: dict, json_payload: dict
    ) -> tuple[dict, int]:
        """Prepares and returns the success response."""
        response = {
            "success": "File uploaded and records created successfully",
            "document_id": document_id,
            "job_id": job_id,
            "feed_manager_response": feed_manager_response,
        }

        if additional_data := json_payload.get("additional_data"):
            response["additional_data"] = additional_data

        return response, 201

    @secure_endpoint()
    def post(self):
        # Job types that use this endpoint:
        # - Procedure Import (job_type_id = 3)
        # - Artifact Generation (job_type_id = 4)
        # - Markup Change Identification (job_type_id = 5)
        # - Optimize Procedure (job_type_id = 8)

        logging.basicConfig(level=logging.DEBUG)

        try:
            logging.debug("Received file upload request")

            # Parse JSON payload from form
            json_payload = request.form.to_dict()

            # Validate job type and get uploaded file
            job_type_id = self._validate_job_type(json_payload)
            uploaded_file = self.validate_file_for_job_type(request.files, job_type_id)
            filename = secure_filename(uploaded_file.filename)
            logging.debug(f"Secure filename: {filename}")

            # Get domain_id and validate fields
            domain_id = self._get_domain_id(job_type_id, json_payload)
            self._validate_required_fields(json_payload)
            self._validate_optional_fields(json_payload)

            # Create and save document
            document_dto = self._create_document_dto(domain_id, json_payload)
            s3_location, error = self._upload_to_s3(filename, uploaded_file)
            if error:
                logging.error(f"Failed to upload file to S3: {error}")
                return jsonify({"error": f"Failed to upload file to S3: {error}"}), 500

            document_dto.s3_location = s3_location
            new_document = content_accessor.save_document(document_dto)
            logging.debug(f"Document record created with ID: {new_document.document_id}")

            # Create job and submit to feed manager
            new_job = self._create_job(new_document.document_id, job_type_id)
            logging.debug(f"Job record created with ID: {new_job.job_id}")

            feed_manager_response = feed_manager_service.submit_initial_document(
                new_document.document_id, new_job.job_id
            )

            return self._prepare_response(
                new_document.document_id, new_job.job_id, feed_manager_response, json_payload
            )

        except FileValidationException as e:
            abort(400, message=f"File validation error: {e}")
        except ValueError as e:
            abort(400, message=f"Value error: {e}")
        except ContentAccessorException as e:
            abort(500, message=f"Error uploading file: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@file_namespace.route("/compare-file-versions")
class CompareFileVersions(BaseResource):

    def _validate_files(self, file_list: list, job_type_id: int):
        """
        Validates a list of uploaded files for allowed extensions and presence.

        Args:
            file_list (list): List of FileStorage objects.
            job_type_id (int): The job type ID used to validate allowed extensions.

        Returns:
            List[FileStorage]: List of validated file objects.

        Raises:
            FileValidationException: If no files are found or if any file is invalid.
        """
        if not file_list:
            logging.error("No files in request")
            raise FileValidationException("No files in request")

        validated_files = []

        for file in file_list:
            logging.debug("Processing file: %s", file.filename)

            if file.filename == "":
                logging.warning("Skipping empty filename")
                continue

            if not file_upload.is_extension_allowed(file.filename, job_type_id):
                file_extension = file_upload.get_file_extension(file.filename)
                logging.error(
                    "File extension '%s' not allowed for job_type_id %s",
                    file_extension,
                    job_type_id,
                )
                raise FileValidationException(
                    f"File extension '{file_extension}' not allowed for job_type_id of {job_type_id}"
                )

            validated_files.append(file)

        if not validated_files:
            logging.error("No valid files found")
            raise FileValidationException("No valid files found in request")

        return validated_files

    def _validate_required_fields(self, form_data):
        if "job_type_id" in form_data and form_data["job_type_id"]:
            if field_validation_error := self.validate_integer("job_type_id", form_data):
                return field_validation_error

        if field_validation_error := self.validate_required_fields(
            form_data, ["domain_id", "name"]
        ):
            return field_validation_error
        if field_validation_error := self.validate_integer("domain_id", form_data):
            return field_validation_error
        if field_validation_error := self.validate_required_fields(form_data, ["name"]):
            return field_validation_error
        if field_validation_error := self.validate_string("name", form_data):
            return field_validation_error

    def _validate_optional_fields(self, form_data):
        if "business_id" in form_data and form_data["business_id"]:
            if field_validation_error := self.validate_integer("business_id", form_data):
                return field_validation_error
        if "start_page" in form_data and form_data["start_page"]:
            if field_validation_error := self.validate_integer("start_page", form_data):
                return field_validation_error
        if "end_page" in form_data and form_data["end_page"]:
            if field_validation_error := self.validate_integer("end_page", form_data):
                return field_validation_error

    @secure_endpoint()
    def post(self):
        logging.basicConfig(level=logging.DEBUG)

        try:
            logging.debug("Received multiple files upload request")

            # Extract form data
            form_data = request.form.to_dict()

            # Validate required fields
            if field_validation_error := self._validate_required_fields(form_data):
                return field_validation_error

            # Validate optional fields
            if field_validation_error := self._validate_optional_fields(form_data):
                return field_validation_error

            # Extract files
            file_list = request.files.getlist("files")
            if not file_list:
                return {"message": "No files uploaded"}, 400

            uploaded_files = self._validate_files(file_list, JobTypeEnum.VERSION_COMPARISON.value)

            if len(uploaded_files) < 2:
                return {"message": "Please upload at least two files."}, 400

            original_file = uploaded_files[0]
            updated_file = uploaded_files[1]

            original_bytes = original_file.read()
            updated_bytes = updated_file.read()

            # Create the original document
            original_document_dto = DocumentCreateDTO(
                domain_id=int(form_data["domain_id"]),
                name=form_data["name"] + " -previous",
                filename=uploaded_files[0].filename,
                document_bytes=original_bytes,
                business_id=int(form_data.get("business_id", 1)),
            )

            original_document_response = document_accessor.create_document(original_document_dto)
            original_document_id = original_document_response["document_id"]

            # Create the updated document
            updated_document_dto = DocumentCreateDTO(
                domain_id=int(form_data["domain_id"]),
                name=form_data["name"],
                filename=uploaded_files[1].filename,
                document_bytes=updated_bytes,
                business_id=int(form_data.get("business_id", 1)),
                original_document_id=original_document_id,
            )
            updated_document_job = JobCreateDTO(JobTypeEnum.VERSION_COMPARISON.value)

            updated_document_response = document_accessor.create_document_and_job(
                DocumentJobCreateDTO(updated_document_dto, updated_document_job)
            )

            # Submit the document and job to the feed manager
            feed_manager_response = feed_manager_service.submit_initial_document(
                updated_document_response["document_id"],
                updated_document_response["job_id"],
            )

            # Prepare and return the response
            response = {
                "success": "File uploaded and records created successfully",
                "document_id": updated_document_response["document_id"],
                "job_id": updated_document_response["job_id"],
                "feed_manager_response": feed_manager_response,
            }

            return response, 201

        except FileValidationException as e:
            abort(400, message=f"File validation error: {e}")
        except ValueError as e:
            abort(400, message=f"Value error: {e}")
        except ContentAccessorException as e:
            abort(500, message=f"Error uploading file: {e}")
        except Exception as e:
            abort(500, message=f"Unexpected error: {e}")


@file_namespace.route("/excel-export")
class ExportExcelFile(Resource):
    @secure_endpoint()
    def post(self):
        session = Session()

        try:
            # Parse the JSON request payload
            data = request.get_json()
            document_id = data.get("document_id")
            requirements_only = data.get("requirements_only")

            if document_id is None or requirements_only is None:
                return (
                    jsonify(
                        {"error": "Invalid input, please provide document_id and requirements_only"}
                    ),
                    400,
                )

            # Convert requirements_only to boolean
            requirements_only = str(requirements_only).lower() == "true"
            logging.debug(
                f"Received request with document_id={document_id} and requirements_only={requirements_only}"
            )

            # Query to get the required data
            logging.debug("Querying for job data...")
            job = (
                session.query(Job)
                .join(Document, Job.reference_id == Document.document_id)
                .options(
                    joinedload(Job.document).joinedload(Document.domain),
                    joinedload(Job.document)
                    .joinedload(Document.business)
                    .joinedload(Business.enterprises),
                )
                .filter(Document.document_id == document_id)
                .first()
            )

            if not job:
                logging.error(f"Job not found for document_id={document_id}")
                return jsonify({"error": "Job not found"}), 404

            s3_location = job.document.s3_location
            match = re.search(r"upload/([^/]+)(?:\.[^/.]+)?$", s3_location)
            extracted_filename = match.group(1) if match else None

            enterprise_name = (
                job.document.business.enterprises[0].name
                if job.document.business.enterprises
                else None
            )

            # Set up the header data
            header_data = {
                "Job Name": job.job_name,
                "File Name": extracted_filename,
                "Enterprise": enterprise_name,
                "Business": job.document.business.name,
                "Domain": job.document.domain.name,
            }
            logging.debug(f"Header data set: {header_data}")

            # Retrieve table data based on requirements_only flag
            if requirements_only:
                logging.debug("Querying for unique statements data...")

                if (
                    job.job_type_id == JobTypeEnum.FEDERAL_REGISTER_CHANGE_IDENTIFICATION.value
                    or job.job_type_id == JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value
                ):

                    source_identifier_field = cast(Statement.text, JSONB)[
                        "updated_identifier"
                    ].astext.label("source_identifier")

                    source_statement_field = cast(Statement.text, JSONB)[
                        "change_statement"
                    ].astext.label("source_statement")
                else:
                    source_identifier_field = Chunk.chunk_id.label("source_identifier")
                    source_statement_field = literal("").label("source_statement")

                unique_statements = (
                    session.query(
                        UniqueStatement,
                        Subtopic,
                        Topic,
                        UniqueStatementSubtopicAssociation.is_primary,
                        source_identifier_field,
                        source_statement_field,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .join(
                        Subtopic,
                        UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id,
                    )
                    .join(
                        Topic,
                        Subtopic.topic_id == Topic.topic_id,
                    )
                    .outerjoin(Statement, UniqueStatement.statement_id == Statement.statement_id)
                    .outerjoin(Chunk, UniqueStatement.chunk_id == Chunk.chunk_id)
                    .filter(UniqueStatement.document_id == document_id)
                    .all()
                )

                export_type = "unique_statement_excel"

                table_data = [
                    {
                        "Requirement ID": unique_statement.identifier,
                        "Topic": topic.name,
                        "Subtopic": subtopic.name,
                        "Primary Subtopic": "Yes" if is_primary else "No",
                        "Description": unique_statement.text,
                        "Rating": unique_statement.rating.value,
                        "Source Identifier": source_identifier,
                        "Source Statement": source_statement,
                    }
                    for unique_statement, subtopic, topic, is_primary, source_identifier, source_statement in unique_statements
                ]
            else:
                logging.debug("Executing SQL query for extended unique statements data...")

                export_type = "requirements_bundle_excel"

                sql = text("SELECT * FROM get_requirements_bundle_data(:document_id)")

                result = session.execute(sql, {"document_id": document_id})
                table_data = [
                    {
                        "Requirement ID": row.unique_statement_identifier,
                        "User Story ID": row.user_story_identifier,
                        "Acceptance Criteria ID": row.acceptance_criteria_identifier,
                        "Test Case ID": row.test_case_identifier,
                        "Topic": row.topic_name,
                        "Subtopic": row.subtopic_name,
                        "Primary Subtopic": "Yes" if row.is_primary else "No",
                        "Artifact Type": row.artifact_type,
                        "Description": row.description,
                        "Rating": row.rating,
                    }
                    for row in result
                ]

            logging.debug(f"Table data prepared with {len(table_data)} records")

            # Use TransformEngine to transform data to Excel format
            logging.debug("Transforming data to Excel format...")
            output = TransformEngine.transform_to_excel(export_type, header_data, table_data)

            if not isinstance(output, bytes):
                logging.error("Invalid output format from TransformEngine")
                return jsonify({"error": "Invalid output format from TransformEngine"}), 500

            # Create an in-memory output file for the new workbook.
            output_stream = io.BytesIO(output)

            logging.debug("Before sending response")

            # Return the output as a response
            return send_file(
                output_stream,
                download_name="output.xlsx",
                as_attachment=True,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )

        except Exception as e:
            logging.error(f"Exception occurred: {str(e)}")
            return jsonify({"error": str(e)}), 500
        finally:
            session.close()
            logging.debug("Database session closed")


@file_namespace.route("/requirements-explorer-excel-export")
class RequirementsExplorerExportExcelFile(Resource):
    @secure_endpoint()
    def post(self):
        session = Session()

        try:
            # Parse the JSON request payload
            data = request.get_json()
            document_ids = data.get("document_ids")

            if not document_ids:
                return jsonify({"error": "Invalid input, please provide document_ids"}), 400

            if not isinstance(document_ids, list):
                return jsonify({"error": "Invalid input, document_ids must be a list"}), 400

            table_data = []

            logging.debug("Executing SQL query for the provided document_ids...")

            sql = text(
                """
                SELECT
                    d.name AS document_name,
                    b.name AS business_name,
                    dm.name AS domain_name,
                    INITCAP(st.name) AS statement_type,
                    us.identifier AS requirement_id,
                    t.name AS topic_name,
                    s.name AS subtopic_name,
                    ussa.is_primary,
                    us.text AS description,
                    us.rating
                FROM document d
                    INNER JOIN business b ON d.business_id = b.business_id
                    INNER JOIN domain dm ON d.domain_id = dm.domain_id
                    INNER JOIN unique_statement us ON d.document_id = us.document_id
                    INNER JOIN unique_statement_subtopic_association ussa ON us.unique_statement_id = ussa.unique_statement_id
                    INNER JOIN subtopic s ON ussa.subtopic_id = s.subtopic_id
                    INNER JOIN topic t ON s.topic_id = t.topic_id
                    INNER JOIN statement_type st ON us.statement_type_id = st.statement_type_id
                WHERE
                    d.document_id IN :document_ids;
                """
            )

            result = session.execute(sql, {"document_ids": tuple(document_ids)})

            table_data.extend(
                [
                    {
                        "Document Name": row.document_name,
                        "Business": row.business_name,
                        "Domain": row.domain_name,
                        "Statement Type": row.statement_type,
                        "Statement ID": row.requirement_id,
                        "Topic": row.topic_name,
                        "Subtopic": row.subtopic_name,
                        "Primary Subtopic": "Yes" if row.is_primary else "No",
                        "Description": row.description,
                        "Rating": row.rating,
                    }
                    for row in result
                ]
            )

            if not table_data:
                logging.error(f"No data found for document_ids={document_ids}")
                return jsonify({"error": "No data found"}), 404

            logging.debug(f"Table data prepared with {len(table_data)} records")

            # Use TransformEngine to transform data to Excel format
            logging.debug("Transforming data to Excel format...")
            output = TransformEngine.transform_to_excel(
                "requirements_explorer_excel", {}, table_data
            )

            if not isinstance(output, bytes):
                logging.error("Invalid output format from TransformEngine")
                return jsonify({"error": "Invalid output format from TransformEngine"}), 500

            # Create an in-memory output file for the new workbook.
            output_stream = io.BytesIO(output)

            logging.debug("Before sending response")

            # Return the output as a response
            return send_file(
                output_stream,
                download_name="output.xlsx",
                as_attachment=True,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )

        except Exception as e:
            logging.error(f"Exception occurred: {str(e)}")
            return jsonify({"error": str(e)}), 500
        finally:
            session.close()
            logging.debug("Database session closed")


@file_namespace.route("/change-statement-curator-excel-export")
class ChangeStatementCuratorExportExcelFile(Resource):
    @secure_endpoint()
    def post(self):
        session = Session()

        try:
            # Parse the JSON request payload
            data = request.get_json()
            document_id = data.get("document_id")

            if not document_id:
                return jsonify({"error": "Invalid input, please provide a document_id"}), 400

            if not isinstance(document_id, int):
                return jsonify({"error": "Invalid input, document_id must be an integer"}), 400

            table_data = []

            logging.debug("Executing SQL query for document_id=%s...", document_id)

            # Fetch document and job data properly
            document = session.query(Document).filter(Document.document_id == document_id).first()
            job = session.query(Job).filter(Job.reference_id == document_id).first()

            if not document:
                logging.error(f"Document not found for document_id={document_id}")
                return jsonify({"error": "Document not found"}), 404

            if not job:
                logging.error(f"Job not found for document_id={document_id}")
                return jsonify({"error": "Job not found"}), 404

            # Extract the file name from s3_location
            s3_location = document.s3_location
            match = re.search(r"upload/([^/]+)(?:\.[^/.]+)?$", s3_location)
            extracted_filename = match.group(1) if match else None
            logging.debug(f"Extracted filename from S3 location: {extracted_filename}")

            enterprise_name = (
                document.business.enterprises[0].name
                if document.business and document.business.enterprises
                else None
            )

            # Set up the header data
            header_data = {
                "Job Name": job.job_name,
                "File Name": extracted_filename,
                "Enterprise": enterprise_name,
                "Business": document.business.name,
                "Domain": document.domain.name,
            }
            logging.debug(f"Header data set: {header_data}")

            # Execute SQL query for statements
            sql = text(
                # r tells Python to interpret the string literally without processing escape sequences.
                r"""
                SELECT
                    s.statement_id AS statement_id,
                    s.text AS raw_json_text,  -- Keep original text column
                    (s.text::jsonb->>'identifier') AS identifier,
                    (s.text::jsonb->>'original_text') AS original_text,
                    (s.text::jsonb->>'updated_text') AS updated_text,
                    (s.text::jsonb->>'changed_text') AS changed_text,
                    (s.text::jsonb->>'change_statement') AS change_statement,
                    (s.text::jsonb->>'impact') AS impact_rating,
                    s.rating AS rating
                FROM document d
                INNER JOIN chunk c ON c.document_id = d.document_id
                INNER JOIN statement s ON s.chunk_id = c.chunk_id
                WHERE d.document_id = :document_id
                AND s.text ~ '^\{.*\}$' -- Ensures only valid JSON rows are processed
                ORDER BY s.statement_id ASC;
                """
            )

            result = session.execute(sql, {"document_id": document_id})
            table_data.extend(
                [
                    {
                        "Statement ID": row.statement_id,
                        "Identifier": row.identifier,
                        "Original Text": row.original_text,
                        "Updated Text": row.updated_text,
                        "Changed Text": row.changed_text,
                        "Change Statement": row.change_statement,
                        "Impact": row.impact_rating,
                        "Rating": row.rating,
                    }
                    for row in result
                ]
            )

            if not table_data:
                logging.error(f"No data found for document_id={document_id}")
                return jsonify({"error": "No data found"}), 404

            logging.debug(
                f"Table data keys: {list(table_data[0].keys()) if table_data else 'No Data'}"
            )

            # Use TransformEngine to transform data to Excel format
            logging.debug("Transforming data to Excel format...")
            output = TransformEngine.transform_to_excel(
                "change_statement_curator_excel", header_data, table_data
            )

            if not isinstance(output, bytes):
                logging.error("Invalid output format from TransformEngine")
                return jsonify({"error": "Invalid output format from TransformEngine"}), 500

            # Create an in-memory output file for the new workbook.
            output_stream = io.BytesIO(output)

            logging.debug("Before sending response")

            response = send_file(
                output_stream,
                download_name="output.xlsx",
                as_attachment=True,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
            response.headers["Content-Disposition"] = "attachment; filename=output.xlsx"
            return response

        except Exception as e:
            logging.error(f"Exception occurred: {str(e)}")
            return make_response(jsonify({"error": str(e)}), 500)
        finally:
            session.close()
            logging.debug("Database session closed")
