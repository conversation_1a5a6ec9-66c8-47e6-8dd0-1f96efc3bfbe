import logging
import time
import requests
from flask_restx import Namespace, Resource
from util.langfuse.helpers.burst_langfuse_helpers import BurstLangfuseHelpers
from util.config import ENVIRONMENT_NAME, LANGFUSE_HOST, LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY
from util.langfuse.langfuse_client_manager import LangfuseClientManager

logger = logging.getLogger(__name__)

# Number of cache invalidation attempts to ensure we hit different workers
CACHE_INVALIDATION_ATTEMPTS = 4
# Delay in seconds between cache invalidation attempts
CACHE_INVALIDATION_DELAY = 0.1
# Number of items per page for Langfuse API
ITEMS_PER_PAGE = 100

cache_namespace = Namespace('cache', description='Cache management operations')

@cache_namespace.route('/invalidate-langfuse')
class LangfuseCacheInvalidation(Resource):
    """
    Invalidate the Langfuse cache across all workers.
    This endpoint should be called after updating prompts in Langfuse.
    """
    def post(self):
        """
        Invalidate the Langfuse cache across all workers and force a complete refresh.
        This endpoint makes multiple requests to ensure different workers are hit.
        """
        try:
            # Create an instance of BurstLangfuseHelpers
            langfuse_helpers = BurstLangfuseHelpers()
            env_name = ENVIRONMENT_NAME

            # First, get a fresh Langfuse client instance
            langfuse_client = LangfuseClientManager.get_instance()

            # First, directly fetch all prompts from Langfuse API to ensure we have the latest data
            api_url = f"{LANGFUSE_HOST}/api/public/v2/prompts"
            all_prompts = []
            page = 1
            total_pages = None

            while True:
                try:
                    response = requests.get(
                        api_url,
                        params={
                            "label": env_name,
                            "page": page,
                            "limit": ITEMS_PER_PAGE
                        },
                        auth=(LANGFUSE_PUBLIC_KEY, LANGFUSE_SECRET_KEY),
                        timeout=5
                    )
                    response.raise_for_status()
                    data = response.json()

                    # Get prompts from current page
                    prompts = data.get("data", [])
                    all_prompts.extend(prompts)

                    # Get total pages from metadata
                    if total_pages is None:
                        total_pages = data.get("meta", {}).get("totalPages")

                    logger.info("Fetched page %d/%s of prompts (found %d prompts on this page)",
                              page, total_pages or "?", len(prompts))

                    # Check if we've reached the last page
                    if total_pages is not None and page >= total_pages:
                        break
                    if len(prompts) < ITEMS_PER_PAGE:
                        break

                    page += 1

                except requests.RequestException as e:
                    logger.error("Error fetching page %d from Langfuse API: %s", page, str(e))
                    return {"error": f"Failed to fetch page {page} from Langfuse API: {str(e)}"}, 500

            logger.info("Successfully fetched all %d prompts directly from Langfuse API", len(all_prompts))

            # Now invalidate the cache and refresh prompts
            for i in range(CACHE_INVALIDATION_ATTEMPTS):
                try:
                    # Force a new Langfuse client instance to clear its cache
                    langfuse_client = LangfuseClientManager.get_instance(force_new=True)

                    # Invalidate our custom cache
                    langfuse_helpers.force_cache_invalidation()

                    # Force refresh all prompts for this environment
                    prompts = langfuse_helpers.prefetch_prompts(env_name)

                    logger.info("Successfully invalidated and refreshed cache on attempt %d (found %d prompts)",
                              i+1, len(prompts))
                    time.sleep(CACHE_INVALIDATION_DELAY)  # Small delay between requests
                except (requests.RequestException, ValueError) as e:
                    logger.error("Error during cache invalidation attempt %d: %s", i, str(e))
                    continue

            return {
                "message": "Cache invalidation and refresh completed successfully",
                "prompts_count": len(all_prompts),
                "environment": env_name,
                "total_pages": total_pages
            }, 200
        except (requests.RequestException, ValueError) as e:
            logger.error("Error invalidating Langfuse cache: %s", str(e))
            return {"error": str(e)}, 500
