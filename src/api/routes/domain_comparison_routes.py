import json
import logging

from flask_restx import Namespace, Resource, abort, fields, marshal_with, reqparse

from accessor.content import ContentAccessor
from api.middleware.auth import secure_endpoint
from api.services.feed_manager_service import FeedManagerService
from shared.enums import JobStatusEnum
from shared.enums.job_enums import JobTypeEnum
from shared.models.domain_comparison import DomainComparison
from util.audit.user_ctx import get_user_ctx
from util.database import Session

logger = logging.getLogger(__name__)


# Create a namespace to be used for the swagger file
domain_comparison_namespace = Namespace(
    "domain comparisons", description="Domain comparison related operations"
)

domain_comparison_fields = {
    "domain_1_id": fields.Integer,
    "domain_2_id": fields.Integer,
    "subtopic_id": fields.Integer,
    "comparison_level_id": fields.Integer,
    "statement_type_id": fields.Integer,
    "domain_1_exclusive_summary": fields.List(fields.String),
    "domain_1_exclusive_detail": fields.List(fields.String),
    "domain_2_exclusive_summary": fields.List(fields.String),
    "domain_2_exclusive_detail": fields.List(fields.String),
    "similarities_summary": fields.List(fields.String),
    "similarities_detail": fields.List(fields.String),
    "similarities_with_material_differences_summary": fields.List(fields.String),
    "similarities_with_material_differences_detail": fields.List(fields.String),
}

# Instantiate the FeedManagerService
feed_manager_service = FeedManagerService()

# Initialize ContentAccessor
content_accessor = ContentAccessor(logger_name="burst-api")


@domain_comparison_namespace.route("domain-comparison", methods=["POST"])
class DomainComparisonResource(Resource):
    @marshal_with(domain_comparison_fields)
    @secure_endpoint()
    def post(self):
        session = Session()

        parser = reqparse.RequestParser()
        parser.add_argument(
            "domain_1_id", type=int, required=True, help="Domain_1_id cannot be blank."
        )
        parser.add_argument(
            "domain_2_id", type=int, required=True, help="Domain_2_id cannot be blank."
        )
        parser.add_argument(
            "subtopic_id", type=int, required=True, help="Subtopic_id cannot be blank."
        )
        parser.add_argument(
            "statement_type_id", type=int, required=True, help="Statement_type_id cannot be blank."
        )
        args = parser.parse_args()

        print(args)

        try:
            comparison = (
                session.query(DomainComparison)
                .filter_by(
                    domain_1_id=args["domain_1_id"],
                    domain_2_id=args["domain_2_id"],
                    subtopic_id=args["subtopic_id"],
                    statement_type_id=args["statement_type_id"],
                )
                .one_or_none()
            )

            if comparison is None:
                # If no record found, return a custom error message and a 404 status
                abort(404, message="No domain comparison found for the provided IDs.")

            # Print the comparison object for debugging
            print("Comparison object retrieved from database:", comparison)

            # Parse the fields from JSON string to a Python list
            if comparison.domain_1_exclusive_summary:
                comparison.domain_1_exclusive_summary = json.loads(
                    comparison.domain_1_exclusive_summary
                )

            if comparison.domain_1_exclusive_detail:
                comparison.domain_1_exclusive_detail = json.loads(
                    comparison.domain_1_exclusive_detail
                )

            if comparison.domain_2_exclusive_summary:
                comparison.domain_2_exclusive_summary = json.loads(
                    comparison.domain_2_exclusive_summary
                )

            if comparison.domain_2_exclusive_detail:
                comparison.domain_2_exclusive_detail = json.loads(
                    comparison.domain_2_exclusive_detail
                )

            if comparison.similarities_summary:
                comparison.similarities_summary = json.loads(comparison.similarities_summary)

            if comparison.similarities_detail:
                comparison.similarities_detail = json.loads(comparison.similarities_detail)

            if comparison.similarities_with_material_differences_summary:
                comparison.similarities_with_material_differences_summary = json.loads(
                    comparison.similarities_with_material_differences_summary
                )

            if comparison.similarities_with_material_differences_detail:
                comparison.similarities_with_material_differences_detail = json.loads(
                    comparison.similarities_with_material_differences_detail
                )

            # Create a dictionary from the comparison object attributes
            comparison_dict = {
                "domain_1_id": comparison.domain_1_id,
                "domain_2_id": comparison.domain_2_id,
                "subtopic_id": comparison.subtopic_id,
                "comparison_level_id": comparison.comparison_level_id,
                "statement_type_id": comparison.statement_type_id,
                "domain_1_exclusive_summary": comparison.domain_1_exclusive_summary,
                "domain_1_exclusive_detail": comparison.domain_1_exclusive_detail,
                "domain_2_exclusive_summary": comparison.domain_2_exclusive_summary,
                "domain_2_exclusive_detail": comparison.domain_2_exclusive_detail,
                "similarities_summary": comparison.similarities_summary,
                "similarities_detail": comparison.similarities_detail,
                "similarities_with_material_differences_summary": comparison.similarities_with_material_differences_summary,
                "similarities_with_material_differences_detail": comparison.similarities_with_material_differences_detail,
            }

            # Print the comparison dictionary for debugging
            # print("Comparison dictionary to be returned:", comparison_dict)

            return comparison_dict

        finally:
            session.close()


@domain_comparison_namespace.route("policy-requirement-comparison", methods=["POST"])
class PolicyRequirementComparisonResource(Resource):

    @secure_endpoint()
    def post(self):

        parser = reqparse.RequestParser()
        parser.add_argument(
            "document_id", type=int, required=True, help="Document_id cannot be blank."
        )
        parser.add_argument("job_id", type=int, required=True, help="Job_id cannot be blank.")
        args = parser.parse_args()

        document_id = args["document_id"]
        job_id = args["job_id"]

        # Get user context to get domain_id
        user_ctx = get_user_ctx()
        if not user_ctx or not user_ctx.user_id:
            abort(401, message="User context not found")

        domain_id = user_ctx.domain_id

        job_insert_fields = {}
        job_insert_fields["reference_id"] = job_id  # comparison job points to original document job
        job_insert_fields["job_status_id"] = JobStatusEnum.GENERATING_REQUIREMENTS.value
        job_insert_fields["job_type_id"] = JobTypeEnum.POLICY_REQUIREMENT_COMPARISON.value

        new_comparison_job = content_accessor.save_job(job_insert_fields)

        feed_manager_service.logger.info(
            f"Received request to compare a reference document to a domain. domain_id: {domain_id}, document_id: {document_id}, job_id {job_id}"
        )

        result, status_code = feed_manager_service.generate_policy_requirement_comparison(
            document_id, new_comparison_job.job_id, domain_id
        )

        return result, status_code
