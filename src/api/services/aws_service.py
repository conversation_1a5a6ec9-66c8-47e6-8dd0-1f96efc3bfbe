from util.config import (
    AWS_REGION,
    AWS_S3_ACCESS_KEY,
    AWS_S3_SECRET_KEY,
    AWS_S3_BUCKET_NAME,
)

import boto3
import os


def upload_file_to_s3(filename, file):
    s3_client = boto3.client(
        service_name="s3",
        region_name=AWS_REGION,
        aws_access_key_id=AWS_S3_ACCESS_KEY,
        aws_secret_access_key=AWS_S3_SECRET_KEY,
    )

    try:
        print("Saving file temporarily")
        # Save temporarily if needed or adjust to directly stream
        temp_path = os.path.join("/tmp", filename)
        file.save(temp_path)
        print(f"File saved temporarily at {temp_path}")

        # Upload from the temporary path
        print(f"Uploading file to S3 bucket {AWS_S3_BUCKET_NAME} with key {filename}")
        s3_client.upload_file(temp_path, AWS_S3_BUCKET_NAME, filename)
        print("File uploaded to S3")

        # Clean up after upload
        os.remove(temp_path)
        print(f"Temporary file {temp_path} removed")

        s3_location = f"s3://{AWS_S3_BUCKET_NAME}/{filename}"
        print(f"File successfully uploaded to {s3_location}")
        return s3_location, None

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        return None, str(e)
