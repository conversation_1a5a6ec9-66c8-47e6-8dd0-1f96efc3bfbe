import logging
import json
import uuid
import requests

import boto3

from shared.configs.job_configs import J<PERSON><PERSON>_CONFIGS
from shared.enums.job_enums import JobMessage<PERSON><PERSON>
from util.config import (
    AWS_FEED_MANAGER_REGION,
    AWS_FEED_MANAGER_ACCESS_KEY,
    AWS_FEED_MANAGER_SECRET_KEY,
    FEED_MANAGER_API_URL,
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FeedManagerService:

    def __init__(self):
        self.sns_client = boto3.client(
            "sns",
            region_name=AWS_FEED_MANAGER_REGION,
            aws_access_key_id=AWS_FEED_MANAGER_ACCESS_KEY,
            aws_secret_access_key=AWS_FEED_MANAGER_SECRET_KEY,
        )
        self.logger = logger

    def submit_initial_document(self, document_id, job_id):
        return self._begin_job(JobMessageKey.SUBMIT_INITIAL_DOCUMENT, document_id, job_id)

    def generate_unique_requirements(self, document_id, job_id):
        return self._begin_job(JobMessageKey.GENERATE_UNIQUE_REQUIREMENTS, document_id, job_id)

    def generate_requirements_bundle(self, document_id, job_id):
        return self._begin_job(JobMessageKey.GENERATE_REQUIREMENTS_BUNDLE, document_id, job_id)

    def generate_user_stories(self, document_id, job_id):
        return self._begin_job(JobMessageKey.GENERATE_USER_STORIES, document_id, job_id)

    def generate_bulk_acceptance_criteria(self, document_id, job_id):
        return self._begin_job(JobMessageKey.GENERATE_ACCEPTANCE_CRITERIA, document_id, job_id)

    def generate_bulk_test_cases(self, document_id, job_id):
        return self._begin_job(JobMessageKey.GENERATE_TEST_CASES, document_id, job_id)

    def regenerate_statements(self, document_id, job_id, chunk_id):
        return self._begin_job(JobMessageKey.REGENERATE_STATEMENTS, document_id, job_id, chunk_id)

    def generate_policy_requirement_comparison(self, document_id: int, job_id: int, domain_id: int):
        return self._begin_job(
            JobMessageKey.GENERATE_POLICY_REQUIREMENT_COMPARISON,
            document_id,
            job_id,
            domain_id=domain_id,
        )

    def generate_federal_register_change_statements(
        self, document_id: int, job_id: int, domain_id: int
    ):
        return self._begin_job(
            JobMessageKey.GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS,
            document_id,
            job_id,
            domain_id=domain_id,
        )

    def _begin_job(self, job_message_key, document_id, job_id, chunk_id=None, domain_id=None):
        job_message_config = JOB_CONFIGS.get(job_message_key)
        if not job_message_config:
            self.logger.error(f"Unknown job message key: {job_message_key}")
            return {
                "message": f"Unknown job message key: {job_message_key}",
                "status": "failure",
            }, 400

        if not isinstance(document_id, int) or not isinstance(job_id, int):
            self.logger.error("document_id or job_id not integer")
            return {"message": "document_id and job_id must be integers", "status": "failure"}, 400

        if job_message_config.get("requires_chunk_id") and not isinstance(chunk_id, int):
            self.logger.error("chunk_id is required and must be an integer")
            return {"message": "chunk_id must be an integer", "status": "failure"}, 400

        if job_message_config.get("requires_domain_id") and not isinstance(domain_id, int):
            self.logger.error("domain_id is required and must be an integer")
            return {"message": "domain_id must be an integer", "status": "failure"}, 400

        message_body = {"document_id": document_id, "job_id": job_id}

        if chunk_id is not None:
            message_body["chunk_id"] = chunk_id

        if domain_id is not None:
            message_body["domain_id"] = domain_id

        message = {
            "header": {
                # Use the value of the enum
                "type": job_message_config["message_type"].value
            },
            "body": message_body,
        }

        success = self._publish_sns_message(job_message_config["sns_topic_arn"], message)

        if success:
            return {"status": "success"}, 200
        else:
            return {"status": "failure"}, 500

    def _publish_sns_message(self, topic_arn, message):
        message_deduplication_id = str(uuid.uuid4())
        try:
            response = self.sns_client.publish(
                TopicArn=topic_arn,
                Message=json.dumps(message),
                MessageGroupId=str(uuid.uuid4()),
                MessageDeduplicationId=message_deduplication_id,
            )

            http_status = response["ResponseMetadata"]["HTTPStatusCode"]
            message_id = response.get("MessageId", "")

            if http_status == 200 and message_id:
                self.logger.info(f"Message published successfully with MessageId: {message_id}")
                return True

            self.logger.error(f"Failed to publish message: {response}")
            return False
        except Exception as e:
            self.logger.exception(f"Error when attempting to publish message: {e}", exc_info=True)
            return False

    def generate_acceptance_criteria(self, user_story_id):
        return self._post_to_feed_manager(
            endpoint=f"/api/user-story/{user_story_id}/generate-acceptance-criteria"
        )

    def generate_test_cases_for_acceptance_criteria(self, acceptance_criteria_id):
        return self._post_to_feed_manager(
            endpoint=f"/api/acceptance-criteria/{acceptance_criteria_id}/generate-test-cases"
        )

    def _post_to_feed_manager(self, endpoint):
        url = f"{FEED_MANAGER_API_URL}{endpoint}"
        logger.info(f"URL = '{url}'")

        try:
            response = requests.post(url)
            response.raise_for_status()  # Raises an HTTPError if the HTTP request returned an unsuccessful status code
            logger.info(f"Feed manager response: {response.json()}")
            return response.json(), response.status_code
        except requests.exceptions.HTTPError as http_err:
            logger.error(f"HTTP error occurred: {str(http_err)}")
            return {
                "error": "An HTTP error occurred while processing the request."
            }, response.status_code
        except Exception as err:
            logger.error(f"An error occurred: {str(err)}")
            return {"error": "An internal error occurred while processing the request."}, 500
