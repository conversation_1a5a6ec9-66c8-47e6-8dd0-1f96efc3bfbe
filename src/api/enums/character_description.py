from enum import Enum


# Define the enum
class CharacterEnum(Enum):
    FAROOQ = "Farooq"
    YOLANDA = "Yoland<PERSON>"
    MICHAEL = "Michael"


class CharacterDescriptionEnum(Enum):
    FAROOQ_DESCRIPTION = "Create an illustration of <PERSON><PERSON><PERSON>, a distinguished man in his late 50s with warm "\
        "brown skin, distinct Middle Eastern features, a short, neatly groomed black beard, and an 'Imperial' "\
        "style mustache. He has a modern, neat haircut with touches of gray, and wears classic black-rimmed "\
        "glasses. <PERSON><PERSON><PERSON> is dressed in a well-tailored suit, symbolizing his sophistication and "\
        "professionalism as a NYC coop building engineer. He embodies an intellectual appearance with minimal "\
        "but refined accessories, such as a classic watch. The setting is the elegant interior of a NYC coop "\
        "building, highlighting his professional identity."
    YOLANDA_DESCRIPTION = "Create an illustration of <PERSON><PERSON><PERSON>, a 55-year-old Latin woman with white skin and "\
        "a distinguished demeanor. Her rich, black hair is always tied back in a neat bun at the nape of her "\
        "neck, showcasing a polished and professional image. <PERSON><PERSON><PERSON> is dressed in a tailored suit that "\
        "complements her commanding presence, exuding confidence and respect. She wears minimal accessories, "\
        "only a statement watch and a pair of simple pearl earrings, emphasizing her refined taste."
    MICHAEL_DESCRIPTION = "Create an illustration of <PERSON>, a resilient and vital 60-year-old firefighter. "\
        "He has an athletic build with broad shoulders and a solid stance, reminiscent of a seasoned athlete. "\
        "His short, styled hair showcases distinguished gray, especially at the temples, indicating wisdom and "\
        "experience. Michael is clean-shaven, enhancing his disciplined appearance. He wears a classic flannel "\
        "shirt over a T-shirt, reflecting his practical yet stylish off-duty persona. His posture exudes "\
        "confidence and grace, embodying both strength and approachability."


class DomainEnum(Enum):
    ALL = "All"
    GNMA = "Ginnie Mae"
    MORTGAGE = "Mortgage"
    FNMA = "Fannie Mae Mortgage Servicing"
    VA = "VA Medical/Dental Claims Examination"
    REGX_RESPA = "Reg X/RESPA"
    FHA = "FHA"
    USDA_RURAL_HOUSING = "USDA Rural Housing"
    VA_MORTGAGE_SERVICING = "VA Mortgage Servicing"
    MISMO = "MISMO"
    VCF = "VCF"
    VCFPROPOSAL = "VCF Proposal"
    CFPB = "CFPB"
    STUDENT_LOANS = "Student Loans"
    REGZ_TILA = "Reg Z/TILA"
    HUD = "HUD"
