from flask_restx import fields
from datetime import datetime

audit_activity_fields = {
    "audit_activity_id": fields.Integer,
    "audit_object_type_id": fields.Integer,
    "audit_object_type_name": fields.String,
    "object_key": fields.Integer,
    "data_snapshot": fields.Raw,
    "user_id": fields.Integer,
    "user_name": fields.String,
    "audit_operation_type_id": fields.Integer,
    "audit_operation_type_name": fields.String,
    "recorded_at": fields.DateTime(
        dt_format="iso8601",
        attribute=lambda x: (
            x.recorded_at.isoformat() if isinstance(x.recorded_at, datetime) else x.recorded_at
        ),
    ),
}
