import logging
import sys

from flask import Flask, jsonify, request
from flask_cors import CORS
from flask_restx import Api, Resource
from routes.acceptance_criteria_routes import acceptance_criteria_namespace
from routes.business_routes import business_namespace
from routes.cache_routes import cache_namespace
from routes.change_statement_routes import change_statement_namespace
from routes.chunk_routes import chunk_namespace
from routes.department_routes import department_namespace
from routes.document_routes import document_namespace
from routes.domain_comparison_routes import domain_comparison_namespace
from routes.domain_group_routes import domain_group_namespace
from routes.domain_routes import domain_namespace
from routes.enterprise_routes import enterprise_namespace
from routes.file_routes import file_namespace
from routes.image_routes import images_namespace
from routes.job_routes import job_namespace
from routes.job_status_routes import job_status_namespace
from routes.job_type_routes import job_type_namespace
from routes.keyword_routes import keyword_namespace
from routes.persona_routes import persona_namespace
from routes.statement_routes import statement_namespace
from routes.statement_type_routes import statement_type_namespace
from routes.subtopic_routes import subtopic_namespace
from routes.test_case_routes import test_case_namespace
from routes.topic_routes import topic_namespace
from routes.unique_statement_routes import unique_statement_namespace
from routes.user_routes import user_namespace
from routes.user_story_routes import user_story_namespace
from sqlalchemy.orm import configure_mappers

from util.audit.audit_service_container import AuditServiceContainer
from util.audit.user_ctx import clear_user_ctx
from shared.exceptions import AuthError

# from utils.config import ENABLE_SWAGGER_UI


logger = logging.getLogger(__name__)


def create_app():

    app = Flask(__name__)

    # Ensure ORM mappings are set up first
    configure_mappers()

    api = Api(app, version="1.0", title="Burst API", description="Burst API")

    # Instantiate and eagerly connect the auditor to the event signal
    audit_container = AuditServiceContainer()
    _ = audit_container.auditor()

    # 6/7/24 NK Commented out for now until we can find a way to put it behind a login page
    #           The api should only show up on the dev api site
    # For the time being only generate the swagger page if this env var is set to true
    # We will keep it set to false on the server
    # swagger_ui_enabled = ENABLE_SWAGGER_UI and ENABLE_SWAGGER_UI.lower() == 'true'
    # api = Api(app, version='1.0', title='Burst API', description='Burst API',
    # doc='/' if swagger_ui_enabled else False)

    # Register your namespaces
    api.add_namespace(acceptance_criteria_namespace, path="/")
    api.add_namespace(business_namespace, path="/")
    api.add_namespace(cache_namespace, path="/")
    api.add_namespace(change_statement_namespace, path="/")
    api.add_namespace(chunk_namespace, path="/")
    api.add_namespace(department_namespace, path="/")
    api.add_namespace(document_namespace, path="/")
    api.add_namespace(domain_comparison_namespace, path="/")
    api.add_namespace(domain_group_namespace, path="/")
    api.add_namespace(domain_namespace, path="/")
    api.add_namespace(enterprise_namespace, path="/")
    api.add_namespace(file_namespace, path="/file")
    api.add_namespace(images_namespace, path="/")
    api.add_namespace(job_namespace, path="/")
    api.add_namespace(job_type_namespace, path="/")
    api.add_namespace(job_status_namespace, path="/")
    api.add_namespace(keyword_namespace, path="/")
    api.add_namespace(persona_namespace, path="/")
    api.add_namespace(statement_namespace, path="/")
    api.add_namespace(statement_type_namespace, path="/")
    api.add_namespace(subtopic_namespace, path="/")
    api.add_namespace(test_case_namespace, path="/")
    api.add_namespace(topic_namespace, path="/")
    api.add_namespace(unique_statement_namespace, path="/")
    api.add_namespace(user_namespace, path="/")
    api.add_namespace(user_story_namespace, path="/")

    @app.before_request
    def before_request_func():
        if request.method == "OPTIONS":
            return "", 200

    # Log every request (for debugging)
    @app.before_request
    def log_request():
        logger.info("Received %s request on %s", request.method, request.path)

        logging.info(
            "Incoming request: %s %s from %s | User-Agent: %s",
            request.method,
            request.path,
            request.remote_addr,
            request.headers.get("User-Agent", "Unknown"),
        )

    @api.route("/health")
    class Health(Resource):
        def get(self):
            return {"status": "healthy"}, 200

    # Log API ready
    logger.info("Burst API is now running!")

    # Error Handler for Authentication Errors
    @app.errorhandler(AuthError)
    def handle_auth_error(ex):
        response = jsonify(ex.error)
        response.status_code = ex.status_code
        return response

    # General Error Handler for Unexpected Exceptions
    @app.errorhandler(Exception)
    def handle_general_error(ex):
        if isinstance(ex, (KeyboardInterrupt, SystemExit)):
            raise ex  # Prevents swallowing system exit signals

        logging.error("Unhandled exception: %s", str(ex))
        response = jsonify({"error": "Internal server error"})
        response.status_code = 500
        return response

    @app.teardown_request
    def clear_user_context(exception=None):

        clear_user_ctx()

    return app
