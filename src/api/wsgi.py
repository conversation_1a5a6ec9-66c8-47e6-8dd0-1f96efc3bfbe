import logging
import sys

from flask_cors import CORS
from burstapi import create_app

# Create the Flask app
app = create_app()

# Gun<PERSON> expects `application` instead of `app`
application = app

# Apply CORS globally with proper headers
CORS(
    application,
    supports_credentials=True,
    allow_headers=["Authorization", "Content-Type", "x-api-key"],
)

# Ensure Flask exceptions are properly propagated
application.config["PROPAGATE_EXCEPTIONS"] = True

# Ensure logs flush immediately
sys.stdout.flush()

# Ensure Flask's built-in logs (Werkzeug) are captured properly
logging.getLogger("werkzeug").setLevel(logging.INFO)
