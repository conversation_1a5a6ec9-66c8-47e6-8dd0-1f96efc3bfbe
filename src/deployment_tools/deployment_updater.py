"""
A utility script for updating Kubernetes deployment.yaml files with environment variables.

This script allows adding an environment variable to deployment.yaml files dynamically. The value
can be based on the environment name (derived from folder names) or customized. It supports optional
prepending of the environment name to the specified value for greater flexibility.
"""

import argparse
import os

import yaml


class DeploymentUpdater:
    """
    A utility class for managing updates to deployment.yaml files.

    Attributes:
        base_paths (list): A list of base directories containing deployment folders.
    """

    def __init__(self, base_paths):
        """
        Initializes the DeploymentUpdater with a list of base paths.

        Args:
            base_paths (list): A list of base directories containing deployment folders.
        """
        self.base_paths = base_paths
        self.target_containers = {"phoenix-burst-api", "phoenix-burst-feed-manager"}

    def add_env_variable(self, key, value, prepend_env_name=False):
        """
        Adds an environment variable to deployment.yaml files.

        The value can be static or dynamically generated based on the environment name
        (derived from the folder name). Optionally, the environment name can be prepended
        to the value.

        Args:
            key (str): The key of the environment variable to add.
            value (str): The base value of the environment variable. An empty string means
                         the value will only be the environment name.
            prepend_env_name (bool): Whether to prepend the environment name to the value.
        """
        for base_path in self.base_paths:
            abs_path = os.path.abspath(base_path)
            print(f"Processing deployments in: {abs_path}")
            for root, dirs, _ in os.walk(abs_path):
                for dir_name in dirs:
                    # Determine the value based on the folder name and prepend option
                    env_value = (
                        f"{dir_name}-{value}"
                        if prepend_env_name and value
                        else dir_name if prepend_env_name else value
                    )
                    deployment_file = os.path.join(root, dir_name, "deployment.yaml")

                    if os.path.exists(deployment_file):
                        self._update_deployment_file(deployment_file, key, env_value)
                    else:
                        print(f"deployment.yaml not found in: {os.path.join(root, dir_name)}")

    def _update_deployment_file(self, file_path, key, value):
        """
        Updates the deployment.yaml file with the specified key and value for target containers.

        Args:
            file_path (str): Path to the deployment.yaml file.
            key (str): The environment variable key to add or update.
            value (str): The value of the environment variable to add or update.
        """
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                deployment_data = yaml.safe_load(file)

            containers = deployment_data["spec"]["template"]["spec"]["containers"]
            updated = False

            for container in containers:
                if container["name"] in self.target_containers:
                    env_list = container.get("env", [])

                    # Check if the key already exists
                    existing_env = next((env for env in env_list if env["name"] == key), None)
                    if existing_env:
                        # Update the value if it exists
                        if existing_env["value"] != value:
                            existing_env["value"] = value
                            updated = True
                    else:
                        # Add a new key-value pair
                        env_list.append({"name": key, "value": value})
                        updated = True

                    # Update the container's env list
                    container["env"] = env_list

            if updated:
                with open(file_path, "w", encoding="utf-8") as file:
                    yaml.dump(
                        deployment_data,
                        file,
                        default_flow_style=False,
                        Dumper=CustomDumper,
                    )
                print(f"Updated: {file_path} with {key}={value}")
            else:
                print(f"No updates needed in: {file_path}")

        except KeyError as e:
            print(f"Skipping {file_path} due to missing keys: {e}")
        except Exception as e:
            print(f"Error updating {file_path}: {e}")


def main():
    """
    The main entry point for the script.

    Parses command-line arguments and updates deployment.yaml files with the specified
    environment variable key and value. Optionally, the environment name can be prepended
    to the value.

    Command-line Arguments:
        key (str): The key to add to the environment variables.
        value (str): The base value to associate with the key. Use an empty string for just
                     the environment name.
        --prepend-env-name (bool): Whether to prepend the environment name to the value.
        --paths (list): A list of base directories containing deployment folders. Defaults to
                        ../api/deploy and ../manager/feed/deploy.
    """
    # Set up argument parsing
    parser = argparse.ArgumentParser(description="Update deployment.yaml files with specified key.")
    parser.add_argument(
        "key",
        type=str,
        help="The key to add to the environment variables.",
    )
    parser.add_argument(
        "value",
        type=str,
        help="The base value to associate with the key. Use an empty string for just the environment name.",
    )
    parser.add_argument(
        "--prepend-env-name",
        action="store_true",
        help="Whether to prepend the environment name to the value.",
    )
    parser.add_argument(
        "--paths",
        type=str,
        nargs="+",
        default=["../api/deploy", "../manager/feed/deploy"],
        help="List of base directories containing deployment folders (default: ../api/deploy and ../manager/feed/deploy).",
    )

    args = parser.parse_args()

    # Initialize the DeploymentUpdater
    updater = DeploymentUpdater(args.paths)

    # Add the specified key and dynamically generated value
    updater.add_env_variable(args.key, args.value, args.prepend_env_name)


class CustomDumper(yaml.Dumper):
    """
    Custom YAML Dumper to ensure block scalar style for multiline strings.
    """

    def represent_scalar(self, tag, value, style=None):
        if isinstance(value, str) and "\n" in value:
            style = "|"  # Use block scalar style for multiline strings
        return super().represent_scalar(tag, value, style=style)


if __name__ == "__main__":
    main()
