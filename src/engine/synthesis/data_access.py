"""
This module provides a data access layer for handling database operations related to
chunks, statements, documents, topics, and related entities. It uses SQLAlchemy as the ORM
to interact with a PostgreSQL database. The module defines several SQLAlchemy models
representing the database tables and a `DataAccess` class with methods to perform various
CRUD operations on these tables.
"""

from typing import Optional

from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from shared.models import (
    AcceptanceCriteria,
    Business,
    Chunk,
    Document,
    Domain,
    DomainComparison,
    RatingEnum,
    Statement,
    Subtopic,
    TestCase,
    Topic,
    UniqueStatement,
    UserStory,
    AcceptanceCriteria,
    RatingEnum,
    UniqueStatementSubtopicAssociation,
)

from util.database import Session


class DataAccess:
    """
    Provides methods for performing CRUD operations on various database tables, including
    chunks, statements, topics, subtopics, user stories, acceptance criteria, and more.
    This class manages database sessions and handles commit, rollback, and session closure.
    """

    def __init__(self):
        """
        Initializes the DataAccess instance and sets up the database session.
        """
        self.session_factory = Session

    def bulk_get_statements_by_chunks_and_type(self, chunk_ids: list, statement_type_id: int):
        """
        Fetch statements in batches using pagination.
        """
        session = self.session_factory()
        try:
            if not chunk_ids:
                return []

            # Fetch statements with pagination
            statements = (
                session.query(Statement)
                .filter(
                    Statement.chunk_id.in_(chunk_ids),
                    Statement.statement_type_id == statement_type_id,
                    Statement.rating != RatingEnum.negative.value,
                )
                .all()
            )

            return statements
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def find_unique_statements_for_chunk_backfill(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ):
        session = self.session_factory()
        try:
            unique_statements = (
                session.query(UniqueStatement)
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatementSubtopicAssociation.subtopic_id
                    == subtopic_id,  # Use subtopic_id from association table
                    UniqueStatement.statement_type_id == statement_type_id,
                    UniqueStatement.rating != RatingEnum.negative.value,
                    UniqueStatement.chunk_id.is_(None),
                )
                .all()
            )
            return unique_statements
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_topic_by_id(self, topic_id: int):
        session = self.session_factory()
        try:
            topic = session.query(Topic).filter(Topic.topic_id == topic_id).first()
            return topic
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_topic_by_name(self, topic_name: str):
        session = self.session_factory()
        try:
            topic = session.query(Topic).filter(Topic.name == topic_name).first()
            return topic
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_min_chunk_id_by_document(self, document_id: int) -> Optional[int]:
        session = self.session_factory()
        try:
            # Query to get the minimum chunk_id for the given document_id
            min_chunk_id = (
                session.query(Chunk.chunk_id)
                .filter(Chunk.document_id == document_id)
                .order_by(Chunk.chunk_id.asc())
                .first()
            )

            # Extract the integer value safely
            return min_chunk_id[0] if min_chunk_id is not None else None
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_max_chunk_id_by_document(self, document_id: int) -> Optional[int]:
        session = self.session_factory()
        try:
            # Query to get the maximum chunk_id for the given document_id
            max_chunk_id = (
                session.query(Chunk.chunk_id)
                .filter(Chunk.document_id == document_id)
                .order_by(Chunk.chunk_id.desc())
                .first()
            )

            # Return the maximum chunk_id or None if no result found
            return max_chunk_id[0] if max_chunk_id is not None else None
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_business_persona_by_document(self, document_id: int) -> Optional[str]:
        session = self.session_factory()
        try:
            # Perform a join from Document to Business using the document_id
            business_persona = (
                session.query(Business.persona)
                .join(Document, Business.business_id == Document.business_id)
                .filter(Document.document_id == document_id)
                .first()
            )

            # Return the persona field if not None and not empty, else return None
            return business_persona[0] if business_persona and business_persona[0].strip() else None
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_subtopic_by_name(self, subtopic_name: str):
        session = self.session_factory()
        try:
            subtopics = session.query(Subtopic).filter(Subtopic.name == subtopic_name).first()
            return subtopics
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_topics(self, business_id: int):
        session = self.session_factory()
        try:
            topics = session.query(Topic).filter(Topic.business_id == business_id).all()
            return topics
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_no_subtopic_statements(self, chunk_id: int):
        session = self.session_factory()
        try:
            no_subtopic_statements = (
                session.query(Statement)
                .filter(Statement.text == "No relevant topics", Statement.chunk_id == chunk_id)
                .all()
            )
            return no_subtopic_statements
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def get_hallucinated_statement_ids(self):
        session = self.session_factory()
        try:
            result = session.execute(text("SELECT * FROM public.get_hallucinated_statement_ids()"))
            statement_ids = [row[0] for row in result]
            return statement_ids
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def find_chunks_excluding_placeholder_content(
        self, subtopic_id: int, document_id: int, statement_type_id: int
    ):
        subtopic_statement_type_id = 8
        placeholder_map = {
            1: "No Requirement statements found",
            2: "No Opportunity statements found",
            3: "No Definition statements found",
            6: "No Authorization statements found",
            11: "No Procedure Change statements found",
            12: "No Organizational Change statements found",
        }

        session = self.session_factory()
        try:

            placeholder_text = placeholder_map.get(statement_type_id, "")

            chunks_with_statement_type = (
                session.query(Chunk.chunk_id)
                .distinct()
                .join(Statement, Statement.chunk_id == Chunk.chunk_id)
                .filter(
                    Statement.statement_type_id == statement_type_id,
                    Statement.text != placeholder_text,
                    Chunk.document_id == document_id,
                )
                .subquery()
            )

            chunks_with_statement_type_8_and_subtopic = (
                session.query(Chunk.chunk_id)
                .distinct()
                .join(Statement, Statement.chunk_id == Chunk.chunk_id)
                .join(Subtopic, Statement.text == Subtopic.name)
                .filter(
                    Statement.statement_type_id == subtopic_statement_type_id,
                    Subtopic.subtopic_id == subtopic_id,
                    Chunk.document_id == document_id,
                )
                .subquery()
            )

            query = (
                session.query(chunks_with_statement_type.c.chunk_id)
                .join(
                    chunks_with_statement_type_8_and_subtopic,
                    chunks_with_statement_type.c.chunk_id
                    == chunks_with_statement_type_8_and_subtopic.c.chunk_id,
                )
                .distinct()
            )
            result = query.all()
            chunk_ids = [row[0] for row in result]
            return chunk_ids
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def delete_duplicate_statements(self):
        session = self.session_factory()
        try:
            session.execute(text("CALL public.delete_duplicate_statements()"))
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def upsert_domain_comparison(
        self,
        domain_1_id: int,
        domain_2_id: int,
        subtopic_id: int,
        statement_type_id: int,
        comparison_level_id: int,
        domain_1_exclusive_summary: str,
        domain_1_exclusive_detail: str,
        domain_2_exclusive_summary: str,
        domain_2_exclusive_detail: str,
        similarities_summary: str,
        similarities_detail: str,
        similarities_with_material_differences_summary: str,
        similarities_with_material_differences_detail: str,
    ):
        session = self.session_factory()
        try:
            domain_comparison = (
                session.query(DomainComparison)
                .filter_by(
                    domain_1_id=domain_1_id,
                    domain_2_id=domain_2_id,
                    subtopic_id=subtopic_id,
                    statement_type_id=statement_type_id,
                    comparison_level_id=comparison_level_id,
                )
                .first()
            )

            if domain_comparison:
                # Update existing record
                domain_comparison.domain_1_exclusive_summary = domain_1_exclusive_summary
                domain_comparison.domain_1_exclusive_detail = domain_1_exclusive_detail
                domain_comparison.domain_2_exclusive_summary = domain_2_exclusive_summary
                domain_comparison.domain_2_exclusive_detail = domain_2_exclusive_detail
                domain_comparison.similarities_summary = similarities_summary
                domain_comparison.similarities_detail = similarities_detail
                domain_comparison.similarities_with_material_differences_summary = (
                    similarities_with_material_differences_summary
                )
                domain_comparison.similarities_with_material_differences_detail = (
                    similarities_with_material_differences_detail
                )
            else:
                # Insert new record
                domain_comparison = DomainComparison(
                    domain_1_id=domain_1_id,
                    domain_2_id=domain_2_id,
                    subtopic_id=subtopic_id,
                    statement_type_id=statement_type_id,
                    comparison_level_id=comparison_level_id,
                    domain_1_exclusive_summary=domain_1_exclusive_summary,
                    domain_1_exclusive_detail=domain_1_exclusive_detail,
                    domain_2_exclusive_summary=domain_2_exclusive_summary,
                    domain_2_exclusive_detail=domain_2_exclusive_detail,
                    similarities_summary=similarities_summary,
                    similarities_detail=similarities_detail,
                    similarities_with_material_differences_summary=similarities_with_material_differences_summary,
                    similarities_with_material_differences_detail=similarities_with_material_differences_detail,
                )
                session.add(domain_comparison)

            session.commit()
            session.refresh(domain_comparison)
            return domain_comparison
        except SQLAlchemyError as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def bulk_insert_test_cases(self, acceptance_criteria_id: int, test_case_descriptions: list):
        session = self.session_factory()
        try:
            test_cases = [
                TestCase(acceptance_criteria_id=acceptance_criteria_id, description=description)
                for i, description in enumerate(test_case_descriptions)
            ]
            session.bulk_save_objects(test_cases)
            session.commit()
        except ImportError as e:
            session.rollback()
            print(f"Error during bulk insert: {e}")
        finally:
            session.close()
