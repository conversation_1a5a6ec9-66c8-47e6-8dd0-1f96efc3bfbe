"""
Responsible for linking unique statements back to source chunks.
"""

from logging import Logger

from accessor.content import ContentAccessor
from langfuse.decorators import observe

from engine.synthesis.data_access import DataAccess
from engine.synthesis.text_extractor import TextExtractor


class UniqueStatementToChunkMapper:
    """
    Attempts to map unique statements back to originating chunks.

    This class is responsible for mapping unique statements (extracted from documents) to their
    source chunks by using a combination of database queries and AI-based evaluation. The mapping
    process is carried out by evaluating candidate statements that match specific criteria and using
    the highest-probability match.
    """

    def __init__(self, data_access: DataAccess, text_extractor: TextExtractor, logger: Logger):
        self.data_access = data_access
        self.text_extractor = text_extractor
        self.logger = logger
        self.content_accessor = ContentAccessor(logger.name)

    @observe()
    def map_by_document_statement_type(self, document_id, statement_type_id):
        """
        Maps unique statements to their corresponding source chunks for a given document and statement type.

        This method retrieves all subtopics related to a given document and processes each subtopic
        by invoking the `map_by_document_subtopic_statement_type` method. It logs the progress and
        coordinates the mapping of unique statements to their source chunks based on the provided
        document and statement type.

        Args:
            document_id (int): The ID of the document for which to map unique statements.
            statement_type_id (int): The statement type ID used to filter the unique statements for mapping.
        """

        self.logger.debug(
            "Mapping unique statements for: document_id %s, and statement_type_id %s",
            document_id,
            statement_type_id,
        )

        subtopics = self.content_accessor.get_subtopics_by_document(document_id)

        for subtopic in subtopics:
            self.map_by_document_subtopic_statement_type(
                document_id, subtopic.subtopic_id, statement_type_id
            )

    @observe()
    def map_by_document_subtopic_statement_type(
        self, document_id, subtopic_id, statement_type_id, batch_size=500
    ):
        """
        Maps unique statements to relevant source chunks for a specific document, subtopic, and statement type.

        This method processes a given document, subtopic, and statement type by fetching the unique statements
        and relevant chunk IDs. It identifies candidate statements associated with the chunks and processes
        them in batches to map unique statements to their highest-probability source chunks.

        Args:
            document_id (int): The ID of the document being processed.
            subtopic_id (int): The ID of the subtopic related to the document.
            statement_type_id (int): The ID of the statement type to filter the unique statements and chunks.
            batch_size (int, optional): The size of the batch used for processing candidate statements. Defaults to 500.

        Steps:
            1. Fetch unique statements based on document, subtopic, and statement type.
            2. Fetch relevant chunk IDs for the given subtopic and document, excluding placeholder content.
            3. Fetch candidate statements associated with the relevant chunks.
            4. Process each unique statement by evaluating candidate statements in batches.
            5. Accumulate matches for each unique statement during batch processing.
            6. Make a final decision by identifying the highest-probability match for each unique statement.

        Returns:
            None: This method does not return a value but logs the process and updates the mapping accordingly.
        """
        self.logger.debug(
            "Mapping unique statements for: document_id %s, subtopic_id %s, statement_type_id %s",
            document_id,
            subtopic_id,
            statement_type_id,
        )

        # Step 1: Fetch unique statements for the given document, subtopic, and statement type
        subtopic_unique_statements = self.data_access.find_unique_statements_for_chunk_backfill(
            document_id, subtopic_id, statement_type_id
        )

        if not subtopic_unique_statements:
            self.logger.debug(
                "No unique statements to map for document_id %s and subtopic_id %s",
                document_id,
                subtopic_id,
            )
            return

        # Step 2: Fetch relevant chunk IDs for the subtopic and document
        relevant_chunk_ids = self.data_access.find_chunks_excluding_placeholder_content(
            subtopic_id, document_id, statement_type_id
        )

        if not relevant_chunk_ids or len(relevant_chunk_ids) == 0:
            self.logger.debug(
                "No chunks to map for document_id %s and subtopic_id %s",
                document_id,
                subtopic_id,
            )
            return

        # Step 3: Fetch candidate statements once
        candidate_statements = self._fetch_all_candidate_statements(
            relevant_chunk_ids, statement_type_id
        )

        if not candidate_statements:
            self.logger.debug(f"No candidate statements found for subtopic {subtopic_id}")
            return

        # Step 4: Process each unique statement
        for unique_statement in subtopic_unique_statements:
            # Step 5: Process candidate statements in batches and accumulate matches
            accumulated_matches = self._process_in_batches(
                unique_statement.text,
                unique_statement.unique_statement_id,
                candidate_statements,
                batch_size,
            )

            # Step 6: Make the final decision for the unique statement
            self._make_final_decision(
                unique_statement.text, unique_statement.unique_statement_id, accumulated_matches
            )

    @observe(capture_output=False)
    def _fetch_all_candidate_statements(self, relevant_chunk_ids, statement_type_id):
        """
        Fetches all candidate statements for the given chunk IDs and statement type.

        This method retrieves candidate statements that match the provided relevant chunk IDs and
        the specified statement type. It logs the operation and returns the statements in a structured format.

        Args:
            relevant_chunk_ids (list): A list of chunk IDs for which candidate statements are to be fetched.
            statement_type_id (int): The ID of the statement type to filter the candidate statements.

        Returns:
            list: A list of dictionaries where each entry contains 'chunk_id' and 'statement' as keys,
                representing the candidate statement data.
        """
        self.logger.debug(f"Fetching candidate statements for chunk IDs {relevant_chunk_ids}")
        candidate_statements = self.data_access.bulk_get_statements_by_chunks_and_type(
            relevant_chunk_ids, statement_type_id
        )

        return [
            {"chunk_id": statement.chunk_id, "statement": statement.text}
            for statement in candidate_statements
        ]

    @observe(capture_input=False)
    def _process_in_batches(
        self, unique_statement_text, unique_statement_id, candidate_statements, batch_size
    ):
        """
        Processes candidate statements in batches and accumulates matching results.

        This method splits the candidate statements into smaller batches and sends each batch for AI evaluation
        to identify potential matches with the unique statement. Valid matches, consisting of a chunk ID and
        statement, are accumulated across all batches.

        Args:
            unique_statement_text (str): The text of the unique statement to be evaluated.
            unique_statement_id (int): The ID of the unique statement being evaluated.
            candidate_statements (list): A list of candidate statements, where each entry is a dictionary with
                'chunk_id' and 'statement' as keys.
            batch_size (int): The size of each batch for processing.

        Returns:
            list: A list of accumulated matches, each containing a chunk ID and its associated statement.
        """
        accumulated_matches = []

        # Split the candidate statements into batches and process each batch
        for offset in range(0, len(candidate_statements), batch_size):
            batch = candidate_statements[offset : offset + batch_size]

            # Call the AI for each batch
            batch_result = self.text_extractor.identify_statement_chunk_source_candidates(
                unique_statement_text, batch
            )

            # Accumulate the valid results
            if batch_result and isinstance(batch_result, list):
                for result in batch_result:
                    if "chunk_id" in result and "statement" in result:
                        accumulated_matches.append(result)

            self.logger.debug(
                f"Accumulated {len(accumulated_matches)} matches so far for unique statement {unique_statement_id}"
            )

        return accumulated_matches

    @observe()
    def _make_final_decision(self, unique_statement_text, unique_statement_id, accumulated_matches):
        """
        Makes a final decision on the best matching chunk for a unique statement based on accumulated matches.

        This method uses AI to determine the highest probability match from the accumulated matches
        for a unique statement. If a valid match (chunk_id) is found, the unique statement is updated
        with the chunk_id in the database. The process logs the final result or any errors encountered
        during the evaluation.

        Args:
            unique_statement_text (str): The text of the unique statement to be mapped.
            unique_statement_id (int): The ID of the unique statement being evaluated.
            accumulated_matches (list): A list of candidate matches for the unique statement, containing
                chunk IDs and associated statements.

        Returns:
            int or None: Returns the chunk ID if a valid match is found and successfully mapped, otherwise returns None.
        """
        if accumulated_matches:
            final_result = self.text_extractor.identify_highest_probability_statement_chunk_source(
                unique_statement_text, accumulated_matches
            )

            if final_result:
                try:
                    chunk_id = int(final_result)
                    self.content_accessor.update_unique_statement_chunk_id(
                        unique_statement_id, chunk_id
                    )
                    self.logger.debug(
                        "Unique statement: '%s' mapped to final chunk ID %s",
                        unique_statement_text,
                        chunk_id,
                    )
                    return final_result  # only returning for observational purposes.
                except ValueError:
                    self.logger.exception(f"'{final_result}' is not a valid integer.")
            else:
                self.logger.error(
                    "Failed to map unique_statement_id %d to a chunk after final evaluation",
                    unique_statement_id,
                )
        else:
            self.logger.error(
                "Failed to map unique_statement_id %d to a chunk after processing all candidate batches",
                unique_statement_id,
            )
        return None
