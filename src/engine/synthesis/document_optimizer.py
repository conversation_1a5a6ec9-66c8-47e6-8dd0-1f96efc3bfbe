import concurrent.futures
import logging

from langfuse.decorators import observe
from accessor.ai import <PERSON>Accessor
from accessor.content.content_accessor import ContentAccessor
from engine.synthesis.interfaces.i_document_optimizer import IDocumentOptimizer


class DocumentOptimizer(IDocumentOptimizer):

    def __init__(self, logger_name, content_accessor=None):
        self.logger = logging.getLogger(logger_name)
        self.ai_accessor = AiAccessor
        self.content_accessor = content_accessor or ContentAccessor(logger_name)

    def optimize_document(self, document_id: int) -> str:
        chunks = self.content_accessor.get_chunks_by_document(document_id)

        # We'll store (index, future) pairs so we can reconstruct order.
        futures = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            for index, chunk in enumerate(chunks):
                future = executor.submit(self._optimize_text, chunk.text_content)
                futures.append((index, future))

        # Prepare a list for final results in correct order.
        results = [None] * len(chunks)
        for index, future in futures:
            results[index] = future.result()

        return "\n".join(results)

    @observe(as_type="generation")
    def _optimize_text(self, input_text: str) -> str:
        """
        Takes input text and optimizes it using AI.
        
        Args:
            input_text (str): The text to optimize
            
        Returns:
            str: The optimized text
        """
        try:
            # Use Langfuse prompt via get_completion
            changed_text = self.ai_accessor.get_completion(
                prompt_name="optimize_procedure",
                messages=[{"role": "user", "content": input_text}]
            )
            
            return changed_text
            
        except Exception as e:
            self.logger.exception("Error optimizing text: %s", e)
            
            return input_text  # Return original text as last resort
