import logging
from typing import List

from langfuse.decorators import observe

from accessor.document.dto import (
    AcceptanceCriteriaGenerationDTO,
    DocumentJobDTO,
    PolicyRequirementComparisonRequirementDTO,
    TestCaseGenerationDTO,
)
from accessor.reference.dto import (
    OriginalRegulationSectionDTO,
    RegulationSectionComparisonDTO,
)
from engine.synthesis.data_access import DataAccess
from engine.synthesis.document_optimizer import DocumentOptimizer
from engine.synthesis.document_processor import DocumentProcessor
from engine.synthesis.interfaces.i_document_optimizer import IDocumentOptimizer
from engine.synthesis.text_extractor import TextExtractor
from engine.synthesis.unique_statement_to_chunk_mapper import (
    UniqueStatementToChunkMapper,
)
from shared.enums import StatementTypeEnum, JobTypeEnum
from shared.models import Chunk, UniqueStatement
from util.langfuse.decorators import trace_context

from .config import InitServices
from .document_processor import DocumentProcessor


class SynthesisEngine:
    def __init__(self, logger_name):
        self.pinecone_client_init = lambda: InitServices.init_pinecone()  # Pass as a callable
        self.logger = logging.getLogger(logger_name)

        # Instantiate internal components
        self.data_access = DataAccess()
        self.text_extractor = TextExtractor(logger_name)
        self.document_optimizer: IDocumentOptimizer = DocumentOptimizer(logger_name)

        # Instantiate DocumentProcessor with internal dependencies
        self.document_processor = DocumentProcessor(
            self.pinecone_client_init,
            data_access=self.data_access,
            text_extractor=self.text_extractor,
            logger_name=logger_name,
        )

        # Instantiate UniqueStatementToChunkMapper with internal dependencies
        self.unique_statement_mapper = UniqueStatementToChunkMapper(
            data_access=self.data_access, text_extractor=self.text_extractor, logger=self.logger
        )

    def generate_document_statements(self, document_id: int, job_type_id: int):
        min_chunk_id = self.document_processor.data_access.get_min_chunk_id_by_document(document_id)
        max_chunk_id = self.document_processor.data_access.get_max_chunk_id_by_document(document_id)
        business_persona = self.document_processor.data_access.get_business_persona_by_document(
            document_id
        )

        # Check for None values and raise an exception if any are None
        if min_chunk_id is None:
            raise ValueError(f"Minimum chunk ID not found for document_id: {document_id}")
        if max_chunk_id is None:
            raise ValueError(f"Maximum chunk ID not found for document_id: {document_id}")
        if business_persona is None:
            raise ValueError(f"Business persona not found for document_id: {document_id}")

        self.document_processor.generate_document_statements(
            document_id=document_id,
            persona=business_persona,
            start_chunk_id=min_chunk_id,
            end_chunk_id=max_chunk_id,
            job_type_id=job_type_id,
        )

        self.document_processor.generate_document_subtopics(
            document_id=document_id,
            start_chunk_id=min_chunk_id,
            end_chunk_id=max_chunk_id,
        )

    def generate_chunk_statements(self, document_id: int, chunk_id: int, job_type_id: int):
        business_persona = self.document_processor.data_access.get_business_persona_by_document(
            document_id
        )

        if business_persona is None:
            raise ValueError(f"Business persona not found for chunk_id: {chunk_id}")

        self.document_processor.generate_document_statements(
            document_id=document_id,
            persona=business_persona,
            start_chunk_id=chunk_id,
            end_chunk_id=chunk_id,
            job_type_id=job_type_id,
        )

        self.document_processor.generate_document_subtopics(
            document_id=document_id,
            start_chunk_id=chunk_id,
            end_chunk_id=chunk_id,
        )

    def generate_unique_statements(self, dto: DocumentJobDTO):

        if (
            dto.job_type_id == JobTypeEnum.FEDERAL_REGISTER_CHANGE_IDENTIFICATION.value
            or dto.job_type_id == JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value
        ):
            self._generate_unique_statements_from_change_statements(dto.document_id)

        else:
            statement_type_ids = [
                StatementTypeEnum.REQUIREMENT.value,
                StatementTypeEnum.OPPORTUNITY.value,
                StatementTypeEnum.AUTHORIZATION.value,
                StatementTypeEnum.PROCEDURE_CHANGE.value,
                StatementTypeEnum.ORGANIZATIONAL_CHANGE.value,
            ]

            for statement_type_id in statement_type_ids:
                self.document_processor.generate_unique_statements(
                    document_id=dto.document_id,
                    statement_type_id=statement_type_id,
                )

                self.unique_statement_mapper.map_by_document_statement_type(
                    document_id=dto.document_id,
                    statement_type_id=statement_type_id,
                )

    def _generate_unique_statements_from_change_statements(self, document_id: int):

        self.document_processor.generate_unique_statements_for_document_change_statements(
            document_id=document_id,
        )

    def generate_requirements_bundle(
        self,
        document_id: int,
        business_id: int,
        statement_type_id: int,
        subtopic_id: int,
        unique_statements: List[UniqueStatement],
    ):
        self.document_processor.generate_requirements_bundle(
            document_id=document_id,
            business_id=business_id,
            statement_type_id=statement_type_id,
            subtopic_id=subtopic_id,
            unique_statements=unique_statements,
        )

    def generate_user_stories(
        self,
        document_id: int,
        business_id: int,
        statement_type_id: int,
        subtopic_id: int,
        unique_statements: List[UniqueStatement],
    ):
        self.document_processor.generate_user_stories(
            document_id=document_id,
            business_id=business_id,
            statement_type_id=statement_type_id,
            subtopic_id=subtopic_id,
            unique_statements=unique_statements,
        )

    def generate_acceptance_criteria(self, dto: AcceptanceCriteriaGenerationDTO):
        self.document_processor.generate_acceptance_criteria_for_user_story(
            dto,
        )

    def generate_test_cases_for_acceptance_criteria(self, dto: TestCaseGenerationDTO):
        self.document_processor.generate_test_cases_for_acceptance_criteria(
            dto,
        )

    def generate_procedure_changes_for_requirement(
        self, dto: PolicyRequirementComparisonRequirementDTO
    ):
        self.document_processor.generate_procedure_changes_for_requirement(dto)

    def generate_change_statements_for_document(self, dto: RegulationSectionComparisonDTO):
        self.document_processor.generate_change_statements_for_document(dto)

    def generate_deleted_requirements_for_document(
        self,
        document_id: int,
        all_original_requirements: list,
        original_sections: list[OriginalRegulationSectionDTO],
    ):
        self.document_processor.generate_deleted_requirements_for_document(
            document_id, all_original_requirements, original_sections
        )

    def extract_all_original_requirements(self, all_original_requirements_text: str):
        return self.document_processor.extract_all_original_requirements(
            all_original_requirements_text
        )

    def get_chunk_subtopics(self, chunk: Chunk, business_id: int) -> List[str]:
        return self.document_processor.get_chunk_subtopics(chunk, business_id)

    def save_identified_subtopics(self, chunk_id: int, chunk_new_subtopics: list[str]):
        self.document_processor.save_identified_subtopics(chunk_id, chunk_new_subtopics)

    def generate_optimized_text(self, document_id: int) -> str:
        """
        Optimizes the document with the given document ID.

        Parameters:
            document_id (int): The ID of the document to optimize.

        Returns:
            str: The optimized document content.

        Raises:
            ValueError: If document_id is None or not greater than 0.
        """
        if document_id is None or document_id <= 0:
            raise ValueError("document_id must be a positive integer.")

        return self.document_optimizer.optimize_document(document_id)
