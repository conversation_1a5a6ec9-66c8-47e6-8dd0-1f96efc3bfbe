"""
This module defines the prompt templates used in text_extractor.
"""

class FallbackPrompts:
    """
    This class provides fallback prompt templates for the text_extractor file.
    This template is used in case of a Langfuse outage.
    """

    @staticmethod
    def update_user_stories_with_personas(user_story: str, available_personas: str) -> str:
        return f"""
    You are an expert in assigning job roles based on responsibilities. Your task is to update the UserStory by assigning the most relevant persona based on the user story’s context.
    The original UserStory follows this structure: **'As a <who>, I must <what>, so that <why>.'**

    ### Your Task
    1. Analyze the persona names and descriptions from the **AvailablePersonas** list.
    2. Select the **best matching persona name** from the provided list.
    3. Replace **<who>** in the provided user story with the **best matching persona** from the `AvailablePersonas` list. Do **not** change the `<what>` or `<why>` sections of the user story.
    4. If no persona is an exact match, select the **closest possible persona** based on responsibilities.

    ### Grammar & Wording Rules
    - Use **'As an'** if the selected persona starts with a vowel sound (A, E, I, O, U).
    - Otherwise, use **'As a'**.
    - Capitalization and spacing must match natural English grammar.

    ### Output Rules (STRICT)
    - **Only output the updated user story.**
    - **Do NOT** include any labels like `UserStory:` or `User Story:` or any explanation.
    - **Do NOT** include extra text, notes, line numbers, or markdown formatting.
    - Keep the full original structure: 'As a <persona>, I must <what>, so that <why>.' with only the `<persona>` replaced.

    **REMEMBER:**  
    - Replace only the persona.  
    - Ensure correct usage of “a” vs “an.”  
    - Return ONLY the updated user story. No labels. No extras.

    UserStory: {user_story}
    AvailablePersonas: {available_personas}
    """

    @staticmethod
    def create_ac_for_user_story():
        """
        Returns the system prompt template for creating acceptance criteria based on
        a user story and requirement.
        """
        return (
            "Given a user story and a requirement, generate a comprehensive set of acceptance criteria "
            "for the user story that aligns with the requirement. The acceptance criteria should be "
            "clear, concise, testable, and verifiable, and should directly support the purpose of "
            "the user story. Use the provided helpful context to deepen your understanding of the "
            "user story and requirement before generating acceptance criteria."
            "\n\n"
            "Guidelines: "
            "1. Each acceptance criterion must describe a specific, verifiable condition. "
            "2. Generate between 5 and 15 acceptance criteria that fully cover the user story and its requirement. "
            "3. Format the output as a JSON structure, using the following template:\n"
            """{
            "acceptanceCriteria": [
                {"id": "AC001","description": "Acceptance criterion description."},
                {"id": "AC002","description": "Acceptance criterion description."},
                {"id": "AC003","description": "Acceptance criterion description."},
                {"id": "AC004","description": "Acceptance criterion description."},
                {"id": "AC005","description": "Acceptance criterion description."},
                {"id": "AC006","description": "Acceptance criterion description."},
                {"id": "AC007","description": "Acceptance criterion description."}
            ]
            }"""
        )

    @staticmethod
    def create_tc_for_ac():
        """
        Returns the system prompt template for creating test cases based on an acceptance criterion and requirement.
        """
        return (
            "Given an acceptance criterion and a requirement, generate a comprehensive set of test cases "
            "for the acceptance criteria that aligns with the requirement. The test cases should be "
            "clear, concise, testable, and verifiable, and should directly support the purpose of "
            "the acceptance criteria. Use the provided helpful context to deepen your understanding of the "
            "acceptance criteria and requirement before generating test cases."
            "\n\n"
            "Guidelines: "
            "1. Each test case must describe a specific, verifiable condition. "
            "2. Generate between 5 and 15 test cases that fully cover the acceptance criteria and its requirement. "
            "3. Format the output as a JSON structure, using the following template:\n"
            """{
            "testCases": [
                {"id": "TC001","description": "Test case description."},
                {"id": "TC002","description": "Test case description."},
                {"id": "TC003","description": "Test case description."},
                {"id": "TC004","description": "Test case description."},
                {"id": "TC005","description": "Test case description."},
                {"id": "TC006","description": "Test case description."},
                {"id": "TC007","description": "Test case description."}
            ]
            }"""
        )


    @staticmethod
    def create_user_stories_for_requirement(unique_statement_text: str, persona: str) -> str:
        return f"""
    Output an array of values with each value being a user story (in the style of 'As a <who>, 
    I must <what>, so that <why>'). 

    Ensure that:
    1. Each user story explicitly begins with 'As a' followed by the Persona provided in the user prompt.
    2. Only identify user stories where the Persona is the 'who.'
    3. Output only the array and nothing else.
    4. Array values must be delimited by '|||' and string values must not be enclosed in quotes.
    5. The first character of the response must be '[' and the last character must be ']'.

    Strictly adhere to these formatting requirements.

    Requirement: {unique_statement_text}
    Persona: {persona}

    Ensure that each user story:
    1. Begins with 'As a {persona}, I must...'
    2. Strictly follows the format 'As a <who>, I must <what>, so that <why>.'
    3. Is valid only if the Persona matches '{persona}'.
    """

    @staticmethod
    def extract_subtopics(node_text: str, subtopics: dict) -> str:
        return f"""
        Your task is to evaluate each topic in the CanonicalTopics list provided in the user prompt 
        (each canonical topic in the list is composed of the TopicName :: TopicDefinition) and 
        determine whether that canonical topic is substantially discussed in the TextToEvaluate 
        (which is also provided in the user prompt). Primarily use the TopicDefinition to determine 
        whether that canonical topic is substantially discussed in the TextToEvaluate. If the 
        canonical topic is substantially discussed in the TextToEvaluate. Add the TopicName ONLY 
        as an item to the output array. If the canonical topic is not substantially discussed in the 
        TextToEvaluate, move on to the next canonical topic to evaluate. Pay no attention to any 
        other topics that might be discussed in the TextToEvaluate. Your job is not to identify all 
        the topics discussed in the TextToEvaluate, but rather to identify which of the CanonicalTopics 
        are discussed in the TextToEvaluate. VERY IMPORTANT! Your response must exclusively consist 
        of topic names from the CanonicalTopic list. You will fail at your job if you identify and 
        output any topic names that are not from the CanonicalTopic list. If no CanonicalTopics are 
        substantially discussed, output an array with one value: 'No relevant topics'. Ensure that 
        the values in the output array are delimited by '|||' and that string values are not enclosed 
        in quotes. Use only '|||' to delimit values. Do not use new lines to help with delimiting the 
        array values. Accordingly, the first character of the response must be '[' and the last 
        character of the response must be ']'. The response should strictly follow the format 
        guidelines and include no extraneous content or formatting.
        
        TextToEvaluate: {node_text}
        CanonicalTopics: {subtopics}
        """

    @staticmethod
    def reevaluate_subtopic(original_subtopic: str, node_text: str, subtopics: dict[list]) -> str:
        return f"""
        Your task is to compare the Original_Subtopic with the list of CanonicalTopics (each composed 
        of TopicName :: TopicDefinition) provided in the user prompt and determine if there is a better 
        match from the CanonicalTopics based on the TextToEvaluate. Primarily use the TopicDefinition 
        to make this determination. If you find a CanonicalTopic that is a better match than the 
        Original_Subtopic, output it as the only item in the output array. If no CanonicalTopic is a 
        better match than the Original_Subtopic, output an array with one value: 'No better match'. 
        VERY IMPORTANT! Your response must exclusively consist of topic names from the CanonicalTopics 
        list. You will fail at your job if you identify and output any topic names that are not from 
        the CanonicalTopics list. Ensure that the values in the output array are delimited by '|||' 
        and that string values are not enclosed in quotes. Use only '|||' to delimit values. Do not 
        use new lines to help with delimiting the array values. Accordingly, the first character of 
        the response must be '[' and the last character of the response must be ']'. The response 
        should strictly follow the format guidelines and include no extraneous content or formatting.
        
        OriginalSubtopic: {original_subtopic}
        TextToEvaluate: {node_text}
        CanonicalTopics: {subtopics}
        """

    @staticmethod
    def extract_topics(node_text: str, topics: dict[list]) -> str:
        return f"""
        Your task is to evaluate each topic in the CanonicalTopics list provided in the user prompt 
        (each canonical topic in the list is composed of the TopicName :: TopicDefinition) and 
        determine whether that canonical topic is substantially discussed in the TextToEvaluate 
        (which is also provided in the user prompt). Primarily use the TopicDefinition to determine 
        whether that canonical topic is substantially discussed in the TextToEvaluate. If the 
        canonical topic is substantially discussed in the TextToEvaluate, add the TopicName ONLY as 
        an item to the output array. If the canonical topic is not substantially discussed in the 
        TextToEvaluate, move on to the next canonical topic to evaluate. Pay no attention to any 
        other topics that might be discussed in the TextToEvaluate. Your job is not to identify all 
        the topics discussed in the TextToEvaluate, but rather to identify which of the CanonicalTopics 
        are discussed in the TextToEvaluate. VERY IMPORTANT! Your response must exclusively consist 
        of topic names from the CanonicalTopic list. You will fail at your job if you identify and 
        output any topic names that are not from the CanonicalTopic list. If no CanonicalTopics are 
        substantially discussed, output an array with one value: 'No relevant topics'. Ensure that 
        the values in the output array are delimited by '|||' and that string values are not 
        enclosed in quotes. Use only '|||' to delimit values. Do not use new lines to help with delimiting 
        the array values. Accordingly, the first character of the response must be '[' and the last 
        character of the response must be ']'. The response should strictly follow the format 
        guidelines and include no extraneous content or formatting.

        TextToEvaluate: {node_text}
        CanonicalTopics: {topics}
        """

    @staticmethod
    def is_statement_relevant_to_chunk_topics(statement_text: str, topic_list: list) -> str:
        return f"""
        Your task is to determine if the StatementText is highly relevant to any of the topics 
        (each composed of TopicName :: TopicDefinition) from the TopicList. Both the TopicList and 
        the StatementText are provided in the user prompt. If the StatementText is relevant to any 
        topics in the TopicList, respond with the list of relevant TopicNames only. For each relevant 
        topic, explain why the StatementText is highly relevant to it. If the StatementText is not 
        relevant to any of the topics in the TopicList, your response must be exactly 'Not_Relevant'. 
        Add no other words to the response. The response should strictly follow the format guidelines 
        and include no extraneous content or formatting.

        StatementText: {statement_text}
        TopicList: {topic_list}
        """

    @staticmethod
    def identify_best_topic_for_orphan_statement(
        statement_text: str, chunk_text: str, chunk_header: str, subject_matter: str
    ) -> str:
        return f"""
        The StatementText is present in the ChunkText. Your task is to determine what topic in the 
        SubjectMatter, the StatementText most pertains to. Use both the StatementText and its 
        surrounding context (ChunkText and its header ChunkHeader) to help you make this 
        determination. StatementText, ChunkText, ChunkHeader, and SubjectMatter are all provided 
        in the user prompt. Output the best fit topic name and a brief description in the format 
        TopicName - TopicBriefDescription. Provide nothing else in the output.

        StatementText: {statement_text}
        ChunkText: {chunk_text}
        ChunkHeader: {chunk_header}
        SubjectMatter: {subject_matter}
        """

    @staticmethod
    def identify_statement_chunk_source_candidates(
        statement_to_evaluate: str, candidate_statements: list
    ) -> str:
        return f"""
        Your task is to compare the StatementToEvaluate with the list of CandidateStatements (each 
        composed of a chunk id :: statement text) provided in the user prompt. Determine if one or 
        more statements in the list of candidate statements are high probability matches with the 
        StatementToEvaluate based on meaning. If high probability matches are found, respond with 
        the chunk id and the corresponding statement(s) in valid JSON format.

        The JSON structure should be: 
        ["chunk_id": "<chunk_id>", "statement": "<candidate_statement>", ...].

        If no high probability matches are found, respond with an empty JSON object. 
        Your response must be valid JSON and contain no extra words or information. 
        Ensure the response doesn't start with json```

        StatementToEvaluate: {statement_to_evaluate}
        CandidateStatements: {candidate_statements}
        """

    @staticmethod
    def identify_highest_probability_statement_chunk_source(
        statement_to_evaluate: str, candidate_statements: list
    ) -> str:
        return f"""
        Your task is to compare the StatementToEvaluate with the list of CandidateStatements (each 
        composed of a chunk id :: statement text) provided in the user prompt. Determine which single 
        statement in the list of candidate statements is the highest probability match based on meaning 
        with the StatementToEvaluate. Respond with only the chunk id of the highest probability match 
        as plain text. If no high probability match is found, respond with 'None'. Your response must 
        contain no extra words or information.

        StatementToEvaluate: {statement_to_evaluate}
        CandidateStatements: {candidate_statements}
        """

    @staticmethod
    def step1_extract_statement_type_info(
        node_text: str, chunk_header: str, persona: str, unprocessed_types: list
    ) -> str:
        return f"""
        The TextToEvaluate in the user prompt is text from a source document in a section with the header 
        SectionHeader. Depending on the StatementType provided, evaluate the text using the following guidelines:

        Key Definitions:
        - Requirement: A mandatory behavior explicitly or implicitly required from the Persona, or an action 
          explicitly disallowed unless certain conditions are met.
        - Authorization: Purely optional actions explicitly permitted for the Persona (e.g., phrases like "may," 
          "can," or "is authorized to"). Excludes mandatory or encouraged actions.
        - Opportunity: Actions that are both authorized AND explicitly encouraged (e.g., phrases like "encouraged" 
          or "could benefit from"). Includes contextual or conditional details.
        - Procedure Change: Modifications to an existing method, procedure, or workflow (e.g., new steps, removed steps, 
          reordered steps, or new conditions).
        - Organizational Change: Modifications to the structure, roles, responsibilities, or governance of an 
          organization (e.g., reporting structures, team creation/elimination, role modifications).
        - Definition: Statements that explain the meaning, scope, or specific attributes of a term, concept, or phrase.

        Guidelines:
        1. Evaluate the TextToEvaluate based on the StatementType provided in the user prompt.
        2. Extract relevant information based on the rules for each StatementType:
          - Requirements: Identify behaviors required or explicitly disallowed and any conditions.
          - Authorizations: Identify optional actions permitted but not required or encouraged.
          - Opportunities: Extract actions both authorized and explicitly encouraged.
          - Procedure Changes: Extract modifications to procedures.
          - Organizational Changes: Extract modifications to organizational structure, roles, or governance.
          - Definitions: Extract explanations of terms, concepts, or phrases.
        3. Exclude duplicate or highly similar statements, and consolidate overlapping meanings where applicable.

        Output Format:
        The response must always adhere to this JSON format:
        {{
          "Statements": [
            {{
              "Statement_Text": "<Full text of the analyzed statement>",
              "<KeyField1>": "<Value1>",
              "<KeyField2>": "<Value2>",
              "<KeyField3>": "<Value3>"
            }},
            ...
          ]
        }}

        (Statement type-specific examples follow here as detailed...)

        Instructions:
        1. Replace <KeyField1>, <KeyField2>, etc., with fields relevant to the StatementType.
        2. Consolidate semantically similar statements to reduce redundancy.
        3. Ensure clarity, completeness, and contextual relevance for the specified StatementType.

        TextToEvaluate: {node_text}
        SectionHeader: {chunk_header}
        Persona: {persona}
        StatementTypes: {unprocessed_types}
        """

    @staticmethod
    def step2_filter_statements_for_persona(
        requirements: dict, authorizations: dict, opportunities: dict, persona: str
    ) -> str:
        return f"""
        The statements provided are collections of requirements, authorizations, 
        and opportunities, along with their associated analyses. Each statement type 
        includes specific attributes to filter based on the Persona: {persona}.

        Your task:
        1. **Requirements**:
           - Remove statements where:
             - The required behavior is attributed to an entity other than the Persona.
             - The required behavior depends on content from a table or grid that is not 
               included in the `Statement_Text`.
        2. **Authorizations**:
           - Remove statements where:
             - The authorized actions pertain to an entity other than the Persona.
             - The authorization depends on content from a table or grid that is not 
               included in the `Statement_Text`.
        3. **Opportunities**:
           - Remove statements where:
             - The opportunity pertains to an entity other than the Persona.
             - The opportunity depends on content from a table or grid that is not 
               included in the `Statement_Text`.

        Retain the original structure and formatting of the JSON, excluding the removed statements.
        Format the output as a JSON object with keys for each statement type (`requirements`, 
        `authorizations`, `opportunities`).

        Requirements: {requirements}
        Authorizations: {authorizations}
        Opportunities: {opportunities}
        Persona: {persona}
        """

    @staticmethod
    def step3_table_context_statements_for_persona(
        requirements, authorizations, opportunities, persona, chunk_text
    ) -> str:
        return f"""
        The statements provided include collections of requirements, authorizations, 
        and opportunities, along with their respective analyses. Each statement includes 
        attributes such as the statement text, associated actions or behaviors, stakeholders, 
        and conditions. Use the `TextPassage` provided to add additional context.

        Your task:
        1. **Requirements**:
           - Recapture the `Statement_Text` for any requirement statement present in a table within `TextPassage`.
           - Incorporate additional context from table column headers or the sentence introducing the table.
        2. **Authorizations**:
           - Recapture the `Statement_Text` for any authorization statement present in a table within `TextPassage`.
           - Incorporate additional context from table column headers or the sentence introducing the table.
        3. **Opportunities**:
           - Recapture the `Statement_Text` for any opportunity statement present in a table within `TextPassage`.
           - Incorporate additional context from table column headers or the sentence introducing the table.

        Retain the original JSON structure, updating only the `Statement_Text` values with additional context.

        Requirements: {requirements}
        Authorizations: {authorizations}
        Opportunities: {opportunities}
        Persona: {persona}
        TextPassage: {chunk_text}
        """

    @staticmethod
    def step4_pivot_statements_for_persona(
        requirements, authorizations, opportunities, persona
    ) -> str:
        return f"""
        The statements provided include requirements, authorizations, and opportunities, along with their respective analyses. 
        Each statement includes attributes such as the statement text, required behaviors, authorized actions, or encouraged actions, 
        as well as conditions from the section header.

        Your task:
        1. **Requirements**:
           - Recraft the `Statement_Text` to explicitly incorporate the `Behavior_Required` field for any statements where the Persona's behavior is required.
           - Ensure the revised text clearly and concisely states the required behavior from the Persona while maintaining the original meaning.
        2. **Authorizations**:
           - Recraft the `Statement_Text` to explicitly and clearly specify the `Authorized_Actions` for any statements where the Persona's actions are authorized.
           - Include any relevant context or conditions from the `Authorized_Actions` and `Conditions_From_Header` fields in the recrafted text.
           - Ensure the revised text fully reflects the authorized, but not required or encouraged, actions for the Persona.
        3. **Opportunities**:
           - Recraft the `Statement_Text` to explicitly state the `Potential_Benefit` to the Persona for any statements where the Persona is the entity for whom the action is encouraged.
           - Ensure the revised text maintains logical flow and fully reflects the value or advantage to the Persona.

        Output the statements in the exact same JSON format, grouped by type (`requirements`, `authorizations`, `opportunities`).

        Requirements: {requirements}
        Authorizations: {authorizations}
        Opportunities: {opportunities}
        Persona: {persona}
        """

    @staticmethod
    def step5_recraft_statement_type_conditions(
        node_text: str, chunk_header: str, statement: str
    ) -> str:
        return f"""
        The ChunkText in the user prompt is a section of text from a source document in a section with the header SectionHeader, also provided in the user prompt.
        The IdentifiedStatement is a statement identified from the ChunkText, along with analysis about that statement.

        Your task:
        1. For **Requirements**:
           - Recraft the `IdentifiedStatement` to incorporate any conditions from the ChunkHeader, ensuring the recrafted statement integrates these conditions seamlessly into its logical flow.
           - If no additional conditions apply, return the `IdentifiedStatement` verbatim.
        2. For **Authorizations**:
           - Recraft the `IdentifiedStatement` to incorporate any conditions from the ChunkHeader, ensuring the conditions are seamlessly integrated into the logical flow of the statement.
           - If no additional conditions apply, return the `IdentifiedStatement` verbatim.
        3. For **Opportunities**:
           - Recraft the `IdentifiedStatement` to incorporate any conditions from the ChunkHeader.
           - If no additional conditions apply, return the `IdentifiedStatement` verbatim.

        Output only the recrafted statement text. Do not preface the output with any additional text.

        ChunkText: {node_text}
        ChunkHeader: {chunk_header}
        IdentifiedStatement: {statement}
        """

    @staticmethod
    def step6_extended_statement_type_recrafting(
        statement_type: str, statement: str, persona: str
    ) -> str:
        return f"""
        The IdentifiedStatement provided in the user prompt is a statement along with its context. Your task depends on the statement type:
        1. **Requirements**:
           - If the IdentifiedStatement states that a persona is NOT REQUIRED to do X so long as they do Y, recraft it to read as 
             persona is required to do Y if they choose not to do X.
           - If the IdentifiedStatement does not explicitly state this, output it verbatim.
        2. **Authorizations**:
           - If the IdentifiedStatement states that a persona is NOT AUTHORIZED to do X so long as they do Y, recraft it to read as 
             persona is authorized to do Y if they choose not to do X.
           - If the IdentifiedStatement does not explicitly state this, output it verbatim.
        3. **Opportunities**:
           - If the IdentifiedStatement suggests opportunities for improvement, growth, or strategic actions, recraft it to clearly state the 
             potential benefit or advantage of pursuing this opportunity, even if implied.
           - If the opportunity depends on specific conditions or resources (e.g., funding or situational factors), include these conditions 
             to clarify when or how the opportunity could be pursued.
           - If no recrafting is necessary, output the IdentifiedStatement verbatim.
        Output only the text of the recrafted statement or the original IdentifiedStatement.
        Do not preface that text with anything (e.g., "Recrafted Statement:").

        StatementType: {statement_type}
        IdentifiedStatement: {statement}
        Persona: {persona}
        """

    @staticmethod
    def step7_scan_for_statement_types(statements: list) -> str:
        return f"""
        For the statements provided in the user prompt, determine whether any of them are actually valid statements based on the specified statement type:

        1. For **Requirements**:
           - Determine if the provided statements contain any required behaviors.
           - Output Yes if there are statements of required behavior; otherwise, output No.

        2. For **Authorizations**:
           - Determine if the provided statements contain any authorized, but not required or encouraged, behaviors.
           - Output Yes if there are statements of authorization; otherwise, output No.

        3. For **Opportunities**:
           - Determine if the provided statements contain any encouraged, but not required, behaviors or opportunities.
           - Output Yes if there are statements of opportunity; otherwise, output No.

        Your answer must strictly be Yes or No with no additional words.

        Statements: {statements}
        """

    @staticmethod
    def scan_for_requirements(requirements: str) -> str:
        return f"""
        For the Requirements provided in the user prompt, determine whether any of them are 
        actually statements of required behavior.

        Output Yes if any required behaviors are found. Output No if none are found. 

        Your answer must strictly be Yes or No with no additional text.

        Requirements: {requirements}
        """

    @staticmethod
    def assemble_requirements(
        node_text: str,
        requirement_statements: list,
        existing_requirements: list,
        subtopic: list,
    ) -> str:
        return f"""
        The ChunkRequirements passed in the user prompt are the set of requirements present in the 
        SourceChunk, which is also provided in the user prompt.

        Your task is to identify any requirements within ChunkRequirements that are BOTH:
        1. Highly relevant to the Subtopic provided.
        2. Not already present (or semantically similar) in the ExistingRequirements.

        Guidelines:
        - If ExistingRequirements is empty, return all requirement statements highly relevant to the Subtopic.
        - If no new requirements meet both criteria above, return an empty array.
        - DO NOT return messages like "No new requirements statements relevant to the subtopic found."
        - SourceChunk is provided only to help determine semantic similarity. Do not use it otherwise.

        Output Format:
        - Return the list of new, relevant requirement statements as an array.
        - Delimit each statement with '|||'.
        - Do not enclose the string values in quotes.
        - The first character must be '[' and the last character must be ']'.
        - Do not use new lines for formatting or delimiting.

        Strictly follow this formatting requirement.

        SourceChunk: {node_text}
        ChunkRequirements: {requirement_statements}
        ExistingRequirements: {existing_requirements}
        Subtopic: {subtopic}
        """
    
    @staticmethod
    def assemble_requirements_from_change_statements(
        chunk_text: str,
        change_statements: list,
        chunk_subtopics: str,
    ) -> str:
        return f"""
        You are a technical writer tasked with writing software requirements from a given set of change statements that identify specific changes made to a mortgage servicing regulatory update.

        You will also be provided with "chunk_text" which contains the full text of the new regulatory rule, and a list of subtopics ("chunk_subtopics").

        Each change statement includes:
        - "original_text": The previous version of the rule.
        - "updated_text": The current version of the rule.
        - "changed_text": The specific verbiage updated.
        - "change_statement": A simplified summary of the change.
        - "identifier": The section of the rule being discussed.
        - "impact": The impact of the change.

        Your tasks:
        1. Craft a requirement specifying what the mortgage servicing system must do to comply with the change.
        2. Skip items where `changed_text` is "No changes detected" or `impact` is "None".
        3. Ensure each requirement:
           - Begins with "The mortgage servicing system must..."
           - Is concise, clear, and actionable.
           - Reflects only the relevant updates from `changed_text` or `change_statement`.

        Subtopic Association:
        - Each requirement must be linked to at least one subtopic from the list provided in chunk_subtopics.
        - You may associate multiple subtopics, but only one may have `"is_primary": true`.

        Output Format:
        - Return an array of objects with:
          - `"requirement"`: The full requirement statement.
          - `"statement_id"`: The statement's ID.
          - `"subtopics"`: A list of associated subtopics with `"subtopic"` and `"is_primary"` fields.

        Example Output:
        [
          {{
            "requirement": "The servicer must verify that full payment, including all fees, has been received before satisfying a borrower's account.",
            "statement_id": 3433,
            "subtopics": [
              {{ "subtopic": "Borrower Maintenance", "is_primary": true }},
              {{ "subtopic": "Modified Terms", "is_primary": false }},
              {{ "subtopic": "Funds Allocation", "is_primary": false }}
            ]
          }}
        ]

        change_statements: {change_statements}
        chunk_text: {chunk_text}
        chunk_subtopics: {chunk_subtopics}
        """

    @staticmethod
    def scan_for_authorizations(authorizations: str) -> str:
        return f"""
        Determine whether the provided Authorizations include any statements of optional actions 
        (i.e., actions that are authorized but not required or encouraged).

        Output "Yes" if any such statements are present. Output "No" otherwise.

        Your response must be either "Yes" or "No" with no additional words.

        Authorizations: {authorizations}
        """

    @staticmethod
    def assemble_authorizations(
        node_text: str,
        authorization_statements: list,
        existing_authorizations: list,
        subtopic: str,
    ) -> str:
        return f"""
        The ChunkAuthorizations provided are a collection of authorization statements from the SourceChunk.

        Your task is to identify any authorizations that are BOTH:
        1. Highly relevant to the Subtopic.
        2. Not already present (or not semantically similar) to any in ExistingAuthorizations.

        Guidelines:
        - If ExistingAuthorizations is empty, return all authorizations relevant to the subtopic.
        - If no new relevant authorizations are found, return an empty array.
        - DO NOT include text like 'No new authorization statements...'.
        - Use SourceChunk ONLY for context when comparing authorizations—do not output it.

        Output:
        - Output an array of new, relevant authorization statements only.
        - Delimit values using '|||'.
        - Do not quote the string values or use new lines.
        - First character of the response must be '[' and the last must be ']'.

        Strictly follow these output requirements.

        SourceChunk: {node_text}
        ChunkAuthorizations: {authorization_statements}
        ExistingAuthorizations: {existing_authorizations}
        Subtopic: {subtopic}
        """

    @staticmethod
    def scan_for_opportunities(opportunities: str) -> str:
        return f"""
        For the Opportunities provided, determine whether any of them are actual statements of encouraged behavior 
        or opportunities.

        Output 'Yes' if any exist, or 'No' if none do.

        Your answer must be exactly one word: Yes or No.

        Opportunities: {opportunities}
        """

    @staticmethod
    def assemble_opportunities(
        node_text: str,
        opportunity_statements: list,
        existing_opportunities: list,
        subtopic: list,
    ) -> str:
        return f"""
        The ChunkOpportunities represent encouraged actions from the SourceChunk provided.

        Your task is to identify opportunities that are BOTH:
        1. Highly relevant to the given Subtopic.
        2. Not already present in the ExistingOpportunities list (or semantically similar to any existing ones).

        Guidelines:
        - If ExistingOpportunities is empty, return all relevant ChunkOpportunities.
        - If no new opportunities are found, return an empty array (just '[]').
        - Do NOT return any explanatory text.
        - Use SourceChunk only as contextual aid when comparing.

        Output:
        - An array of opportunity statements.
        - Delimit values using '|||'.
        - Do not quote the values or use new lines.
        - Start with '[' and end with ']'.

        SourceChunk: {node_text}
        ChunkOpportunities: {opportunity_statements}
        ExistingOpportunities: {existing_opportunities}
        Subtopic: {subtopic}
        """

    @staticmethod
    def extract_procedure_changes(node_text: str) -> str:
        return f"""
        The ChunkProcedures contain potential procedure change statements from a source document.

        Definitions:
        - Procedure Change: A change to procedures, workflows, steps, or operations—often involving new methods, 
          removed or reordered steps, or newly introduced conditions or tools.

        Indicators include: "added", "updated", "clarified", "procedure", "workflow", "effective immediately", etc.

        Your task is to identify procedure changes in ChunkProcesses that:
        1. Are highly relevant to the Subtopic provided.
        2. Are not already present (or semantically similar) in ExistingProcedures.

        Guidelines:
        - If ExistingProcedures is empty, return all relevant procedure changes.
        - If none meet criteria, return only an empty array: '[]'.
        - Use SourceChunk only as context—do not include or describe it.

        Output:
        - An array of procedure change statements.
        - Use '|||' as the delimiter.
        - No quotes around strings.
        - Do not include new lines.
        - Output must begin with '[' and end with ']'.

        TextToEvaluate: {node_text}
        """

    @staticmethod
    def assemble_procedure_changes(
        node_text: str,
        procedure_change_statements: list,
        existing_procedure_changes: list,
        subtopic: str,
    ) -> str:
        return f"""
        The ChunkProcedures passed in the user prompt are the set of procedure changes present in the 
        SourceChunk which is also provided in the user prompt.

        Your task is to identify any procedure changes within ChunkProcedures that are BOTH:
        1. Highly relevant to the Subtopic provided.
        2. Not already present (or semantically similar) in the ExistingProcedures.

        Guidelines:
        - If ExistingProcedures is empty, return all relevant procedure change statements.
        - If no new procedure changes are found, return an empty array (just '[]').
        - Use SourceChunk for context only.

        Output:
        - An array of new, relevant procedure changes.
        - Use '|||' to delimit values.
        - Do not use quotes or new lines.
        - Output must start with '[' and end with ']'.

        SourceChunk: {node_text}
        ChunkProcedures: {procedure_change_statements}
        ExistingProcedures: {existing_procedure_changes}
        Subtopic: {subtopic}
        """

    @staticmethod
    def extract_organizational_changes(node_text: str) -> str:
        return f"""
        The ChunkOrganizationalChanges contains possible statements of organizational change from the 
        SourceChunk provided in the prompt.

        **Organizational Changes** include: changes to structure, roles, teams, reporting, leadership, etc.

        Focus on keywords like:
        - Structural: "reorganized", "merged", "split"
        - Roles/Teams: "role eliminated", "team formed", "reassigned"
        - Leadership: "appointed", "terminated"
        - Departments: "created", "relocated"
        - Policies: "reporting structure"

        Your task is to extract organizational change statements that:
        1. Are highly relevant to the Subtopic.
        2. Are not already present in ExistingOrganizationalChanges.

        Guidelines:
        - Return all relevant changes if ExistingOrganizationalChanges is empty.
        - If no new changes are found, return '[]' only.
        - Use SourceChunk only for context.

        Output:
        - An array of statements delimited by '|||'.
        - Do not use quotes or new lines.
        - Output must start with '[' and end with ']'.

        TextToEvaluate: {node_text}
        """

    @staticmethod
    def assemble_organizational_changes(
        node_text: str,
        organizational_change_statements: list,
        existing_organizational_changes: list,
        subtopic: list,
    ) -> str:
        return f"""
        The ChunkOrganizationalChanges represent changes from the SourceChunk provided in the prompt.

        Your task:
        - Identify organizational change statements that are:
          1. Highly relevant to the Subtopic.
          2. Not present in ExistingOrganizationalChanges (or semantically similar).

        Guidelines:
        - Return all relevant changes if ExistingOrganizationalChanges is empty.
        - Return only an empty array '[]' if none qualify.
        - Use SourceChunk for comparison context only.

        Output:
        - A list of new statements, delimited by '|||'.
        - Do not use quotes or new lines.
        - Start with '[' and end with ']'.

        SourceChunk: {node_text}
        ChunkOrganizationalChanges: {organizational_change_statements}
        ExistingOrganizationalChange: {existing_organizational_changes}
        Subtopic: {subtopic}
        """
    
    @staticmethod
    def extract_definitions(node_text: str) -> str:
        return f"""
        The TextToEvaluate contains statements that may define terms, concepts, or processes.

        **Definition**: An explicit explanation of a term’s meaning—look for:
        - Phrases like: "is defined as", "means", "refers to", "is used to describe"
        - Introductions like: "<Term> is...", "<Concept> means..."

        Your task:
        - Extract complete, explicit definitions with full context.
        - Include only information present in the text—do not infer.
        - If none found, return '[]' only.

        Output:
        - Use the format: 'Term - Definition'
        - Delimit values with '|||'
        - Do not quote values or use new lines
        - Start and end with '[' and ']'

        Example:
        [Procedure Change - A modification of a procedure.|||Role - A specific job function within an organization.]

        TextToEvaluate: {node_text}
        """

    @staticmethod
    def extract_change_statements(node_text: str) -> str:
        return (
            f"You are a compliance officer analyzing a federal register rule change. Your task is to compare "
            f"each line of the new rule to the full previous version and identify what has changed.\n\n"
            f"For each line of the new rule:\n"
            f"- Extract the original sentence (if present).\n"
            f"- Extract the updated sentence.\n"
            f"- Provide the section identifier.\n"
            f"- Describe the change (changed_text).\n"
            f"- Provide a plain-language summary (change_statement).\n"
            f"- Classify the impact: High, Medium, Low, or None.\n"
            f"If no changes, return 'No changes detected.'\n\n"
            f"Format the result as JSON (no markdown).\n"
            f"Example:\n"
            f"[{{\n"
            f'  "original_text": "Old version...",\n'
            f'  "updated_text": "New version...",\n'
            f'  "identifier": "<89 FR 66189><3555><303><(c)(1)>",\n'
            f'  "changed_text": "Added clarification about fees",\n'
            f'  "change_statement": "Clarifies that fees must be included.",\n'
            f'  "impact": "Medium"\n'
            f"}}]\n\n"
            f"TextToEvaluate: {node_text}"
        )

    @staticmethod
    def extract_original_requirements(target_section_text: str):
        return (
            """
            Role & Task
            You are an expert legal analyst. Your task is to break down legal text into structured segments, preserving the
            hierarchical structure and ensuring each segment is self-contained and logically complete.

            Each segment must:
            - Start with its corresponding identifier (e.g., "(a)") exactly as it appears in the source.
            - Be split at every new identifier level, including deeply nested levels.
            - Maintain the full meaning of each clause without duplicating content.

            Segmentation Rules
            1. General Formatting
            - Preserve all numbering and identifiers exactly as they appear.
            - Section headings (e.g., "§ 1234.567 Hello world.") must be included as their own segments.
            - Remove all embedded HTML tags from the text.
            - Exclude any segments that contain "XREF" or "CITA" in the identifier.
            - Join any lines broken by line breaks unless a new identifier begins.
            - The identifier must always appear at the beginning of the segment's text field.

            2. Parent and Child Segments
            - If a parent identifier (e.g., "(a)") is followed immediately by a child identifier (e.g., "(1)"), the parent 
              segment must include only the heading and any text that appears before the first child identifier.
            - The child identifier and all of its associated text must be extracted into a separate segment.
            - If the parent has no standalone text before the child identifier, it should still be included as its own 
              segment with just the heading.
            - PARENT SEGMENTS MUST NEVER INCLUDE ANY TEXT FROM CHILD IDENTIFIERS.

            3. Nested Segments
            - Every level of nesting must be separated into its own segment.
            - Parent segments must never include the text of child segments.
            - Each of the following must appear as its own segment:
              - Lettered identifiers: (a), (b), (c)
              - Numbered identifiers: (1), (2), (3)
              - Lowercase roman numerals: (i), (ii), (iii)
              - Uppercase letters: (A), (B), (C)

            4. Introductory Phrases and Lists
            - If a segment contains an introductory phrase followed by a list of sub-items, the introductory phrase 
              must be in its own segment.
            - Each item in the list must be placed in its own separate segment.

            5. Conjunctions After Semicolons
            - If a segment ends with a semicolon (;) and is immediately followed by a conjunction such as "and" or "or", the conjunction must be included at the end of the current segment.

            Input & Output Format
            Input: Legal text from the CFR or Federal Register in structured form (HTML/XML/line-delimited).
            Output: A JSON array of structured segments like:

            [
              {
                "identifier": "3555.303(a)",
                "text": "(a) Eligibility."
              },
              {
                "identifier": "3555.303(a)(1)",
                "text": "(1) The borrower must occupy the property as their primary residence;"
              }
            ]
            """.strip()
            + "\n\nTarget Version:\n"
            + target_section_text
        )

    @staticmethod
    def identify_requirement_matches_and_differences(
        updated_requirement: str, original_requirements: list
    ):
        return (
            """
            Compliance Officer Task

            Your task has two phases:

            Phase 1: Identify the Best-Matching Original Requirement
            - Compare the provided updated requirement against all original requirements (do NOT use identifiers).
            - Select the original requirement that has the most semantically similar meaning.
            - If there is no semantically similar match, return "Not applicable" for the original requirement and identifier.

            Matching Guidance:
            - Use meaning only to determine similarity (not formatting, structure, or identifiers).
            - Select a match even if it's partial—only use "Not applicable" if no meaningful overlap exists.

            Phase 2: Classify the Change
            - Compare the updated requirement to the best-matching original requirement and determine the change type:

            1. Semantic Match
            - The updated rule means the same as the original, with possible formatting or wording differences.
            - Include the exact wording differences in `changed_text`.
            - Use `semantic_match: true`.

            2. Modification
            - The updated rule changes the meaning, conditions, or scope of the original.
            - The updated rule should include ~50% or more of the original’s text or concepts.
            - Use `semantic_match: false`.

            3. New Addition
            - No original requirement is meaningfully similar to the updated rule.
            - Use `"original_text": "Not applicable"` and `semantic_match: false`.

            Impact Levels:
            - High: Adds requirements, uses words like "must", "required", or creates new obligations.
            - Medium: Clarifies conditions, rewords without changing obligations, or adds details.
            - Low: Minor punctuation, formatting, or grammatical corrections.
            - None: No real changes in meaning or application.

            Output Format (return only JSON, do not wrap in markdown or explanation):
            {
              "original_text": "<original sentence or 'Not applicable'>",
              "updated_text": "<updated sentence>",
              "updated_identifier": "<Identifier from the updated rule>",
              "original_identifier": "<Identifier from the original rule or 'Not applicable'>",
              "changed_text": "<Exact changes that were made>",
              "change_statement": "<Plain English summary of what changed>",
              "semantic_match": true/false,
              "impact": "None" | "Low" | "Medium" | "High"
            }

            Updated requirement:
            """
            + updated_requirement
            + "\n\nOriginal/old requirements:\n"
            + "\n".join(original_requirements)
        )

    @staticmethod
    def extract_updated_requirements(updated_requirement_content: str):
        return (
            """Role & Task
            You are an expert legal analyst. Your task is to break the provided legal text into structured segments, 
            preserving the hierarchical format. Each segment must represent a standalone clause, sub-clause, or 
            requirement, clearly identified by its lowest-level identifier (e.g., "(a)", "(1)", "(i)").

            Output Format
            ```json
            [
              {
                "identifier": "{full identifier path}",
                "text": "(a) Full clause text goes here."
              }
            ]
            ```

            - identifier: Full path of the clause or requirement (e.g., 3555.303(b)(3)(iv))
            - text: The text of the clause or requirement. Must start with the identifier and include all related content.

            Segmentation Rules

            1. General Structure
            - Each clause, sub-clause, or requirement must be placed in its own segment.
            - Use the lowest-level identifier for each segment (e.g., (i), (2), (C)).
            - Do not combine sub-segments with their parent segments.

            2. Section Headers
            - The first segment should contain the full section header including the "§" symbol.
              - Example: §3555.303 Traditional servicing options.
              - Store it with identifier 3555.303, and include the full text as the text value.
            - All other segments should exclude the "§" symbol from the identifier but retain the full identifier path.

            3. Identifier Handling
            - Every new identifier, regardless of depth (e.g., (a), (1), (A), (i)), must start a new segment.
            - Preserve the identifiers exactly as they appear, including parentheses.
            - Include all text following an identifier until the next identifier appears.

            4. Content Guidelines
            - Each segment must be logically complete and self-contained.
            - If a segment is only a heading or introductory phrase, include all text that logically belongs with it, 
              up to the next identified sub-segment.
            - If a clause introduces a list (e.g., “may include the following:”), that introductory phrase should be 
              a separate segment. Each item in the list must then be its own segment.
            - Join lines that are broken by line breaks unless they begin with a new identifier.

            Do Not
            - Do not duplicate text across segments.
            - Do not combine content from separate identifiers into a single segment.
            - Do not omit or modify any identifiers.

            Summary
            Break the legal text into discrete, logically coherent segments using the identifier hierarchy as a guide.
            Each segment must be its own JSON object, beginning with the lowest-level identifier, and must preserve the
            structure and wording of the original text.

            Updated requirements: """
            + updated_requirement_content
        )

    @staticmethod
    def identify_deleted_requirement(updated_requirements: list, original_requirement: str):
        return (
            """
            Goal: Compare a single original requirement against a list of updated requirements to determine if it
            has been deleted while ensuring minor differences (such as punctuation, formatting, slight wording
            variations, and synonymous phrases) do not falsely classify it as deleted.

            Processing Steps

            1. Checking for a "Deleted Requirement"
              a. Take one original requirement as input.
              b. Search the updated list for an identical or semantically equivalent requirement.
              c. It does not have to be an exact match.

            2. Normalization Rules to Avoid False Deletions:
              a.  Ignore formatting differences such as:
                - Trailing punctuation (;, ,, .).
                - Minor conjunctions like "and", "or" at the end of the sentence.
                - Extra whitespace and casing (e.g., " TEXT " → "text").
                - Additional spaces between words.

              b. Reordered words with the same meaning should not cause false deletions.
                - Example: "Use of the property is not inhibited" vs. "Property use is not inhibited" should be 
                  treated as equivalent.

              c. Allow minor wording variations, such as:
                - Passive voice vs. active voice changes.
                - Contractions vs. full words ("do not" vs. "don't").

              d. Handle common synonymous phrases using NLP-based semantic similarity.
                - Example: "special servicing options" vs. "streamline servicing options" should not be marked as different.
                - Example: "prior to consideration" vs. "before considering" should be treated as equivalent.

              e. Use a similarity threshold:
                - If two sentences are at least 85-90% semantically similar, do not classify the requirement as deleted.

              f. If an original sentence ends with "; and" but the updated version ends with ";", treat them as equivalent.

            3. Handling Edge Cases
              a. Formatting Differences Only: If the requirement has the same meaning but differs only in punctuation,
                spaces, casing, or synonymous wording, it should not be classified as deleted.
              b. Truly Removed Requirements: If no match is found in the updated list after applying all normalization
                rules and semantic similarity checks, classify it as "Deleted Requirement".

            Input example:
            Updated requirements
            [
              '(a) Eligibility. To be eligible for traditional servicing, all the following conditions must be met:',
              '(i) A reduction in or loss of income that was supporting the mortgage loan;'
            ]

            Original requirement
            '(a) Eligibility. To be eligible for traditional servicing, all the following conditions must be met:'

            ### Expected Output:
            - Return **only** `"true"` if the requirement **does not exist** in the list of updated requirements, 
              even after applying normalization and semantic similarity.  
            - Return **only** `"false"` if the requirement **exists** in the list of updated requirements (after 
              applying normalization and semantic similarity rules).  
            - **Do not return any explanations, reasoning, or additional text—only `"true"` or `"false"`**.
            - **Strictly return the response in lowercase (`"true"` or `"false"`), with no punctuation or formatting.**

            Updated requirements:\n"""
            + "\n".join(updated_requirements)
            + "\nOriginal/old requirement: "
            + original_requirement
        )
    
    # pdf_markup_parser.py fallback prompts
    @staticmethod
    def text_comparison() -> str:
        """
        Returns the system prompt template for the text_comparison prompt.
        """
        return (
            """
Below are two texts. Analyze them and find all differences, including words added, removed, or changed. Respond with the detailed changes in sentence form, not with a numbered list.

Here are examples:
1. Example: 
Original:

"The Agency must show that any reduction in the servicer's loss claim which corresponds with the servicer's action or failure to act."

New:

"The Agency must show that any reduction to the servicer's loss claim corresponds with the servicer's action or failure to act."

Example Response:

Changed 'in' to 'to' before 'the servicer's loss claim'. Removed the word 'which'.

2. Example: 
Original:

"We advise the servicer of the PSVC used. The Agency must provide appeal rights if denied."

New:

"We advise the servicer of the PSVC used. The Agency must provide appeal rights if denied promptly."

Example Response:

Added 'promptly' after 'if denied'.

3. Example: Swapping a Term and Inserting a Comma
Original:

"Applicants should submit their forms no later than June 30 for consideration."

New:

"Applicants should provide their forms, no later than June 30 for consideration."


Replaced 'submit' with 'provide'. Inserted a comma after 'forms'.

4. Example:
Original:

"The Agency was evaluating the claims and determined the funding should continue."

New:

"The Agency evaluated the claims and determined the funding should continue."

Changed 'was evaluating' to 'evaluated,'.

5. Example: 
Original:

"All borrowers must comply with program regulations."

New:

"All borrowers must comply with program regulations and provide quarterly reports."

Added 'and provide quarterly reports'.

6. Example: 
Original:

"The committee members unanimously agreed to completely finalize the draft proposal."

New:

"The committee members unanimously agreed to finalize the draft proposal."

Example Response:

Removed the word 'completely'.
"""
        )
    
    @staticmethod
    def text_comparison_summary() -> str:
        """
        Returns the system prompt template for the text_comparison_summary prompt.
        """
        return (
            """
You are a document comparison assistant. You have the following data:

1. The original text (exactly as written).
2. The new text (exactly as written).
3. A short string describing the key changes (added, removed, or modified words).

Your task:
- Provide a brief statement on the overall effect, impact, and implications of these changes. Use domain specific language if the domain is clear.
- Do not repeat the exact token-level differences; instead, summarize how the changes affect clarity, scope, or meaning.

Return only one short paragraph or sentence. Do not include any extra commentary or fields.
"""
        )

    @staticmethod
    def text_comparison_impact() -> str:
        """
        Returns the system prompt template for the text_comparison_impact prompt.
        """
        return (
            """
You are a document comparison assistant. 
Your job is to assess the impact of changes made between two texts. 
You must return exactly one word: "High", "Medium", or "Low".

Definition of impact levels:
- High: Changes that significantly alter meaning, requirements, or scope.
- Medium: Changes that clarify or adjust details without major effects.
- Low: Minor adjustments (e.g., punctuation, minor wording, or formatting).

No additional commentary, JSON, or punctuation—only the impact rating word.

Your inputs will be Original Text, New Text, Change, and Summary. Example:
- Original: "The Agency staff will use information ... (etc.)"
- New: "The Agency staff will use information ... (etc.)"
- Change: "Changed 'in' to 'to' before 'the servicer's loss claim' and removed the word 'which.'"
- Summary: "The modification enhances the grammatical clarity and coherence of the text, ensuring that the criteria for reducing the servicer's loss claim are more explicitly tied to the servicer's actions or inactions. This improves the logical flow and precision of information important for decision-making regarding loss claims and penalties."

Based on these edits, do they significantly alter the meaning or scope, simply clarify details, or just fix minor issues?

Return exactly one word: High, Medium, or Low
"""
        )
    
    # pdf_page_parser.py fallback prompt
    @staticmethod
    def pdf_page_parser() -> str:
        """
        Returns the system prompt template for the pdf_page_parser prompt.
        """
        return (
            """
        You are an AI language model that analyzes document pages provided as images. For each page image provided, perform the following tasks:
        1. Extract the text content in the image, preserving the order from top to bottom. Ensure any headers and footers are captured. Preserve formatting characters like ellipses, hyphens, and special symbols.
        2. Identify any tables and convert them into human-readable sentences. For example, if there is an "If/Then" table, combine each column header with the cell below like: "If ..., then ...".
        3. Describe any images or diagrams on the page, placing them in the correct order within the text.
        4. Ensure that the output is a JSON object with the following structure:
        {
          "content": [
            {
              "type": "text",
              "text": "First text block."
            },
            {
              "type": "image_description",
              "text": "Description of the image."
            },
            ...
          ]
        }
        Respond only with the JSON object and no additional text.
        """ )
    
    # document_optimizer.py fallback prompt
    @staticmethod
    def optimize_procedure() -> str:
        """
        Returns the system prompt template for the optimize_procedure prompt.
        """
        return (
            r"""
    You are an expert in policy and procedure documentation that specializes in improving documents for clarity and accuracy.
    Guidelines:
    - Avoid unnecessary jargon.
    - Maintain the technical accuracy of existing content.
    - Include all file/URL links and references from the source text.
    - Retain all procedural steps from the source text.
    - DO NOT EXCLUDE ANY STEPS FROM SOURCE TEXT.

    When provided with a chunk of a policy or procedure document, assume the content is accurate and in the correct order, but the current headers and subheaders may be illogical, poorly formatted, and difficult to navigate.
    Please:
    Analyze the existing text and identify the key topics and subtopics within each section.
    Create a new set of headers and subheaders that accurately reflect the content of each section.
    Maintain the original order of the document.
    Use clear, concise, and informative language for all headers and subheaders.
    Consider using a consistent hierarchical structure (e.g., H1 for main sections, H2 for subsections, H3 for sub-subsections) for improved readability.

    Critical Instructions: 
    The users of this GPT will share a chunk of a document, your role is to deliver back that chunk in its entirety, with new headers and sub-headers. Use the regular expression below to identify text that should remain in place and unedited. Make sure to include all matches in your responses.
    ```
    !\[[\s\S]*?\]\([\s\S]*?\)\{width="[^"]+"\s*height="[^"]+"\}
    ```

    Only reply with the updated text. Do not include anything else in your response.
    """
        )