"""
Module for processing and managing documents, including extracting statements,
subtopics, and integrating with vector stores.
"""

import json
import logging
import re
from typing import List

from langfuse.decorators import observe
from llama_index.core import VectorStoreIndex
from llama_index.core.schema import MetadataMode
from llama_index.core.vector_stores import <PERSON><PERSON><PERSON>perator, MetadataFilter, MetadataFilters
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.pinecone import PineconeVectorStore

from accessor.content import ContentAccessor
from accessor.content.exceptions import ContentAccessorException
from accessor.document.dto import (
    AcceptanceCriteriaGenerationDTO,
    PolicyRequirementComparisonRequirementDTO,
    TestCaseGenerationDTO,
)
from accessor.reference.dto import OriginalRegulationSectionDTO, RegulationSectionComparisonDTO
from engine.synthesis.config import Config
from engine.synthesis.data_access import DataAccess
from engine.synthesis.text_extractor import TextExtractor
from shared.enums import StatementTypeEnum
from shared.enums.job_enums import JobTypeEnum
from shared.models import Chunk, RatingEnum, Statement
from util.audit.auditor import Auditor
from util.database import Session


class DocumentProcessor:
    """
    A class to handle document processing, including extracting statements, subtopics,
    and integrating with vector stores for retrieval and storage.
    """

    def __init__(
        self,
        pinecone_client,
        data_access: DataAccess,
        text_extractor: TextExtractor,
        logger_name: str,
    ):
        self._pinecone_client_init = pinecone_client  # Store the init function
        self._pinecone_client = None  # Initialize the client as None
        self._persona_mapping = None

        self.logger = logging.getLogger(logger_name)
        self.data_access = data_access
        self.text_extractor = text_extractor
        self.embedding_model_name = Config.EMBEDDING_MODEL_NAME
        self.content_accessor = ContentAccessor(logger_name)

        self.auditor = Auditor(session_factory=Session)

    @property
    def pinecone_client(self):
        """Lazily initialize the Pinecone client when accessed."""
        if self._pinecone_client is None:
            self._pinecone_client = self._pinecone_client_init()  # Call the init function
        return self._pinecone_client

    def generate_unique_statements(self, document_id: int, statement_type_id: int):
        """
        Generate unique statements for the relevant subtopics of a given document.

        Args:
            document_id (int): The unique ID of the document.
            statement_type_id (int): The statement type ID to process.

        Returns:
            None: This method does not return any value. Instead, it processes
                  and saves unique statements for each relevant subtopic in the database.
        """
        # Retrieve subtopics already linked to the document
        relevant_subtopics = (
            self.content_accessor.get_relevant_subtopics_for_unique_statement_generation(
                document_id
            )
        )

        if not relevant_subtopics:
            self.logger.info("No relevant subtopics found for document ID: %s", document_id)
            return

        # Process unique statements only for relevant subtopics
        for subtopic_id, subtopic_name in relevant_subtopics:
            self.logger.debug(
                "Generating unique statements for relevant subtopic: %s (ID: %d)",
                subtopic_name,
                subtopic_id,
            )
            self._generate_unique_statements_for_document_and_subtopic(
                document_id=document_id,
                subtopic_id=subtopic_id,
                statement_type_id=statement_type_id,
            )

    # TODO: Obsolete needs to be removed eventually
    def generate_requirements_bundle(
        self,
        document_id: int,
        business_id: int,
        statement_type_id: int,
        subtopic_id: int,
        unique_statements: list,
    ):
        self.logger.debug(
            "Generating requirements bundle for document_id: %s, business_id: %s, "
            "statement_type_id: %s, subtopic_id: %s",
            document_id,
            business_id,
            statement_type_id,
            subtopic_id,
        )
        persona = self.data_access.get_business_persona_by_document(document_id)
        self._generate_user_stories_for_subtopic_and_document_and_statement(
            persona=persona,
            unique_statements=unique_statements,
            document_id=document_id,
        )

    def generate_user_stories(
        self,
        document_id: int,
        business_id: int,
        statement_type_id: int,
        subtopic_id: int,
        unique_statements: list,
    ):
        self.logger.debug(
            "Generating user stories for document_id: %s, business_id: %s, "
            "statement_type_id: %s, subtopic_id: %s",
            document_id,
            business_id,
            statement_type_id,
            subtopic_id,
        )
        persona = self.data_access.get_business_persona_by_document(document_id)
        self._generate_user_stories_for_subtopic_and_document_and_statement(
            persona=persona,
            unique_statements=unique_statements,
            document_id=document_id,
        )

    def generate_acceptance_criteria_for_user_story(
        self,
        dto: AcceptanceCriteriaGenerationDTO,
    ):
        self.logger.debug(
            "Generating acceptance criteria for user_story_id: %s, document_id: %s ",
            dto.user_story_id,
            dto.document_id,
        )
        self._generate_acceptance_criteria_for_subtopic_and_document_and_statement(dto)

    def generate_test_cases_for_acceptance_criteria(
        self,
        dto: TestCaseGenerationDTO,
    ):
        self.logger.debug(
            "Generating test cases for acceptance_criteria_id: %s, user_story_id: %s, document_id: %s ",
            dto.acceptance_criteria_id,
            dto.user_story_id,
            dto.document_id,
        )
        self._generate_test_cases_for_subtopic_and_document_and_statement(dto)

    def generate_document_statements(
        self,
        document_id: int,
        persona: str,
        start_chunk_id: int,
        end_chunk_id: int,
        job_type_id: int,
    ):
        document_chunks = self.content_accessor.get_chunks_by_document(document_id)

        for chunk in document_chunks:

            if start_chunk_id <= chunk.chunk_id <= end_chunk_id:  # type: ignore
                self.logger.info("Processing ChunkID: %s", str(chunk.chunk_id))

                # Handle markup job types (generate change statements only)
                if (
                    job_type_id == JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value
                    or job_type_id == JobTypeEnum.VERSION_COMPARISON.value
                ):
                    change_statements = self.text_extractor.extract_change_statements(
                        node_text=chunk.text_content
                    )
                    for change_statement in change_statements.get("change_statements", []):
                        insert_fields = {
                            "chunk_id": chunk.chunk_id,
                            "statement_type_id": StatementTypeEnum.CHANGE_STATEMENT.value,
                            "text": json.dumps(change_statement, indent=2),
                        }
                        new_statement = self.content_accessor.save_statement(insert_fields)

                        self.logger.debug(
                            "Change Statement ID: %s", str(new_statement.statement_id)
                        )

                    # Skip further processing for other statement types
                    continue

                results = self.text_extractor.process_chunk_for_artifact_statement_types(
                    chunk.chunk_id, persona
                )

                statement_mappings = {
                    "requirements": StatementTypeEnum.REQUIREMENT,
                    "authorizations": StatementTypeEnum.AUTHORIZATION,
                    "opportunities": StatementTypeEnum.OPPORTUNITY,
                    "procedure_changes": StatementTypeEnum.PROCEDURE_CHANGE,
                    "organizational_changes": StatementTypeEnum.ORGANIZATIONAL_CHANGE,
                    "definitions": StatementTypeEnum.DEFINITION,
                }

                for statement_type_name, statement_type_enum in statement_mappings.items():
                    self.process_statements(
                        chunk, results, statement_type_name, statement_type_enum
                    )

    def process_statements(
        self,
        chunk: Chunk,
        results: dict,
        statement_type_name: str,
        statement_type_enum: StatementTypeEnum,
    ):
        """Processes statements for a given key and statement type."""
        if results.get(statement_type_name) and results[statement_type_name].get("Statements"):
            for statement in results[statement_type_name]["Statements"]:
                if isinstance(statement, dict):
                    statement_text = statement.get("Statement_Text", "")
                else:
                    statement_text = statement

                if statement_text:  # Filter out blanks
                    insert_fields = {
                        "chunk_id": chunk.chunk_id,
                        "statement_type_id": statement_type_enum.value,
                        "text": statement_text,
                    }
                    new_statement = self.content_accessor.save_statement(insert_fields)
                    self.logger.debug(
                        "%s ID: %s", statement_type_enum.name, str(new_statement.statement_id)
                    )

    def generate_document_subtopics(self, document_id: int, start_chunk_id: int, end_chunk_id: int):
        """Generate subtopics for the specified document chunks."""
        detailed_document = self.content_accessor.get_document_by_id(document_id)
        document_chunks = self.content_accessor.get_chunks_by_document(document_id)
        detailed_domain = self.content_accessor.get_domain_by_id(detailed_document.domain_id)

        for chunk in document_chunks:
            if start_chunk_id <= chunk.chunk_id <= end_chunk_id:  # Ensure chunk range
                self.logger.info("Processing Subtopics for ChunkID: %s", chunk.chunk_id)

                # Retrieve existing subtopics for restartability
                existing_chunk_subtopics = self.content_accessor.get_statements_by_chunk_and_type(
                    chunk.chunk_id, StatementTypeEnum.IDENTIFIED_SUBTOPIC.value
                )

                if not existing_chunk_subtopics:
                    chunk_new_subtopics = self.get_chunk_subtopics(
                        chunk, detailed_document.business_id
                    )
                    self.logger.debug("New Subtopics Identified: %s", chunk_new_subtopics)

                    try:
                        # Save subtopics in Pinecone and database
                        index = self.pinecone_client.Index(detailed_domain.vector_database_name)
                        pinecone_node_data = index.fetch([str(chunk.vector_id)])
                        metadata = pinecone_node_data["vectors"][str(chunk.vector_id)]["metadata"]
                        metadata["found_subtopics"] = chunk_new_subtopics
                        index.upsert(
                            [
                                (
                                    str(chunk.vector_id),
                                    pinecone_node_data["vectors"][str(chunk.vector_id)]["values"],
                                    metadata,
                                )
                            ]
                        )
                    except ImportError as e:
                        self.logger.error("Error during subtopic upload: %s", e)

                    # Save subtopics to the database
                    self.save_identified_subtopics(chunk.chunk_id, chunk_new_subtopics)

    def save_identified_subtopics(self, chunk_id: int, chunk_new_subtopics: list[str]):
        for new_sub in chunk_new_subtopics:
            insert_fields = {
                "chunk_id": chunk_id,
                "statement_type_id": StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                "text": new_sub,
            }

            new_identified_subtopic_statement = self.content_accessor.save_statement(insert_fields)
            self.logger.debug(
                "Identified_Subtopic ID: %s", str(new_identified_subtopic_statement.statement_id)
            )

    def get_chunk_subtopics(self, chunk: Chunk, business_id: int) -> list[str]:
        """
        This method retrieves the subtopics relevant to a given chunk within the context of a specific business.
        It first fetches the list of topics associated with the provided business ID, then iterates over these topics
        to extract subtopics from the chunk's text content using a text extraction utility.
        If relevant subtopics are found, they are appended to a list, which is ultimately returned.
        The method is designed to identify and return a list of subtopic strings related to the chunk's content.
        """

        business_topics = self.data_access.get_topics(business_id)  # type: ignore

        chunk_subtopics = []

        for topic in business_topics:
            found_relevant_subtopics = self.text_extractor.extract_subtopics(
                node_text=chunk.text_content,
                subtopics=self.text_extractor.assemble_subtopics(
                    self.content_accessor.get_subtopics_by_topic_id(topic.topic_id)  # type: ignore
                ),
            )

            for found_subtopic in found_relevant_subtopics:
                if found_subtopic != "No relevant topics":
                    chunk_subtopics.append(found_subtopic)
        self.logger.debug("PRE - Subtopics: %s", str(chunk_subtopics))

        return chunk_subtopics

    def generate_procedure_changes_for_requirement(self, dto: RegulationSectionComparisonDTO):
        self.logger.debug(
            "Generating procedure changes for unique_statement_id: %s, subtopic_id: %s ",
            dto.unique_statement_id,
            dto.subtopic_id,
        )
        self._generate_procedure_changes_for_subtopic_and_document_and_requirement(dto)

    def generate_change_statements_for_document(
        self, dto: PolicyRequirementComparisonRequirementDTO
    ):
        self.logger.debug("Generating change statements for document_id: %s", dto.document_id)
        self._generate_change_statements_for_document(dto)

    def generate_deleted_requirements_for_document(
        self,
        document_id: int,
        all_original_requirements: list,
        original_sections: list[OriginalRegulationSectionDTO],
    ):
        self.logger.debug("Generating deleted requirements for document_id: %s", document_id)
        self._generate_deleted_requirements_for_document(
            document_id, all_original_requirements, original_sections
        )

    def _generate_deleted_requirements_for_document(
        self,
        document_id: int,
        all_original_requirements: list,
        original_sections: list[OriginalRegulationSectionDTO],
    ):
        # Steps
        # 1. Get all existing requirements that had a match
        # 2. Set up a chunk hashtable so that we can associate deleted requiremenmts to each chunk it came from
        # 3. Loop through all original requirements
        #  - If match found, don't create a deleted requirement
        #  - If no match found, associate to chunk it came from
        # 4. Iterate through chunk hashtable and create/save deleted requirements

        # Pull back all existing requirements (ones that already have been matched)
        existing_change_statements = self.content_accessor.get_statements_by_document_and_type(
            document_id, StatementTypeEnum.CHANGE_STATEMENT.value
        )

        fed_register_chunks = self.content_accessor.get_chunks_by_document(document_id)

        # Create a chunk hashtable using the chunk_id as the index
        chunk_hashtable = {chunk.chunk_id: chunk for chunk in fed_register_chunks}

        # Create a dictionary for quick lookup of section -> index mapping
        section_lookup = {section.section: idx for idx, section in enumerate(original_sections)}

        matched_original_identifiers = self._get_matched_original_modifiers(
            existing_change_statements
        )

        # Loop through all_original_requirements
        for original_requirement in all_original_requirements:
            original_identifier = original_requirement["identifier"]

            # Did we already match on this identifier?
            if original_identifier in matched_original_identifiers:
                continue  # Skip this requirement as it already exists in change statements

            # We need to determine which chunk to associated this deleted requirement to
            chunk_index = self._determine_chunk_index(original_identifier, section_lookup)

            if chunk_index == -1:
                continue  # Skip if section title is not found

            chunk = fed_register_chunks[chunk_index]

            if chunk:
                # Add the original_requirement to the hashtable item using chunk_id
                if chunk.chunk_id in chunk_hashtable:
                    if not hasattr(chunk_hashtable[chunk.chunk_id], "deleted_requirements"):
                        chunk_hashtable[chunk.chunk_id].deleted_requirements = []

                    chunk_hashtable[chunk.chunk_id].deleted_requirements.append(
                        original_requirement
                    )

        # Iterate over chunk_hashtable and create deleted change statements
        for chunk in chunk_hashtable.values():
            if hasattr(chunk, "deleted_requirements") and chunk.deleted_requirements:
                deleted_change_statements = []

                # Iterate over deleted requirements for this chunk
                for deleted_requirement in chunk.deleted_requirements:
                    deleted_change_statements.append(
                        {
                            "original_text": deleted_requirement["text"],
                            "updated_text": "Not applicable",
                            "updated_identifier": "Not applicable",
                            "original_identifier": deleted_requirement["identifier"],
                            "changed_text": "Deleted requirement",
                            "change_statement": "The requirement has been deleted from the updated version.",
                            "semantic_match": True,
                            "impact": "High",
                        }
                    )

                # Save deleted change statements for this chunk
                self._save_change_statements(deleted_change_statements, chunk.chunk_id)

    def _get_matched_original_modifiers(self, existing_change_statements: List[Statement]) -> set:
        # Create a set of existing change identifiers for quick lookup
        existing_change_identifiers = set()

        for stmt in existing_change_statements:
            try:
                text_data = json.loads(stmt.text)  # Parse text field as JSON
                if "original_identifier" in text_data:
                    existing_change_identifiers.add(text_data["original_identifier"])
            except json.JSONDecodeError:
                continue  # Skip if text is not valid JSON

        return existing_change_identifiers

    def _determine_chunk_index(self, original_identifier: str, section_lookup: dict) -> int:
        # Extract the section title (before the first "(")
        section_title = original_identifier.split("(")[0]

        # Find the index of the section title using the lookup dictionary
        return section_lookup.get(section_title, -1)

    def _generate_user_stories_for_subtopic_and_document_and_statement(
        self, persona, unique_statements, document_id
    ):
        # Don't proceed with calls to OpenAI and the database if there is nothing to generate
        if not unique_statements:
            self.logger.debug("No unique statements to process. Exiting.")
            return

        for unique_statement in unique_statements:
            existing_user_stories = self.content_accessor.get_user_stories_for_unique_statement(
                unique_statement.unique_statement_id
            )

            if existing_user_stories:
                self.logger.debug(
                    "Skipping Unique Statement ID: %s. User Stories already exist.",
                    unique_statement.unique_statement_id,
                )
                continue

            self.logger.debug(
                "Generating User Stories for Unique Statement: %s",
                unique_statement.text,
            )

            # Generate User Stories For Business Persona
            user_stories = self.text_extractor.create_user_stories_for_requirement(
                persona=persona, unique_statement_text=unique_statement.text
            )
            if not user_stories:
                self.logger.debug(
                    "No User Stories generated for Unique Statement ID: %s",
                    unique_statement.unique_statement_id,
                )
                continue

            # Update User Stories with Dynamic Personas
            updated_user_stories = self._update_user_story_personas(
                unique_statement.unique_statement_id, user_stories, document_id
            )

            # Save Only the Updated User Stories
            for updated_user_story in updated_user_stories:
                insert_fields = {
                    "unique_statement_id": unique_statement.unique_statement_id,
                    "description": updated_user_story,
                    "user_entered": False,
                }
                saved_user_story = self.content_accessor.save_user_story(insert_fields)
                self.logger.debug("Created User Story: %s", saved_user_story.description)

        self.logger.debug(
            "User Story generation process completed for Unique Statement ID: %s.",
            unique_statement.unique_statement_id,
        )

    def _generate_acceptance_criteria_for_subtopic_and_document_and_statement(
        self, dto: AcceptanceCriteriaGenerationDTO
    ):
        # Get a list of all subtopics associated with the parent unique statement of this user story
        subtopics = self.content_accessor.get_subtopics_for_unique_statement(
            dto.unique_statement_id
        )

        all_subtopics_context = ""

        for subtopic in subtopics:
            all_subtopics_context += self.generate_subtopic_context(
                subtopic.subtopic_id, dto.document_id
            )

        self.logger.debug("Generating artifacts for user story %s", dto.user_story_description)

        acceptance_criteria = self.text_extractor.create_ac_for_user_story(
            helpful_context=all_subtopics_context,
            user_story_text=dto.user_story_description,
            requirement_text=dto.unique_statement_text,
        )

        for ac in acceptance_criteria["acceptanceCriteria"]:
            insert_fields = {
                "user_story_id": dto.user_story_id,
                "description": ac["description"],
                "user_entered": False,
            }

            saved_ac = self.content_accessor.save_acceptance_criteria(insert_fields)

            self.logger.debug("Acceptance Criteria Created: %s", saved_ac.description)

    def _generate_test_cases_for_subtopic_and_document_and_statement(
        self, dto: TestCaseGenerationDTO
    ):
        # Get a list of all subtopics associated with the parent unique statement of this user story
        subtopics = self.content_accessor.get_subtopics_for_unique_statement(
            dto.unique_statement_id
        )

        all_subtopics_context = ""

        for subtopic in subtopics:
            all_subtopics_context += self.generate_subtopic_context(
                subtopic.subtopic_id, dto.document_id
            )

        self.logger.debug(
            "Generating test cases for acceptance criteria %s",
            dto.acceptance_criteria_description,
        )

        test_case = self.text_extractor.create_tc_for_ac(
            helpful_context=all_subtopics_context,
            acceptance_criteria_text=dto.acceptance_criteria_description,
            requirement_text=dto.unique_statement_text,
        )

        for tc in test_case["testCases"]:
            insert_fields = {
                "acceptance_criteria_id": dto.acceptance_criteria_id,
                "description": tc["description"],
                "user_entered": False,
            }

            saved_tc = self.content_accessor.save_test_case(insert_fields)

            self.logger.debug("Test Case Created: %s", saved_tc.description)

    def generate_unique_statements_for_domain_and_subtopic(
        self, domain_id, document_id, subtopic_id, statement_type_id
    ):
        detailed_subtopic = self.content_accessor.get_subtopic_by_id(subtopic_id)
        detailed_domain = self.content_accessor.get_domain_by_id(domain_id)

        vector_db_filter = self._create_metadata_filter(detailed_subtopic.name)

        pinecone_index = self.pinecone_client.Index(detailed_domain.vector_database_name)
        vector_store = PineconeVectorStore(pinecone_index=pinecone_index)
        embed_model = OpenAIEmbedding(model=self.embedding_model_name)
        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store, embed_model=embed_model
        )

        retriever = index.as_retriever(filters=vector_db_filter, similarity_top_k=1000)
        domain_subtopic_results = retriever.retrieve("What is available?")
        domain_subtopic_requirements = []
        rich_subtopic = f"{detailed_subtopic.name} - {detailed_subtopic.description}"

        for item in domain_subtopic_results:
            node = item.node
            node_requirements = self.content_accessor.get_statements_by_vector_and_type(
                node.node_id, StatementTypeEnum.REQUIREMENT.value
            )
            requirements_list = self.text_extractor.extract_statement_text_into_list(
                node_requirements
            )
            self.logger.debug("Subtopic node id: %s", str(node.node_id))
            self.logger.debug(
                "Subtopic node associated raw requirement statements: %s",
                str(requirements_list),
            )
            domain_subtopic_requirements = self.text_extractor.assemble_requirements(
                node.text,
                requirements_list,
                domain_subtopic_requirements,
                rich_subtopic,
            )
            self.logger.debug(
                "Node incorporated requirement statements: %s",
                str(domain_subtopic_requirements),
            )

        for uniquestatement in domain_subtopic_requirements:
            try:
                insert_fields = {
                    "domain_id": domain_id,
                    "document_id": document_id,
                    "statement_type_id": statement_type_id,
                    "subtopics": [{"subtopic_id": subtopic_id, "is_primary": True}],
                    "text": uniquestatement,
                    "identifier": "TBD",
                    "rating": RatingEnum.unrated.value,
                    "user_entered": False,
                    "topic_id": detailed_subtopic.topic_id,
                }

                new_unique_statement = self.content_accessor.create_unique_statement_with_subtopics(
                    insert_fields
                )

                self.logger.debug(
                    "Unique statement: %s inserted with ID: %s",
                    uniquestatement,
                    new_unique_statement.unique_statement_id,
                )

            except ContentAccessorException as e:
                self.logger.error("Error saving unique statement: %s", str(e))

    def _generate_unique_statements_for_document_and_subtopic(
        self, document_id, subtopic_id, statement_type_id
    ):
        detailed_subtopic = self.content_accessor.get_subtopic_by_id(subtopic_id)
        document_subtopic_statements = []
        rich_subtopic = f"{detailed_subtopic.name} - {detailed_subtopic.description}"

        document_subtopic_chunk_ids = self.content_accessor.get_chunk_ids_for_subtopic_and_document(
            subtopic_id, document_id
        )

        domain_id = self.content_accessor.get_document_by_id(document_id).domain_id

        for chunk_id in document_subtopic_chunk_ids:
            detailed_chunk = self.content_accessor.get_chunk_by_id(chunk_id)
            chunk_statements = self.content_accessor.get_statements_by_chunk_and_type(
                chunk_id, statement_type_id
            )

            # If no positive or unrated statements are found, skip to the next chunk_id
            if not chunk_statements:
                continue

            statements_list = self.text_extractor.extract_statement_text_into_list(chunk_statements)
            self.logger.debug(
                "Subtopic node associated raw statements of type %s: %s",
                statement_type_id,
                statements_list,
            )

            if statement_type_id == StatementTypeEnum.REQUIREMENT.value:
                document_subtopic_statements = self.text_extractor.assemble_requirements(
                    detailed_chunk.text_content,
                    statements_list,
                    document_subtopic_statements,
                    rich_subtopic,
                )

            elif statement_type_id == StatementTypeEnum.AUTHORIZATION.value:
                document_subtopic_statements = self.text_extractor.assemble_authorizations(
                    detailed_chunk.text_content,
                    statements_list,
                    document_subtopic_statements,
                    rich_subtopic,
                )

            elif statement_type_id == StatementTypeEnum.OPPORTUNITY.value:
                document_subtopic_statements = self.text_extractor.assemble_opportunities(
                    detailed_chunk.text_content,
                    statements_list,
                    document_subtopic_statements,
                    rich_subtopic,
                )

            self.logger.debug("node incorporated statements: %s", str(document_subtopic_statements))

        for unique_statement in document_subtopic_statements:
            try:
                insert_fields = {
                    "domain_id": domain_id,
                    "document_id": document_id,
                    "statement_type_id": statement_type_id,
                    "subtopics": [{"subtopic_id": subtopic_id, "is_primary": True}],
                    "text": unique_statement,
                    "identifier": "TBD",
                    "rating": RatingEnum.unrated.value,
                    "user_entered": False,
                    "topic_id": detailed_subtopic.topic_id,
                }

                new_unique_statement = self.content_accessor.create_unique_statement_with_subtopics(
                    insert_fields
                )

                self.logger.debug(
                    "Unique statement: %s inserted with ID: %s",
                    unique_statement,
                    new_unique_statement.unique_statement_id,
                )

            except ContentAccessorException as e:
                self.logger.error("Error saving unique statement: %s", str(e))

    def generate_unique_statements_for_document_change_statements(self, document_id: int):
        """
        Generate unique statements for the change statements of a given document.

        Args:
            document_id (int): The unique ID of the document.

        Returns:
            None: This method does not return any value. Instead, it processes
                  and saves unique statements for each change statement in the database.
        """

        # Fetch required data
        document_chunks = self.content_accessor.get_chunks_by_document(document_id)
        domain_id = self.content_accessor.get_document_by_id(document_id).domain_id

        for chunk in document_chunks:
            chunk_identified_subtopics = self.content_accessor.get_identified_subtopics_by_chunk(
                chunk.chunk_id
            )
            if not chunk_identified_subtopics:
                self.logger.info("No identified subtopics found for chunk ID: %s", chunk.chunk_id)
                continue

            # Prepare mappings and fetch chunk statements
            subtopic_mappings = self._create_subtopic_mappings(chunk_identified_subtopics)
            chunk_change_statements = self.content_accessor.get_statements_by_chunk_and_type(
                chunk.chunk_id, StatementTypeEnum.CHANGE_STATEMENT.value
            )
            if not chunk_change_statements:
                self.logger.info("No chunk statements found for chunk ID: %s", chunk.chunk_id)
                continue

            # Process chunk statements and extract requirements
            chunk_statements_text_list = self._generate_statement_text_list(chunk_change_statements)
            chunk_subtopics_text = self.text_extractor.assemble_subtopics(
                chunk_identified_subtopics
            )
            requirements = self.text_extractor.assemble_requirements_from_change_statements(
                chunk.text_content, chunk_statements_text_list, chunk_subtopics_text
            )
            self.logger.debug("chunk incorporated statements: %s", str(requirements))

            # Process each requirement
            for requirement in requirements:
                self._process_requirement(requirement, domain_id, document_id, subtopic_mappings)

    def _create_subtopic_mappings(self, chunk_subtopics):
        subtopic_mapping = {subtopic.name: subtopic.subtopic_id for subtopic in chunk_subtopics}
        subtopic_topic_mapping = {
            subtopic.subtopic_id: subtopic.topic_id for subtopic in chunk_subtopics
        }
        return {
            "subtopic_mapping": subtopic_mapping,
            "subtopic_topic_mapping": subtopic_topic_mapping,
        }

    def _generate_statement_text_list(self, chunk_statements):
        statement_text_mapping = {
            statement.text: statement.statement_id for statement in chunk_statements
        }
        statement_text_list = self.text_extractor.extract_statement_text_into_list(chunk_statements)
        return [
            f"statement_id: {statement_text_mapping.get(statement)}, text: {statement}"
            for statement in statement_text_list
        ]

    def _process_requirement(self, requirement, domain_id, document_id, subtopic_mappings):
        try:
            unique_statement = requirement.get("requirement", "")
            statement_id = requirement.get("statement_id", "")
            subtopics = requirement.get("subtopics", [])

            mapped_subtopics, topic_id = self._map_subtopics(subtopics, subtopic_mappings)

            insert_fields = {
                "domain_id": domain_id,
                "document_id": document_id,
                "statement_type_id": 1,
                "subtopics": mapped_subtopics,
                "text": unique_statement,
                "identifier": "TBD",
                "rating": RatingEnum.unrated.value,
                "user_entered": False,
                "topic_id": topic_id,
            }

            if statement_id:  # Only add if it exists
                insert_fields["statement_id"] = statement_id

            new_unique_statement = self.content_accessor.create_unique_statement_with_subtopics(
                insert_fields
            )
            self.logger.debug(
                "Unique statement: %s inserted with ID: %s",
                unique_statement,
                new_unique_statement.unique_statement_id,
            )

        except ContentAccessorException as e:
            self.logger.error("Error saving unique statement: %s", str(e))

    def _map_subtopics(self, subtopics, subtopic_mappings):
        subtopic_mapping = subtopic_mappings["subtopic_mapping"]
        subtopic_topic_mapping = subtopic_mappings["subtopic_topic_mapping"]

        mapped_subtopics = []
        topic_id = None
        for subtopic in subtopics:
            subtopic_name = subtopic.get("subtopic")
            is_primary = subtopic.get("is_primary", False)
            subtopic_id = subtopic_mapping.get(subtopic_name)

            if is_primary:
                topic_id = subtopic_topic_mapping.get(subtopic_id)

            if subtopic_id is not None:
                mapped_subtopics.append({"subtopic_id": subtopic_id, "is_primary": is_primary})
            else:
                self.logger.warning(f"Subtopic '{subtopic_name}' not found in database.")

        return mapped_subtopics, topic_id

    def _create_metadata_filter(self, subtopic_name):
        filters = MetadataFilters(
            filters=[
                MetadataFilter(
                    key="found_subtopics",
                    operator=FilterOperator.EQ,
                    value=subtopic_name,
                ),
            ]
        )

        return filters

    def generate_subtopic_context(self, subtopic_id: int, document_id: int, domain_id=None):
        detailed_subtopic = self.content_accessor.get_subtopic_by_id(subtopic_id)

        # Use the provided domain_id if available, otherwise infer from document_id
        if domain_id:
            detailed_domain = self.content_accessor.get_domain_by_id(domain_id)
        else:
            detailed_document = self.content_accessor.get_document_by_id(document_id)
            detailed_domain = self.content_accessor.get_domain_by_id(detailed_document.domain_id)

        vector_db_filter = self._create_metadata_filter(detailed_subtopic.name)

        pinecone_index = self.pinecone_client.Index(detailed_domain.vector_database_name)
        vector_store = PineconeVectorStore(pinecone_index=pinecone_index)
        embed_model = OpenAIEmbedding(model=self.embedding_model_name)
        index = VectorStoreIndex.from_vector_store(
            vector_store=vector_store, embed_model=embed_model
        )

        rich_subtopic = f"{detailed_subtopic.name} - {detailed_subtopic.description}"

        retriever = index.as_retriever(filters=vector_db_filter, similarity_top_k=10)
        corpus_results = retriever.retrieve(
            f"What are the requirements for servicers related to {rich_subtopic}?"
        )
        subtopic_context = ""

        for node in corpus_results:
            subtopic_context += node.get_content(metadata_mode=MetadataMode.NONE)
        return subtopic_context

    def _generate_procedure_changes_for_subtopic_and_document_and_requirement(
        self, dto: PolicyRequirementComparisonRequirementDTO
    ):
        # Generate subtopic context from vector database
        domain_subtopic_context = self.generate_subtopic_context(
            subtopic_id=dto.subtopic_id,
            document_id=dto.document_id,
            domain_id=dto.domain_id,
        )

        self.logger.debug("Generating procedure changes for requirement %s", dto.requirement_text)

        # Call text_extractor to create procedure changes for the requirement
        procedure_changes = self.text_extractor.assemble_procedure_changes_for_requirement(
            domain_name=dto.domain_name,
            domain_subtopic_context=domain_subtopic_context,
            requirement_text=dto.requirement_text,
            subtopic_text=dto.subtopic_name_description,
        )

        # Iterate through each procedure change and save as a new unique requirement
        for procedure_change_requirement in procedure_changes:
            try:
                insert_fields = {
                    "domain_id": dto.domain_id,
                    "document_id": dto.document_id,
                    "statement_type_id": StatementTypeEnum.PROCEDURE_CHANGE.value,
                    "subtopics": [
                        {"subtopic_id": dto.subtopic_id, "is_primary": True},
                    ],
                    "text": procedure_change_requirement,
                    "identifier": "TBD",
                    "rating": RatingEnum.unrated.value,
                    "user_entered": False,
                    "topic_id": dto.topic_id,
                    "parent_requirement_id": dto.unique_statement_id,
                }

                new_unique_statement = self.content_accessor.create_unique_statement_with_subtopics(
                    insert_fields
                )

                self.logger.debug(
                    "Unique statement: %s inserted with ID: %s",
                    procedure_change_requirement,
                    new_unique_statement.unique_statement_id,
                )

            except ContentAccessorException as e:
                self.logger.error("Error saving unique statement: %s", str(e))

    def _generate_change_statements_for_document(self, dto: RegulationSectionComparisonDTO):
        """
        Generates change statements for a document by comparing original and updated requirements.

        Args:
            dto (RegulationSectionComparisonDTO): Data transfer object containing comparison details.

        Returns:
            list: A list of change statements.
        """

        updated_requirements_for_section = self._extract_updated_requirements_for_section(dto)
        change_statements = self._extract_change_statements_for_updated_section(
            updated_requirements_for_section, dto.all_original_requirements
        )

        self.logger.debug(
            "%s Change Statements found",
            len(change_statements),
        )

        self._save_change_statements(change_statements, dto.chunk_id)

    def _extract_change_statements_for_updated_section(
        self, updated_requirements, original_requirements
    ):
        change_statements = []

        for updated_requirement in updated_requirements:

            exact_text_match_found = self._exact_text_match_exists(
                updated_requirement, original_requirements
            )

            # If there is an exact match, no need to use LLM (not 100% reliable, better to use code)
            if exact_text_match_found:
                # If there is a match and the identifier is the same, create the change statement
                if updated_requirement["identifier"] == exact_text_match_found["identifier"]:
                    change_statement = self._create_exact_match_change_statement(
                        updated_requirement
                    )
                else:
                    change_statement = self._create_reordered_change_statement(
                        updated_requirement, exact_text_match_found
                    )

            else:
                change_statement = self.text_extractor.identify_requirement_matches_and_differences(
                    updated_requirement=updated_requirement,
                    original_requirements=original_requirements,
                )

            # Assign the updated_identifier value to a new identifier field
            if "updated_identifier" in change_statement:
                change_statement["identifier"] = change_statement["updated_identifier"]

            self.logger.debug("Generated new change statement: %s", change_statement)
            change_statements.append(change_statement)

        return change_statements

    def extract_all_original_requirements(self, all_original_requirements_text: str) -> list:
        all_original_requirements = self.text_extractor.extract_original_requirements(
            target_section_text=all_original_requirements_text,
        )

        return all_original_requirements

    def _extract_updated_requirements_for_section(self, dto):
        updated_requirements = self.text_extractor.extract_updated_requirements(
            updated_requirement_content=dto.updated_section_info.content,
        )

        return updated_requirements

    def _identify_deleted_requirements(self, original_requirements, updated_requirements, dto):
        """
        Identifies deleted requirements by comparing the original and updated requirements.

        Args:
            original_requirements (list): List of original requirement dictionaries.
            updated_requirements (list): List of updated requirement dictionaries.
            dto (RegulationSectionComparisonDTO): Data transfer object containing section name.

        Returns:
            list: A list of change statements indicating deleted requirements.
        """
        change_statements = []

        # Filter original requirements by section
        original_data_filtered_by_section = [
            item for item in original_requirements if dto.updated_section_name in item["identifier"]
        ]

        # Extract text-only version of updated requirements
        updated_requirements_text_only = [
            requirement["text"] for requirement in updated_requirements
        ]

        # Identify deleted requirements
        for original_requirement in original_data_filtered_by_section:
            found_deleted_requirement = self.text_extractor.identify_deleted_requirement(
                updated_requirements=updated_requirements_text_only,
                original_requirement=original_requirement["text"],
            )

            if found_deleted_requirement:
                change_statements.append(
                    {
                        "original_text": original_requirement["text"],
                        "updated_text": "Not applicable",
                        "updated_identifier": "Not applicable",
                        "original_identifier": original_requirement["identifier"],
                        "changed_text": "Deleted requirement",
                        "change_statement": "The requirement has been deleted from the updated version.",
                        "semantic_match": True,
                        "impact": "High",
                    }
                )

        return change_statements

    def _save_change_statements(self, change_statements, chunk_id: int):
        """
        Saves change statements as unique requirements in the database.

        Args:
            change_statements (list): A list of change statements to save.
            chunk_id (int): Chunk ID to associate the change statements with.
        """
        for change_statement in change_statements:
            try:
                insert_fields = {
                    "chunk_id": chunk_id,
                    "statement_type_id": StatementTypeEnum.CHANGE_STATEMENT.value,
                    "text": json.dumps(change_statement),
                }

                new_statement = self.content_accessor.save_statement(insert_fields)

                self.logger.debug(
                    "Change Statement inserted with ID: %s", new_statement.statement_id
                )

            except ContentAccessorException as e:
                self.logger.error("Error saving change statement: %s", str(e))

    def _exact_text_match_exists(self, updated_requirement, original_requirement_list):
        """
        Searches for an exact text match of the updated requirement in the list of original requirements,
        ignoring leading parenthetical identifiers (e.g., '(i)', '(iv)', '(3)', '(a)').

        Args:
            updated_requirement (dict): The updated requirement containing 'text'.
            original_requirement_list (list): List of dictionaries, each containing 'text' and 'identifier'.

        Returns:
            dict or None: The matching original requirement if found, otherwise None.
        """

        def remove_leading_parens(text):
            """Removes leading parenthetical identifiers (e.g., '(i)', '(3)', '(iv)', '(a)')"""
            return re.sub(r"^\(\w+\)\s*", "", text).strip()  # Remove (identifier) at the beginning

        updated_text = remove_leading_parens(updated_requirement["text"])  # Normalize updated text

        for original in original_requirement_list:
            original_text = remove_leading_parens(original["text"])  # Normalize original text
            if updated_text == original_text:  # Compare without the leading identifier
                return original  # Return the matching original requirement

        return None  # No match found

    def _create_exact_match_change_statement(self, updated_requirement):
        return {
            "original_text": updated_requirement["text"],
            "updated_text": updated_requirement["text"],
            "updated_identifier": updated_requirement["identifier"],
            "original_identifier": updated_requirement["identifier"],
            "changed_text": "No changes detected",
            "change_statement": "No changes detected",
            "semantic_match": True,
            "impact": "None",
        }

    def _create_reordered_change_statement(self, updated_requirement, original_requirement):
        return {
            "original_text": original_requirement["text"],
            "updated_text": updated_requirement["text"],
            "updated_identifier": updated_requirement["identifier"],
            "original_identifier": original_requirement["identifier"],
            "changed_text": f"""The identifier has changed from {original_requirement["identifier"]} 
                to {updated_requirement["identifier"]} but the text remains unchanged.""",
            "change_statement": f"""The rule has been reordered within the section, changing its
                identifier from {original_requirement["identifier"]} to {updated_requirement["identifier"]}
                while keeping the text unchanged.""",
            "semantic_match": True,
            "impact": "None",
        }

    def _update_user_story_personas(
        self, unique_statement_id: int, user_stories: list, document_id: int
    ):
        """
        Updates generated user stories by replacing the persona with the best matching one.
        """
        # Populate _persona_mapping only if it's None
        if self._persona_mapping is None:
            self._persona_mapping = self.content_accessor.get_personas_for_unique_statements(
                document_id
            )

        persona_list = self._persona_mapping.get(unique_statement_id, [])

        if not persona_list:
            self.logger.warning(
                "No personas found for Unique Statement ID %s. User stories will retain the default persona.",
                unique_statement_id,
            )
            return user_stories

        # Extract persona names for logging
        persona_names = [p["name"] for p in persona_list]

        self.logger.debug(
            "Retrieved %d personas for Unique Statement ID %s: %s",
            len(persona_list),
            unique_statement_id,
            persona_names,
        )

        updated_user_stories = self.text_extractor.update_user_stories_with_personas(
            user_stories, persona_list
        )

        self.logger.debug(
            "Updated user stories for Unique Statement ID %s: %s",
            unique_statement_id,
            updated_user_stories,
        )

        return updated_user_stories
