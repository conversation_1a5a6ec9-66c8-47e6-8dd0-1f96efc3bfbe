"""
This module handles the extraction and processing of textual information including requirements,
authorizations, opportunities, procedure changes, organizational changes, definitions, subtopics,
and other related artifacts from source documents. It uses OpenAI's GPT-based models to generate
meaningful insights from the extracted text for further analysis.
"""

import json
import logging
import re
from typing import List

from langfuse.decorators import langfuse_context, observe

from accessor.ai import <PERSON>Accessor
from accessor.content import ContentAccessor
from shared.enums.statement_type_enum import StatementTypeEnum
from shared.models.statement import Statement
from shared.models.subtopic import Subtopic
from shared.models.topic import Topic

from .data_access import DataAccess


class TextExtractor:
    """
    A class used to extract and process text from documents. The `TextExtractor` class
    is responsible for tasks like extracting subtopics, requirements, authorizations, and
    definitions, as well as generating acceptance criteria and test cases for requirements.
    The class leverages OpenAI's GPT models to perform these operations.
    """

    def __init__(self, logger_name):
        self.logger = logging.getLogger(logger_name)
        self.data_access = DataAccess()
        self.content_accessor = ContentAccessor(logger_name)
        self.chunk_tracker = {}  # Central tracking dictionary for processed chunks
        self.statement_type_mapping = {
            StatementTypeEnum.REQUIREMENT.value: "requirements",
            StatementTypeEnum.OPPORTUNITY.value: "opportunities",
            StatementTypeEnum.AUTHORIZATION.value: "authorizations",
        }
        self.ai_accessor = AiAccessor

    def extract_statement_text_into_list(self, statements: List[Statement]) -> List[str]:
        """
        Extract text content from a list of Statement objects.
        """
        return [statement.text for statement in statements]  # type: ignore

    def assemble_subtopics(self, subtopics: List[Subtopic]) -> List[str]:
        """
        Assemble subtopics into a list of formatted strings.
        """
        return [f"{subtopic.name}::{subtopic.description}" for subtopic in subtopics]

    def assemble_subtopics_with_topics(self, subtopics: List[Subtopic]) -> List[str]:
        """
        Assemble subtopics and their associated topics into a list of formatted strings.
        Retrieves the corresponding Topic object for each Subtopic using its topic ID.
        """
        result = []
        for subtopic in subtopics:
            topic = self.data_access.get_topic_by_id(subtopic.topic_id)  # type: ignore
            combined_description = topic.description + " " + subtopic.description
            result.append(subtopic.name + "::" + combined_description)
        return result

    def assemble_topics(self, topics: List[Topic]) -> List[str]:
        """
        Assemble topics into a list of formatted strings.
        Formats each Topic object into a string combining its name and description,
        separated by "::".
        """
        return [f"{topic.name}::{topic.description}" for topic in topics]

    @observe(as_type="generation")
    def extract_statements(
        self, node_text: str, chunk_header: str, persona: str
    ) -> dict[str, dict]:
        """
        Step 1 of the Multi-Step Prompting Process
        Extracts requirements, authorizations, and opportunities from a text node.

        Args:
            node_text (str): The text of the node to evaluate.
            chunk_header (str): The header of the chunk where the text resides.
            persona (str): The persona to tailor the extraction process.

        Returns:
            dict[str, dict]: A dictionary with extracted statements grouped by type.
        """
        try:
            # Define the statement types to extract
            statement_types = list(self.statement_type_mapping.values())

            prompt_name = "step1_extract_statement_type_info"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "chunk_header": chunk_header,
                    "persona": persona,
                    "statement_types": ", ".join(statement_types)
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            extracted_info = json.loads(response_text)

            return {st: extracted_info.get(st, {"Statements": []}) for st in statement_types}

        except Exception as e:
            self.logger.error("Error in statement extraction: %s", e)
            return {st: {"Statements": []} for st in self.statement_type_mapping.values()}

    @observe(as_type="generation")
    def filter_statements_for_persona(
        self, requirements: dict, authorizations: dict, opportunities: dict, persona: str
    ) -> dict[str, dict]:
        """
        Step 2 of the Multi-Step Prompting Process
        Filters requirements, authorizations, and opportunities for a specific persona.

        Args:
            statements (dict[str, dict]): A dictionary containing statements grouped by type.
                Example:
                {
                    "requirements": {"Statements": [...]},
                    "authorizations": {"Statements": [...]},
                    "opportunities": {"Statements": [...]}
                }
            persona (str): The persona for which to filter statements.

        Returns:
            dict[str, dict]: A dictionary with filtered statements grouped by type.
        """
        try:
            # Define the statement types to extract
            statement_types = list(self.statement_type_mapping.values())

            prompt_name = "step2_filter_statements_for_persona"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "requirements": requirements,
                    "authorizations": authorizations,
                    "opportunities": opportunities,
                    "persona": persona
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            filtered_statements = json.loads(response_text)

            return {st: filtered_statements.get(st, {"Statements": []}) for st in statement_types}

        except Exception as e:
            self.logger.error("Error filtering statements for persona: %s", e)
            return {st: {"Statements": []} for st in self.statement_type_mapping.values()}

    @observe(as_type="generation")
    def generate_table_context_statements_for_persona(
        self,
        requirements: dict,
        authorizations: dict,
        opportunities: dict,
        persona: str,
        chunk_text: str,
    ) -> dict[str, dict]:
        """
        Step 3 of the Multi-Step Prompting Process
        Adds table context to requirements, authorizations, and opportunities
        for a given persona based on the provided data and chunk text.

        Args:
            statements (dict[str, dict]): A dictionary containing statements grouped by type.
                Example:
                {
                    "requirements": {"Statements": [...]},
                    "authorizations": {"Statements": [...]},
                    "opportunities": {"Statements": [...]}
                }
            persona (str): The persona for which the context is being generated.
            chunk_text (str): The text passage to analyze for context.

        Returns:
            dict[str, dict]: A dictionary containing contextualized statements grouped by type.
        """
        try:
            # Prepare the input data for the prompt
            statements = {
                "requirements": requirements,
                "authorizations": authorizations,
                "opportunities": opportunities,
            }
            prompt_name = "step3_table_context_statements_for_persona"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    **statements,
                    "persona": persona,
                    "chunk_text": chunk_text
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            contextualized_statements = json.loads(response_text)

            return {st: contextualized_statements.get(st, {"Statements": []}) for st in statements}

        except Exception as e:
            self.logger.error("Error retrieving table context statements for persona: %s", e)
            return {st: {"Statements": []} for st in statements}

    @observe(as_type="generation")
    def pivot_statements_for_persona(
        self, requirements: dict, authorizations: dict, opportunities: dict, persona: str
    ) -> dict[str, dict]:
        """
        Step 4 of the Multi-Step Prompting Process
        Pivots requirements, authorizations, and opportunities for a given persona
        by generating recrafted outputs.

        Args:
            statements (dict[str, dict]): A dictionary containing statements grouped by type.
                Example:
                {
                    "requirements": {"Statements": [...]},
                    "authorizations": {"Statements": [...]},
                    "opportunities": {"Statements": [...]}
                }
            persona (str): The persona for which the statements are being contextualized.

        Returns:
            dict[str, dict]: A dictionary containing pivoted statements grouped by type.
        """
        try:
            statements = {
                "requirements": requirements,
                "authorizations": authorizations,
                "opportunities": opportunities,
            }
            prompt_name = "step4_pivot_statements_for_persona"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    **statements,
                    "persona": persona
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            pivoted_statements = json.loads(response_text)

            return {st: pivoted_statements.get(st, {"Statements": []}) for st in statements}

        except Exception as e:
            self.logger.error("Error pivoting statements for persona: %s", e)
            return {st: {"Statements": []} for st in statements}

    @observe(as_type="generation")
    def recraft_statement_conditions(
        self, node_text: str, chunk_header: str, statements: dict
    ) -> dict[str, dict]:
        """
        Step 5 of the Multi-Step Prompting Process

        Recrafts conditions for requirements, authorizations, and opportunities based on provided statements JSON.

        Args:
            statements (dict): JSON containing requirements, authorizations, and opportunities.
            node_text (str): The full text content of the chunk.
            chunk_header (str): The header of the chunk for contextual conditions.

        Returns:
            dict[str, dict]: A dictionary containing recrafted statements grouped by statement type.
        """
        try:
            prompt_name = "step5_recraft_statement_type_conditions"
            recrafted_statements = {}

            for statement_type, statements_dict in statements.items():
                if not statements_dict["Statements"]:  # Skip empty statement types
                    recrafted_statements[statement_type] = {"Statements": []}
                    continue

                updated_statements = []

                for statement in statements_dict["Statements"]:
                    # Use Langfuse prompt with built-in fallback handling
                    response_text = self.ai_accessor.get_completion(
                        prompt_name=prompt_name,
                        prompt_variables={
                            "node_text": node_text,
                            "chunk_header": chunk_header,
                            "statement": statement["Statement_Text"]
                        }
                    )

                    # Only update Statement_Text
                    statement["Statement_Text"] = response_text
                    updated_statements.append(statement)

                # Update the recrafted statements dictionary
                recrafted_statements[statement_type] = {"Statements": updated_statements}

            return recrafted_statements

        except Exception as e:
            self.logger.error("Error in recrafting statement conditions: %s", e)
            return {
                st: {"Statements": []} for st in ["requirements", "authorizations", "opportunities"]
            }

    @observe(as_type="generation")
    def extended_statement_recrafting(
        self, statement_type: str, statement: dict, persona: str
    ) -> dict:
        """
        Applies extended recrafting to a statement and returns the updated statement dictionary.

        Args:
            statement_type (str): The type of the statement (e.g., 'requirements', 'authorizations').
            statement (dict): The original statement dictionary containing Statement_Text.
            persona (str): The persona context.

        Returns:
            dict: The updated statement dictionary with recrafted Statement_Text.
        """
        try:
            prompt_name = "step6_extended_statement_type_recrafting"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "statement_type": statement_type,
                    "statement": statement["Statement_Text"],
                    "persona": persona
                }
            )


            # Update only the Statement_Text field and return the full statement
            statement["Statement_Text"] = response_text
            return statement

        except Exception as e:
            self.logger.error("Error in extended recrafting for %s: %s", statement_type, e)
            return statement  # Return the original statement if recrafting fails

    @observe(as_type="generation")
    def scan_for_requirements(self, requirements: dict) -> dict[str, list]:
        """
        Step 7 of the Multi-Step Prompting Process (requirements)
        Scans and processes the provided requirements to generate contextualized outputs.

        Args:
            requirements (dict): A dictionary of requirements to scan and process.

        Returns:
            dict[str, list]: A dictionary containing processed requirements grouped by type.
        """
        try:
            prompt_name = "scan_for_requirements"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "requirements": requirements
                }
            )

            return response_text

        except Exception as e:
            self.logger.error("Error scanning for requirements: %s", e)
            return {"Statements": []}

    @observe(as_type="generation")
    def scan_for_authorizations(self, authorizations: dict) -> dict[str, list]:
        """
        Step 7 of the Multi-Step Prompting Process (authorizations)
        Scans and processes the provided authorizations to generate contextualized outputs.

        Args:
            authorizations (dict): A dictionary of authorizations to scan and process.

        Returns:
            dict[str, list]: A dictionary containing processed authorizations grouped by type.
        """
        try:
            prompt_name = "scan_for_authorizations"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "authorizations": authorizations
                }
            )

            return response_text

        except Exception as e:
            self.logger.error("Error scanning for authorizations: %s", e)
            return {"Statements": []}

    @observe(as_type="generation")
    def scan_for_opportunities(self, opportunities: dict) -> dict[str, list]:
        """
        Step 7 of the Multi-Step Prompting Process (opportunities)
        Scans and processes the provided opportunities to generate contextualized outputs.

        Args:
            opportunities (dict): A dictionary of opportunities to scan and process.

        Returns:
            dict[str, list]: A dictionary containing processed opportunities grouped by type.
        """
        try:
            prompt_name = "scan_for_opportunities"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "opportunities": opportunities
                }
            )

            return response_text

        except Exception as e:
            self.logger.error("Error scanning for opportunities: %s", e)
            return {"Statements": []}

    def extract_statements_from_chunk(self, chunk_id: str, persona: str) -> dict[str, dict]:
        """
        Extracts and processes requirements, authorizations, and opportunities
        (multi-step) and non-multi-step statement types from a given chunk for a specific persona.
        """
        if chunk_id in self.chunk_tracker:
            self.logger.info("Chunk %s already processed.", chunk_id)
            return {
                key: {"Statements": []}
                for key in [
                    "requirements",
                    "authorizations",
                    "opportunities",
                    "procedure_changes",
                    "organizational_changes",
                    "definitions",
                ]
            }

        detailed_chunk = self.content_accessor.get_chunk_by_id(chunk_id)
        chunk_text = detailed_chunk.text_content
        chunk_header = detailed_chunk.chunk_metadata["header_name"]

        # Initialize results
        results = {
            key: {"Statements": []}
            for key in [
                "requirements",
                "authorizations",
                "opportunities",
                "procedure_changes",
                "organizational_changes",
                "definitions",
            ]
        }

        # Step 1 to 5: Extract, filter, contextualize, pivot, and recraft statements
        raw_statements = self.extract_statements(
            node_text=chunk_text, chunk_header=chunk_header, persona=persona
        )
        self.logger.info("Step 1 Complete: Extracted statement type info for chunk %s.", chunk_id)
        filtered_statements = self.filter_statements_for_persona(**raw_statements, persona=persona)
        self.logger.info(
            "Step 2 Complete: Filtered statement type for persona for chunk %s.", chunk_id
        )
        contextualized_statements = self.generate_table_context_statements_for_persona(
            **filtered_statements, persona=persona, chunk_text=chunk_text
        )
        self.logger.info(
            "Step 3 Complete: Extracted table context for statement type for chunk %s.", chunk_id
        )
        pivoted_statements = self.pivot_statements_for_persona(
            **contextualized_statements, persona=persona
        )
        self.logger.info("Step 4 Complete: Pivoted statements for persona for chunk %s.", chunk_id)
        recrafted_statements = self.recraft_statement_conditions(
            node_text=chunk_text, chunk_header=chunk_header, statements=pivoted_statements
        )
        self.logger.info("Step 5 Complete: Recrafted statements for chunk %s.", chunk_id)

        # Step 6: Extended recrafting
        for statement_type, statements in recrafted_statements.items():
            extended_statements = []

            for statement in statements["Statements"]:
                recrafted_statement = self.extended_statement_recrafting(
                    statement_type=statement_type, statement=statement, persona=persona
                )
                if recrafted_statement and "do X" not in recrafted_statement:
                    extended_statements.append(recrafted_statement)
                else:
                    self.logger.warning(
                        "Invalid extended statement detected: %s", recrafted_statement
                    )

            if extended_statements:
                results[statement_type]["Statements"] = extended_statements

        self.logger.info("Step 6 Complete: Applied extended recrafting for chunk %s.", chunk_id)

        # Step 7: Scan for valid statements
        validated_statements = {
            "requirements": {
                "Statements": self.scan_for_requirements(results["requirements"]["Statements"])
            },
            "authorizations": {
                "Statements": self.scan_for_authorizations(results["authorizations"]["Statements"])
            },
            "opportunities": {
                "Statements": self.scan_for_opportunities(results["opportunities"]["Statements"])
            },
        }
        self.logger.info("Step 7 Complete: Scanned for valid statements in chunk %s.", chunk_id)

        # Extract non-multi-step statement types
        results["procedure_changes"]["Statements"] = self.extract_procedure_changes(
            node_text=chunk_text
        )
        results["organizational_changes"]["Statements"] = self.extract_organizational_changes(
            node_text=chunk_text
        )
        results["definitions"]["Statements"] = self.extract_definitions(node_text=chunk_text)

        self.logger.info(
            "Step 8 Complete: Extracted non-multi-step statements for chunk %s.", chunk_id
        )

        # Filter out empty statement types
        results = {key: value for key, value in results.items() if value["Statements"]}

        # Include validated statements separately
        results["validated"] = validated_statements

        return results

    def process_chunk_for_artifact_statement_types(
        self, chunk_id: str, persona: str
    ) -> dict[str, dict]:
        """
        Processes a single chunk for all statement types: multistep and non-multistep.

        Args:
            chunk_id (str): The ID of the chunk.
            persona (str): The persona context.

        Returns:
            dict[str, dict]: A dictionary with processed statements for all types.
        """
        results = self.extract_statements_from_chunk(chunk_id, persona)
        self.logger.info("Processed all statement types for chunk %s.", chunk_id)

        return results

    @observe(as_type="generation")
    def extract_procedure_changes(self, node_text: str) -> dict[str, list]:
        try:
            prompt_name = "extract_procedure_changes"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            extracted_procedure_changes = [s.strip().strip('"') for s in response_text.split("|||")]

            # return [] if the result is an empty list
            if not extracted_procedure_changes or extracted_procedure_changes == []:
                return []

            return extracted_procedure_changes

        except Exception as e:
            self.logger.error("Error extracting procedure changes: %s", str(e))
            return []

    @observe(as_type="generation")
    def extract_organizational_changes(self, node_text: str) -> dict[str, list]:
        try:
            prompt_name = "extract_organizational_changes"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            extracted_organizational_changes = [
                s.strip().strip('"') for s in response_text.split("|||")
            ]

            # return [] if the result is an empty list
            if not extracted_organizational_changes or extracted_organizational_changes == []:
                return []

            return extracted_organizational_changes

        except Exception as e:
            self.logger.error("Error extracting organizational changes: %s", str(e))
            return []

    @observe(as_type="generation")
    def extract_definitions(self, node_text: str) -> dict[str, list]:
        try:
            prompt_name = "extract_definitions"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            extracted_definitions = [s.strip().strip('"') for s in response_text.split("|||")]

            # return [] if the result is an empty list
            if not extracted_definitions or extracted_definitions == []:
                return []

            return extracted_definitions

        except Exception as e:
            self.logger.error("Error extracting definitions: %s", str(e))
            return []

    @observe(as_type="generation")
    def extract_change_statements(self, node_text: str) -> dict[str, list[dict]]:
        """
        Extracts and reformats change statements directly from the provided chunk text.

        Args:
            node_text (str): The JSON-encoded text content of the chunk.

        Returns:
            dict[str, list[dict]]: A dictionary containing the extracted and reformatted change statements.
        """
        try:
            # Parse the node_text assuming it's a JSON string
            change_statements = json.loads(node_text)

            # Initialize the formatted results
            formatted_statements = []

            for idx, statement in enumerate(change_statements, start=1):
                # Validate and reformat each statement
                if not all(
                    key in statement
                    for key in (
                        "original_text",
                        "updated_text",
                        "identifier",
                        "changed_text",
                        "change_statement",
                        "impact",
                    )
                ):
                    self.logger.warning(
                        "Skipping statement %d due to missing keys: %s", idx, statement
                    )
                    continue

                formatted_statement = {
                    "original_text": statement["original_text"],
                    "updated_text": statement["updated_text"],
                    "identifier": statement["identifier"],
                    "changed_text": statement["changed_text"],
                    "change_statement": statement["change_statement"],
                    "impact": statement["impact"],
                }
                formatted_statements.append(formatted_statement)

            self.logger.info(
                "Extracted and formatted %d change statements from the provided chunk text.",
                len(formatted_statements),
            )

            # Return the formatted change statements
            return {"change_statements": formatted_statements}

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse chunk text as JSON: %s", e)
            return {"change_statements": []}

        except Exception as e:
            self.logger.error("Unexpected error while extracting change statements: %s", e)
            return {"change_statements": []}

    @observe(as_type="generation")
    def extract_subtopics(self, node_text: str, subtopics: str) -> dict[str, list]:
        try:
            prompt_name = "extract_subtopics"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "subtopics": subtopics
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            extracted_topics = [s.strip().strip('"') for s in response_text.split("|||")]

            return extracted_topics

        except Exception as e:
            self.logger.error("Error extracting subtopics: %s", str(e))
            return []

    @observe(as_type="generation")
    def extract_topics(self, node_text: str, topics: dict[list]) -> dict[str, list]:
        try:
            prompt_name = "extract_topics"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "topics": topics
                }
            )

            # Convert the string into a list of strings
            extracted_topics = [s.strip().strip('"') for s in response_text.split("|||")]

            return extracted_topics

        except Exception as e:
            self.logger.error("Error extracting topics: %s", str(e))
            return []

    @observe()
    def reevaluate_subtopic(
        self, original_subtopic: str, node_text: str, subtopics: str
    ) -> dict[str, list]:
        try:
            prompt_name = "reevaluate_subtopic"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "original_subtopic": original_subtopic,
                    "node_text": node_text,
                    "subtopics": subtopics
                }
            )

            # Convert the string into a list of strings
            extracted_topics = [s.strip().strip('"') for s in response_text.split("|||")]

            return extracted_topics

        except Exception as e:
            self.logger.error("Error extracting subtopics: %s", str(e))
            return []

    @observe()
    def is_statement_relevant_to_chunk_topics(
        self, statement_text: str, topic_list: List[Topic]
    ) -> dict[str, list]:
        try:
            prompt_name = "is_statement_relevant_to_chunk_topics"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "statement_text": statement_text,
                    "topic_list": topic_list
                }
            )

            response_text = self._clean_json_response(response_text)

            return response_text

        except Exception as e:
            self.logger.error(
                "Error identifying if statement is relevant to chunk topics: %s", str(e)
            )
            return []

    @observe(as_type="generation")
    def identify_best_topic_for_orphan_statement(
        self,
        statement_text: str,
        chunk_text: str,
        chunk_header: str,
        subject_matter: str,
    ) -> str:
        try:
            prompt_name = "identify_best_topic_for_orphan_statement"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "statement_text": statement_text,
                    "chunk_text": chunk_text,
                    "chunk_header": chunk_header,
                    "subject_matter": subject_matter
                }
            )

            response_text = self._clean_json_response(response_text)

            return response_text

        except Exception as e:
            self.logger.error("Error identifying best topic for orphan statement: %s", str(e))
            return []

    @observe()
    def identify_statement_chunk_source_candidates(
        self, statement_to_evaluate: str, candidate_statements: list
    ) -> list[dict]:
        """
        Compare a given statement with a list of candidate statements to identify high-probability matches.

        This method uses OpenAI's language model to compare the meaning of a `statement_to_evaluate`
        with a provided list of `candidate_statements`. If one or more matches are found, it returns
        the chunk ID and the corresponding candidate statement(s) in valid JSON format. In case of an
        error, it will retry up to the specified number of attempts.

        Args:
            statement_to_evaluate (str): The primary statement to evaluate against the list of candidate statements.
            candidate_statements (list): A list of candidate statements (each with a chunk ID and statement text)
                to compare against the `statement_to_evaluate`.

        Returns:
            list[dict] or None: If successful, returns a list of dictionaries, each containing:
                - "chunk_id" (str): The ID of the matched chunk.
                - "statement" (str): The matched candidate statement.
            If no matches are found or if an error occurs, returns `None`.

        Example:
            [{"chunk_id": "1234", "statement": "This is the best match"}]
        """
        try:
            prompt_name = "identify_statement_chunk_source_candidates"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "statement_to_evaluate": statement_to_evaluate,
                    "candidate_statements": candidate_statements
                }
            )

            match_data = self._parse_chunk_id_and_statement(response_text)
            if (
                match_data
                and isinstance(match_data, list)
                and all("chunk_id" in entry and "statement" in entry for entry in match_data)
            ):
                return match_data
            return (
                None  # Not expected to always find a match. Given llm config, no sense in retrying
            )

        except Exception as e:
            self.logger.error("Error identifying statement chunk source candidates: %s", str(e))
            return None

    @observe()
    def identify_highest_probability_statement_chunk_source(
        self, statement_to_evaluate: str, candidate_statements: list
    ) -> str:
        """
        Identify the highest probability match for a given statement from a list of candidate statements.

        This method uses OpenAI's language model to compare the meaning of a `statement_to_evaluate`
        with a provided list of `candidate_statements`. It returns the chunk ID of the highest probability
        match based on the comparison. If no high probability match is found, it returns 'None'.

        Args:
            statement_to_evaluate (str): The primary statement to evaluate against the list of candidate statements.
            candidate_statements (list): A list of candidate statements (each with a chunk ID and statement text)
                to compare against the `statement_to_evaluate`.

        Returns:
            str or None: If successful, returns the chunk ID of the highest probability match as a string.
            If no match is found or if an error occurs, returns `None`.

        Example:
            "1234"
        """
        try:
            prompt_name = "identify_highest_probability_statement_chunk_source"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "statement_to_evaluate": statement_to_evaluate,
                    "candidate_statements": candidate_statements
                }
            )

            match_data = self._parse_chunk_id_and_statement(response_text)

            # Check if the response contains valid match data
            if match_data and match_data != "None":
                return match_data
            self.logger.debug("No statement chunk source found.")
            return None  # given llm config, no point in retrying.

        except Exception as e:
            self.logger.error("Error identifying statement chunk source: %s", str(e))
            return None

    def _parse_chunk_id_and_statement(self, response_text):
        """
        Parses the response from ChatGPT into a chunk ID and corresponding statement.
        Expects a valid JSON structure: {"chunk_id": "<chunk_id>", "statement": "<candidate_statement>"}
        """
        try:
            return json.loads(response_text)
        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse JSON response: %s", e)
            return None

    @observe(as_type="generation")
    def assemble_requirements(
        self,
        node_text: str,
        requirement_statements: list,
        existing_requirements: list,
        subtopic: str,
    ) -> list:
        try:
            prompt_name = "assemble_requirements"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "requirement_statements": requirement_statements,
                    "existing_requirements": existing_requirements,
                    "subtopic": subtopic
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            new_requirements = [s.strip().strip('"') for s in response_text.split("|||")]

            # Combine the new relevant requirements with the existing ones, avoiding duplicates
            if response_text:
                if existing_requirements:
                    updated_requirements = existing_requirements + [
                        req for req in new_requirements if req not in existing_requirements
                    ]
                else:
                    updated_requirements = [
                        req for req in new_requirements if req not in existing_requirements
                    ]
            else:
                updated_requirements = existing_requirements

            return updated_requirements

        except Exception as e:
            self.logger.error("Error assembling requirements: %s", str(e))

    @observe(as_type="generation")
    def assemble_requirements_from_change_statements(
        self,
        chunk_text: str,
        change_statements: list,
        chunk_subtopics: str,
    ) -> list:
        try:
            prompt_name = "assemble_requirements_from_change_statements"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "chunk_text": chunk_text,
                    "change_statements": change_statements,
                    "chunk_subtopics": chunk_subtopics
                }
            )

            response_text = self._clean_json_response(response_text)

            try:
                # Parse the cleaned JSON string into a Python object
                new_requirements = json.loads(response_text)
                return new_requirements
            except json.JSONDecodeError as e:
                # Raise an error if JSON parsing fails
                raise ValueError(f"Invalid JSON: {e}")

        except ImportError as e:
            self.logger.error("Error assembling requirements: %s", str(e))

    @observe(as_type="generation")
    def assemble_authorizations(
        self,
        node_text: str,
        authorization_statements: list,
        existing_authorizations: list,
        subtopic: str,
    ) -> list:
        try:
            prompt_name = "assemble_authorizations"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "authorization_statements": authorization_statements,
                    "existing_authorizations": existing_authorizations,
                    "subtopic": subtopic
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            new_authorizations = [s.strip().strip('"') for s in response_text.split("|||")]

            # Combine the new relevant requirements with the existing ones, avoiding duplicates
            if response_text:
                if existing_authorizations:
                    updated_authorizations = existing_authorizations + [
                        req for req in new_authorizations if req not in existing_authorizations
                    ]
                else:
                    updated_authorizations = [
                        req for req in new_authorizations if req not in existing_authorizations
                    ]
            else:
                updated_authorizations = existing_authorizations

            return updated_authorizations

        except Exception as e:
            self.logger.error("Error assembling authorizations: %s", str(e))

    @observe(as_type="generation")
    def assemble_opportunities(
        self,
        node_text: str,
        opportunity_statements: list,
        existing_opportunities: list,
        subtopic: str,
    ) -> list:
        try:
            prompt_name = "assemble_opportunities"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "opportunity_statements": opportunity_statements,
                    "existing_opportunities": existing_opportunities,
                    "subtopic": subtopic
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            new_opportunities = [s.strip().strip('"') for s in response_text.split("|||")]

            # Combine the new relevant requirements with the existing ones, avoiding duplicates
            if response_text:
                if existing_opportunities:
                    updated_opportunities = existing_opportunities + [
                        req for req in new_opportunities if req not in existing_opportunities
                    ]
                else:
                    updated_opportunities = [
                        req for req in new_opportunities if req not in existing_opportunities
                    ]
            else:
                updated_opportunities = existing_opportunities

            return updated_opportunities

        except Exception as e:
            self.logger.error("Error assembling opportunities: %s", str(e))

    @observe(as_type="generation")
    def assemble_procedure_changes(
        self,
        node_text: str,
        procedure_change_statements: list,
        existing_procedure_changes: list,
        subtopic: str,
    ) -> list:
        try:
            prompt_name = "assemble_procedure_changes"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "procedure_change_statements": procedure_change_statements,
                    "existing_procedure_changes": existing_procedure_changes,
                    "subtopic": subtopic
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            new_procedure_changes = [s.strip().strip('"') for s in response_text.split("|||")]

            # Combine the new relevant authorizations with the existing ones, avoiding duplicates
            if response_text:
                if existing_procedure_changes:
                    updated_procedure_changes = existing_procedure_changes + [
                        req
                        for req in new_procedure_changes
                        if req not in existing_procedure_changes
                    ]
                else:
                    updated_procedure_changes = [
                        req
                        for req in new_procedure_changes
                        if req not in existing_procedure_changes
                    ]
            else:
                updated_procedure_changes = existing_procedure_changes

            return updated_procedure_changes

        except Exception as e:
            self.logger.error("Error assembling procedure changes: %s", str(e))

    @observe(as_type="generation")
    def assemble_organizational_changes(
        self,
        node_text: str,
        organizational_change_statements: list,
        existing_organizational_changes: list,
        subtopic: str,
    ) -> list:
        try:
            prompt_name = "assemble_organizational_changes"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "node_text": node_text,
                    "organizational_change_statements": organizational_change_statements,
                    "existing_organizational_changes": existing_organizational_changes,
                    "subtopic": subtopic
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            new_organizational_changes = [s.strip().strip('"') for s in response_text.split("|||")]

            # Combine the new relevant authorizations with the existing ones, avoiding duplicates
            if response_text:
                if existing_organizational_changes:
                    updated_organizational_changes = existing_organizational_changes + [
                        req
                        for req in new_organizational_changes
                        if req not in existing_organizational_changes
                    ]
                else:
                    updated_organizational_changes = [
                        req
                        for req in new_organizational_changes
                        if req not in existing_organizational_changes
                    ]
            else:
                updated_organizational_changes = existing_organizational_changes

            return updated_organizational_changes

        except Exception as e:
            self.logger.error("Error assembling organizational changes: %s", str(e))

    @observe(as_type="generation")
    def assemble_procedure_changes_for_requirement(
        self,
        domain_name: str,
        domain_subtopic_context: str,
        requirement_text: str,
        subtopic_text: str,
    ) -> list:
        try:
            prompt_name = "create_procedure_changes_from_document_domain_comparison"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "source_domain": domain_name,
                    "domain_subtopic_context": domain_subtopic_context,
                    "requirement_text": requirement_text,
                    "persona": "mortgage servicer"
                }
            )

            response_text = self._clean_json_response(response_text)

            try:
                new_requirements = json.loads(response_text).get("differences_software_not_PnP", [])
            except json.JSONDecodeError:
                print("Error parsing JSON.. %s", str(response_text))

            return new_requirements

        except ImportError as e:
            self.logger.error("Error assembling requirements: %s", str(e))

    @observe(as_type="generation")
    def extract_updated_requirements(
        self,
        updated_requirement_content: str,
    ) -> list:
        try:
            prompt_name = "extract_updated_requirements"
            # Initialize the variable to ensure it is always defined
            updated_requirements = []

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "updated_requirement_content": updated_requirement_content,
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            updated_requirements = json.loads(response_text)

        except Exception as e:
            self.logger.error("Error extracting change statements: %s", str(e))
            # Optionally, you can re-raise the exception or handle it differently
            raise

        return updated_requirements

    @observe(as_type="generation")
    def extract_original_requirements(
        self,
        target_section_text: str,
    ) -> list:
        try:
            prompt_name = "extract_original_requirements"
            original_requirements = []

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "target_section_text": target_section_text,
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            original_requirements = json.loads(response_text)

        except Exception as e:
            self.logger.error("Error extracting original requirements: %s", str(e))
            # Optionally, you can re-raise the exception or handle it differently
            raise

        return original_requirements

    @observe(as_type="generation")
    def identify_requirement_matches_and_differences(
        self, updated_requirement: any, original_requirements: list
    ) -> any:
        try:
            prompt_name = "identify_requirement_matches_and_differences"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "updated_requirement": updated_requirement,
                    "original_requirements": original_requirements,
                }
            )

            # Response clean-up
            if response_text.startswith("```json") and response_text.endswith("```"):
                response_text = response_text[7:-3].strip()
            else:
                match = re.search(r"```json\n(.*?)\n```", response_text, re.DOTALL)
                response_text = match.group(1) if match else None

            try:
                new_change_statements = json.loads(response_text)
            except json.JSONDecodeError as e:
                # Log detailed error and the invalid JSON for troubleshooting
                self.logger.error("Error parsing JSON: %s", response_text)
                raise ValueError("Invalid JSON response received from OpenAI.") from e

        except Exception as e:
            self.logger.error("Error extracting change statements: %s", str(e))
            # Optionally, you can re-raise the exception or handle it differently
            raise

        return new_change_statements

    @observe(as_type="generation")
    def identify_deleted_requirement(
        self, updated_requirements: list, original_requirement: any
    ) -> list:
        try:
            prompt_name = "identify_deleted_requirement"
            deleted_requirements = []

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "updated_requirements": updated_requirements,
                    "original_requirement": original_requirement,
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            deleted_requirements = json.loads(response_text)

        except Exception as e:
            self.logger.error("Error extracting change statements: %s", str(e))
            # Optionally, you can re-raise the exception or handle it differently
            raise

        return deleted_requirements

    @observe(as_type="generation")
    def create_user_stories_for_requirement(
        self, unique_statement_text: str, persona: str
    ) -> dict[str, list]:
        try:
            prompt_name = "create_user_stories_for_requirement"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "unique_statement_text": unique_statement_text,
                    "persona": persona
                }
            )

            response_text = response_text[1:-1]

            # Convert the string into a list of strings
            new_user_stories = [s.strip().strip('"') for s in response_text.split("|||")]

            return new_user_stories

        except Exception as e:
            self.logger.error("Error creating User Stories with Business Persona: %s", e)
            return []

    @observe(as_type="generation")
    def update_user_stories_with_personas(self, user_stories: list, personas: list) -> list:
        """
        Calls a second Langfuse prompt to update User Stories with dynamically assigned personas.
        """
        try:
            prompt_name = "update_user_stories_with_personas"
            updated_user_stories = []

            for user_story in user_stories:
                # Use Langfuse prompt with built-in fallback handling
                response_text = self.ai_accessor.get_completion(
                    prompt_name=prompt_name,
                    prompt_variables={
                        "user_story": user_story,
                        "available_personas": personas
                    }
                )

                updated_user_story = response_text.strip()
                updated_user_stories.append(updated_user_story)

                self.logger.debug("Updated User Story with Persona: %s", updated_user_story)

        except Exception as e:
            self.logger.error("Error updating persona in User Story: %s", e)
            updated_user_stories.append(
                user_story
            )  # Fall back to original story if an error occurs

        return updated_user_stories

    @observe(as_type="generation")
    def create_ac_for_user_story(
        self,
        helpful_context: str,
        user_story_text: str,
        requirement_text: str,
    ) -> dict[str, list]:
        try:
            prompt_name = "create_ac_for_user_story"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "helpful_context": helpful_context,
                    "user_story_text": user_story_text,
                    "requirement_text": requirement_text
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            ac_for_user_story = json.loads(response_text)

            return ac_for_user_story

        except Exception as e:
            self.logger.error("Error creating acceptance criteria: %s", e)
            return {"acceptanceCriteria": []}

    @observe(as_type="generation")
    def create_tc_for_ac(
        self,
        helpful_context: str,
        acceptance_criteria_text: str,
        requirement_text: str,
    ) -> dict[str, list]:
        try:
            prompt_name = "create_tc_for_ac"

            # Use Langfuse prompt with built-in fallback handling
            response_text = self.ai_accessor.get_completion(
                prompt_name=prompt_name,
                prompt_variables={
                    "helpful_context": helpful_context,
                    "acceptance_criteria_text": acceptance_criteria_text,
                    "requirement_text": requirement_text
                }
            )

            response_text = self._clean_json_response(response_text)

            # Parse the response and return structured results
            tc_for_ac = json.loads(response_text)

            return tc_for_ac

        except Exception as e:
            self.logger.error("Error creating test cases: %s", e)
            return {"testCases": []}

    def extract_chunk_id(self, raw_chunk_id):
        """
        Extract an integer chunk ID from a raw string. TODO: Move to helper/utility classes.

        Args:
            raw_chunk_id (str): The raw string potentially containing a chunk ID.

        Returns:
            int or None: The extracted integer chunk ID, or None if not found or invalid input.
        """
        if not raw_chunk_id:
            self.logger.error("raw_chunk_id is None")
            return None

        if not isinstance(raw_chunk_id, str):
            self.logger.error("raw_chunk_id is not a string: %s", type(raw_chunk_id))
            return None
        match = re.search(r"\b\d+\b", raw_chunk_id)
        if match:
            return int(match.group())
        self.logger.error("No integer found in raw_chunk_id: %s", raw_chunk_id)
        return None

    def _clean_json_response(self, response_text: str) -> str:
        """
        Cleans and extracts valid JSON from the API response.

        This method removes markdown syntax (```json ... ```) and attempts to fix
        incomplete JSON responses by adding missing closing brackets.
        """
        try:
            # Trim whitespace
            response_text = response_text.strip()

            # Remove markdown if present
            if response_text.startswith("```json") and response_text.endswith("```"):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith("```json"):
                response_text = response_text[7:].strip()
            elif response_text.startswith("```"):
                response_text = response_text[3:].strip()

            # Remove outer triple quotes if present
            if response_text.startswith('"""') and response_text.endswith('"""'):
                response_text = response_text[3:-3].strip()

            # Remove trailing commas before closing brackets
            response_text = re.sub(r",\s*([\]}])", r"\1", response_text)

            # Count opening and closing brackets
            open_brackets = response_text.count("[")
            close_brackets = response_text.count("]")
            if open_brackets > close_brackets:
                response_text += "]" * (open_brackets - close_brackets)

            open_braces = response_text.count("{")
            close_braces = response_text.count("}")
            if open_braces > close_braces:
                response_text += "}" * (open_braces - close_braces)

            # Validate JSON to ensure it's well-formed
            json.loads(response_text)
            return response_text
        
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON is still malformed after cleanup: {e}")
