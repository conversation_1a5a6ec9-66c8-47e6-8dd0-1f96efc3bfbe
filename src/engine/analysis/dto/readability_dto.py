"""
Module representing readability metrics for a given text.
"""

from dataclasses import dataclass


@dataclass
class ReadabilityDTO:
    """
    Data Transfer Object for readability metrics.
    """

    flesch_kincaid_grade: float
    flesch_reading_ease: float
    reading_ease_descriptor: str
    text_standard: str
    word_count: int
    avg_sentence_length: float
    avg_word_length: float
    difficult_words: int
