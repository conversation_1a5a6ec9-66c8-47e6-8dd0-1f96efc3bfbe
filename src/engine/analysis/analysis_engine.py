"""
Responsible for performing various analysis operations.
"""

import logging

from readability import Readability
from textstat import textstat

from engine.analysis.dto.readability_dto import ReadabilityDTO


class AnalysisEngine:
    """
    Analyses various scenarious.
    """

    def __init__(self, logger_name):
        self.logger = logging.getLogger(logger_name)

    def analyze_readability(self, text: str) -> ReadabilityDTO:
        """
        Analyzes the readability of the provided text and returns a ReadabilityDTO with metrics.

        Parameters:
            text (str): The text to analyze.

        Returns:
            ReadabilityDTO: A data transfer object containing readability metrics.
        """
        word_count = textstat.lexicon_count(text, removepunct=True)
        if word_count < 100:
            return self._handle_short_text(word_count)

        readability_obj = Readability(text)
        fk_grade = float(readability_obj.flesch_kincaid().grade_level)
        flesch_results = readability_obj.flesch()
        flesch_score = float(flesch_results.score)

        avg_sentence_length = textstat.avg_sentence_length(text)
        avg_word_length = textstat.avg_letter_per_word(text)
        difficult_words = textstat.difficult_words(text)

        return ReadabilityDTO(
            flesch_kincaid_grade=float(fk_grade),
            flesch_reading_ease=float(flesch_score),
            reading_ease_descriptor=str(flesch_results.ease),
            text_standard=str(textstat.text_standard(text)),
            word_count=int(word_count),
            avg_sentence_length=float(avg_sentence_length),
            avg_word_length=float(avg_word_length),
            difficult_words=int(difficult_words),
        )

    def _handle_short_text(self, word_count: int) -> ReadabilityDTO:
        """
        Handles cases where the text is too short for readability analysis.

        Parameters:
            word_count (int): The number of words in the text.

        Returns:
            ReadabilityDTO: A data transfer object with default values for short texts.
        """
        return ReadabilityDTO(
            flesch_kincaid_grade=0.0,
            flesch_reading_ease=0.0,
            reading_ease_descriptor="Not enough text",
            text_standard="N/A",
            word_count=word_count,
            avg_sentence_length=0.0,
            avg_word_length=0.0,
            difficult_words=0,
        )
