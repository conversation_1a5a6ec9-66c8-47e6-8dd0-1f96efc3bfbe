from flask import Flask, request, jsonify
import logging
from threading import Thread
from llama_index_helper import LlamaIndexHelper

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

class ParsingEngine:
    def __init__(self, app):
        self.app = app
        self.llama_helper = LlamaIndexHelper()  # Initialize the helper
        self.setup_routes()

    def setup_routes(self):
        @self.app.route('/parse', methods=['POST'])
        def parse_documents():
            data = request.get_json()
            document_id = data.get('document_id')
            domain_id = data.get('domain_id')

            #Get document details with id.
            #TODO: Remove hard coding.
            path = "./data"

            #Get domain index with id.
            #TODO: Remove hard coding.
            index = "parsing-engine-test"

            thread = Thread(target=self.async_parse, args=(document_id, path, index))
            thread.start()
            return jsonify({"message": "Task started", "document_id": document_id, "domain_id": domain_id}), 202

    def async_parse(self, document_id, path, index):
        logging.info(f"Starting async parsing for path {path} and index {index}.")
        
        # Use the helper to process the document
        result = self.llama_helper.process_document(document_id, path, index)
        
        logging.info(f"Document {document_id} processed with result: {result}.")

app = Flask(__name__)
engine = ParsingEngine(app)

if __name__ == '__main__':
    app.run(debug=True, port=5000)
