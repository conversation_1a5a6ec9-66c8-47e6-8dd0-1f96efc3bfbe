"""
This module provides functionality to parse PDF documents into structured text nodes using
GPT-4 Vision. It includes the `DocumentComparisonParser` class, which processes PDF pages, extracts
text and metadata, and converts them into `TextNode` instances.
"""

import difflib
import json
import logging
import re

import fitz
from langfuse.decorators.langfuse_decorator import observe
from llama_index.core.schema import TextNode


from accessor.ai import AiAccessor
from accessor.document.dto.document_comparison_job_dto import DocumentComparisonJobDTO
from engine.parsing.llama_index_extensions.parser.pdf_parser import pdf_utils


# We skip page counters if they're in the format "3/45" near the bottom
FOOTER_COUNTER_PATTERNS = [re.compile(r"^\d+/\d+$")]


class DocumentComparisonParser:

    def __init__(self, logger_name: str = "DocumentComparisonParser"):
        self.logger = logging.getLogger(logger_name)
        self.ai_accessor = AiAccessor

    def generate_change_statement_chunks(self, dto: DocumentComparisonJobDTO) -> list[TextNode]:
        """
        Generates a list of TextNode objects representing meaningful textual changes between
        the original and updated versions of a PDF document.

        The method follows a multi-step process:
        1. **Text Extraction & Tokenization**: Extracts cleaned text from both the original
        and updated documents, then tokenizes the content for comparison.
        2. **Token Diffing**: Computes a token-level diff to identify additions, deletions,
        and changes between the two versions.
        3. **Context Mapping**: Splits text into sentences with positional offsets and aligns
        old and new sentences to provide surrounding context for each change.
        4. **Page Grouping**: Groups the identified changes based on their page number using
        metadata from the updated document.
        5. **Image Extraction**: Extracts images from each page of the updated document for
        visual reference.
        6. **Node Construction**: Converts grouped changes into a list of `TextNode` objects,
        which encapsulate the page, change type, text, and context for downstream processing.

        Parameters:
            dto (DocumentComparisonJobDTO): Data transfer object containing the original and updated
            document bytes and other metadata.

        Returns:
            list[TextNode]: A list of structured nodes representing the contextualized differences
            between the two documents.
        """

        # Step 1: Extract and tokenize old and new text
        old_text, _ = self._extract_cleaned_text_from_pdf(pdf_bytes=dto.original_document_bytes)
        old_tokens = self._simple_tokenize(old_text)

        new_text, new_pages_info = self._extract_cleaned_text_from_pdf(
            pdf_bytes=dto.document_bytes, do_page_offsets=True
        )
        new_tokens = self._simple_tokenize(new_text)

        # Step 2: Diff and group
        diff_result = self._diff_tokens(old_tokens, new_tokens)
        grouped = self._group_token_changes(diff_result)

        # Step 3: Sentence-level context mapping
        old_sentences = self._split_sentences_with_offsets(old_text)
        new_sentences = self._split_sentences_with_offsets(new_text)
        sentences = self._align_sentences_with_offsets(old_sentences, new_sentences)
        combined = self._combine_changes_with_context(grouped, new_pages_info, sentences)

        # Step 4: Group by page
        page_map = self._group_changes_by_page(combined)

        # Step 5: Extract images from the updated document
        doc = fitz.open("pdf", dto.document_bytes)
        page_images = self._pdf_page_to_image(doc, 1, -1)  # Process all pages
        doc.close()

        # Step 6: Build nodes with image data
        nodes = self._build_text_nodes_from_page_map(page_map, page_images)

        self.logger.debug("%s - Finished parsing nodes", self.__class__.__name__)
        return nodes

    def _pdf_page_to_image(self, doc, start_page, end_page, batch_size=5) -> list:
        """
        Converts PDF pages to images for a specified range and extracts bounding boxes.
        :param doc: The PDF document
        :param start_page: The starting page for processing
        :param end_page: The ending page for processing (-1 means all pages)
        :param batch_size: Number of pages to process at once to conserve memory
        :return: A list of tuples containing page numbers, image bytes, and bounding boxes
        """
        page_images = []
        total_pages = len(doc)
        if end_page == -1:
            end_page = total_pages

        # Process pages in batches to conserve memory
        for batch_start in range(start_page - 1, min(end_page, total_pages), batch_size):
            batch_end = min(batch_start + batch_size, min(end_page, total_pages))
            self.logger.debug(f"Processing PDF pages {batch_start+1}-{batch_end} as images")

            for page_number in range(batch_start, batch_end):
                page = doc.load_page(page_number)
                pix = page.get_pixmap(dpi=200)
                img_bytes = pix.tobytes("png")
                bbox = [page.rect.x0, page.rect.y0, page.rect.x1, page.rect.y1]
                page_images.append((page_number + 1, img_bytes, bbox))

                # Free memory immediately
                del pix
        return page_images

    def _extract_cleaned_text_from_pdf(self, pdf_bytes, do_page_offsets=False, threshold=50):
        """
        Opens the PDF and extracts a single big string for diffing.
        - Removes known header/footer patterns.
        - Skips page counters near the bottom (like '3/45').
        - Merges text blocks, preserving hyphen logic.
        If do_page_offsets=True, also returns page offset info for each page.
        """
        doc = fitz.open("pdf", pdf_bytes)
        page_texts = []
        pages_info = []

        for page_index in range(len(doc)):
            page = doc[page_index]
            blocks = page.get_text("blocks") or []

            # *** Sort blocks top->bottom, then left->right:
            blocks_sorted = sorted(blocks, key=lambda b: (b[1], b[0]))

            page_height = page.rect.height

            cleaned_blocks = []
            for b in blocks_sorted:
                x0, y0, x1, y1, btxt, btype, bno = b
                # Convert newlines -> space
                btxt = btxt.replace("\n", " ")
                cleaned_blocks.append((x0, y0, x1, y1, btxt, btype, bno))

            page_str = self._merge_page_blocks(cleaned_blocks, page_height, threshold=threshold)
            page_texts.append(page_str)

        combined = " ".join(page_texts)

        if do_page_offsets:
            # build pages_info
            offset = 0
            big_chunks = []
            for i, txt in enumerate(page_texts):
                start = offset
                end = start + len(txt)
                pages_info.append({"page_index": i, "start_offset": start, "end_offset": end})
                big_chunks.append(txt)
                offset = end + 1

            big_text = " ".join(big_chunks)
            return big_text, pages_info
        else:
            return combined, None

    def _simple_tokenize(self, text):
        """
        Tokenizes the input text into a list of word tokens with their start positions.

        This simple tokenizer uses a regular expression to find non-whitespace sequences
        (words) in the text. Each token is represented as a dictionary containing:
            - "text": the word itself
            - "start": the character index in the original text where the word begins

        This method is useful for lightweight text processing where precise token offsets
        are needed for downstream alignment or diffing operations.

        Parameters:
            text (str): The raw input string to tokenize.

        Returns:
            list[dict]: A list of token dictionaries, each with "text" and "start" keys.
        """

        tokens = []
        for match in re.finditer(r"\S+", text):
            word = match.group(0)
            start = match.start()
            tokens.append({"text": word, "start": start})
        return tokens

    def _align_sentences_with_offsets(
        self, old_sentences: list[dict], new_sentences: list[dict]
    ) -> list[dict]:
        """
        Aligns old and new sentences by index and returns a combined list with offset metadata.

        This method pairs corresponding sentences from the original and updated versions of the
        text, assuming they are aligned by order. Each entry in the returned list includes:
            - "text_original": sentence text from the original document
            - "text_new": sentence text from the new document
            - "start": start offset of the sentence in the new document
            - "end": end offset of the sentence in the new document
            - "index": the position of the sentence in the sequence

        Only aligns up to the shortest list length to prevent index errors.

        Parameters:
            old_sentences (list[dict]): List of sentence dictionaries from the original text, each
                with at least a "text" field.
            new_sentences (list[dict]): List of sentence dictionaries from the updated text, each
                with "text", "start", and "end" fields.

        Returns:
            list[dict]: A list of dictionaries representing aligned sentence pairs with positional metadata.
        """

        sentences = []
        for i in range(min(len(old_sentences), len(new_sentences))):
            sentences.append(
                {
                    "text_original": old_sentences[i]["text"],
                    "text_new": new_sentences[i]["text"],
                    "start": new_sentences[i]["start"],
                    "end": new_sentences[i]["end"],
                    "index": i,
                }
            )
        return sentences

    def _group_changes_by_page(self, changes: list[dict]) -> dict[int, list[dict]]:
        """
        Groups a list of change dictionaries by their page index.

        This method organizes change records based on the "page_index" field in each dictionary.
        If a change does not explicitly include a "page_index", it defaults to 0. The result is
        a mapping of page indices to lists of associated changes, which allows for page-level
        processing and analysis.

        Parameters:
            changes (list[dict]): A list of change dictionaries, each expected to have a
                "page_index" key indicating the page where the change occurs.

        Returns:
            dict[int, list[dict]]: A dictionary where each key is a page index and the value is a
            list of change dictionaries that belong to that page.
        """

        page_map = {}
        for change in changes:
            page_index = change.get("page_index", 0)
            page_map.setdefault(page_index, []).append(change)
        return page_map

    def _build_text_nodes_from_page_map(
        self, page_map: dict[int, list[dict]], page_images: list = None
    ) -> list[TextNode]:
        """
        Builds a list of `TextNode` objects by processing grouped page-level changes with GPT.

        This method iterates over a dictionary of changes grouped by page. For each page:
        - It serializes the list of changes into JSON format, capturing both original and new text.
        - It invokes a GPT-based processing method to analyze and summarize the identified changes.
        - If a result is returned, it wraps the output in a `TextNode` object along with metadata,
            including the page number and a placeholder for bounding box coordinates.
        - If page_images is provided, it includes the corresponding page image data in the metadata.

        Parameters:
            page_map (dict[int, list[dict]]): A dictionary mapping page indices to lists of change
                dictionaries. Each change is expected to have "original" and "new" text fields.
            page_images (list, optional): A list of tuples containing page numbers, image bytes, and bounding boxes.

        Returns:
            list[TextNode]: A list of `TextNode` instances containing the GPT-processed results
            and associated metadata for each page.
        """
        nodes = []
        for page_index, changes in page_map.items():
            identified_text = json.dumps(
                [{"Original": c["original"], "New": c["new"]} for c in changes],
                ensure_ascii=False,
            )

            # Find corresponding page image if available
            page_image_data = None
            if page_images:
                for page_num, _, bbox in page_images:
                    if page_num == page_index + 1:
                        # Convert bbox coordinates to integers for the required schema
                        bbox_int = [int(coord) for coord in bbox]
                        page_image_data = {
                            "blocks": [{"bbox": bbox_int, "page_number": page_num}],
                            "header_name": "",
                            "page_number": page_num,
                            "ending_page_number": page_num,
                            "starting_page_number": page_num,
                        }
                        break

            page_result = self._process_page_with_gpt(
                identified_text=identified_text,
                page_number=page_index + 1,
                bbox=page_image_data["blocks"][0]["bbox"] if page_image_data else "",
            )

            if page_result:
                raw_json_text = json.dumps(page_result, ensure_ascii=False)
                metadata = (
                    page_image_data
                    if page_image_data
                    else {
                        "blocks": [],
                        "header_name": "",
                        "page_number": page_index + 1,
                        "ending_page_number": page_index + 1,
                        "starting_page_number": page_index + 1,
                    }
                )

                nodes.append(TextNode(text=raw_json_text, metadata=metadata))

        return nodes

    def _diff_tokens(self, old_tokens: list[dict], new_tokens: list[dict]) -> list[dict]:
        """
        Computes a token-level diff between two lists of tokens using sequence matching.

        This method compares two sequences of tokens (from the original and updated texts)
        and categorizes the differences using the `difflib.SequenceMatcher`. It labels each
        token as one of the following types:
        - "equal": unchanged token
        - "removed": token present in the original but not in the updated text
        - "added": token present in the updated text but not in the original

        For each token, the result includes:
        - "type": the change type (added, removed, equal)
        - "text": the token string
        - "start": the start character index of the token in its respective source

        Parameters:
            old_tokens (list[dict]): Token list from the original text, each with "text" and "start".
            new_tokens (list[dict]): Token list from the updated text, each with "text" and "start".

        Returns:
            list[dict]: A flat list of token-level change dictionaries representing the diff.
        """

        matcher = difflib.SequenceMatcher(
            None, [t["text"] for t in old_tokens], [t["text"] for t in new_tokens]
        )
        changes = []

        def add_changes(start_index, end_index, tokens, change_type):
            changes.extend(
                {"type": change_type, "text": tokens[i]["text"], "start": tokens[i]["start"]}
                for i in range(start_index, end_index)
            )

        for tag, old_start, old_end, new_start, new_end in matcher.get_opcodes():
            if tag == "equal":
                add_changes(old_start, old_end, old_tokens, "equal")
            elif tag == "replace":
                add_changes(old_start, old_end, old_tokens, "removed")
                add_changes(new_start, new_end, new_tokens, "added")
            elif tag == "delete":
                add_changes(old_start, old_end, old_tokens, "removed")
            elif tag == "insert":
                add_changes(new_start, new_end, new_tokens, "added")

        return changes

    def _get_page_index(self, start, pages_info):
        """
        Determines the page index corresponding to a character offset based on page metadata.

        This method searches through a list of page information dictionaries to find the page
        where the given character offset (`start`) falls. Each page entry includes:
        - "start_offset": the character index where the page begins
        - "end_offset": the character index where the page ends
        - "page_index": the page's index in the document

        If the offset is within the bounds of a page, the method returns that page's index.
        If no matching page is found, it returns `None` as a fallback.

        Parameters:
            start (int): The character offset to locate.
            pages_info (list[dict]): List of page metadata dictionaries containing "start_offset",
                "end_offset", and "page_index".

        Returns:
            int | None: The index of the page containing the offset, or `None` if not found.
        """

        for page in pages_info:
            if page["start_offset"] <= start < page["end_offset"]:
                return page["page_index"]
        return None  # fallback if not found

    def _group_token_changes(self, diff_list: list[dict]) -> list[dict]:
        """
        Groups consecutive token changes from a diff list into categorized chunks.

        This method processes a flat list of token-level diffs (with "type", "text", and "start")
        and consolidates adjacent tokens of the same change type ("added" or "removed") into
        grouped chunks. Unchanged tokens ("equal") act as boundaries that flush any active chunk.

        Each resulting chunk includes:
        - "type": either "added" or "removed"
        - "words": a list of token dictionaries with "text" and "start" fields

        This grouping simplifies downstream processing by organizing changes into coherent blocks.

        Parameters:
            diff_list (list[dict]): A list of token-level diff entries with fields "type", "text", and "start".

        Returns:
            list[dict]: A list of grouped change chunks, each representing a sequence of added or removed tokens.
        """

        chunks = []
        current = None

        for entry in diff_list:
            typ = entry["type"]
            token = {"text": entry["text"], "start": entry["start"]}

            if typ == "equal":
                if current:
                    chunks.append(current)
                    current = None
                continue

            if not current or current["type"] != typ:
                if current:
                    chunks.append(current)
                current = {"type": typ, "words": [token]}
            else:
                current["words"].append(token)

        if current:
            chunks.append(current)
        return chunks

    def _merge_page_blocks(self, blocks_sorted, page_height, threshold=50):
        """
        1) Skip any block that is purely a page counter near bottom (e.g. '3/45').
        2) Merge the remaining blocks:
        - If the current block ends with '-' (ignoring trailing spaces)
            and the next block starts with a letter or digit, unify them with no extra space.
        - Otherwise, add a space between them.
        """
        filtered = []
        for blk in blocks_sorted:
            x0, y0, x1, y1, b_text, *rest = blk
            if self._is_footer_counter_block(b_text, blk, page_height, threshold):
                continue
            filtered.append(b_text)

        if not filtered:
            return ""

        merged_str = filtered[0]
        for i in range(len(filtered) - 1):
            curr = filtered[i]
            nxt = filtered[i + 1]
            if self._ends_with_hyphen_ignoring_spaces(curr):
                if re.match(r"^[A-Za-z0-9]", nxt.lstrip()):
                    # unify with no space
                    merged_str = merged_str.rstrip() + nxt.lstrip()
                    continue
            # else add a space
            if not merged_str.endswith(" "):
                merged_str += " "
            merged_str += nxt.lstrip()

        return merged_str

    def _ends_with_hyphen_ignoring_spaces(self, s: str) -> bool:
        """
        True if, after trimming trailing spaces, s ends with '-'.
        """
        stripped = s.rstrip()
        return stripped.endswith("-")

    def _is_footer_counter_block(self, block_text, block_coords, page_height, threshold=50):
        """
        Returns True if the block is purely a page counter (e.g. "3/45") near the bottom.
        """
        x0, y0, x1, y1, *_ = block_coords
        # block must be near bottom
        if y1 < (page_height - threshold):
            return False
        # check if block_text matches any of the footer counter patterns
        block_stripped = block_text.strip()
        return any(p.match(block_stripped) for p in FOOTER_COUNTER_PATTERNS)

    def _split_sentences_with_offsets(self, text: str) -> list[dict]:
        """
        Splits input text into sentences and captures their character offsets.

        This method uses a regular expression to approximate sentence boundaries based on punctuation
        (e.g., `.`, `!`, `?`) followed by whitespace or the end of the text. Each identified sentence
        is stripped of leading/trailing whitespace and recorded along with:
        - "text": the sentence content
        - "start": the starting character index in the original text
        - "end": the ending character index in the original text
        - "index": the sentence's position in the sequence

        This information is useful for aligning and comparing sentences across document versions.

        Parameters:
            text (str): The full text string to be split into sentences.

        Returns:
            list[dict]: A list of dictionaries, each representing a sentence with text content and offset metadata.
        """

        pattern = re.compile(r".*?[.!?](?:\s|$)", re.DOTALL)
        matches = list(pattern.finditer(text))
        sentences = []
        for i, match in enumerate(matches):
            sentence = match.group().strip()
            if sentence:
                sentences.append(
                    {"text": sentence, "start": match.start(), "end": match.end(), "index": i}
                )
        return sentences

    def _find_sentence_index(self, offset: int, sentences: list[dict]) -> int:
        """
        Finds the index of the sentence that contains the given character offset.

        This method searches through a list of sentence metadata dictionaries to locate the
        sentence whose start and end offsets encompass the provided character offset. If a
        match is found, it returns the sentence's index. If no sentence contains the offset,
        it returns -1 as a fallback.

        Parameters:
            offset (int): The character offset to locate within the sentence list.
            sentences (list[dict]): A list of sentence dictionaries, each containing
                "start", "end", and "index" fields.

        Returns:
            int: The index of the sentence containing the offset, or -1 if not found.
        """

        for sentence in sentences:
            if sentence["start"] <= offset < sentence["end"]:
                return sentence["index"]
        return -1

    def _group_sentence_indices(
        self, change_indices: list[int], sentences: list[dict]
    ) -> list[tuple[int, int]]:
        """
        Groups consecutive sentence indices into ranges and expands each range with surrounding context.

        This method takes a list of sentence indices that correspond to detected changes,
        identifies sequences of consecutive indices, and then expands each sequence by
        including one sentence before and after the range—if within bounds.

        The result is a list of (start_index, end_index) tuples that define inclusive index
        ranges. These are useful for retrieving surrounding sentence context for blocks of
        changes in text comparison tasks.

        Parameters:
            change_indices (list[int]): A list of sentence indices that have been marked as changed.
            sentences (list[dict]): A list of sentence dictionaries, used to ensure expanded indices
                stay within valid bounds.

        Returns:
            list[tuple[int, int]]: A list of index ranges (start, end), each covering a group of
            consecutive change indices with additional context.
        """

        if not change_indices:
            return []

        change_indices = sorted(set(change_indices))
        ranges = []
        start = change_indices[0]
        prev = start

        for i in change_indices[1:]:
            if i == prev + 1:
                prev = i
            else:
                ranges.append((start, prev))
                start = i
                prev = i
        ranges.append((start, prev))

        expanded_ranges = []
        for r_start, r_end in ranges:
            r_start = max(r_start - 1, 0)
            r_end = min(r_end + 1, len(sentences) - 1)
            expanded_ranges.append((r_start, r_end))

        return expanded_ranges

    def _combine_changes_with_context(self, diffs, pages_info, sentences) -> list[dict]:
        """
        Combines low-level diff changes into higher-level contextual changes using sentence alignment.

        This method aligns token-level diffs with sentences to produce grouped, context-rich change
        summaries. It works in several steps:
        1. Identifies sentence indices affected by changes.
        2. Groups those indices into contiguous ranges and expands each range with one sentence
            of context before and after.
        3. Concatenates original and updated sentence texts within each expanded range.
        4. Associates each contextual change with its corresponding page index (if page metadata is available).

        The result is a list of change objects that provide both original and new versions of the
        surrounding text, along with page awareness for better traceability.

        Parameters:
            diffs (list[dict]): A list of token-level change dictionaries with at least "start" and "type" fields.
            pages_info (list[dict]): Metadata for each page, including "start_offset", "end_offset", and "page_index".
            sentences (list[dict]): A list of sentence alignment dictionaries with "text_original", "text_new", "start", etc.

        Returns:
            list[dict]: A list of contextual change dictionaries, each containing:
                - "type": always set to "contextual_change"
                - "original": concatenated original sentence text
                - "new": concatenated updated sentence text
                - "page_index": index of the page where the change starts (if available)
        """

        # Step 1: Identify changed sentence indices
        changed_indices = self._get_changed_sentence_indices(diffs, sentences)

        # Step 2: Expand ranges to include context
        context_ranges = self._group_sentence_indices(changed_indices, sentences)

        # Step 3: Build contextual change objects
        contextual_changes = []
        for start_idx, end_idx in context_ranges:
            original_text = self._concat_sentence_text(
                sentences, start_idx, end_idx, key="text_original"
            )
            updated_text = self._concat_sentence_text(sentences, start_idx, end_idx, key="text_new")

            offset = sentences[start_idx]["start"]
            page_index = self._get_page_index(offset, pages_info) if pages_info else None

            contextual_changes.append(
                {
                    "type": "contextual_change",
                    "original": original_text,
                    "new": updated_text,
                    "page_index": page_index,
                }
            )

        return contextual_changes

    def _get_changed_sentence_indices(self, diffs: list[dict], sentences: list[dict]) -> list[int]:
        """
        Identifies sentence indices that contain meaningful token-level changes.

        This method scans through a list of token change chunks (e.g., from a diff operation),
        filters out non-change types ("equal"), and locates the sentence index where each
        change begins based on the start offset of its first word. The result is a list of
        sentence indices impacted by either "added", "removed", or "changed" tokens.

        Parameters:
            diffs (list[dict]): A list of change chunks, each with a "type" field and optionally
                a "words" list containing tokens with "start" offsets.
            sentences (list[dict]): A list of sentence dictionaries with "start", "end", and "index"
                fields used to locate which sentence each change belongs to.

        Returns:
            list[int]: A list of sentence indices where changes have occurred.
        """

        indices = []
        for diff in diffs:
            if diff["type"] not in {"added", "removed", "changed"}:
                continue
            if not diff.get("words"):
                continue
            first_word = diff["words"][0]
            offset = first_word.get("start")
            if offset is not None:
                idx = self._find_sentence_index(offset, sentences)
                if idx != -1:
                    indices.append(idx)
        return indices

    def _concat_sentence_text(self, sentences: list[dict], start: int, end: int, key: str) -> str:
        """
        Concatenates sentence text values over a specified index range using a given key.

        This method extracts sentence content from a list of sentence dictionaries between
        the specified `start` and `end` indices (inclusive). It uses the provided `key`
        (e.g., "text_original" or "text_new") to fetch the sentence text. If the specified
        key is missing in a sentence, it falls back to the "text" field.

        The resulting sentence fragments are joined with a space and returned as a single
        string, useful for assembling contextual change descriptions.

        Parameters:
            sentences (list[dict]): A list of sentence dictionaries containing sentence text
                and metadata fields.
            start (int): The starting index of the sentence range (inclusive).
            end (int): The ending index of the sentence range (inclusive).
            key (str): The key to extract text from in each sentence dictionary.

        Returns:
            str: The concatenated sentence text spanning the specified index range.
        """

        parts = []
        for i in range(start, end + 1):
            sentence = sentences[i]
            parts.append(sentence.get(key) or sentence.get("text", ""))
        return " ".join(parts)

    def _clean_text(self, text: str) -> str:
        """
        Collapses multiple whitespace characters into a single space and
        strips leading/trailing whitespace. Converts ligatures.
        """
        cleaned = pdf_utils.replace_ligatures_literal(text)
        cleaned = re.sub(r"\s+", " ", cleaned)
        return cleaned.strip()

    @observe(as_type="generation")
    def _process_page_with_gpt(
        self, identified_text: str, page_number: int, bbox: list
    ) -> list[dict]:
        """
        Process a single PDF page using identified Original/New text and return a list of change records.
        """
        final_nodes = []

        if not identified_text:
            return []

        try:
            items = json.loads(identified_text)
        except json.JSONDecodeError:
            self.logger.error("identified_text is not valid JSON.")
            return []

        if not isinstance(items, list):
            self.logger.error("identified_text must be a JSON list.")
            return []

        for item in items:
            if "Original" in item and isinstance(item["Original"], str):
                item["Original"] = self._clean_text(item["Original"])
            if "New" in item and isinstance(item["New"], str):
                item["New"] = self._clean_text(item["New"])

        for doc_obj in items:
            original_text = doc_obj.get("Original", "")
            new_text = doc_obj.get("New", "")

            changed_text = self._call_text_comparison(original_text, new_text)
            change_statement = self._call_text_comparison_summary(
                original_text, new_text, changed_text
            )
            impact = self._call_text_comparison_impact(
                original_text, new_text, changed_text, change_statement
            )

            final_payload = {
                "original_text": original_text,
                "updated_text": new_text,
                "identifier": "TBD",
                "changed_text": changed_text,
                "impact": impact,
                "change_statement": change_statement,
                "page_number": page_number,
                "bbox": bbox,
            }

            final_nodes.append(final_payload)

        return final_nodes

    def _call_text_comparison(self, original_text: str, new_text: str) -> str:
        """
        Calls prompt to get text comparison result.
        """
        user_content = f'Original: "{original_text}"\nNew: "{new_text}"\n'

        # Use Langfuse prompt via get_completion
        changed_text = ""
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                changed_text = self.ai_accessor.get_completion(
                    prompt_name="text_comparison",
                    messages=[{"role": "user", "content": user_content}],
                )
                break
            except Exception as e:
                self.logger.exception("Attempt %d - text_comparison error: %s", attempt, e)
                if attempt == retries:
                    self.logger.error("All retries failed for text_comparison prompt.")
        return changed_text

    def _call_text_comparison_summary(
        self, original_text: str, new_text: str, changed_text: str
    ) -> str:
        """
        Calls prompt to get a short summary statement.
        """
        user_content = f'Original: "{original_text}"\nNew: "{new_text}"\nChange: "{changed_text}"\n'

        # Use Langfuse prompt via get_completion
        summary = ""
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                summary = self.ai_accessor.get_completion(
                    prompt_name="text_comparison_summary",
                    messages=[{"role": "user", "content": user_content}],
                )
                break
            except Exception as e:
                self.logger.exception("Attempt %d - text_comparison_summary error: %s", attempt, e)
                if attempt == retries:
                    self.logger.error("All retries failed for text_comparison_summary prompt.")
        return summary

    def _call_text_comparison_impact(
        self, original_text: str, new_text: str, changed_text: str, summary: str
    ) -> str:
        """
        Calls prompt to get change impact level.
        """
        user_content = (
            f'Original: "{original_text}"\n'
            f'New: "{new_text}"\n'
            f'Change: "{changed_text}"\n'
            f'Summary: "{summary}"\n'
            "Based on these edits, is it High, Medium, or Low impact?"
        )

        # Use Langfuse prompt via get_completion
        impact = "Low"  # default fallback
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                assistant_reply = self.ai_accessor.get_completion(
                    prompt_name="text_comparison_impact",
                    messages=[{"role": "user", "content": user_content}],
                )

                # We expect exactly "High", "Medium", or "Low"
                if assistant_reply in ["High", "Medium", "Low"]:
                    impact = assistant_reply
                else:
                    self.logger.warning("Unexpected impact reply: %s", assistant_reply)
                break
            except Exception as e:
                self.logger.exception("Attempt %d - text_comparison_impact error: %s", attempt, e)
                if attempt == retries:
                    self.logger.error("All retries failed for text_comparison_impact prompt.")
        return impact
