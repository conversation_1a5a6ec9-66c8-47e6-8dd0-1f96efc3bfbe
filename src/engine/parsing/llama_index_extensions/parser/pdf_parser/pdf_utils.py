# pdf_utils.py
# Utility functions for document processing

# TODO: Move functions into document_models.py as Block methods

import unicodedata

from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import (
    Block,  # pylint: disable=line-too-long
)


def replace_ligatures(text):
    """
    Replace common ligature characters in the provided text with their standard
    character equivalents.

    Args:
    text (str): Text to process.

    Returns:
    str: Text with ligatures replaced.
    """

    # Normalize the text to NFC form
    text = unicodedata.normalize("NFC", text)

    ligatures = {
        # List symbols commented out to allow list identification in blocks
        "\u0000": "",
        "\u00a9": "©",  # (copyright symbol)
        "\u00ae": "®",  # (registered trademark symbol)
        "\u00ad": "-",  # (soft-hyphen, used at end of line)
        "\u2010": "‐",  # (hyphen)
        "\u2013": "–",  # (en dash)
        "\u2014": "—",  # (em dash)
        "\u2018": "'",
        "\u2019": "'",
        "\u201c": '"',
        "\u201d": '"',
        "\u2022": "•",  # (bullet point)
        "\u2026": "…",  # (ellipsis)
        "\u2122": "™",  # (trademark symbol)
        "\u2713": "✓",  # (checkmark symbol)
        "\ufb00": "ff",
        "\ufb01": "fi",
        "\ufb02": "fl",
        "\ufb03": "ffi",
        "\ufb04": "ffl",
        "\ufb05": "ft",
        "\ufb06": "st",
        # Additional ligatures can be added here as needed.
    }
    for ligature, replacement in ligatures.items():
        text = text.replace(ligature, replacement)
    return text


def replace_ligatures_literal(text):
    """
    Replace common ligature characters and other unusual PDF characters
    in the provided text with their more standard or preferred equivalents
    (using literal Unicode characters where possible).

    Args:
        text (str): Text to process.

    Returns:
        str: Text with ligatures and common PDF artifacts replaced.
    """

    # Normalize the text to NFC form (helps standardize accented characters, etc.)
    text = unicodedata.normalize("NFC", text)

    # Here are some typical replacements you might encounter in PDFs:
    # 1) Ligatures (ﬀ, ﬁ, ﬂ, etc.)
    # 2) Dashes and quotes
    # 3) Soft hyphens, null chars
    # 4) Common bullet replacements
    # 5) The "replacement character" U+FFFD (rendered as a square with a question mark)
    # 6) Wingdings-like bullets (e.g., \uf0b7), etc.

    ligatures = {
        # Invisible/Control Characters
        "\u0000": "",  # Null character
        "\u00ad": "-",  # Soft hyphen (often unwanted in final text)
        # Common “Typographic” Characters
        "©": "©",  # U+00A9 (copyright symbol)
        "®": "®",  # U+00AE (registered trademark symbol)
        "‐": "‐",  # U+2010 (hyphen)
        "–": "–",  # U+2013 (en dash)
        "—": "—",  # U+2014 (em dash)
        "‘": "'",  # U+2018 (left single quote)
        "’": "'",  # U+2019 (right single quote / apostrophe)
        "“": '"',  # U+201C (left double quote)
        "”": '"',  # U+201D (right double quote)
        "•": "•",  # U+2022 (bullet)
        "…": "…",  # U+2026 (ellipsis)
        "™": "™",  # U+2122 (trademark symbol)
        "✓": "✓",  # U+2713 (checkmark)
        # Ligatures (Latin Alphabet, U+FB00–U+FB06)
        "ﬀ": "ff",  # U+FB00
        "ﬁ": "fi",  # U+FB01
        "ﬂ": "fl",  # U+FB02
        "ﬃ": "ffi",  # U+FB03
        "ﬄ": "ffl",  # U+FB04
        "ﬅ": "ft",  # U+FB05
        "ﬆ": "st",  # U+FB06
        # Replacement Character (often appears as a "square with ?")
        "�": "?",  # U+FFFD → remove or replace with "?" depending on your preference
        # Wingdings-like or private-use glyphs that often appear as bullets
        # in generated PDFs. You can replace them with a standard bullet or anything else.
        "\uf0b7": "•",  # Common bullet in some PDF exports (Wingdings bullet)
        "\uf0a7": "•",  # Another private-use bullet (if encountered)
        # Add more as needed
    }

    for ligature, replacement in ligatures.items():
        text = text.replace(ligature, replacement)

    return text


def is_within_table(block, tables):
    """
    Determines if a given text block is within any of the bounding boxes of
    tables identified on a page.

    This function checks if the bounding box of a text block falls entirely
    within the bounding box of any table on the page.

    Args:
        block (dict): A dictionary representing the text block, containing
                      a 'bbox' key with the bounding box coordinates as a list
                      of four values [x_min, y_min, x_max, y_max].
        tables (list): A list of table objects, each with a 'bbox' attribute
                      representing the bounding box as a list of four values
                      [x_min, y_min, x_max, y_max].

    Returns:
        bool: True if the block is within any table's bounding box,
              False otherwise.
    """
    block_bbox = block["bbox"]
    for table in tables:
        table_bbox = table.bbox
        if (
            block_bbox[0] >= table_bbox[0]
            and block_bbox[2] <= table_bbox[2]
            and block_bbox[1] >= table_bbox[1]
            and block_bbox[3] <= table_bbox[3]
        ):
            return True
    return False


def are_on_same_line(
    box1: tuple, box2: tuple, horizontal_threshold: float = 25.0, vertical_threshold: float = 2.0
) -> bool:
    """
    Check if two bounding boxes are on the same horizontal line by comparing
    their vertical positions and horizontal proximity.

    This function considers two bounding boxes to be on the same line if:
    1. They have the same maximum y-coordinate (i.e., their bottom edges
       are aligned).
    2. The horizontal distance between the right edge of the first box and
       the left edge of the second box is within the specified threshold.

    Args:
        box1 (tuple): A tuple representing the first bounding box with
                    coordinates (x1_min, y1_min, x1_max, y1_max).
        box2 (tuple): A tuple representing the second bounding box with
                    coordinates (x2_min, y2_min, x2_max, y2_max).
        horizontal_threshold (float): The maximum allowed horizontal distance
                    between the right edge of the first box and the left edge
                    of the second box to consider them on the same line.
                    Default is 25.0.
        vertical_threshold (float): The maximum allowed vertical distance
                    between the bottom edge of each box to consider them on
                    the same line. Default is 2.0.


    Returns:
        bool: True if the bounding boxes are on the same horizontal line,
              False otherwise.
    """
    _, _, x1_max, y1_max = box1
    x2_min, _, _, y2_max = box2
    return abs(y2_max - y1_max) < vertical_threshold and x1_max > (x2_min - horizontal_threshold)


def are_consecutive_lines(box1: tuple, box2: tuple, threshold: float = 8.0) -> bool:
    """
    Check if two text boxes are close enough vertically to be considered
    consecutive lines based on three conditions:
    1. They start at the same x-coordinate.
    2. The length of the lines is within a specified threshold (to account for paragraph indentation).
    3. They are centered on the page within a certain threshold.

    This function considers two text boxes to be consecutive lines if any of
    the three conditions above are met and the vertical distance between the
    bottom edge of the first box and the top edge of the second box is within
    the specified threshold.

    Args:
        box1 (tuple): A tuple representing the first text box with coordinates
                      (x1_min, y1_min, x1_max, y1_max).
        box2 (tuple): A tuple representing the second text box with coordinates
                      (x2_min, y2_min, x2_max, y2_max).
        threshold (float): The maximum allowed vertical distance between the
                           bottom edge of the first box and the top edge of
                           the second box to consider them consecutive lines.
                           Default is 5.0.

    Returns:
        bool: True if the text boxes are close enough vertically to be
              considered consecutive lines based on the conditions, False
              otherwise.
    """
    x1_min, y1_min, x1_max, y1_max = box1
    x2_min, y2_min, x2_max, y2_max = box2

    # Calculate the horizontal center of each box
    center1 = (x1_min + x1_max) / 2
    center2 = (x2_min + x2_max) / 2

    # Define thresholds
    center_aligned_threshold = 30
    line_length_compare_threshold = 50

    # Calculate the differences for conditions
    center_aligned_diff = abs(center1 - center2)
    line_length_compare = abs((x2_max - x2_min) - (x1_max - x1_min))

    # Check conditions
    start_same_x = x1_min == x2_min
    line_length_condition = line_length_compare < line_length_compare_threshold
    center_aligned_condition = center_aligned_diff < center_aligned_threshold

    return (start_same_x or line_length_condition or center_aligned_condition) and abs(
        y2_min - y1_max
    ) <= threshold
