"""
Logging Configuration Module

This module provides a centralized configuration for logging across the
application. It ensures consistent logging behavior and makes it easier
to manage and adjust logging settings as needed.
"""

import logging
from logging.handlers import RotatingFileHandler

def configure_logging(
        log_root_path: str = "src/engine/parsing/llama_index_extensions/parser/pdf_parser/log/",
        log_file: str = "pdf_text_extractor_default.log",
        log_level: int = logging.DEBUG
    ):
    """
    Configures the logging for the application.

    Args:
        log_file (str): The name of the log file.
        log_level (int): The logging level (default: logging.DEBUG).
    """
    log_file_path = log_root_path + log_file
    log_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(funcName)s - %(message)s'
    )

    # File handler with UTF-8 encoding
    file_handler = RotatingFileHandler(
        log_file_path, maxBytes=10**8, backupCount=5, encoding='utf-8'
    )
    file_handler.setFormatter(log_formatter)

    # Stream handler for errors only with UTF-8 encoding
    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.ERROR)
    stream_handler.setFormatter(log_formatter)
    stream_handler.stream.reconfigure(encoding='utf-8')

    # Get the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(stream_handler)

    # Suppress overly verbose logs from other libraries
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

# Example usage - Orchestrator:
# configure_logging("my_app.log", logging.INFO)

# Example usage - Modules:
# logger = logging.getLogger(__name__)
