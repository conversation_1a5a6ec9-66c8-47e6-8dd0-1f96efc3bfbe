"""
PDF Table Classifier

This module provides functionality to classify tables extracted from PDF documents.
It includes methods to classify tables based on header and row properties,
as well as group header blocks by row index.
"""

from collections import Counter, defaultdict
from typing import List, Tuple
import logging
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import TableBlock  # pylint: disable=line-too-long

class PDFTableClassifier:
    """
    A class to classify tables based on header and row properties.
    """
    def __init__(self):
        """
        Initialize the PDFTableClassifier.
        """
        pass

    def classify_table(self, blocks: List[TableBlock]) -> Tuple[str, List[TableBlock], List[dict], int]:
        """
        Classify the table based on header and row properties.

        Args:
            blocks (List[TableBlock]): List of table blocks.

        Returns:
            Tuple[str, List[TableBlock], List[dict], int]: A tuple containing the table type,
                header blocks, grouped header rows, and the number of columns.
        """
        header_blocks = []
        font_sizes = [block.size for block in blocks if block.text]
        unique_font_sizes = set(font_sizes)

        if len(unique_font_sizes) == 1:
            header_font_size = 0  # Default to 0 if all sizes are the same
        else:
            header_font_size = max(font_sizes)

        row_counts = Counter(block.row_index for block in blocks)
        col_counts = max(block.col_index for block in blocks) + 1

        for block in blocks:
            if (
                block.text and 
                (
                    block.size == header_font_size or  # Default to 0 fails check
                    'bold' in block.font.lower() or
                    (
                        row_counts[block.row_index] == 1 and
                        block.rowspan == col_counts  # Only block in the row
                    )
                )
            ):
                header_blocks.append(block)

        header_rows = self.group_header_blocks_by_row_index(header_blocks)
        header_rows_count = len(header_rows)

        for block in blocks:
            if (
                block.row_index == 0 and
                block.col_index == 0 and
                block.text == "✓"  # "\u2713"
            ):
                logging.debug(
                    "Classification: Checklist, header_rows_count: %d, header_blocks length: %d",
                    header_rows_count, len(header_blocks)
                )
                return "Checklist", header_blocks, header_rows, col_counts

        if header_rows_count and len(header_blocks) == 1:
            logging.debug(
                "Classification: Single Header, header_rows_count: %d, header_blocks length: %d",
                header_rows_count, len(header_blocks)
            )
            return "Single-Header", header_blocks, header_rows, col_counts

        if header_rows_count > 1:
            row_indices = [block.row_index for block in header_blocks]
            rows_are_neighbors = all(
                abs(row_indices[i] - row_indices[i - 1]) <= 1
                for i in range(1, len(row_indices))
            )

            if rows_are_neighbors:
                logging.debug(
                    "Classification: Double Header, header_rows_count: %d, header_blocks length: %d",
                    header_rows_count, len(header_blocks)
                )
                return "Double-Header", header_blocks, header_rows, col_counts
            logging.debug(
                "Classification: Multi-Header, header_rows_count: %d, header_blocks length: %d",
                header_rows_count, len(header_blocks)
            )
            return "Multi-Header", header_blocks, header_rows, col_counts

        logging.debug(
            "Classification: Uniform Columns (default), header_rows_count: %d, header_blocks length: %d",
            header_rows_count, len(header_blocks)
        )
        return "Uniform-Columns", header_blocks, header_rows, col_counts

    def group_header_blocks_by_row_index(self, header_blocks: List[TableBlock]) -> List[dict]:
        """
        Group header blocks by row index.

        Args:
            header_blocks (List[TableBlock]): List of header blocks.

        Returns:
            List[dict]: A list of dictionaries grouped by row index.
        """
        header_rows = defaultdict(list)
        for block in header_blocks:
            header_rows[block.row_index].append(block)
        return [header_rows[row_index] for row_index in sorted(header_rows)]