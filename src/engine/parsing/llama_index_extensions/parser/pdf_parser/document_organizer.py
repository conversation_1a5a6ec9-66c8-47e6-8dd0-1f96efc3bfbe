"""
Document Organizer

This module provides functionality to parse and structure documents by
headers based on predefined schema patterns using regular expressions.
It classifies and organizes document blocks under various headers such
as Part, Subpart, Chapter, Section, and Topic, which are specified in
the `header_patterns`. The module enhances the processing and
organization of document text, making it amenable to structured
representation and analysis.

Classes:
    DocumentOrganizer:
        A class to organize document blocks into a structured hierarchy based
        on header schemas. If no specific schema pattern is found for a block,
        the header will be searched by page number to find the closest matching
        header.
    HeaderCleaner:
        A utility class to clean header text by removing unwanted formats.
    HeaderFinder:
        A class to find and match headers within a document.

Usage:
    document = Document(blocks=[...])
    organizer = DocumentOrganizer()
    processed_document = organizer.organize_document_blocks(document, domain)
    sorted_document = DocumentOrganizer.sort_document(processed_document)
    # The 'sorted_document' now contains a structured hierarchy of headers
    # and blocks.
"""

import re
import logging
import bisect
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import DocumentHeader, Document # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.header_patterns import header_patterns # pylint: disable=line-too-long

class HeaderCleaner:
    """A utility class to clean header text by removing unwanted formats."""

    @staticmethod
    def clean(header: str) -> str:
        """Cleans headers by removing dates in parentheses and page numbers.

        Args:
            header (str): The header text to be cleaned. 

        Returns:
            str: The cleaned header text.
        """
        cleaned_header = re.sub(
            r"\(\d{2}/\d{2}/\d{4}\)|Page\s\d+|\.{2,}.*$", "", header).strip()
        logging.debug("Cleaned header: %s", cleaned_header)
        return cleaned_header

class HeaderFinder:
    """A class to find and match headers within a document."""

    def __init__(self, headers):
        self.headers = headers
        self.sorted_headers = []
        self.sorted_pages = []
        self.header_cleaner = HeaderCleaner()
        self.prepare_headers_for_search(headers)

    def prepare_headers_for_search(self, headers):
        """Flattens the list of headers to enable binary search."""
        flat_list = []
        def _flatten(headers):
            for header in headers:
                flat_list.append(header)
                _flatten(header.headers)
        _flatten(headers)
        self.sorted_headers = sorted(flat_list, key=lambda x: x.starting_page_number)
        self.sorted_pages = [header.starting_page_number for header in self.sorted_headers]
        logging.debug("Prepared sorted headers for binary search.")

    def find_closest_header_by_page(self, page_number):
        """Finds the closest header for the given page number using binary search.

        Args:
            headers: The list of headers to search within.
            page_number (int): The page number to match.

        Returns:
            DocumentHeader or None: The matched header or None if not found.
        """
        logging.debug(
            "Finding header by page using binary search for page: %d",
            page_number
        )
        index = bisect.bisect_right(self.sorted_pages, page_number) - 1
        if index == -1:
            logging.debug("No headers found before page: %d", page_number)
            return None
        closest_header = self.sorted_headers[index]
        logging.debug(
            "Closest header found: %s at page %d",
            closest_header.name, closest_header.starting_page_number
        )
        return closest_header

    def find_header_by_text(self, headers, text):
        """Finds a header by matching the header text.

        Args:
            headers: The list of headers to search within.
            text (str): The text to match.

        Returns:
            DocumentHeader or None: The matched header or None if not found.
        """

        # Remove . and : from header text to match (USDA Task Order 03)
        cleaned_text = text.lower().replace(" ", "").replace(":", "").replace(".", "")
        for header in headers:
            if cleaned_text == header.name.lower().replace(" ", "").replace(":", "").replace(".", ""):
                logging.debug("Exact match found: %s", header.name)
                return header
            result = self.find_header_by_text(header.headers, text)
            if result:
                return result
        return None


    def find_deepest_header_by_page(self, headers, page_number):
        """Finds the deepest header by matching the page number.

        Args:
            headers: The list of headers to search within.
            page_number (int): The page number to match.

        Returns:
            DocumentHeader or None: The deepest matched header or None
            if not found.
        """
        logging.debug("Finding deepest header for page: %d", page_number)
        deepest_header = None
        for header in headers:
            if header.starting_page_number <= page_number:
                if deepest_header is None or header.level > deepest_header.level:
                    deepest_header = header
            result = self.find_deepest_header_by_page(header.headers, page_number)
            if result:
                if deepest_header is None or result.level > deepest_header.level:
                    deepest_header = result
        if deepest_header:
            logging.debug(
                "Deepest header found: %s at level %d",
                deepest_header.name, deepest_header.level
            )
        else:
            logging.debug("No deepest header found for page: %d", page_number)
        return deepest_header

    def find_or_create_header_by_pattern(self, current_path, level, text):
        """Searches for an existing matching header or creates one if none
        found.

        Args:
            current_path (list): The current path in the document hierarchy.
            level (int): The level of the header.
            text (str): The header text.

        Returns:
            DocumentHeader: The existing or newly created header.
        """
        cleaned_text = self.header_cleaner.clean(text)
        parent = current_path[-1]
        if not hasattr(parent, 'headers'):
            raise TypeError(
                f"Expected a Document or DocumentHeader object,"
                f" got {type(parent).__name__}."
            )
        # Check existing headers under the current parent
        for header in parent.headers:
            if cleaned_text.replace(" ", "") in header.name.replace(" ", "") and header.level == level:
                logging.debug("Found existing header: %s", header.name)
                return header
        # Create a new header if none found
        new_header = DocumentHeader(
            name=cleaned_text,
            level=level,
            blocks=[],
            headers=[]
        )
        parent.headers.append(new_header)
        logging.debug("Created new header: %s", new_header.name)
        return new_header

class DocumentOrganizer:
    """A class to organize document blocks into a structured hierarchy based
    on header schemas."""

    def __init__(self, document, domain: Document):
        """Initializes the DocumentOrganizer with document headers."""
        self.document = document
        self.domain = domain
        self.header_finder = HeaderFinder(document.headers)

    def organize_document_blocks(self) -> Document:
        """Organize Document Blocks under DocumentHeaders using provided header schemas."""
        self.document.sort()
        current_path = [self.document]
        blocks_to_organize = self.document.blocks[:]
        blocks_added = set()
        self.document.clear_blocks()
        current_header = None

        logging.info("Starting to organize document blocks for domain: %s", self.domain)
        logging.info("Number of Document Headers: %s", len(self.document.headers))

        domain_patterns = [p for p in header_patterns if p['domain'].lower() == self.domain.lower()]
        toc_text_matched = False
        toc_page_matched = False

        if domain_patterns:
            pattern_matched = False
            for block_index, block in enumerate(blocks_to_organize, start=1):
                block_id = (
                    f"<{block_index}>"
                    f"Page:[{block.page_number}]"
                    f"-Block:[{block.block_number}]\n"
                    f"-Text: {block.text}"
                )
                text = block.text.strip()
                logging.debug("Processing block %d: %s", block_index, block_id)
                header_matched = False

                for header_schema in domain_patterns:
                    if re.compile(header_schema['pattern']).match(text):
                        cleaned_text = HeaderCleaner.clean(text)
                        logging.debug(
                            "Matched header: %s to pattern: %s",
                            cleaned_text, header_schema['pattern']
                        )
                        while (isinstance(current_path[-1], DocumentHeader) and
                               len(current_path) > 1 and
                               current_path[-1].level >= header_schema['level']):
                            current_path.pop()
                            logging.debug(
                                "Popped from current path, new path length: %d",
                                len(current_path)
                            )
                        header = self.header_finder.find_or_create_header_by_pattern(
                            current_path,
                            header_schema['level'],
                            cleaned_text
                        )
                        logging.debug("Current Path: %s", current_path[-1].name)
                        logging.debug("Current Header: %s", header.name)

                        if header and block_id not in blocks_added:
                            # header.blocks.append(block)   # Do not include header block
                            blocks_added.add(block_id)
                            logging.debug(
                                "Block added to current header: %s", header.name
                            )

                        current_path.append(header)
                        logging.debug(
                            "Updated current path to: %s", current_path[-1].name
                        )
                        header_matched = True
                        pattern_matched = True

                if not header_matched and block not in blocks_added:
                    current_path[-1].blocks.append(block)
                    blocks_added.add(block)
                    logging.debug("Block added to %s", current_path[-1].name)
                    logging.debug("Current Path: %s", current_path[-1].name)

        if not domain_patterns or not pattern_matched:
            current_path = [self.document]
            blocks_added = set()
            self.document.clear_blocks()
            current_header = None

            for block_index, block in enumerate(blocks_to_organize, start=1):
                block_id = (
                    f"<{block_index}>"
                    f"Page:[{block.page_number}]"
                    f"-Block:[{block.block_number}]\n"
                    f"-Text: {block.text}"
                )
                text = self.header_finder.header_cleaner.clean(block.text.strip())

                logging.debug("Processing block %d: %s", block_index, block_id)
                header_matched = False            
                found_header = self.header_finder.find_header_by_text(self.document.headers, text)
                if found_header:
                    logging.debug(
                        "Matched header by text: %s",
                        found_header.name
                    )
                    while (isinstance(current_path[-1], DocumentHeader) and
                        len(current_path) > 1 and
                        current_path[-1].level >= found_header.level):
                        current_path.pop()
                        logging.debug(
                            "Popped from current path, new path length: %d",
                            len(current_path)
                        )

                    # found_header.blocks.append(block)     # Do not include header block
                    current_path.append(found_header)
                    header_matched = True
                    toc_text_matched = True
                    continue
                
                if not header_matched and block not in blocks_added:
                    logging.debug("No matching header found at current level for text: %s", text)
                    current_path[-1].blocks.append(block)
                    blocks_added.add(block)
                    logging.debug("Block added to %s", current_path[-1].name)
                    logging.debug("Current Path: %s", current_path[-1].name)                    

        if (not domain_patterns or not pattern_matched) and not toc_text_matched:
            current_path = [self.document]
            blocks_added = set()
            self.document.clear_blocks()
            current_header = None            

            for block_index, block in enumerate(blocks_to_organize, start=1):
                block_id = (
                    f"<{block_index}>"
                    f"Page:[{block.page_number}]"
                    f"-Block:[{block.block_number}]\n"
                    f"-Text: {block.text}"
                )
                text = block.text.strip()
                logging.debug("Processing block %d: %s", block_index, block_id)
                header_matched = False            

                found_header = self.header_finder.find_closest_header_by_page(
                    block.page_number)
                if found_header:
                    logging.debug(
                        "Matched header by page: %s on page %d",
                        found_header.name, block.page_number
                    )

                    while (isinstance(current_path[-1], DocumentHeader) and
                        len(current_path) > 1 and
                        current_path[-1].level >= found_header.level):
                        current_path.pop()
                        logging.debug(
                            "Popped from current path, new path length: %d",
                            len(current_path)
                        )
                    found_header.blocks.append(block)     # Must include header block
                    current_path.append(found_header)
                    header_matched = True
                    toc_page_matched = True
                    continue

                if not header_matched and block not in blocks_added:
                    current_path[-1].blocks.append(block)
                    blocks_added.add(block)
                    logging.debug("Block added to %s", current_path[-1].name)
                    logging.debug("Current Path: %s", current_path[-1].name)

        if (not domain_patterns or not pattern_matched) and not toc_text_matched and not toc_page_matched:
            current_path = [self.document]
            blocks_added = set()
            self.document.clear_blocks()
            current_header = None  

            for block_index, block in enumerate(blocks_to_organize, start=1):
                block_id = (
                    f"<{block_index}>"
                    f"Page:[{block.page_number}]"
                    f"-Block:[{block.block_number}]\n"
                    f"-Text: {block.text}"
                )
                text = block.text.strip()
                logging.debug("Processing block %d: %s", block_index, block_id)            
                self.document.blocks.append(block)

        def split_large_headers(header):
            for sub_header in header.headers:
                split_large_headers(sub_header)  # Traverse to the deepest headers first

            if header.calculate_total_words > max_header_word_count:
                logging.info("Header %s has more than 1000 words, checking for sub-headers", header.name)
                new_sub_headers = []
                current_sub_header = None

                # Create a copy of header.blocks to iterate over
                blocks_to_process = header.blocks[:]
                header.blocks = []

                font_sizes = [block.size for block in blocks_to_process]
                unique_font_sizes = {size: font_sizes.count(size) for size in set(font_sizes)}
                most_common_font_size = max(unique_font_sizes, key=unique_font_sizes.get)


                for block in blocks_to_process:
                    font_lower = block.font.lower().strip()
                    if "bold" in font_lower or block.size > most_common_font_size:
                        # Create a new sub-header for block
                        if current_sub_header:
                            new_sub_headers.append(current_sub_header)
                        current_sub_header = DocumentHeader(name=block.text, level=header.level + 1)
                    elif current_sub_header:
                        current_sub_header.blocks.append(block)
                    else:
                        header.blocks.append(block)

                # Append the last current_sub_header if it exists
                if current_sub_header:
                    new_sub_headers.append(current_sub_header)

                header.headers.extend(new_sub_headers)

        max_header_word_count = 500
        for header in self.document.headers:
            split_large_headers(header)

        self.document.sort()
        logging.info("Categorized document blocks")
        return self.document

    @staticmethod
    def update_current_path(current_path: list, level: int, new_header: DocumentHeader) -> None:
        """Update the current path up to the given header level and append new
        header.

        Args:
            current_path (list): The current path in the document hierarchy.
            level (int): The level of the new header.
            new_header (DocumentHeader): The new header to be appended.
        """
        while len(current_path) > 1 and current_path[-1].level >= level:
            current_path.pop()
        current_path.append(new_header)
        logging.debug(
            "Updated current path: %s",
            [header.name for header in current_path]
        )
