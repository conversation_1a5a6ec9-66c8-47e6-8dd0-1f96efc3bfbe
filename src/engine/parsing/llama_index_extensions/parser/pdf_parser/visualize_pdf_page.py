import io
from typing import List, Union, IO
import fitz  # PyMuPDF
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import Text<PERSON><PERSON>, TableBlock, Document, DocumentHeader
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_text_extractor import PDFTextExtractor
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_utils import replace_ligatures, is_within_table, are_on_same_line # pylint: disable=line-too-long


def visualize_pdf_page(doc, document_blocks, page_number=0, figsize=(12, 16)):
    """
    Visualize bounding boxes for TextBlock and TableBlock objects on a specified page of a PDF document.

    Parameters:
    - doc: The PyMuPDF document object.
    - document_blocks: A list of blocks containing bounding box data.
    - page_number: The page number to visualize (0-indexed).
    - figsize: The size of the figure (width, height).

    Returns:
    - fig: The Matplotlib figure object.
    - ax: The Matplotlib axis object.
    """
    # Visualize the bounding boxes
    fig, ax = plt.subplots(figsize=figsize)  # Adjust figure size here

    # Load the specified page as an image
    page = doc.load_page(page_number)
    pix = page.get_pixmap()
    img_bytes = pix.tobytes("png")

    # Convert the raw bytes to an image
    image = Image.open(io.BytesIO(img_bytes))

    # Display the image using Matplotlib
    ax.imshow(image)

    # Customize axis ticks and labels
    ax.set_xticks(range(0, image.width, 10))
    ax.set_yticks(range(0, image.height, 10))
    ax.set_xticklabels(range(0, image.width, 10))
    ax.set_yticklabels(range(0, image.height, 10))
    ax.tick_params(axis='both', which='major', labelsize=10, length=6, width=2)

    # Move X axis to the top
    ax.xaxis.set_ticks_position('top')
    ax.xaxis.set_label_position('top')

    # Rotate the X-axis labels
    plt.xticks(rotation=90)

    # Draw bounding boxes for TextBlocks and TableBlocks
    for block in document_blocks:
        if block.block_type == 'text':
            #continue
            rect = patches.Rectangle(
                (block.bbox[0], block.bbox[1]),
                block.bbox[2] - block.bbox[0],
                block.bbox[3] - block.bbox[1],
                linewidth=1,
                edgecolor='r',
                facecolor='none'
            )
            ax.add_patch(rect)
            ax.text(block.bbox[0], block.bbox[1], '', color='red', fontsize=8, verticalalignment='top')
        elif block.block_type == 'table':
            rect = patches.Rectangle(
                (block.bbox[0], block.bbox[1]),
                block.bbox[2] - block.bbox[0],
                block.bbox[3] - block.bbox[1],
                linewidth=3,
                edgecolor='b',
                facecolor='none'
            )
            ax.add_patch(rect)
            ax.text(block.bbox[0], block.bbox[1], '', color='blue', fontsize=8, verticalalignment='top')
    
    # Add green outlines for each cell in the table after processing all blocks
    tables = page.find_tables()
    for i, table in enumerate(tables):
        for i, row in enumerate(table.rows):
            for cell in row.cells:
                if cell and isinstance(cell, tuple) and len(cell) == 4:
                    rect = patches.Rectangle(
                        (cell[0], cell[1]),
                        cell[2] - cell[0],
                        cell[3] - cell[1],
                        linewidth=1,
                        edgecolor='g',
                        facecolor='none'
                    )
                    ax.add_patch(rect)
    
    plt.show()

    return fig, ax
