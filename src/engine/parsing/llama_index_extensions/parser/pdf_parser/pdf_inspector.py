# pdfTableInspector.py
# Provides tools to extract and analyze table data from PDF files using PyMuPDF and pandas.


import fitz  # PyMuPDF
import pandas as pd
import json
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_table_classifier import PDFTableClassifier  # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_text_extractor import PDFTextExtractor


def extract_tables(pdf_path):
    """
    Extract tables from a PDF file.

    Parameters:
    - pdf_path (str): Path to the PDF file from which tables are to be extracted.

    Returns:
    - list: A list of dictionaries, each representing a page in the PDF. Each dictionary contains the page number and a list of tables extracted from that page.
    
    Each table is detailed with its bounding box, the text of the first row (for identification), and structured data for each row and cell in the table.
    """
    doc_temp = fitz.open(pdf_path)
    pdf_bytes = doc_temp.write()        # simulate bytestream handoff
    doc = fitz.open("pdf", pdf_bytes)
    extractor = PDFTextExtractor(pdf_bytes)
    classifier = PDFTableClassifier()
    all_pages_data = []
    
    for page_number, page in enumerate(doc, start=1):
        table_blocks = extractor.extract_table_blocks_from_page(page, page_number)
        page_data = {'page_number': page_number, 'tables': []}
        table_finder = page.find_tables()  
        tables = table_finder.tables 

        for table_index, table in enumerate(tables, start=1):
            table_type, header_blocks, header_rows, num_columns = classifier.classify_table(table_blocks)
            table_data = {
                'table_index': table_index,
                'table_type': table_type,
                'header_rows': len(header_rows),
                'num_columns': num_columns,
                'first_row_text': [],
                'table_bbox': table.bbox,
                'rows': []}
            cell_texts = []  

            for row_index, row in enumerate(table.rows, start=1):
                row_data = {
                    'row_index': row_index, 
                    'cells': []}
                for cell_number, cell in enumerate(row.cells, start=1):
                    cell_text = page.get_text("text", clip=cell)
                    cell_text = cell_text.replace('\n', ' ')
                    if cell:
                        row_data['cells'].append({
                            'cell_number': cell_number,
                            'cell_text': cell_text.strip(),
                            'cell_bbox': cell
                        })
                    if row_index == 1:
                        cell_texts.append(cell_text)
                table_data['rows'].append(row_data)
            if cell_texts:
                table_data['first_row_text'] = ''.join(cell_texts).strip()
            page_data['tables'].append(table_data)
        all_pages_data.append(page_data)
    
    return all_pages_data

def analyze_tables(tables):
    """
    Analyze the structure of extracted tables to gather statistics and identify anomalies.

    Parameters:
    - tables (list): List of table data as returned by `extract_tables`.

    Returns:
    - dict: Categorized groups of tables based on their row structure and potential anomalies detected.
    
    This function categorizes tables into groups based on uniformity in row structures across pages, highlighting anomalies where row structures differ significantly.
    """    
    anomalies = []
    for page in tables:
        for table in page['tables']:
            row_structure = {}
            for row in table['rows']:
                cell_count = len(row['cells'])
                if cell_count not in row_structure:
                    row_structure[cell_count] = 1
                else:
                    row_structure[cell_count] += 1

            if len(row_structure) > 1:
                anomalies.append({
                    'page_number': page['page_number'],
                    'table_index': table['table_index'],
                    'row_structure': row_structure
                })

    return anomalies

def group_and_categorize_tables(tables):
    """
    Categorizes tables based on their row structure to identify common formats and potential anomalies.

    This function processes tables extracted from PDF documents to categorize them into groups such as tables with a
    single header row followed by uniform data rows, uniformly structured tables, or tables that present anomalies in
    row structure. This grouping helps in analyzing the consistency of table formats across multiple documents or pages.

    Parameters:
    - tables (list): A list of dictionaries where each dictionary contains details about tables extracted from a single
      PDF page.

    Returns:
    - dict: A dictionary categorizing tables into groups such as 'Single_Header', 'Uniform_Columns', and 'Anomaly',
      based on their row structures. Each category contains lists of tables that fit the criteria.
    
    The function iterates over each table, counts the occurrence of each unique row structure (defined by the number of cells
    in each row), and groups tables by these structures. It also detects tables that deviate from common structures, flagging
    them as anomalies.
    """
    grouped_tables = {}
    categories = {"Uniform_Columns": [], "Single_Header": [], "Anomaly": []}

    for page in tables:
        for table in page['tables']:
            row_counts = [len(row['cells']) for row in table['rows']]
            first_row_text = " ".join([cell['cell_text'] for cell in table['rows'][0]['cells']])  
            key = f"{len(table['rows'])}-{sorted(row_counts)}"
            if key not in grouped_tables:
                grouped_tables[key] = {
                    'key': key,
                    'count': 0,
                    'tables': []
                }
            grouped_tables[key]['tables'].append({
                'page_number': page['page_number'],
                'table_index': table['table_index'],
                'first_row_text': first_row_text,
                'table_bbox': table['table_bbox'],
                'row_structure': row_counts
            })
            grouped_tables[key]['count'] += 1

    for key, group in grouped_tables.items():
        row_counts = eval(key.split('-')[1])
        if len(row_counts) > 1 and row_counts[0] == 1 and len(set(row_counts[1:])) == 1:
            categories["Single_Header"].append(group)
        elif len(set(row_counts)) == 1:
            categories["Uniform_Columns"].append(group)
        else:
            categories["Anomaly"].append(group)

    return categories



def generate_reports(tables, anomalies, json_path, excel_path):
    """
    Generate reports based on the analyzed table data and identified anomalies, saving results in JSON and Excel formats.

    Parameters:
    - tables (list): Table data as returned by `extract_tables`.
    - anomalies (list): Anomalies as identified by `analyze_tables`.
    - json_path (str): File path to save the JSON report.
    - excel_path (str): File path to save the Excel report.
    
    This function processes the categorized table data and anomalies, producing a JSON file with detailed information and an Excel file with a summary suitable for further review or reporting.
    """
   
    categories = group_and_categorize_tables(tables)

    with open(json_path, 'w') as f_json:
        json.dump(categories, f_json, indent=4)

    rows = []
    for category_name, category_tables in categories.items():
        for group in category_tables:
            for table in group['tables']:
                row = {
                    'Page Number': table['page_number'],
                    'Table Index': table['table_index'],
                    'First Row Text': table['first_row_text'],
                    'Category': category_name,
                    'Row Count Key': group['key'],
                    'Table Count': group['count'],
                }
                rows.append(row)

    df = pd.DataFrame(rows)
    if not df.empty:
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Table Analysis')
    else:
        print("No data to write to Excel.")
  
