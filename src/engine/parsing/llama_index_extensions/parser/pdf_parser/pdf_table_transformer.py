"""
PDF Table Transformer

This module provides functions to transform and clean table blocks extracted
from PDF documents. It includes functions to remove duplicate table blocks,
merge table blocks, concatenate cells, and classify table types.
"""

from collections import defaultdict
from typing import List, Tuple, Union, Dict
import logging
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import TextBlock, TableBlock  # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_table_classifier import PDFTableClassifier  # pylint: disable=line-too-long

class PDFTableTransformer:
    """
    A class to handle merging of table blocks and concatenation of text
        from header and row cells.
    """

    def __init__(self, blocks: List[Union[TextBlock, TableBlock]]):
        """
        Initialize the PDFTableTransformer with the blocks from a document.

        Args:
            blocks (List[Union[TextBlock, TableBlock]]): List of text and table blocks.
        """
        self.blocks = blocks
        self.merged_blocks = self.extract_text_blocks(blocks)
        self.tables = self.extract_table_blocks(blocks)
        logging.debug("Total tables found: %d", len(self.tables))

    def extract_text_blocks(
            self, blocks: List[Union[TextBlock, TableBlock]]
        ) -> List[TextBlock]:
        """Extract text blocks from the list of blocks."""
        return [b for b in blocks if b.block_type == 'text']

    def extract_table_blocks(
            self, blocks: List[Union[TextBlock, TableBlock]]
        ) -> defaultdict:
        """Extract table blocks from the list of blocks."""
        tables = defaultdict(list)
        for block in blocks:
            if block.block_type == 'table':
                tables[(block.page_number, block.table_index)].append(block)
        return tables

    def merge_table_cells(self) -> List[TableBlock]:
        """
        Merge table blocks and concatenate text from header and row cells.

        Returns:
            List[TableBlock]: List of merged table blocks.
        """
        for (page_number, table_index), table_blocks in self.tables.items():
            if any(block.text for block in table_blocks):
                self.process_table(page_number, table_index, table_blocks)
        return sorted(
            self.merged_blocks,
            key=lambda b: (b.page_number, b.bbox[1], b.bbox[0])
        )

    def process_table(
            self,
            page_number: int,
            table_index: int,
            table_blocks: List[TableBlock]
        ):
        """Process each table by merging rows and adding the result to merged blocks."""
        classifier = PDFTableClassifier()
        table_type, header_blocks, header_rows, num_columns = classifier.classify_table(table_blocks)
        logging.debug(
            "Before merging - Page: %d, Table: %d, Type: %s, Blocks: %d",
            page_number, table_index + 1, table_type, len(table_blocks)
        )
        header_row_count = len(header_rows)
        rows = self.group_table_blocks_by_rows(table_blocks)
        num_rows = len(rows)

        if header_row_count < 1 or not rows[0]:  # TODO: add headerless table_type
            logging.debug("Table has no header rows")
            return

        if not rows[0][0]:
            logging.debug(
                "No blocks in the first column of the header row"
                "for table at page %d, index %d",
                page_number, table_index
            )
            return

        #self.concatenate_table_text(page_number, table_index, rows, header_rows, header_row_count, num_columns)
        #return
        
        if table_type == "Single-Header":
            self.process_single_header_table(page_number, table_index, rows, num_columns)
        elif table_type in ("Double-Header", "Multi-Header"):
            self.process_multi_header_table(
                page_number,
                table_index,
                rows,
                num_columns,
                table_type,
                header_rows,
                header_row_count
            )
        else:
            self.process_uniform_columns_table(page_number, table_index, rows, num_columns)

    def process_single_header_table(
            self,
            page_number: int,
            table_index: int,
            rows: defaultdict,
            num_columns: int
        ):
        header_row = rows[0]  # Assuming the first row is the header
        rowspan_tracker = {}

        for row_index in range(1, len(rows)):
            if row_index not in rows:
                continue
            self.merge_row(page_number, table_index, header_row, rows[row_index], num_columns, rowspan_tracker)

    def process_multi_header_table(
            self,
            page_number: int,
            table_index: int,
            rows: defaultdict,
            num_columns: int,
            table_type: str,
            header_rows: list[dict],
            header_row_count: int):

        if header_row_count == 1:
            header_row = header_rows[0]
            for row_index in range(1, len(rows)):
                self.merge_row(page_number, table_index, header_row, rows[row_index], num_columns, {})
        elif header_row_count == 2 and table_type == "Double-Header":
            first_header_row = header_rows[0]
            second_header_row = header_rows[1]
            self.merge_row(page_number, table_index, first_header_row, second_header_row, num_columns, {})
            for row_index in range(2, len(rows)):
                self.merge_row(page_number, table_index, second_header_row, rows[row_index], num_columns, {})
        elif header_row_count > 1:
            first_header_row = header_rows[0]
            first_subsequent_row = rows[1]
            second_header_row = header_rows[1]
            self.merge_row(page_number, table_index, first_header_row, first_subsequent_row, num_columns, {})
            for row_index in range(3, len(rows)):
                self.merge_row(page_number, table_index, second_header_row, rows[row_index], num_columns, {})

    def process_uniform_columns_table(self, page_number: int, table_index: int, rows: defaultdict, num_columns: int):
        header_row = rows[0]  # Assuming the first row is the header
        rowspan_tracker = {}

        for row_index in range(1, len(rows)):
            if row_index not in rows:
                continue
            self.merge_row(page_number, table_index, header_row, rows[row_index], num_columns, rowspan_tracker)
    

    def group_table_blocks_by_rows(self, table_blocks: List[TableBlock]) -> defaultdict:
        """Group table blocks by rows."""
        rows = defaultdict(lambda: defaultdict(list))
        for block in table_blocks:
            rows[block.row_index][block.col_index].append(block)
        return rows

    def merge_row(
            self,
            page_number: int,
            table_index: int,
            header_row: dict,
            row: dict,
            num_columns: int,
            rowspan_tracker: dict
        ):
        """Merge cells in a row and add the merged block to merged blocks."""
        concatenated_text = ""
        bboxes = []

        col_index = 0
        while col_index < num_columns:
            header_text = ""
            cell_text = ""

            # Handle rowspan from previous rows
            if col_index in rowspan_tracker:
                cell_text += ' '.join(
                    rowspan_tracker[col_index].text
                    for _ in range(rowspan_tracker[col_index].colspan)
                ) + " "
                bboxes.extend([
                    rowspan_tracker[col_index].bbox
                    for _ in range(rowspan_tracker[col_index].colspan)
                ])
                rowspan_tracker[col_index].rowspan -= 1
                if rowspan_tracker[col_index].rowspan == 0:
                    del rowspan_tracker[col_index]

            if col_index in header_row:
                header_text = ' '.join(
                    block.text for block in header_row[col_index]
                ) + " "
            if col_index in row:
                for block in row[col_index]:
                    cell_text += ' '.join(
                        block.text for _ in range(block.colspan)
                    ) + " "
                    bboxes.extend([block.bbox for _ in range(block.colspan)])
                    if block.rowspan > 1:
                        rowspan_tracker[col_index] = block

            concatenated_text += (
                f"{header_text}{cell_text} ".strip() + "; "  # ;-delimited
            )

            col_index += 1

        if concatenated_text and not self.should_exclude_text(concatenated_text):
            if bboxes:  # Ensure there are bounding boxes to process
                bbox = self.compute_concat_bbox(bboxes)
                #ref_block = row[0][0] if 0 in row and row[0] else header_row[0][0]
                ref_block = (
                    row[list(row.keys())[0]][0]
                    if row
                    else header_row[list(header_row.keys())[0]][0]
                )
                new_block = TableBlock(
                    page_number=page_number,
                    printed_page_number=ref_block.printed_page_number,
                    block_type="table",
                    block_number=ref_block.block_number,
                    table_index=table_index,
                    row_index=ref_block.row_index,
                    col_index=0,  # Indicative, as this covers all columns
                    font=ref_block.font,
                    size=ref_block.size,
                    color=ref_block.color,
                    bbox=bbox,
                    text=concatenated_text.strip().rstrip(";")
                )
                self.merged_blocks.append(new_block)
                logging.debug(
                    "Added block from page %d, bbox: %s, text: %s...",
                    page_number, bbox, new_block.text[:50] # pylint: disable=no-member
                )
        else:
            logging.warning(
                "No bounding boxes found for row in table on page %d, index %d",
                page_number, table_index
            )

        row_index_value = row[0][0].row_index if row and 0 in row and row[0] else None
        """
        logging.debug(
            "After merging - Page: %d, Table: %d, Row: %d, Merged Blocks: %d",
            page_number, table_index + 1, row_index_value, len(self.merged_blocks)
        )
        """
    @staticmethod
    def should_exclude_text(block_text: str) -> bool:
        """Check if the block text should be excluded based on predefined criteria."""
        return block_text in ["✓"]  # more dynamic when needed

    @staticmethod
    def compute_concat_bbox(
        bboxes: List[Tuple[float, float, float, float]]
    ) -> Tuple[float, float, float, float]:
        """Compute the bounding box that encompasses all given bounding boxes."""
        x_min = min(bbox[0] for bbox in bboxes)
        y_min = min(bbox[1] for bbox in bboxes)
        x_max = max(bbox[2] for bbox in bboxes)
        y_max = max(bbox[3] for bbox in bboxes)
        return x_min, y_min, x_max, y_max
    
    def concatenate_table_text(
            self,
            page_number: int,
            table_index: int,
            rows: Dict[int, Dict[int, List[TableBlock]]],
            header_rows: List[Dict[int, List[TableBlock]]],
            header_row_count: int,
            num_columns: int
        ):
        #Concatenate text from table cells.
        concatenated_texts = []
        rowspan_tracker = defaultdict(lambda: {'text': '', 'rowspan': 0})
        colspan_tracker = defaultdict(lambda: {'text': '', 'colspan': 0})

        #for row_index in range(header_row_count, len(rows)):
        for row_index in range(1, len(rows)):

            concatenated_text = ""
            bboxes = []
            col_index = 0

            while col_index < num_columns:
                header_text = ""
                cell_text = ""

                # Handle rowspan from previous rows
                if col_index in rowspan_tracker and rowspan_tracker[col_index]['rowspan'] > 0:
                    cell_text += rowspan_tracker[col_index]['text'] + " "
                    rowspan_tracker[col_index]['rowspan'] -= 1

                # Handle colspan from previous columns
                if col_index in colspan_tracker and colspan_tracker[col_index]['colspan'] > 0:
                    cell_text += colspan_tracker[col_index]['text'] + " "
                    colspan_tracker[col_index]['colspan'] -= 1
                    col_index += 1
                    continue  # Skip to the next column as this one is covered by colspan

                # Process headers
                for header_row in header_rows:
                    if any(block.col_index == col_index for block in header_row):
                        header_text += ' '.join(
                            block.text for block in header_row if block.col_index == col_index
                        ) + " "

                # Process data cells
                if col_index in rows[row_index]:
                    for block in rows[row_index][col_index]:
                        cell_text += block.text + " "
                        if block.rowspan > 1:
                            rowspan_tracker[col_index] = {
                                'text': block.text,
                                'rowspan': block.rowspan - 1
                            }
                        if block.colspan > 1:
                            colspan_tracker[col_index] = {
                                'text': block.text,
                                'colspan': block.colspan - 1
                            }

                concatenated_text += f"{header_text.strip()} : {cell_text.strip()} " if header_text and cell_text else ""

                col_index += 1

            concatenated_texts.append(concatenated_text.strip())

        if concatenated_text and bboxes:
            bbox = self.compute_concat_bbox(bboxes)
            ref_block = rows[row_index][0][0] if rows[row_index] else header_rows[0][0]

            new_block = TableBlock(
                page_number=page_number,
                printed_page_number=ref_block.printed_page_number,
                block_type="table",
                block_number=ref_block.block_number,
                table_index=table_index,
                row_index=row_index,
                col_index=0,  # Indicative, as this covers all columns
                font=ref_block.font,
                size=ref_block.size,
                color=ref_block.color,
                bbox=bbox,
                text=concatenated_text.strip().rstrip(" :")
            )
            self.merged_blocks.append(new_block)
            logging.debug(
                "Added block from page %d, bbox: %s, text: %s...",
                page_number, bbox, new_block.text[:50]  # pylint: disable=no-member
            )
