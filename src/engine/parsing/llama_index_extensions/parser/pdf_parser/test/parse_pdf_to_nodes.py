import json
import logging
import time
import os
import spacy
from datetime import datetime
from flask import Flask, request, jsonify
import fitz  # PyMuPDF
import pandas as pd
from collections import Counter
from openpyxl.styles import Font
from openpyxl import load_workbook
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_node_parser import PDFNodeParser
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_parser_stats import save_statistics

# Add the pdf_parser directory to sys.path
#current_dir = os.path.dirname(os.path.abspath(__file__))
#sys.path.append(current_dir)

#from pdf_node_parser import PDFNodeParser
#from pdf_parser_stats import save_statistics

nlp = spacy.load("en_core_web_sm")

def count_text_statistics(text):
    """Count characters, words, and tokens in the given text."""
    char_count = len(text.replace(" ", ""))
    word_count = len(text.split())
    unique_words = len(Counter(text.split()))  # Unique words
    return char_count, word_count, unique_words

def parse_documents():
    """Endpoint to parse PDF documents."""
    documents = request.get_json()
    all_nodes = []
    run_statistics = []

    logging.info("Received %d document paths for parsing.", len(documents))

    for file_number, document in enumerate(documents):
        document_path = document.get('path', "")
        document_name = document.get('document_name', "")
        domain = document.get('domain', "")
        start_page = int(document.get('start_page', 1))
        end_page = int(document.get('end_page', -1))
        start_time = time.time()
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            doc = fitz.open(document_path)
            logging.info("Opened and read document: %s", document_path)
            pdf_bytes = doc.write()
            document_size = len(pdf_bytes)
            num_pages = doc.page_count

            # Read and process text from each page
            raw_text = ""
            raw_tokens = 0
            for page in doc:
                page_text = page.get_text()
                raw_text += page_text
                doc_spacy = nlp(page_text)  # per page to avoid spacy 1m soft limit
                raw_tokens += len(doc_spacy)                

            raw_characters, raw_words, unique_words = count_text_statistics(raw_text)


        except Exception as e:
            logging.error("Failed to open or read document: %s, Error detail: %s", document_path, e)
            continue

        parser = PDFNodeParser()
        nodes = parser._parse_nodes(pdf_bytes, document_name, domain, start_page, end_page)

        node_dict_list = [node.dict() for node in nodes]
        all_nodes.extend(node_dict_list)

        output_json_file_name = f"/home/<USER>/phoenix-burst-api/src/engine/parsing/llama_index_extensions/parser/pdf_parser/test/output/{file_number}_output.json"
        try:
            with open(output_json_file_name, 'w', encoding='utf-8') as f:
                json.dump(node_dict_list, f, indent=4, ensure_ascii=False)
            logging.info("Saved output to %s", output_json_file_name)
        except IOError as e:
            logging.error("Failed to write output file: %s, Error detail: %s", output_json_file_name, e)
            continue

        end_time = time.time()
        run_time = end_time - start_time
        minutes, seconds = divmod(run_time, 60)
        run_time_str = f"{int(minutes)}m {seconds:.2f}s"
        
        stats = parser.calculate_node_statistics(nodes)
        stats.update({
            "filepath": os.path.basename(document_path),  # Extract only the filename
            "domain": domain,
            "run_time": run_time_str,
            "timestamp": timestamp,
            "document_size_bytes": document_size,
            "num_pages": num_pages,
            "nodes_per_page": len(nodes) / num_pages if num_pages > 0 else 0,
            "pages_per_node": num_pages / len(nodes) if len(nodes) > 0 else 0,
            "total_nodes": len(nodes),
            "raw_characters": raw_characters,
            "raw_words": raw_words,
            "unique_words": unique_words,
            "raw_tokens": raw_tokens,
            "parsed_characters": stats["parsed_characters"],
            "parsed_words": stats["parsed_words"],  
            "parsed_tokens": stats["parsed_tokens"]
        })
        run_statistics.append(stats)

        stats_output_file = "/home/<USER>/phoenix-burst-api/src/engine/parsing/llama_index_extensions/parser/pdf_parser/test/output/run_statistics.xlsx"
        save_statistics(stats, stats_output_file)

        logging.info("Number of nodes extracted: %d", len(node_dict_list))
        logging.info("Total number of tokens: %d", stats["parsed_tokens"])

    logging.info("Finished processing all documents.")

    # Extract header path, token count, and first 250 characters of text
    summarized_nodes = []
    for node in all_nodes:
        metadata = node.get("metadata", {})
        summarized_node = {
            "header_path": metadata.get("header_path", ""),
            "token_count": metadata.get("token_count", 0),
            "first_250_characters": node.get("text", "")[:250]
        }
        summarized_nodes.append(summarized_node)

    # Convert to DataFrame and save to Excel
    summary_df = pd.DataFrame(summarized_nodes)
    summary_excel_file_name = "/home/<USER>/phoenix-burst-api/src/engine/parsing/llama_index_extensions/parser/pdf_parser/test/output/summary_output.xlsx"
    try:
        summary_df.to_excel(summary_excel_file_name, index=False)
        logging.info("Saved summarized output to %s", summary_excel_file_name)
    except IOError as e:
        logging.error("Failed to write summarized output file: %s, Error detail: %s",
                      summary_excel_file_name, e
                      )

    return jsonify(all_nodes)
    #return all_nodes
    #return nodes               #TypeError: Object of type TextNode is not JSON serializable
    #return jsonify(nodes)      #TypeError: Object of type TextNode is not JSON serializable
