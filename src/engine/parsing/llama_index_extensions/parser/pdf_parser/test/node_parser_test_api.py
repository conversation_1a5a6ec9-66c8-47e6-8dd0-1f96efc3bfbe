from flask import Flask
from engine.parsing.llama_index_extensions.parser.pdf_parser.test.parse_pdf_to_nodes import parse_documents
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_parser_logging_config import configure_logging

# Initialize Flask app
app = Flask(__name__)

# Configure logging
# configure_logging()

# Define the /parse endpoint
app.add_url_rule('/parse', 'parse_documents', parse_documents, methods=['POST'])

if __name__ == "__main__":
    # Run the app
    app.run(host='0.0.0.0', port=5000, debug=False)
