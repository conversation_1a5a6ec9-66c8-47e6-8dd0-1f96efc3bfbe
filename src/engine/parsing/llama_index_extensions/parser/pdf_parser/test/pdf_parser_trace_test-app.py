import io
import fitz  # PyMuPDF
import json
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import Text<PERSON>lock, TableBlock, Document, DocumentHeader
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_text_extractor import PDFTextExtractor # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_table_transformer import PDFTableTransformer # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_parser_logging_config import configure_logging
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_node_parser import PDFNodeParser


#configure_logging("log/table_concat_test.log")



#file_name = "fnma_servicing_guide_95_table_uc3_rowspan.pdf"
#file_name = "fnma_servicing_guide_68_table_multiheader.pdf"
#file_name = "fnma_servicing_guide_43-44_table_step_multipage_multiheader.pdf"
#file_name = "fnma_servicing_guide_40_table_step.pdf"
#file_name = "fnma_servicing_guide_36_table_uc3.pdf"
#file_name = "fnma_servicing_guide_33-35_table_steps_multipage.pdf"
#file_name = "fnma_servicing_guide_30-32_table_uc_multipage_lists.pdf"
#file_name = "fnma_servicing_guide_28_table_checklist.pdf"
#file_name = "fnma_servicing_guide_27_table_uc.pdf"
#file_name = "fha_handbook.pdf"
#file_name = "fhlmc_servicing_guide.pdf"
file_name = "fhlmc_servicing_guide_1000.pdf"
#file_name = "cfpb_exam_2023.pdf"
#file_name = "cfpb_exam_1014-1067_mortgage_regs.pdf"
#file_name = "cfpb_exam_456-489_mortgage_servicing.pdf"
#file_name = "valeri-servicer-user-guide.pdf"

input_root_path = "/src/engine/parsing/llama_index_extensions/parser/pdf_parser/input/"
output_root_path = "/src/engine/parsing/llama_index_extensions/parser/pdf_parser/output/trace/"
input_path = input_root_path + file_name

doc_temp = fitz.open(input_path)
pdf_bytes = doc_temp.write()        # simulate bytestream handoff
doc = fitz.open("pdf", pdf_bytes)
domain = 'fhlmc'
document_name = file_name
start_page = 1

extractor = PDFTextExtractor(pdf_bytes, document_name, domain, start_page)

document_json = extractor.document.model_dump_json(indent=4)
json_file_name = output_root_path + "parsing_trace_step0.json"
with open(json_file_name, 'w', encoding='utf-8') as json_file:
    json_file.write(document_json)

for page_number, page in enumerate(doc, start=1):
    printed_page_number = extractor.extract_page_number(page)
    page_table_blocks = extractor.extract_table_blocks_from_page(page, page_number)
    all_blocks = extractor.assign_text_to_blocks(page, page_number, printed_page_number, page_table_blocks)
    extractor.document.blocks.extend(all_blocks)

document_json = extractor.document.model_dump_json(indent=4)
json_file_name = output_root_path + "parsing_trace_step1.json"
with open(json_file_name, 'w', encoding='utf-8') as json_file:
    json_file.write(document_json)

extractor.document.blocks = extractor.merge_text_blocks(extractor.document.blocks)

document_json = extractor.document.model_dump_json(indent=4)
json_file_name = output_root_path + "parsing_trace_step2.json"
with open(json_file_name, 'w', encoding='utf-8') as json_file:
    json_file.write(document_json)

extractor.document.blocks = PDFTableTransformer(extractor.document.blocks).merge_table_cells()

document_json = extractor.document.model_dump_json(indent=4)
json_file_name = output_root_path + "parsing_trace_step3.json"
with open(json_file_name, 'w', encoding='utf-8') as json_file:
    json_file.write(document_json)
