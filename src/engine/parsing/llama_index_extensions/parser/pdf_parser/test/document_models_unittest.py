import unittest
from datetime import date
from document_models import Block, TextBlock, TableBlock, ContentSection, DocumentHeader, Document, clean_text

class TestDocumentModels(unittest.TestCase):

    def test_block_creation(self):
        block = Block(
            page_number=1,
            printed_page_number="1",
            block_type="text",
            block_number=1,
            font="Arial",
            size=12.0,
            color=0,
            bbox=(0, 0, 100, 100),
            text="Hello, world!"
        )
        self.assertEqual(block.page_number, 1)
        self.assertEqual(block.text, "Hello, world!")
        self.assertEqual(block.count_non_space_characters(), 12)
        self.assertEqual(block.count_words(), 2)

    def test_text_block_initialization(self):
        block = TextBlock(page_number=1, block_number=1, bbox=(0, 0, 50, 50), text="Hello..world...")
        self.assertEqual(block.text, "Helloworld")

    def test_table_block_initialization(self):
        block = TableBlock(page_number=1, block_number=1, bbox=(0, 0, 50, 50), text="Cell 1... Cell 2..", table_index=0, row_index=0, cell_index=0)
        self.assertEqual(block.text, "Cell 1 Cell 2")

    def test_text_block_merge(self):
        block1 = TextBlock(
            page_number=1,
            block_number=1,
            bbox=(0, 0, 50, 50),
            text="Hello"
        )
        block2 = TextBlock(
            page_number=1,
            block_number=2,
            bbox=(50, 50, 100, 100),
            text="world!"
        )
        block1.merge_with(block2)
        self.assertEqual(block1.text, "Hello world!")
        self.assertEqual(block1.bbox, (0, 0, 100, 100))

    def test_table_block_merge(self):
        block1 = TableBlock(
            page_number=1,
            block_number=1,
            bbox=(0, 0, 50, 50),
            text="Cell 1",
            table_index=0,
            row_index=0,
            cell_index=0
        )
        block2 = TableBlock(
            page_number=1,
            block_number=2,
            bbox=(50, 50, 100, 100),
            text="Cell 2",
            table_index=0,
            row_index=0,
            cell_index=1
        )
        block1.merge_with(block2)
        self.assertEqual(block1.text, "Cell 1 Cell 2")
        self.assertEqual(block1.bbox, (0, 0, 100, 100))

    def test_content_section(self):
        section = ContentSection(name="Section 1")
        block1 = TextBlock(
            page_number=1,
            block_number=1,
            bbox=(0, 0, 50, 50),
            text="Hello"
        )
        block2 = TextBlock(
            page_number=1,
            block_number=2,
            bbox=(50, 50, 100, 100),
            text="world!"
        )
        section.blocks.extend([block1, block2])
        self.assertEqual(section.calculate_non_space_characters, 11)
        self.assertEqual(section.calculate_total_words, 2)
        self.assertEqual(section.get_section_text, "Hello\nworld!")

    def test_document_header(self):
        header = DocumentHeader(name="Header 1", level=0)
        self.assertEqual(header.name, "Header 1")
        self.assertEqual(header.level, 0)

    def test_document_creation(self):
        document = Document(
            name="My Document",
            format="PDF",
            title="Test Title",
            author="Test Author",
            subject="Test Subject",
            keywords="test, document",
            creator="Test Creator",
            creation_date="2024-05-23",
            modified_date="2024-05-24",
            published_date=date(2024, 5, 25)
        )
        self.assertEqual(document.name, "My Document")
        self.assertEqual(document.format, "PDF")
        self.assertEqual(document.title, "Test Title")
        self.assertEqual(document.author, "Test Author")
        self.assertEqual(document.subject, "Test Subject")
        self.assertEqual(document.keywords, "test, document")
        self.assertEqual(document.creator, "Test Creator")
        self.assertEqual(document.creation_date, "2024-05-23")
        self.assertEqual(document.modified_date, "2024-05-24")
        self.assertEqual(document.published_date, date(2024, 5, 25))

    def test_document_metadata_loading(self):
        class MockFitzDoc:
            metadata = {
                "format": "PDF",
                "title": "Test Title",
                "author": "Test Author",
                "subject": "Test Subject",
                "keywords": "test, document",
                "creator": "Test Creator",
                "creationDate": "D:20240523120000",
                "modDate": "D:20240524120000"
            }

        document = Document(name="My Document", fitz_doc=MockFitzDoc())
        self.assertEqual(document.format, "PDF")
        self.assertEqual(document.title, "Test Title")
        self.assertEqual(document.author, "Test Author")
        self.assertEqual(document.subject, "Test Subject")
        self.assertEqual(document.keywords, "test, document")
        self.assertEqual(document.creator, "Test Creator")
        self.assertEqual(document.creation_date, "2024-05-23")
        self.assertEqual(document.modified_date, "2024-05-24")

    def test_document_toc_headers(self):
        class MockFitzDoc:
            def get_toc(self):
                return [
                    [1, "Chapter 1", 1],
                    [2, "Section 1.1", 2],
                    [2, "Section 1.2", 3],
                    [1, "Chapter 2", 4]
                ]

        document = Document(name="My Document")
        document.get_headers_from_toc(MockFitzDoc())
        self.assertEqual(len(document.headers), 2)
        self.assertEqual(document.headers[0].name, "Chapter 1")
        self.assertEqual(document.headers[1].name, "Chapter 2")
        self.assertEqual(len(document.headers[0].headers), 2)
        self.assertEqual(document.headers[0].headers[0].name, "Section 1.1")
        self.assertEqual(document.headers[0].headers[1].name, "Section 1.2")

if __name__ == '__main__':
    unittest.main()
