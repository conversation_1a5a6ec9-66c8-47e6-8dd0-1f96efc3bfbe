from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_inspector import extract_tables, analyze_tables, generate_reports

# Example usage
pdf_path = "input/fhlmc_servicing_guide.pdf"
json_path = "output/fhlmc_servicing_guide_tables_and_anomalies.json"
excel_path = "output/fhlmc_servicing_guide_tables_and_anomalies.xlsx"

tables = extract_tables(pdf_path)
anomalies = analyze_tables(tables)
generate_reports(tables, anomalies, json_path, excel_path)