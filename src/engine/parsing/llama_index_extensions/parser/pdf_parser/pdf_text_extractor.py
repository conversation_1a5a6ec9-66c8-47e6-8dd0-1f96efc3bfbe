"""
PDF Text Extractor

This module provides the functionality to extract structured text data
from a PDF document using PyMuPDF. It defines a class that processes
the PDF document to extract text and tables as structured data blocks.
"""

import re
import logging
from typing import List, Union, IO
import fitz  # !pip install pymupdf
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_utils import replace_ligatures, are_on_same_line, are_consecutive_lines # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_table_transformer import PDFTableTransformer # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import TextBlock, TableBlock, Document # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_organizer import DocumentOrganizer # pylint: disable=line-too-long
from engine.parsing.llama_index_extensions.parser.pdf_parser.header_patterns import header_patterns # pylint: disable=line-too-long

# logger = logging.getLogger(__name__)

class PDFTextExtractor:
    """
    A class to extract text and tables from a PDF document and
    convert them into structured data blocks.
    """

    def __init__(
            self,
            pdf_bytes_data: IO[bytes],
            document_name: str = "",
            domain: str = "",
            start_page: int = 1,
            end_page: int = -1
        ):
        """
        Initialize the PDFTextExtractor with the bytestream of a PDF file.

        Args:
            pdf_bytes_data (IO[bytes]): The bytestream of the PDF file.
            document_name (str, optional): The name of the pdf document. 
            domain (str, optional): The domain to process specific document types.
            start_page (int, optional): The starting page number for extraction.
                Defaults to 1.
        """
        logging.info("Initializing PDFTextExtractor")
        self.doc = fitz.open("pdf", pdf_bytes_data)
        self.document = Document(
            name = document_name,
            domain = domain.lower(),
            blocks = [],
            headers = [],
            fitz_doc = self.doc
        )
        self.start_page = start_page
        self.end_page = end_page

        domain_has_header_pattern = any(header['domain'] == self.document.domain for header in header_patterns)
        if not domain_has_header_pattern:
            self.document.get_headers_from_toc(self.doc)
            if not self.document.domain:
                self.document.detect_domain()

    def extract_text_from_document(self) -> Document:
        """
        Process the entire PDF document and extract text as structured data.

        Returns:
            Document: A Document object containing the structured text and table blocks.
        """
        logging.info("Starting text extraction from document")

        if self.end_page == -1:
            self.end_page = len(self.doc)
        for page_number in range(self.start_page, self.end_page + 1):
            page = self.doc[page_number - 1]
            printed_page_number = self.extract_page_number(page)

            table_blocks = self.extract_table_blocks_from_page(
                page, page_number
            )
            all_blocks = self.assign_text_to_blocks(
                page, page_number, printed_page_number, table_blocks
            )
            self.document.blocks.extend(all_blocks)

        logging.debug("Extracted Blocks count: %d", len(self.document.blocks))
        merged_text_blocks = self.merge_text_blocks(
            self.document.blocks,
            x_threshold=10.0,
            y_threshold=5.0
        )
        logging.debug("Merged Text Blocks: %d", len(merged_text_blocks))

        self.document.blocks = PDFTableTransformer(merged_text_blocks).merge_table_cells()
        self.document = DocumentOrganizer(self.document, self.document.domain).organize_document_blocks()
        logging.info("Completed text extraction from document")
        return self.document

    def extract_page_number(self, page: fitz.Page) -> str:
        """
        Extract the most likely page number from the footer of the page.

        Args:
            page (fitz.Page): The PDF page from which to extract the page number.

        Returns:
            str: The extracted page number or 'Unknown' if not found.
        """
        text_dict = page.get_text("dict")
        page_number_pattern = re.compile(
            r'\b(\d{1,5}|[ixvdlcIXVDLC]{1,5})\b'  # arabic or roman numerals
        )
        candidates = []
        for block in text_dict['blocks']:
            if block['type'] == 0:
                for line in block['lines']:
                    for span in line['spans']:
                        if re.match(page_number_pattern, span['text']):
                            if span['bbox'][3] > page.rect.height - 50:
                                candidates.append((span['text'], span['bbox']))
        if candidates:
            candidates.sort(key=lambda x: -x[1][3])
            return candidates[0][0]
        logging.debug("Page number not found, returning 'Unknown'")
        return "Unknown"

    def extract_table_blocks_from_page(
            self, page: fitz.Page, page_number: int
        ) -> List[TableBlock]:
        """
        Extracts table blocks from a given PDF page.

        This method identifies all tables on the specified PDF page, and processes
        each table to determine the bounding boxes, row spans, and column spans of
        each cell. It returns a list of TableBlock objects that describe the structure
        and layout of the tables on the page.

        Args:
            page (fitz.Page): The PDF page from which to extract table blocks.
            page_number (int): The number of the page in the PDF document.

        Returns:
            List[TableBlock]: A list of TableBlock objects representing the tables
            found on the page.

        Notes:
            - The method processes only non-empty cells.
            - Row spans and column spans are calculated based on the cell's position
              and the table's bounding box.
            - The TableBlock objects include metadata such as the page number, table
              index, row index, cell index, bounding box, and span information.
        """
        table_blocks = []
        tables = page.find_tables().tables
        for table_index, table in enumerate(tables):
            max_row_count = len(table.rows)
            max_col_count = max(len(row.cells) for row in table.rows)
            table_bbox = table.bbox

            for row_index, row in enumerate(table.rows):
                for col_index, cell in enumerate(row.cells):
                    if cell:  # Only process non-empty cells
                        cell_bbox = cell
                        rowspan, colspan = 1, 1

                        # Determine rowspan
                        if cell[3] == table_bbox[3]:
                            rowspan += (max_row_count - 1 - row_index)
                        else:
                            for ri in range(row_index + 1, max_row_count):
                                if col_index < len(table.rows[ri].cells):
                                    next_cell = table.rows[ri].cells[col_index]
                                    if next_cell == cell_bbox:
                                        rowspan += 1
                                    elif next_cell is None:
                                        rowspan += 1
                                    else:
                                        break
                                else:
                                    break

                        # Determine colspan
                        if cell[2] == table_bbox[2]:
                            colspan += (max_col_count - 1 - col_index)
                        else:
                            for ci in range(col_index + 1, max_col_count):
                                if (row_index < len(table.rows) and
                                    ci < len(table.rows[row_index].cells)):
                                    next_cell = table.rows[row_index].cells[ci]
                                    if next_cell == cell_bbox:
                                        colspan += 1
                                    elif next_cell is None:
                                        colspan += 1
                                    else:
                                        break
                                else:
                                    break

                        table_block = TableBlock(
                            page_number = page_number,
                            block_type = "table",
                            table_index = table_index,
                            row_index = row_index,
                            col_index = col_index,
                            bbox = cell_bbox,
                            text = "",
                            rowspan = rowspan,
                            colspan = colspan
                        )
                        table_blocks.append(table_block)
        return table_blocks

    @staticmethod
    def is_within_bbox(block_bbox, cell_bbox) -> bool:
        """
        Check if the block bounding box is within the cell bounding box.

        Args:
            block_bbox (tuple): The bounding box of the block.
            cell_bbox (tuple): The bounding box of the cell.

        Returns:
            bool: True if the block bounding box is within the cell bounding box,
            False otherwise.
        """
        return (
            cell_bbox[0] <= block_bbox[0] and
            cell_bbox[1] <= block_bbox[1] and
            cell_bbox[2] >= block_bbox[2] and
            cell_bbox[3] >= block_bbox[3]
        )

    @staticmethod
    def expand_bbox(cell_bbox, block_bbox):
        """
        Expand the cell bounding box to include the block bounding box.

        Args:
            cell_bbox (tuple): The bounding box of the cell.
            block_bbox (tuple): The bounding box of the block.

        Returns:
            tuple: The expanded bounding box.
        """
        x0 = min(cell_bbox[0], block_bbox[0])
        y0 = min(cell_bbox[1], block_bbox[1])
        x1 = max(cell_bbox[2], block_bbox[2])
        y1 = max(cell_bbox[3], block_bbox[3])
        return (x0, y0, x1, y1)

    def assign_text_to_blocks(
            self, page, page_number, printed_page_number, table_blocks: List[TableBlock]
        ) -> List[Union[TableBlock, TextBlock]]:
        """
        Assign text to the corresponding table blocks on the PDF page.

        Args:
            page (fitz.Page): The PDF page to process.
            page_number (int): The number of the page in the PDF document.
            printed_page_number (str): The printed page number extracted from the page.
            table_blocks (List[TableBlock]): A list of table blocks to assign text to.

        Returns:
            List[Union[TableBlock, TextBlock]]: A list of table blocks and text blocks
            with assigned text.
        """
        blocks = page.get_text("dict")['blocks']
        blocks.sort(key=lambda block: (block['bbox'][1], block['bbox'][0]))

        all_blocks = table_blocks.copy()
        order_number = 0

        for block in blocks:
            if 'lines' in block and block['type'] == 0:
                for line in block['lines']:
                    for span in line['spans']:
                        span_text = span['text'].strip()
                        if (
                            not span_text or self.should_ignore_text(
                            span_text, printed_page_number
                        )):
                            continue
                        block_bbox = span['bbox']
                        block_text = replace_ligatures(span['text'])

                        added_to_table_block = False
                        for table_block in table_blocks:
                            if self.is_within_bbox(block_bbox, table_block.bbox):
                                table_block.text  = (
                                    table_block.text + ' ' + block_text
                                ).strip()
                                table_block.bbox = self.expand_bbox(
                                    table_block.bbox, block_bbox
                                )
                                table_block.order_number = order_number
                                table_block.printed_page_number = printed_page_number
                                table_block.font = span['font']
                                table_block.size = span['size']
                                table_block.color = span['color']
                                added_to_table_block = True
                                break
                        if not added_to_table_block:
                            text_block = TextBlock(
                                page_number = page_number,
                                printed_page_number = printed_page_number,
                                block_type = "text",
                                order_number = order_number,
                                bbox = block_bbox,
                                font = span['font'],
                                size = span['size'],
                                color = span['color'],
                                text = block_text
                            )
                            all_blocks.append(text_block)
                            order_number += 1
        return all_blocks

    def should_ignore_text(self, text: str, printed_page_number: str) -> bool:
        """
        Determine if the text should be ignored based on its similarity
        to the page number.

        Args:
            text (str): The text to check.
            printed_page_number (str): The printed page number extracted from the page.

        Returns:
            bool: True if the text should be ignored, False otherwise.
        """
        if text == printed_page_number:
            return True
        if text.startswith("Published"):
            return True
        if text.startswith("[Click&type]"):
            return True
        return False

    def merge_text_blocks(
            self,
            blocks: List[Union[TextBlock, TableBlock]],
            x_threshold: float = 10.0,
            y_threshold: float = 5.0
        ) -> List[Union[TextBlock, TableBlock]]:
        """
        Merge adjacent text blocks into coherent units if they meet specified
        proximity thresholds.

        Args:
            blocks (List[Union[TextBlock, TableBlock]]): The list of text and table
                blocks to merge.
            x_threshold (float, optional): The maximum horizontal distance between
                blocks to merge. Defaults to 10.0.
            y_threshold (float, optional): The maximum vertical distance between
                blocks to merge. Defaults to 5.0.

        Returns:
            List[Union[TextBlock, TableBlock]]: A list of merged text and table blocks.
        """
        logging.debug(
            "Merging text blocks with thresholds x: %f, y: %f",
            x_threshold, y_threshold
        )
        merged_blocks = []
        current_block = None
        for block in blocks:
            if current_block is None:
                current_block = block
            else:
                can_merge = False
                if (current_block.block_type == block.block_type == 'text' and
                    (
                        are_on_same_line(current_block.bbox, block.bbox) or
                        are_consecutive_lines(current_block.bbox, block.bbox)
                    ) and
                    current_block.page_number == block.page_number and
                    current_block.size == block.size):
                    can_merge = True
                elif (current_block.block_type == block.block_type == 'table' and
                    current_block.page_number == block.page_number and
                    current_block.table_index == block.table_index and
                    current_block.row_index == block.row_index and
                    current_block.col_index == block.col_index and
                    current_block.size == block.size):
                    can_merge = True
                if can_merge:
                    current_block.merge_with(block)
                else:
                    merged_blocks.append(current_block)
                    current_block = block
        if current_block:
            merged_blocks.append(current_block)
        return merged_blocks
