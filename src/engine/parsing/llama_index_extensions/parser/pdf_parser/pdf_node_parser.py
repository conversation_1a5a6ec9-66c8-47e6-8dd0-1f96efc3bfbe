"""
PDF Node Parser

This module parses a PDF document into structured text nodes using 
spacy for NLP and PyMuPDF for PDF processing. It extracts text and 
metadata, organizes document headers, and calculates statistical 
summaries of the parsed nodes.
"""

import json
import logging
from typing import IO, Any, Dict, List, Tuple

import numpy as np
import pandas as pd
import spacy
from llama_index.core.node_parser import NodeParser
from llama_index.core.schema import TextNode

from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import (  # pylint: disable=line-too-long
    Document,
    DocumentHeader,
)
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_text_extractor import (
    PDFTextExtractor,  # pylint: disable=line-too-long
)

# logger = logging.getLogger(__name__)

nlp = spacy.load("en_core_web_sm")


def generate_header_path_as_json(headers: List[str]):
    """
    Generate a JSON representation of the header path.

    Args:
        headers (list): A list of header names.

    Returns:
        list: A list of dictionaries representing the header path.
    """
    header_path = []
    for level, name in enumerate(headers):
        header_path.append({"level": level, "name": name})
    # return json.dumps(header_path, indent=4)
    return header_path


class PDFNodeParser(NodeParser):
    """
    A class to parse PDF documents into structured text nodes using spacy
    for NLP and PyMuPDF for PDF processing. This class extracts text and
    metadata, organizes document headers, and calculates statistical
    summaries of the parsed nodes.
    """

    def _parse_nodes(
        self,
        document: IO[bytes],
        document_name: str = None,
        domain: str = None,
        start_page: int = 1,
        end_page: int = -1,
        show_progress: bool = False,
        **kwargs: Any
    ) -> List[TextNode]:
        """
        Parses nodes from the given PDF document.

        Args:
            document (IO[bytes]): The PDF document as a byte stream.
            show_progress (bool): Whether to show progress during parsing.
            **kwargs: Additional keyword arguments.

        Returns:
            List[TextNode]: A list of parsed TextNode objects.
        """
        logging.info("%s - Starting to parse nodes from document", self.__class__.__name__)
        extractor = PDFTextExtractor(document, document_name, domain, start_page, end_page)
        ordered_document = extractor.extract_text_from_document()
        nodes = self.get_nodes_from_ordered_document(ordered_document, start_page, end_page)
        logging.info("%s - Finished parsing nodes from document", self.__class__.__name__)
        return nodes

    def get_nodes_from_ordered_document(
        self, ordered_document: Document, start_page: int = 1, end_page: int = -1
    ) -> List[TextNode]:
        """
        Parses documents into nodes.
        :param documents: List of Document instances
        :return: List of TextNode instances
        """
        logging.debug("%s - Starting to get nodes from ordered document", self.__class__.__name__)
        nodes = []
        document_metadata = ordered_document.get_metadata()

        base_headers, header_paths = self.traverse_headers(ordered_document.headers)
        for header, path in zip(base_headers, header_paths):
            aggregated_text = " ".join(block.text for block in header.blocks)
            if not header.blocks:
                continue

            starting_page_number = float("inf")
            starting_printed_page_number = 0
            ending_page_number = -1
            ending_printed_page_number = 0

            blocks_metadata = []
            for block in header.blocks:
                if start_page <= block.page_number < starting_page_number:
                    starting_page_number = block.page_number
                    starting_printed_page_number = block.printed_page_number

                if max(start_page, ending_page_number) <= block.page_number:
                    ending_page_number = block.page_number
                    ending_printed_page_number = block.printed_page_number
                blocks_metadata.append({"page_number": block.page_number, "bbox": block.bbox})

            if starting_page_number == float("inf"):
                starting_page_number = 0
            if ending_page_number == -1:
                ending_page_number = 0

            doc = nlp(aggregated_text)
            tokens = [token.text for token in doc]

            metadata = {
                "character_count": len(aggregated_text),
                "word_count": len(aggregated_text.split()),
                "token_count": len(tokens),
                "starting_page_number": starting_page_number,
                "ending_page_number": ending_page_number,
                "starting_printed_page_number": int(
                    pd.to_numeric(starting_printed_page_number, errors="coerce")
                    if not pd.isna(pd.to_numeric(starting_printed_page_number, errors="coerce"))
                    else 0
                ),
                "ending_printed_page_number": int(
                    pd.to_numeric(ending_printed_page_number, errors="coerce")
                    if not pd.isna(pd.to_numeric(ending_printed_page_number, errors="coerce"))
                    else 0
                ),
                "header_name": header.name,
                # "header_path": generate_header_path_as_json(path),
                "domain": ordered_document.domain,
                # "document_metadata": document_metadata,
                "blocks": blocks_metadata,
            }
            node = TextNode(metadata=metadata, text=aggregated_text)
            nodes.append(node)
            logging.debug(
                "%s - Created TextNode with metadata: %s",
                {self.__class__.__name__},
                json.dumps(metadata, indent=2),
            )

        logging.debug("%s - Finished getting nodes from ordered document", self.__class__.__name__)
        return nodes

    @staticmethod
    def traverse_headers(
        headers: List[DocumentHeader], path=[]
    ) -> Tuple[List[DocumentHeader], List[str]]:
        """Recursively traverses document headers to gather metadata and text."""
        logging.debug("PDFNodeParser - Starting to traverse headers")
        base_headers = []
        header_paths = []

        for header in headers:
            current_path = path + [header.name]
            if not header.headers:  # lowest level
                base_headers.append(header)
                header_paths.append(current_path)
            else:
                base_headers.append(header)
                header_paths.append(current_path)
                # Recursively process nested headers
                new_headers, new_paths = PDFNodeParser.traverse_headers(
                    header.headers, current_path
                )
                base_headers.extend(new_headers)
                header_paths.extend(new_paths)

        return base_headers, header_paths

    def calculate_node_statistics(self, nodes: List[Dict]) -> Dict[str, float]:
        """
        Calculates statistical summaries of token lengths in nodes.

        Args:
            nodes (List[Dict]): The nodes to calculate statistics for.

        Returns:
            Dict[str, float]: A dictionary containing statistics.
        """
        logging.info("%s - Calculating node statistics", self.__class__.__name__)
        parsed_characters = [node.metadata["character_count"] for node in nodes]
        parsed_words = [node.metadata["word_count"] for node in nodes]
        parsed_tokens = [node.metadata["token_count"] for node in nodes]

        if not parsed_tokens:
            return {
                "min_tokens_per_node": 0,
                "max_tokens_per_node": 0,
                "avg_tokens_per_node": 0,
                "10th_percentile": 0,
                "25th_percentile": 0,
                "50th_percentile": 0,
                "75th_percentile": 0,
                "90th_percentile": 0,
                "parsed_nodes": 0,
                "parsed_characters": 0,
                "parsed_words": 0,
                "parsed_tokens": 0,
            }

        min_length = min(parsed_tokens)
        max_length = max(parsed_tokens)
        avg_length = sum(parsed_tokens) / len(parsed_tokens)
        p10 = np.percentile(parsed_tokens, 10)
        p25 = np.percentile(parsed_tokens, 25)
        p50 = np.percentile(parsed_tokens, 50)
        p75 = np.percentile(parsed_tokens, 75)
        p90 = np.percentile(parsed_tokens, 90)

        stats = {
            "min_tokens_per_node": min_length,
            "max_tokens_per_node": max_length,
            "avg_tokens_per_node": avg_length,
            "10th_percentile": p10,
            "25th_percentile": p25,
            "50th_percentile": p50,
            "75th_percentile": p75,
            "90th_percentile": p90,
            "parsed_nodes": len(nodes),
            "parsed_characters": sum(parsed_characters),
            "parsed_words": sum(parsed_words),
            "parsed_tokens": sum(parsed_tokens),
        }
        logging.info(
            "%s - Calculated node statistics: %s",
            self.__class__.__name__,
            json.dumps(stats, indent=2),
        )
        return stats
