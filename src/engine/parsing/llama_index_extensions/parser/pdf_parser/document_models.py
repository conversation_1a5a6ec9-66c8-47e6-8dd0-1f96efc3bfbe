"""
Document Content and Block Modeling

This module defines a set of Pydantic models to represent the structured
components of documents, such as blocks of text, tables, and hierarchical
headers. It supports detailed text processing and organization within
documents, facilitating easy extraction, manipulation, and analysis of
text data.

Classes:
    Block (BaseModel):
        Represents the basic structure of a text block within a document,
        including details such as page number, block type, and bounding box
        coordinates.
    TextBlock (Block):
        A specialization of Block for regular text content.
    TableBlock (Block):
        Extends Block to handle text blocks specifically within table
        structures, including table, row, and cell indices. Provides
        methods to concatenate text with other TableBlocks.
    ContentSection (BaseModel):
        Abstract base model for components of a document that can contain other
        blocks or headers. Provides methods for managing blocks and headers and
        calculating text statistics.
    DocumentHeader (ContentSection):
        Represents a document header, which can organize blocks and sub-headers
        within a hierarchical structure.
    Document (ContentSection):
        Represents the entire document, potentially including authorship and
        publication date, along with its content structure.

Features:
    - Models are based on Pydantic for robust data validation and settings
        management.
    - The use of union types and optional fields allows for flexible data
        structures.
    - Methods are provided for counting characters and words, clearing
        content, and concatenating blocks, aiding in text analysis tasks.

Usage:
    document = Document(
        name='My Document',
        headers=[],
        blocks=[]
    )
    header = DocumentHeader(
        name='Chapter 1',
        level=0,
        headers=[],
        blocks=[]
    )
    block = TextBlock(
        page_number=1,
        block_type='text',
        block_number=1,
        text='Hello, world!'
    )
    document.blocks.append(block)
    document.headers.append(header)
    # Use Document and its sub-components to structure and analyze document
        content.
"""

from typing import List, Optional, Tuple, Union
from datetime import date
from collections import Counter
import re
import json
import logging
import fitz
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class Block(BaseModel):
    """Represents a basic structure of a text block within a document."""
    page_number: Optional[int]=None
    printed_page_number: Optional[str]=None
    block_type: Optional[str]=None
    block_number: Optional[int]=None
    order_number: Optional[int]=None
    font: Optional[str]=""
    size: Optional[float]=0
    color: Optional[int]=None
    bbox: Optional[Tuple[float, float, float, float]]=None  # (x0, y0, x1, y1)
    text: Optional[str]=""

    def __init__(self, **data):
        super().__init__(**data)
        if self.text:
            self.text = self.clean_text()

    def clean_text(self) -> str:
        """
        Clean up the text by removing multiple sequential periods.

        Args:
            text (str): The text to be cleaned.

        Returns:
            str: The cleaned text.
        """
        cleaned_text = re.sub(r'\.{2,}', '', self.text)  # Removes sequential periods
        return cleaned_text

    def count_non_space_characters(self):
        """Count the number of non-space characters in the text."""
        return len(self.text.replace(" ", ""))

    def count_words(self):
        """Counts words as sequences of characters separated by whitespace."""
        return len(re.findall(r'\b\w+\b', self.text))

    class Config:
        """Config switch to allow key hashing"""
        frozen = False  # True for hashing / False for cell merging

    def __hash__(self):
        """Generate a hash value for the block."""
        return hash((self.page_number, self.text, self.bbox))


class TextBlock(Block):
    """A specialization of Block for regular text content."""
    block_type: str = "text"

    def merge_with(self, other: 'TextBlock') -> None:
        """Concatenate the text of another TextBlock into this one,
            adjusting bbox."""
        logging.debug(
            "Merging block %s with block %s",
            self.block_number,
            other.block_number
        )
        if (
            other.text.strip() and
            other.text.strip() not in self.text.strip()  # pylint: disable=no-member
        ):  # pylint: disable=no-member
            logging.debug(
                "Concatenating text: '%s' + '%s'",
                self.text,
                other.text
            )  # pylint: disable=no-member
            self.text += " " + other.text  # pylint: disable=no-member
        self.bbox = (
            min(self.bbox[0], other.bbox[0]),
            min(self.bbox[1], other.bbox[1]),
            max(self.bbox[2], other.bbox[2]),
            max(self.bbox[3], other.bbox[3])
        )
        self.order_number = min(self.order_number, other.order_number)
        if ("bold" in self.font.lower() and "bold" not in other.font.lower()):
            self.font = other.font


class TableBlock(Block):
    """Extends Block to handle text blocks specifically within table structures."""
    table_index: int
    row_index: int
    col_index: int
    rowspan: int = 1
    colspan: int = 1

    def merge_with(self, other: [Block]) -> None:
        """Concatenate the text of another TableBlock into this one, adjusting bbox."""
        logging.debug(
            "Merging %s block %s with %s block %s",
            self.block_type,
            self.block_number,
            other.block_type,
            other.block_number
        )
        if (
            other.text.strip() and
            other.text.strip() not in self.text.strip()  # pylint: disable=no-member
        ):  # pylint: disable=no-member
            logging.debug(
                "Concatenating text: '%s' + '%s'",
                self.text,
                other.text
            ) # pylint: disable=no-member
            self.text += " " + other.text # pylint: disable=no-member
        logging.debug(
            "Merging bounding boxes from self (%s) and other (%s)",
            self.bbox,
            other.bbox
        )
        self.bbox = (
            min(self.bbox[0], other.bbox[0]),
            min(self.bbox[1], other.bbox[1]),
            max(self.bbox[2], other.bbox[2]),
            max(self.bbox[3], other.bbox[3])
        )
        if self.col_index == other.col_index:
            self.rowspan = max(
                self.rowspan,
                self.row_index + self.rowspan - other.row_index
            )
        if self.row_index == other.row_index:
            self.colspan = max(
                self.colspan,
                self.col_index + self.colspan - other.col_index
            )
        self.order_number = min(self.order_number, other.order_number)

class TranscriptBlock(Block):
    """A specialization of Block for transcript lines."""
    block_type: str = "transcript"
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    speaker: Optional[str] = None
    parsed_line: Optional[str] = None
    identifier: Optional[str] = None

    def merge_with(self, other: 'TranscriptBlock') -> None:
        """Concatenate the text of another TranscriptBlock into this one,
            adjusting bbox and order number."""
        logging.debug(
            "Merging block %s with block %s",
            self.block_number,
            other.block_number
        )
        if (
            other.text.strip() and
            other.text.strip() not in self.text.strip()  # pylint: disable=no-member
        ):  # pylint: disable=no-member
            logging.debug(
                "Concatenating text: '%s' + '%s'",
                self.text,
                other.text
            )  # pylint: disable=no-member
            self.text += " " + other.text  # pylint: disable=no-member
        self.order_number = min(self.order_number, other.order_number)
        self.start_time = min(self.start_time, other.start_time)
        self.end_time = max(self.end_time, other.end_time)
        self.generate_parsed_line()

    def generate_parsed_line(self):
        """Generate the parsed_line attribute based on the block's data."""
        self.parsed_line = f"<{self.start_time}> {self.speaker}: {self.text}"

    def to_dict(self):
        """Convert the TranscriptBlock to a dictionary format."""
        return {
            "start_time": self.start_time,
            "end_time": self.end_time,
            "speaker": self.speaker,
            "text": self.text,
            "parsed_line": self.parsed_line,
            "identifier": self.identifier
        }


class ContentSection(BaseModel):
    """Abstract base model for components of a document that can contain
        other blocks or headers."""
    name: Optional[str] = None
    blocks: List[Union['TextBlock', 'TableBlock']]=Field(default_factory=list)
    headers: List['DocumentHeader']=Field(default_factory=list)

    def clear_blocks(self):
        """Clear all blocks in the section."""
        self.blocks = []

    def clear_headers(self):
        """Clear all headers in the section."""
        self.headers = []

    @property
    def calculate_non_space_characters(self):
        """Calculate the total number of non-space characters in all blocks."""
        return sum(block.count_non_space_characters() for block in self.blocks)

    @property
    def calculate_total_words(self):
        """Calculate the total number of words in all blocks."""
        return sum(block.count_words() for block in self.blocks)

    @property
    def get_section_text(self):
        """Get the concatenated text of all blocks in the section."""
        return "\n".join(block.text for block in self.blocks)

    @property
    def get_nested_headers(self) -> List['DocumentHeader']:
        """Get all nested headers recursively."""
        nested_headers = []
        for header in self.headers:
            nested_headers.append(header)
            nested_headers.extend(header.get_nested_headers)
        return nested_headers

    @property
    def get_all_blocks(self):
        """Get all blocks including those in nested headers recursively."""
        all_blocks = self.blocks.copy()
        for header in self.headers:
            all_blocks.extend(header.get_all_blocks)
        return all_blocks
    
    def sort(self):
        """Sort the headers and blocks recursively."""
        self.headers.sort(key=lambda header: (header.starting_page_number, header.level))
        self.blocks.sort(key=lambda block: (block.page_number, block.bbox[1], block.bbox[0]))
        for header in self.headers:
            header.sort()
        logging.info("Sorted headers and blocks for section: %s", self.name)


class DocumentHeader(ContentSection):
    """Represents a document header, organizing blocks and
        sub-headers within a hierarchy."""
    level: int
    starting_page_number: Optional[int]=0


class Document(ContentSection):
    """Represents the entire document with its content structure and metadata."""
    domain: Optional[str] = None
    format: Optional[str] = None
    title: Optional[str] = None
    author: Optional[str] = None
    subject: Optional[str] = None
    keywords: Optional[str] = None
    creator: Optional[str] = None
    creation_date: Optional[str] = None
    modified_date: Optional[str] = None
    published_date: Optional[str] = None

    def __init__(self, **data):
        """Initialize the Document with optional fitz document metadata."""
        super().__init__(**data)
        if 'fitz_doc' in data:
            try:
                self.load_metadata_from_fitz(data['fitz_doc'])
            except (RuntimeError, ValueError) as e:
                logging.error("Failed to load metadata from fitz document: %s", e)
        if 'name' not in data or not data['name']:
            self.name = "Default Document Name"


    def load_metadata_from_fitz(self, doc: fitz.Document):
        """Load metadata from a fitz Document object."""
        metadata = doc.metadata
        self.format = metadata.get('format')
        self.title = metadata.get('title')
        self.author = metadata.get('author')
        self.subject = metadata.get('subject')
        self.keywords = metadata.get('keywords')
        self.creator = metadata.get('creator')
        self.creation_date = metadata.get('creationDate')
        self.modified_date = metadata.get('modDate')

        # Clean up date formats
        date_pattern = re.compile(r'D:(\d{4})(\d{2})(\d{2})')
        if self.creation_date:
            match = date_pattern.search(self.creation_date)
            if match:
                self.creation_date = (
                    f"{match.group(1)}-"
                    f"{match.group(2)}-"
                    f"{match.group(3)}"
                )
        if self.modified_date:
            match = date_pattern.search(self.modified_date)
            if match:
                self.modified_date = (
                    f"{match.group(1)}-"
                    f"{match.group(2)}-"
                    f"{match.group(3)}"
                )
        if self.published_date:
            match = date_pattern.search(self.published_date)
            if match:
                self.published_date = (
                    f"{match.group(1)}-"
                    f"{match.group(2)}-"
                    f"{match.group(3)}"
                )

    def get_metadata(self, *fields):
        """Get metadata fields specified, or all if no fields specified."""
        if fields:
            metadata = {field: getattr(self, field, None) for field in fields}
        else:
            metadata = {
                'name': self.name,
                'domain': self.domain,
                'format': self.format,
                'title': self.title,
                'author': self.author,
                'subject': self.subject,
                'keywords': self.keywords,
                'creator': self.creator,
                'creation_date': self.creation_date,
                'modified_date': self.modified_date,
                'published_date': self.published_date
            }
        formatted_metadata = {k: (v if isinstance(v, (str, int, float, bool, list)) else str(v)) for k, v in metadata.items()}
        return formatted_metadata


    def get_headers_from_toc(self, fitz_doc: fitz.Document) -> None:
        """Build headers from the table of contents (TOC) of a fitz Document."""
        def build_headers(
                toc: List[List[Union[int, str]]],
                toc_header_names: Counter,
                current_level: int = 0,
                index: int = 0
            ) -> Tuple[List[DocumentHeader], int]:
            headers = []
            logging.debug(
                "Building headers at level %d starting at index %d",
                current_level,
                index
            )
            while index < len(toc):
                level, name, page = toc[index]
                if name in toc_header_names:
                    index += 1
                    continue
                logging.debug(
                    "Processing TOC entry: level=%d, name=%s, page=%d",
                    level,
                    name,
                    page
                )
                if level == current_level:
                    header = DocumentHeader(
                        level=level,
                        name=name,
                        starting_page_number=page,
                        blocks=[],
                        headers=[]
                    )
                    logging.debug("Adding header: %s", header)
                    index += 1
                    header.headers, index = build_headers(toc, toc_header_names, current_level + 1, index)
                    headers.append(header)
                elif level < current_level:
                    logging.debug(
                        "Returning to previous level. Current headers: %s",
                        headers
                    )
                    return headers, index
                else:
                    child_headers, index = build_headers(toc, toc_header_names, level, index)
                    logging.debug("Appending child headers: %s", child_headers)
                    if headers:
                        headers[-1].headers.extend(child_headers)
                    else:
                        headers.extend(child_headers)
            logging.debug("Completed level %d headers: %s", current_level, headers)
            return headers, index

        try:
            toc = fitz_doc.get_toc()
            logging.debug("Retrieved TOC: %s", toc)

            if not toc:
                raise ValueError("No Table of Contents available in metadata")

            name_counts = Counter(name for level, name, page in toc)
            toc_header_names = [name for name, count in name_counts.items() if count > 1]

            self.headers, _ = build_headers(toc, toc_header_names, 0, 0)
            logging.info("Completed building headers")
        except (RuntimeError, ValueError) as e:
            logging.error("Failed to build headers from TOC: %s", e)

    def detect_domain(self):
        """
        Detect the domain of the document by searching for specific text within the
        highest level headers.

        This method iterates over the headers at level 1 and checks if they contain
        specific keywords to determine the document's domain. If a keyword is found,
        the domain is set according to the corresponding value in the keyword-to-domain
        mapping.

        Side Effects:
            - Sets the `domain` attribute of the instance based on the detected text
            in the headers.
            - Logs the detected domain or a warning if no domain is detected.

        Returns:
            None
        """
        # Dictionary mapping keywords to domains
        keyword_to_domain = {
            "Fannie Mae": "fnma",
            "FNMA": "fnma",
            "Freddie Mac": "fhlmc",
            "FHLMC": "fhlmc",
            "Consumer Financial Protection Bureau": "cfpb",
            "CFPB": "cfpb",
            "Federal Housing Administration": "fha",
            "FHA": "fha",
            "Veterans Administration": "va",
            "VA": "va"
            # Add more keywords and corresponding domains here
        }

        # Iterate over the highest level headers (level 1)
        for header in self.headers:
            if header.level == 1:
                for keyword, domain in keyword_to_domain.items():
                    if keyword.lower() in header.name.lower():
                        self.domain = domain
                        logging.info(f"Domain detected: {domain}")
                        return

        logging.warning("Domain could not be detected from headers")
        
DocumentHeader.model_rebuild()
