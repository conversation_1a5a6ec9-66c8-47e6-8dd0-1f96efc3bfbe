# header_patterns.py

header_patterns = [
    {
        'domain': 'fnma',
        'name': "Part",
        'pattern': r"^Part\s[A-Z],\s.*?",
        # alt-'pattern': r"^Part\s[A-Z]$",  # Example: Part A
        'level': 0
    },
    {
        'domain': 'fnma',
        'name': "Subpart",
        'pattern': r"^Subpart\s[A-Z0-9]+,\s",
        # alt-'pattern': r"^Subpart\s[A-Z0-9]\d$",  # Example: Subpart A1
        'level': 1
    },
    {
        'domain': 'fnma',
        'name': "Chapter",
        'pattern': r"^Chapter\s[A-Z\-]+",
        # alt-'pattern': r"^Chapter\s[A-Z]\d-\d$",  # Example: Chapter A1-1
        'level': 2
    },
    {
        'domain': 'fnma',
        'name': "Section",
        'pattern': r"Section\s[A-Z0-9\.\-]+(?:,\s*.*?)*(?=\s+\.{2,}|\s*$)",
        # alt-'pattern': r"^Section\s[A-Z]\d-\d\.\d$",  # Example: Section A1-1.1
        'level': 3
    },
    {
        'domain': 'fnma',
        'name': "Topic",
        'pattern': r"^[A-Z0-9\.\-]+\-\d{2}",
        # alt-'pattern': r"^[A-Z]\d-\d\.\d-01$",  # Example: A1-1.1-01
        'level': 4
    }
]

""" 
    {
        'domain': 'fha',
        'name': "Level 1 Header",
        'pattern': r"^Section [IVX]+ – .+",
        # Pattern to match headers starting with "Section" followed by Roman numerals and a dash.
        # Example: Section I – Doing Business with FHA
        'level': 0
    },
    {
        'domain': 'fha',
        'name': "Level 2 Header",
        'pattern': r"^[IVX]+\.[A-Z]+[0-9]*\. .+",
        # Pattern to match headers starting with Roman numerals followed by a period,
        # an uppercase letter, optionally followed by digits and another period.
        # Example: I.A.3.b.ii(B) Application Documentation – Required Documentation
        'level': 1
    },
    {
        'domain': 'fha',
        'name': "Level 3 Header",
        'pattern': r"^[IVX]+\.[A-Z]+\.[0-9]+\.[a-z]+\(.*?\) .+",
        # Pattern to match headers with Roman numerals, uppercase letters, digits, 
        # lowercase letters, and optional parentheses.
        # Example: I.A.3.b.ii(B)(1) Office Facilities – Home Office
        'level': 2
    },
    {
        'domain': 'fha',
        'name': "Level 4 Header",
        'pattern': r"^[IVX]+\.[A-Z]+\.[0-9]+\.[a-z]+\.[0-9]+\..+",
        # Pattern to match headers with an additional level of digits and periods.
        # Example: II.A.1.a.i(A)(1) Contents of the Mortgage Application Package – 
        # Maximum Age of Mortgage Documents
        'level': 3
    },
    {
        'domain': 'fha',
        'name': "Level 5 Header",
        'pattern': r"^[IVX]+\.[A-Z]+\.[0-9]+\.[a-z]+\.[0-9]+\.[a-z]+\..+",
        # Pattern to match headers with further nested structure, including lowercase 
        # letters after digits and periods.
        # Example: II.A.1.a.i(A)(1)(a) Specific Requirement
        'level': 4
    }
    """
