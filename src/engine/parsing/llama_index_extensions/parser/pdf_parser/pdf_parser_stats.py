import pandas as pd
import os
from openpyxl import load_workbook
from openpyxl.styles import Font

def save_statistics(statistics, output_path):
    """
    Save statistics to an Excel file, appending a new row for each run.

    Args:
        statistics (Dict): The statistics to save.
        output_path (str): The file path to save the statistics.
    """
    df = pd.DataFrame([statistics])

    # Reorder the columns to match the desired order
    desired_order = [
        "timestamp", "run_time", "filepath", "domain", "document_size_bytes",
        "raw_characters", "raw_words", "unique_words", "raw_tokens","num_pages",
        "total_nodes", "nodes_per_page", "pages_per_node", "parsed_characters",
        "parsed_words", "parsed_tokens",  "min_tokens_per_node", "max_tokens_per_node",
        "avg_tokens_per_node", "10th_percentile", "25th_percentile", "50th_percentile",
        "75th_percentile", "90th_percentile"
    ]
    df = df.reindex(columns=desired_order)
    
    column_formats = {
        'A': 'YYYY-MM-DD HH:MM:SS',  # Date
        'E': '###,###,##0', 'F': '###,###,##0', 'G': '###,###,##0', 'H': '###,##0',
        'I': '###,###,##0', 'J': '###,##0', 'K': '###,##0', 'L': '#0.000',
        'M': '#0.00', 'N': '###,###,##0', 'O': '###,###,##0', 'P': '###,###,##0',
        'Q': '##0', 'R': '###,##0', 'S': '###,##0', 'T': '#,##0', 'U': '#,##0',
        'V': '#,##0', 'W': '#,##0', 'X': '###,##0'
    }

    if not os.path.isfile(output_path):
        # If file does not exist, create it with headers and format
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)
            workbook = writer.book
            worksheet = writer.sheets['Sheet1']

            # Apply formatting
            header_font = Font(bold=True)
            for col in worksheet.iter_cols(min_row=1, max_row=1):
                for cell in col:
                    cell.font = header_font
            # Set number formatting
            for col_letter, number_format in column_formats.items():
                for cell in worksheet[col_letter]:
                    if cell.row > 1:  # Skip header row
                        cell.number_format = number_format

    else:
        # If file exists, append without headers and format
        with pd.ExcelWriter(output_path, mode='a', engine='openpyxl', if_sheet_exists='overlay') as writer:
            workbook = writer.book
            worksheet = workbook.active
            startrow = worksheet.max_row

            df.to_excel(writer, index=False, header=False, startrow=startrow)

            # Set number formatting for the new rows
            for col_letter, number_format in column_formats.items():
                for cell in worksheet[col_letter]:
                    if cell.row > 1:  # Skip header row
                        cell.number_format = number_format
