"""
This module provides the PdfMarkupParser class, which extends PdfPageParser to detect and process
changed text in PDFs.
"""

import json
import math
import re

import fitz
from langfuse.decorators.langfuse_decorator import observe

from accessor.ai import AiAccessor
from engine.parsing.enum.text_decoration import TextDecoration
from engine.parsing.llama_index_extensions.parser.pdf_page_parser.pdf_page_parser import (
    PdfPageParser,
)
from engine.parsing.llama_index_extensions.parser.pdf_parser import pdf_utils

# -----------------------------
# Module-Level Constants
# -----------------------------

# Thresholds and Tolerances for Classification
STRIKE_RATIO: float = 0.15  # Fraction of character height for strikethrough detection
UNDERLINE_TOL: float = 2.0  # Tolerance in points for underline detection
MIN_HORIZ_OVERLAP: float = 1.0  # Min horizontal overlap in points for classification checks
COLOR_TOL: float = 0.15  # Tolerance for Euclidean distance when matching colors
BLOCK_VERTICAL_GAP_TOL: int = 10  # Vertical gap in points for merging text blocks into paragraphs
OVERLAP_GAP_TOL: int = (
    5  # Overlap in points for considering bounding boxes as part of same paragraph
)
SUPER_SUB_LINE_TOLERANCE_POINTS = 3


GRAY_TOL = 0.05  # Tolearance when looking for gray/black text tweak ± (0‒1 scale); 0.05 ≈ 13/255
LINK_TOL = 0.15  # distance threshold tolerance for hyperlink blue
LINK_BLUE = (0.0, 0.0, 1.0)  # base hyperlink blue
HIGHLIGHT_YELLOW = (1.0, 1.0, 0.0)
HIGHLIGHT_TOL = 0.10  # ≈ 26/255


# PDF Rendering Configuration
DEFAULT_ZOOM: float = 0.5
DEFAULT_DPI: int = 72
DEFAULT_COLORSPACE = fitz.csGRAY


class PdfMarkupParser(PdfPageParser):
    """
    A subclass of PdfPageParser that reads redlined or changed text in PDF documents
    and generates differences in JSON format by using both image-based content
    and identified text input.
    """

    def __init__(self, logger_name: str = "PdfMarkupParser"):
        super().__init__(logger_name)
        self.ai_accessor = AiAccessor

        self.target_colors = [
            (0.82, 0.203, 0.219),  # Red
            (0.0, 0.469, 0.832),  # Blue
            (0.8, 0.208, 0.584),  # Purplish
            (0.282, 0.510, 0.020),  # Green
        ]

    def _process_pdf_with_gpt(self, pdf_bytes: bytes, start_page: int, end_page: int) -> list:
        doc = fitz.open("pdf", pdf_bytes)
        all_pages_content = []

        if end_page == -1:
            end_page = len(doc)

        for page_number in range(start_page - 1, min(end_page, len(doc))):
            self.logger.info("Processing page %d", page_number + 1)
            page = doc.load_page(page_number)

            extracted_changes = self._parse_sentences_with_changes_one_flow(
                page, my_colors=self.target_colors, color_tol=COLOR_TOL
            )

            if not extracted_changes:
                self.logger.info(
                    "No markup changes detected on page %d; skipping GPT processing.",
                    page_number + 1,
                )
                continue

            expanded_changes = self._group_sentences_with_context(extracted_changes)

            identified_text = json.dumps(
                [
                    {"Original": change["Original"], "New": change["Updated"]}
                    for change in expanded_changes
                ],
                ensure_ascii=False,
            )

            page_result = self._process_page_with_gpt(identified_text=identified_text)
            if page_result and page_result.get("content"):
                ascii_json_to_store = json.dumps(page_result["content"], ensure_ascii=False)
                blocks = [{"type": "text", "content": ascii_json_to_store}]
                all_pages_content.append(
                    {
                        "page_number": page_number + 1,
                        "blocks": blocks,
                        "bbox": [page.rect.x0, page.rect.y0, page.rect.x1, page.rect.y1],
                    }
                )

        doc.close()
        return all_pages_content

    @observe(as_type="generation")
    def _process_page_with_gpt(self, page_image_bytes=None, identified_text: str = None) -> dict:
        """
        Process a single PDF page using identified Original/New text.
        """

        final_results = []

        if not identified_text:
            return {"content": []}

        try:
            items = json.loads(identified_text)
        except json.JSONDecodeError:
            self.logger.error("identified_text is not valid JSON.")
            return {"content": []}

        if not isinstance(items, list):
            self.logger.error("identified_text must be a JSON list.")
            return {"content": []}

        for item in items:
            if "Original" in item and isinstance(item["Original"], str):
                item["Original"] = self._clean_text(item["Original"])
            if "New" in item and isinstance(item["New"], str):
                item["New"] = self._clean_text(item["New"])

        for doc_obj in items:
            original_text = doc_obj.get("Original", "")
            new_text = doc_obj.get("New", "")

            changed_text = self._call_text_comparison(original_text, new_text)

            change_statement = self._call_text_comparison_summary(
                original_text, new_text, changed_text
            )

            impact = self._call_text_comparison_impact(
                original_text, new_text, changed_text, change_statement
            )

            final_obj = {
                "original_text": original_text,
                "updated_text": new_text,
                "identifier": "TBD",
                "changed_text": changed_text,
                "impact": impact,
                "change_statement": change_statement,
            }
            final_results.append(final_obj)

        return {"content": final_results}

    def _build_text(self, page: dict) -> str:
        """
        Build a human-readable string from the processed page dictionary.
        """
        blocks = page.get("blocks", [])
        all_content = []
        for block in blocks:
            if block.get("type") == "text":
                block_content = block.get("content", "")
                all_content.append(block_content)
        return "\n".join(all_content)

    def _parse_span_color(self, color_val):
        """
        Convert PyMuPDF's color value into an (r, g, b) tuple in [0..1].
        """
        if isinstance(color_val, tuple) and len(color_val) == 3:
            return color_val
        if isinstance(color_val, int):
            return self._decode_argb_int(color_val)
        if isinstance(color_val, float):
            return (color_val, color_val, color_val)
        return (0.0, 0.0, 0.0)

    def _decode_argb_int(self, color_int: int) -> tuple:
        """
        Decode a signed 32-bit ARGB integer into (r, g, b) floats in [0..1].
        """
        unsigned_32 = color_int & 0xFFFFFFFF
        red = (unsigned_32 >> 16) & 0xFF
        green = (unsigned_32 >> 8) & 0xFF
        blue = unsigned_32 & 0xFF
        return (red / 255.0, green / 255.0, blue / 255.0)

    def _group_sentences_with_context(self, all_sentences):
        """
        Group consecutive changed sentences and expand by one sentence on each side if available.
        """
        changed_indices = [i for i, s in enumerate(all_sentences) if s["is_changed"]]
        if not changed_indices:
            return []

        ranges = []
        start = changed_indices[0]
        prev = start
        for i in changed_indices[1:]:
            if i == prev + 1:
                prev = i
            else:
                ranges.append((start, prev))
                start = i
                prev = i
        ranges.append((start, prev))

        expanded_ranges = []
        used = set()
        for r_start, r_end in ranges:
            self.logger.debug(
                "[_group_sentences_with_context] Original changed range: start=%d, end=%d",
                r_start,
                r_end,
            )
            # Attempt to expand
            left = r_start - 1
            if left >= 0 and left not in used:
                r_start = left

            right = r_end + 1
            if right < len(all_sentences) and right not in used:
                r_end = right

            conflict = any((idx in used) for idx in range(r_start, r_end + 1))
            if conflict:
                # skip or handle merges differently
                r_start, r_end = (r_start + 1, r_end - 1) if (r_start < r_end) else (r_start, r_end)
            self.logger.debug(
                "[_group_sentences_with_context] Final expanded range: start=%d, end=%d",
                r_start,
                r_end,
            )
            for x in range(r_start, r_end + 1):
                used.add(x)
            expanded_ranges.append((r_start, r_end))

        result = []
        for start_idx, end_idx in expanded_ranges:
            chunk_sentences = all_sentences[start_idx : end_idx + 1]

            original_texts = []
            new_texts = []
            for s in chunk_sentences:
                if s["is_changed"]:
                    original_texts.append(s["text_original"])
                    new_texts.append(s["text_new"])
                else:
                    # unchanged => same text in original & updated
                    original_texts.append(s["text_original"])
                    new_texts.append(s["text_original"])

            original_str = " ".join(t.strip() for t in original_texts if t.strip())
            new_str = " ".join(t.strip() for t in new_texts if t.strip())

            result.append(
                {
                    "Original": original_str,
                    "Updated": new_str,
                    "Change": "Your logic to describe the change if needed",
                    "Impact": "Low",
                }
            )

        return result

    def _parse_sentences_with_changes_one_flow(
        self, page: fitz.Page, my_colors, color_tol: float = 0.15
    ) -> list:
        """
        Read the entire page's text in a single, top-to-bottom/left-to-right flow,
        ignoring paragraph or block boundaries. Return a list of sentences,
        marking strikethrough/underline changes.
        """

        # --- 1) Collect drawing lines for strike/underline detection ---
        drawings = page.get_drawings()
        line_info = []
        for d in drawings:
            rect = d["rect"]
            (lx0, ly0, lx1, ly1) = rect
            mid_y = (ly0 + ly1) / 2
            line_info.append({"rect": rect, "mid_y": mid_y})

        # --- 2) Extract all text/spans/chars from the page in "rawdict" form ---
        #     We'll ignore blocks/paragraphs and unify everything into one big char list.
        raw = page.get_text("rawdict")
        if "blocks" not in raw:
            return []

        all_chars = []
        for block in raw["blocks"]:
            if block.get("type", 1) != 0:  # skip non-text blocks
                continue
            for line in block.get("lines", []):
                for span in line.get("spans", []):
                    color_val = span.get("color", 0)
                    text_color = self._parse_span_color(color_val)

                    # Collect each character
                    if "chars" not in span:
                        continue
                    for c in span["chars"]:
                        (cx0, cy0, cx1, cy1) = c["bbox"]
                        ch = c["c"]

                        # Initialize flags for both strike and underline
                        is_strike = False
                        is_underline = False

                        # Only check for changes if the text color is not body color
                        # or is in target colors.
                        if self._is_target_color(
                            text_color, self.target_colors, COLOR_TOL
                        ) or not self._is_ignored_color(text_color):
                            # run strike/underline checks

                            char_mid_y = (cy0 + cy1) / 2
                            char_h = cy1 - cy0

                            for li in line_info:
                                (rx0, ry0, rx1, ry1) = li["rect"]
                                line_mid_y = li["mid_y"]
                                overlap_w = min(cx1, rx1) - max(cx0, rx0)
                                if overlap_w < MIN_HORIZ_OVERLAP:
                                    continue

                                # --- revised precedence logic ------------------------------------
                                # 1) first see if the line sits near the glyph bottom → underline
                                if abs(line_mid_y - cy1) <= UNDERLINE_TOL:
                                    is_underline = True
                                    self.logger.debug(
                                        "[underline-from-drawing] '%s' vgap %.2f pt",
                                        ch,
                                        abs(line_mid_y - cy1),
                                    )
                                # 2) otherwise test for a mid-line strike
                                elif abs(line_mid_y - char_mid_y) <= (STRIKE_RATIO * char_h):
                                    is_strike = True
                                    self.logger.debug(
                                        "[strikethrough] '%s' d=%.2f pt (ratio %.3f)",
                                        ch,
                                        abs(line_mid_y - char_mid_y),
                                        abs(line_mid_y - char_mid_y) / char_h,
                                    )

                        # ---- final classification ---------------------------------
                        if is_underline:
                            classification = TextDecoration.UNDERLINE
                        elif is_strike:
                            classification = TextDecoration.STRIKETHROUGH
                        else:
                            classification = TextDecoration.NONE

                        all_chars.append(
                            {
                                "char": ch,
                                "bbox": (cx0, cy0, cx1, cy1),
                                "classification": classification,
                                "color": text_color,
                            }
                        )

        # --- 3) Sort all chars in reading order (top-to-bottom, then left-to-right) ---
        all_chars = self._sort_into_reading_order(all_chars)

        # --- 4) Split into sentences using your punctuation rule ---
        #     We treat . ? ! followed by a space or EoT as a sentence boundary.
        #     Not perfect, likely need to improve in the future.
        sentences = []
        current_sentence_chars = []
        current_bbox = [math.inf, math.inf, -math.inf, -math.inf]
        sentence_enders = {".", "?", "!"}

        for idx, ch_info in enumerate(all_chars):
            ch = ch_info["char"]
            (sx0, sy0, sx1, sy1) = ch_info["bbox"]

            current_sentence_chars.append(ch_info)
            # Expand bounding box
            current_bbox[0] = min(current_bbox[0], sx0)
            current_bbox[1] = min(current_bbox[1], sy0)
            current_bbox[2] = max(current_bbox[2], sx1)
            current_bbox[3] = max(current_bbox[3], sy1)

            # Check for sentence-ending punctuation
            if ch in sentence_enders:
                next_is_space = (idx + 1 == len(all_chars)) or (all_chars[idx + 1]["char"] == " ")
                if next_is_space:
                    # We've reached the end of a sentence
                    sentences.append(
                        {"chars": current_sentence_chars[:], "bbox": tuple(current_bbox)}
                    )
                    current_sentence_chars.clear()
                    current_bbox = [math.inf, math.inf, -math.inf, -math.inf]

        # If there's leftover text that didn't end in .?! => treat it as a final sentence
        if current_sentence_chars:
            sentences.append({"chars": current_sentence_chars, "bbox": tuple(current_bbox)})

        # --- 5) Build original/new text for each sentence, detect changes ---
        result = []
        for i, sentence_data in enumerate(sentences):
            chars = sentence_data["chars"]

            # Original text: keep everything except characters that were ADDED
            orig = "".join(
                ch["char"] for ch in chars if ch["classification"] != TextDecoration.UNDERLINE
            ).strip()

            # New text: keep everything except characters that were DELETED
            new = "".join(
                ch["char"] for ch in chars if ch["classification"] != TextDecoration.STRIKETHROUGH
            ).strip()

            is_changed = any(ch["classification"] != TextDecoration.NONE for ch in chars)

            result.append(
                {
                    "text_original": orig,
                    "text_new": new,
                    "is_changed": is_changed,
                    "bbox": sentence_data["bbox"],
                    "index_in_page": i,
                }
            )

        return result

    def _is_grayscale(self, rgb, tol=GRAY_TOL) -> bool:
        return max(rgb) - min(rgb) <= tol

    def _is_ignored_color(self, rgb) -> bool:
        """True if rgb should be treated as NORMAL (non-tracked) color."""
        return (
            self._is_grayscale(rgb)  # black / dark gray
            or self._color_distance(rgb, LINK_BLUE) <= LINK_TOL  # bright link blue
            or self._color_distance(rgb, HIGHLIGHT_YELLOW) <= HIGHLIGHT_TOL
        )

    def _is_target_color(self, rgbs, color_list, tol=0.1) -> bool:
        """Check if an (r,g,b) color is within `tol` distance of any color in color_list."""
        return any(self._color_distance(rgbs, col) <= tol for col in color_list)

    def _color_distance(self, c1, c2):
        return math.sqrt((c1[0] - c2[0]) ** 2 + (c1[1] - c2[1]) ** 2 + (c1[2] - c2[2]) ** 2)

    def _clean_text(self, text: str) -> str:
        """
        Collapses multiple whitespace characters into a single space and
        strips leading/trailing whitespace. Converts ligatures.
        """
        cleaned = pdf_utils.replace_ligatures_literal(text)
        cleaned = re.sub(r"\s+", " ", cleaned)
        return cleaned.strip()

    def _sort_into_reading_order(self, chars):
        """Return a list of chars in top-to-bottom / left-to-right order,
        but keep superscripts & subscripts attached to their line."""
        chars = sorted(chars, key=lambda c: c["bbox"][1])

        lines = []
        current_line = []
        last_y = None

        for ch in chars:
            y_top = ch["bbox"][1]
            if last_y is None or abs(y_top - last_y) <= SUPER_SUB_LINE_TOLERANCE_POINTS:
                current_line.append(ch)
            else:
                lines.append(current_line)
                current_line = [ch]
            last_y = y_top
        if current_line:
            lines.append(current_line)

        for line in lines:
            line.sort(key=lambda c: c["bbox"][0])

        return [c for line in lines for c in line]

    def _call_text_comparison(self, original_text: str, new_text: str) -> str:
        """
        Calls prompt to get text comparison result.
        """
        user_content = f'Original: "{original_text}"\nNew: "{new_text}"\n'

        # Use Langfuse prompt via get_completion
        changed_text = ""
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                changed_text = self.ai_accessor.get_completion(
                    prompt_name="text_comparison",
                    messages=[{"role": "user", "content": user_content}],
                )
                break
            except Exception as e:
                self.logger.exception("Attempt %d - text_comparison error: %s", attempt, e)
                if attempt == retries:
                    self.logger.error("All retries failed for text_comparison prompt.")
        return changed_text

    def _call_text_comparison_summary(
        self, original_text: str, new_text: str, changed_text: str
    ) -> str:
        """
        Calls prompt to get a short summary statement.
        """
        user_content = f'Original: "{original_text}"\nNew: "{new_text}"\nChange: "{changed_text}"\n'

        # Use Langfuse prompt via get_completion
        summary = ""
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                summary = self.ai_accessor.get_completion(
                    prompt_name="text_comparison_summary",
                    messages=[{"role": "user", "content": user_content}],
                )
                break
            except Exception as e:
                self.logger.exception("Attempt %d - text_comparison_summary error: %s", attempt, e)
                if attempt == retries:
                    self.logger.error("All retries failed for text_comparison_summary prompt.")
        return summary

    def _call_text_comparison_impact(
        self, original_text: str, new_text: str, changed_text: str, summary: str
    ) -> str:
        """
        Calls prompt to get change impact level.
        """
        user_content = (
            f'Original: "{original_text}"\n'
            f'New: "{new_text}"\n'
            f'Change: "{changed_text}"\n'
            f'Summary: "{summary}"\n'
            "Based on these edits, is it High, Medium, or Low impact?"
        )

        # Use Langfuse prompt via get_completion
        impact = "Low"  # default fallback
        retries = 3
        for attempt in range(1, retries + 1):
            try:
                assistant_reply = self.ai_accessor.get_completion(
                    prompt_name="text_comparison_impact",
                    messages=[{"role": "user", "content": user_content}],
                )

                # We expect exactly "High", "Medium", or "Low"
                if assistant_reply in ["High", "Medium", "Low"]:
                    impact = assistant_reply
                else:
                    self.logger.warning("Unexpected impact reply: %s", assistant_reply)
                break
            except Exception as e:
                self.logger.exception("Attempt %d - text_comparison_impact error: %s", attempt, e)
                if attempt == retries:
                    self.logger.error("All retries failed for text_comparison_impact prompt.")
        return impact
