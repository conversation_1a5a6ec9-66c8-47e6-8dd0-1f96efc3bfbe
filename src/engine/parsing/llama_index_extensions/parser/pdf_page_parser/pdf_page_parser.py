"""
This module provides functionality to parse PDF documents into structured text nodes using 
GPT-4 Vision. It includes the `PdfPageParser` class, which processes PDF pages, extracts 
text and metadata, and converts them into `TextNode` instances.
"""
import base64
import json
import logging
import re
from typing import IO, Any, List

import fitz
from langfuse.decorators import observe
from llama_index.core.schema import TextNode

from accessor.ai import AiAccessor
from engine.parsing.llama_index_extensions.parser.pdf_parser import pdf_utils


class PdfPageParser:
    """
    A class to chunk PDF pages and parse their content using GPT-4 Vision.
    """

    def __init__(self, logger_name="PdfPageParser"):
        self.logger_name = logger_name
        self.logger = logging.getLogger(logger_name)
        self.model = "gpt-4o-mini"
        self.ai_accessor = AiAccessor

    def parse_nodes(
        self,
        document: IO[bytes],
        start_page: int = 1,
        end_page: int = -1,
    ) -> List[TextNode]:
        """
        Parses the input PDF document and returns the parsed nodes.
        Adds page-level blocks to metadata.
        :param document: The PDF document as a byte stream
        :param start_page: The starting page for processing
        :param end_page: The ending page for processing (-1 means all pages)
        :return: List of TextNode instances
        """
        if not document:
            raise ValueError("The document cannot be None.")

        if start_page < 1:
            raise ValueError("The start_page must be greater than or equal to 1.")

        if end_page != -1 and end_page < start_page:
            raise ValueError(
                "The end_page must be greater than or equal to start_page or -1 for all pages."
            )

        processed_pages = self._process_pdf_with_gpt(document, start_page, end_page)

        nodes = []
        for page in processed_pages:
            metadata = self._build_metadata(page)
            text = self._build_text(page)
            text = pdf_utils.replace_ligatures_literal(text)
            nodes.append(TextNode(text=text, metadata=metadata))

        self.logger.debug("%s - Finished parsing nodes", self.__class__.__name__)
        return nodes

    def _build_text(self, page: dict) -> str:
        """
        Extracts 'content' from each 'text' block and returns it as a single concatenated string.

        :param page: The processed page dictionary
        :return: A single concatenated string of extracted text content
        """
        content_blocks = page.get("content", [])
        all_content = []

        for block in content_blocks:
            if block.get("type") == "text":
                all_content.append(block.get("text", "").strip())

        return " ".join(all_content)

    def _build_metadata(self, page) -> dict[str, Any]:
        page_number = page["page_number"]
        bbox = page.get("bbox", [])
        metadata = {
            "blocks": [{"bbox": bbox, "page_number": page_number}],
            "header_name": "",
            "ending_page_number": page_number,
            "starting_page_number": page_number,
            "page_number": page_number,
        }

        return metadata

    @observe(name="pdf_page_parser")
    def _process_page_with_gpt(self, page_image_bytes) -> dict[str, list]:
        """
        Processes a single page image with GPT-4 Vision.

        :param page_image_bytes: The image bytes of the page
        :return: The processed content as a JSON object
        """
        base64_image = base64.b64encode(page_image_bytes).decode("utf-8")

        prompt_name = "pdf_page_parser"
        message_content = [
            {
                "type": "text",
                "text": "Here is the page image:",
            },
            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}},
        ]
        messages = [
            {"role": "user", "content": message_content},
        ]

        retries = 3
        result = {"content": []}

        for attempt in range(1, retries + 1):
            try:
                # Use the AI accessor to invoke the vision model
                response_text = self.ai_accessor.get_completion(
                    prompt_name=prompt_name,
                    messages=messages
                )
                
                json_match = re.search(r"\{.*\}", response_text, re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    result = json.loads(json_str)
                    break
                else:
                    self.logger.error(
                        "Attempt %d: No JSON object found in the response.", attempt
                    )
            except Exception as e:
                self.logger.error(
                    "Attempt %d: Error processing page with vision model: %s", attempt, e
                )
                if attempt == retries:
                    self.logger.error("All retries failed.")
        return result

    @observe(name="pdf_parser")
    def _process_pdf_with_gpt(self, pdf_bytes, start_page, end_page) -> list:
        """
        Processes a PDF document with GPT-4 Vision.
        :param pdf_bytes: The PDF document as bytes
        :param start_page: The starting page for processing
        :param end_page: The ending page for processing (-1 means all pages)
        :return: The processed content of all pages
        """
        doc = fitz.open("pdf", pdf_bytes)
        page_images = self._pdf_page_to_image(doc, start_page, end_page)
        all_pages_content = []

        for page_number, page_image_bytes, bbox in page_images:
            self.logger.info("Processing page %d", page_number)
            page_result = self._process_page_with_gpt(page_image_bytes)
            if page_result:
                all_pages_content.append(
                    {
                        "page_number": page_number,
                        "content": page_result.get("content", []),
                        "bbox": bbox,
                    }
                )

        return all_pages_content

    def _pdf_page_to_image(self, doc, start_page, end_page) -> list:
        """
        Converts PDF pages to images for a specified range and extracts bounding boxes.
        :param doc: The PDF document
        :param start_page: The starting page for processing
        :param end_page: The ending page for processing (-1 means all pages)
        :return: A list of tuples containing page numbers, image bytes, and bounding boxes
        """
        page_images = []
        total_pages = len(doc)
        if end_page == -1:
            end_page = total_pages

        for page_number in range(start_page - 1, min(end_page, total_pages)):
            page = doc.load_page(page_number)
            pix = page.get_pixmap(dpi=200)
            img_bytes = pix.tobytes("png")
            bbox = [page.rect.x0, page.rect.y0, page.rect.x1, page.rect.y1]
            page_images.append((page_number + 1, img_bytes, bbox))
        return page_images

