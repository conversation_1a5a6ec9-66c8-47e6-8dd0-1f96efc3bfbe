WEBVTT

cb9ba1ff-379e-4e43-ac96-80615a04f1bc-0
00:00:04.600 --> 00:00:05.000
OK.

f99dd516-697b-4b70-8d25-6b52f5a03856-0
00:00:07.080 --> 00:00:07.600
All right.

238acec7-6364-48ac-b939-b26e48155fd2-0
00:00:12.040 --> 00:00:12.880
Hey, <PERSON>, how are you?

c5a7bd61-c8c7-44d3-9130-3aafe22067f3-0
00:00:13.760 --> 00:00:17.872
Our goal today is to gather
details and insights to

c5a7bd61-c8c7-44d3-9130-3aafe22067f3-1
00:00:17.872 --> 00:00:20.720
streamline the underrating
process.

6c876231-b493-48c7-a372-647619b12a69-0
00:00:21.840 --> 00:00:25.000
We're looking to reduce costs
and improve efficiency.

337009f3-f714-4a62-89b8-2bb6e0964607-0
00:00:27.960 --> 00:00:33.453
So we're looking to understand
some key aspects from you and

337009f3-f714-4a62-89b8-2bb6e0964607-1
00:00:33.453 --> 00:00:39.127
we'll start with the types of
the types of main things that we

337009f3-f714-4a62-89b8-2bb6e0964607-2
00:00:39.127 --> 00:00:43.720
think that we can leverage to
add that efficiency.

122dbfdf-38a9-432d-bba2-04397494a5be-0
00:00:44.400 --> 00:00:49.688
So could you describe some of
the main goals and objectives of

122dbfdf-38a9-432d-bba2-04397494a5be-1
00:00:49.688 --> 00:00:54.556
the our underwriting process
that can be improved through

122dbfdf-38a9-432d-bba2-04397494a5be-2
00:00:54.556 --> 00:00:55.479
automation?

5b0d18e2-7303-44d4-96ab-f56ed5005d52-0
00:01:00.480 --> 00:01:01.480
Yeah, sure.

e8a3bd1f-54da-497e-be46-57bf42cdafb9-0
00:01:01.840 --> 00:01:11.654
So mortgage underwriting is kind
of consists of two primary

e8a3bd1f-54da-497e-be46-57bf42cdafb9-1
00:01:11.654 --> 00:01:12.800
things.

d7c2fbdc-a3d1-4ad6-94c0-bbcc395ffc0a-0
00:01:12.800 --> 00:01:19.250
One is ensuring that the
information that you have about

d7c2fbdc-a3d1-4ad6-94c0-bbcc395ffc0a-1
00:01:19.250 --> 00:01:26.154
the borrower meets the various
rules and guidelines that are

d7c2fbdc-a3d1-4ad6-94c0-bbcc395ffc0a-2
00:01:26.154 --> 00:01:31.926
put forth by a different
investors for the for the

d7c2fbdc-a3d1-4ad6-94c0-bbcc395ffc0a-3
00:01:31.926 --> 00:01:36.680
different products those
investors offer.

0888ae27-d06f-493a-8ddc-4e020c20fa33-0
00:01:38.400 --> 00:01:42.284
But interestingly, that sort of
underwriting is actually not

0888ae27-d06f-493a-8ddc-4e020c20fa33-1
00:01:42.284 --> 00:01:46.360
really undertaken all that much
by the by mortgage originators.

87e428a3-94a1-49c9-a013-b242e89c5592-0
00:01:46.680 --> 00:01:50.012
For the most part that
underwriting is already

87e428a3-94a1-49c9-a013-b242e89c5592-1
00:01:50.012 --> 00:01:54.267
automated by by the investors
themselves like Fannie Mae or

87e428a3-94a1-49c9-a013-b242e89c5592-2
00:01:54.267 --> 00:01:58.521
Freddie Mac through desktop
underwriter load and prospector

87e428a3-94a1-49c9-a013-b242e89c5592-3
00:01:58.521 --> 00:01:59.160
etcetera.

3a124b88-f327-4550-a685-c70aa1c996f8-0
00:02:00.360 --> 00:02:04.637
So really what underwriting
entails for a mortgage

3a124b88-f327-4550-a685-c70aa1c996f8-1
00:02:04.637 --> 00:02:09.585
originator is a verification
process to make sure that the

3a124b88-f327-4550-a685-c70aa1c996f8-2
00:02:09.585 --> 00:02:14.869
information that the originator
has collected about a borrower

3a124b88-f327-4550-a685-c70aa1c996f8-3
00:02:14.869 --> 00:02:19.818
is in fact accurate and and true
and and reflective of the

3a124b88-f327-4550-a685-c70aa1c996f8-4
00:02:19.818 --> 00:02:23.760
borrower's of the borrower's
actual situation.

fd7f7e67-fd55-452b-9c04-a42d7f6ae6f5-0
00:02:26.360 --> 00:02:32.652
And so each material point of
information that you've captured

fd7f7e67-fd55-452b-9c04-a42d7f6ae6f5-1
00:02:32.652 --> 00:02:38.245
from the borrower needs to be
need needs to be verified

fd7f7e67-fd55-452b-9c04-a42d7f6ae6f5-2
00:02:38.245 --> 00:02:42.639
through some through some way or
or method.

9a69dbca-e4f6-44de-b519-8796eb0f2945-0
00:02:43.920 --> 00:02:48.910
And mortgage originators over
the last decade have really

9a69dbca-e4f6-44de-b519-8796eb0f2945-1
00:02:48.910 --> 00:02:53.987
tried to automate as much of
that verification as possible

9a69dbca-e4f6-44de-b519-8796eb0f2945-2
00:02:53.987 --> 00:02:58.376
by, you know, as much as
possible outsourcing that

9a69dbca-e4f6-44de-b519-8796eb0f2945-3
00:02:58.376 --> 00:03:01.560
verification to 3rd party
providers.

0e1440db-77cf-40b4-a13c-8eeafe2eef06-0
00:03:01.840 --> 00:03:06.431
For example, when you're, when
you want to, to confirm, you

0e1440db-77cf-40b4-a13c-8eeafe2eef06-1
00:03:06.431 --> 00:03:10.333
want to confirm employment and
you want to confirm

0e1440db-77cf-40b4-a13c-8eeafe2eef06-2
00:03:10.333 --> 00:03:14.772
characteristics about that
employment, like how much they

0e1440db-77cf-40b4-a13c-8eeafe2eef06-3
00:03:14.772 --> 00:03:15.920
make, etcetera.

409b3cb0-1461-4d25-a9d7-ab165d34265a-0
00:03:15.920 --> 00:03:19.985
They're, they're a variety of
third party data providers, for

409b3cb0-1461-4d25-a9d7-ab165d34265a-1
00:03:19.985 --> 00:03:23.920
example, like the work number
that mortgage originators can

409b3cb0-1461-4d25-a9d7-ab165d34265a-2
00:03:23.920 --> 00:03:27.854
integrate with and then get that
third party to essentially

409b3cb0-1461-4d25-a9d7-ab165d34265a-3
00:03:27.854 --> 00:03:31.986
provide information about the
borrower's employment status and

409b3cb0-1461-4d25-a9d7-ab165d34265a-4
00:03:31.986 --> 00:03:33.560
their salary, et cetera.

e9ecb2e4-ae9f-4be7-9224-d289deca22bc-0
00:03:34.480 --> 00:03:38.119
Which if it gels with what
they've captured from the

e9ecb2e4-ae9f-4be7-9224-d289deca22bc-1
00:03:38.119 --> 00:03:42.514
borrower, then they've, they've,
you know, they've successfully

e9ecb2e4-ae9f-4be7-9224-d289deca22bc-2
00:03:42.514 --> 00:03:46.360
verified those, those pieces and
points of information.

b721e66a-d196-4876-9642-c3d941242e78-0
00:03:47.520 --> 00:03:52.708
When there's not a third party,
when there's not a third party

b721e66a-d196-4876-9642-c3d941242e78-1
00:03:52.708 --> 00:03:57.896
provider that's, that provides
such information, then mortgage

b721e66a-d196-4876-9642-c3d941242e78-2
00:03:57.896 --> 00:04:02.755
originators have to set up
processes usually to verify via

b721e66a-d196-4876-9642-c3d941242e78-3
00:04:02.755 --> 00:04:08.108
via doc documentation that they
asked the borrower to provide to

b721e66a-d196-4876-9642-c3d941242e78-4
00:04:08.108 --> 00:04:08.520
them.

5a2a4d56-eecf-461f-b286-ee7564d38731-0
00:04:09.280 --> 00:04:14.523
And then they ensure that the
documentation that the borrower

5a2a4d56-eecf-461f-b286-ee7564d38731-1
00:04:14.523 --> 00:04:19.937
provides matches what, what the
information that they that they

5a2a4d56-eecf-461f-b286-ee7564d38731-2
00:04:19.937 --> 00:04:20.360
have.

971804bc-3819-45e8-a785-37fcde1b9f78-0
00:04:20.640 --> 00:04:24.800
And if so, then then they're
good to go.

3b65665a-7ddf-4545-ae4a-b62cdc96b2c0-0
00:04:25.040 --> 00:04:30.399
So when we talk about where a
room, what what's room for or,

3b65665a-7ddf-4545-ae4a-b62cdc96b2c0-1
00:04:30.399 --> 00:04:35.407
or where there's room for
efficiency improvements in the

3b65665a-7ddf-4545-ae4a-b62cdc96b2c0-2
00:04:35.407 --> 00:04:40.240
absence of new third party
providers just cropping up.

343b20a9-595d-4ce9-a2ae-7a3559c32621-0
00:04:41.560 --> 00:04:45.736
One of the biggest areas for
efficiency improvements is in

343b20a9-595d-4ce9-a2ae-7a3559c32621-1
00:04:45.736 --> 00:04:50.055
the essentially the processing
of that documentation and the

343b20a9-595d-4ce9-a2ae-7a3559c32621-2
00:04:50.055 --> 00:04:54.373
evaluation of whether or not
that documentation supports the

343b20a9-595d-4ce9-a2ae-7a3559c32621-3
00:04:54.373 --> 00:04:58.904
data that the originators have
captured about the have captured

343b20a9-595d-4ce9-a2ae-7a3559c32621-4
00:04:58.904 --> 00:05:00.320
about the borrowers.

7550e253-e34c-4676-904a-7d5440fe3972-0
00:05:01.640 --> 00:05:06.105
And so one of the exciting
things for generative AI is that

7550e253-e34c-4676-904a-7d5440fe3972-1
00:05:06.105 --> 00:05:10.495
generative AI is actually pretty
good about answering such

7550e253-e34c-4676-904a-7d5440fe3972-2
00:05:10.495 --> 00:05:11.240
questions.

b46deed7-72a7-44de-86ec-1f0201d3ef8f-0
00:05:12.480 --> 00:05:17.166
And if you feed the, if you feed
generative AI, you know the

b46deed7-72a7-44de-86ec-1f0201d3ef8f-1
00:05:17.166 --> 00:05:21.853
document that the borrower has
uploaded and you also feed it

b46deed7-72a7-44de-86ec-1f0201d3ef8f-2
00:05:21.853 --> 00:05:25.080
the relative or the relevant
data points.

7f6eeee2-865a-40fc-a163-162f3b1efd30-0
00:05:25.320 --> 00:05:30.130
Generative AI can can yield an
answer as to whether or not

7f6eeee2-865a-40fc-a163-162f3b1efd30-1
00:05:30.130 --> 00:05:34.860
what's in the documentation
matches or supports what the,

7f6eeee2-865a-40fc-a163-162f3b1efd30-2
00:05:34.860 --> 00:05:39.426
the information that the that's
been collected from the

7f6eeee2-865a-40fc-a163-162f3b1efd30-3
00:05:39.426 --> 00:05:40.160
borrower.

b6986adc-4e7c-44ee-b7d8-fcde2a0e53d2-0
00:05:40.160 --> 00:05:45.339
And of course, if you're able to
set up an automated system to do

b6986adc-4e7c-44ee-b7d8-fcde2a0e53d2-1
00:05:45.339 --> 00:05:50.205
that leveraging generative AI,
then you you should be able to

b6986adc-4e7c-44ee-b7d8-fcde2a0e53d2-2
00:05:50.205 --> 00:05:55.071
kind of remove a lot of existing
human minds from doing that,

b6986adc-4e7c-44ee-b7d8-fcde2a0e53d2-3
00:05:55.071 --> 00:05:56.720
that same evaluation.

eb887f3f-789d-478d-9b00-a33e9c39e27f-0
00:05:56.720 --> 00:06:01.590
And instead do it through do it
through generative AI, kind of

eb887f3f-789d-478d-9b00-a33e9c39e27f-1
00:06:01.590 --> 00:06:06.614
automating it through generative
AI to sort of replace what what

eb887f3f-789d-478d-9b00-a33e9c39e27f-2
00:06:06.614 --> 00:06:10.480
the human underwriter minds are
doing now, right?

0ca3ab0b-c103-4fb4-b060-a8ea0a8c14a9-0
00:06:11.320 --> 00:06:14.000
OK, so a lot of times, like the
human underwriters, they're

0ca3ab0b-c103-4fb4-b060-a8ea0a8c14a9-1
00:06:14.000 --> 00:06:15.520
doing a lot of stair and
compare.

84734bba-8bef-4839-ab7e-ecf2ef0fa8b9-0
00:06:15.520 --> 00:06:18.362
They're looking at the pages,
they're looking at certain

84734bba-8bef-4839-ab7e-ecf2ef0fa8b9-1
00:06:18.362 --> 00:06:19.360
pieces of documents.

319262ac-b564-44a6-9f2e-7f63a4fbe4f6-0
00:06:19.360 --> 00:06:19.920
Spell it right.

c26cb51c-5ee7-4cdd-982d-c71166dab25b-0
00:06:19.920 --> 00:06:21.000
Is it the right address?

f805cd18-aec3-4e2d-80ea-0eb6e124cf24-0
00:06:21.000 --> 00:06:22.600
Is there a signature on this
page?

24667903-735a-43dd-aba6-3adc091a5f58-0
00:06:23.040 --> 00:06:26.120
Are there things that are easier
and or harder?

9c3e40df-6d4e-4429-9c5c-9567a1687a6a-0
00:06:26.120 --> 00:06:27.840
Is there things that we should
prioritize?

b7e48e17-87aa-4728-b80a-f51612c21c52-0
00:06:27.840 --> 00:06:32.625
Like is finding signatures on
the pages or a feature that

b7e48e17-87aa-4728-b80a-f51612c21c52-1
00:06:32.625 --> 00:06:34.440
should be prioritized?

67b68b40-7d13-4992-a4dd-a67b2cd0c4b8-0
00:06:34.480 --> 00:06:37.688
Or is it really hunting and,
and, and looking at the stair

67b68b40-7d13-4992-a4dd-a67b2cd0c4b8-1
00:06:37.688 --> 00:06:39.320
and compare type of documents?

c80811db-d984-4813-a7ff-8cefe6500019-0
00:06:42.120 --> 00:06:44.200
How do you feel about feature
prioritization?

13defd96-46d1-4840-93da-a20b6d4ef396-0
00:06:46.200 --> 00:06:49.985
So it's less, I think it's less
about like a stare and compare

13defd96-46d1-4840-93da-a20b6d4ef396-1
00:06:49.985 --> 00:06:53.650
and it's more, well, I mean, I
guess it's sort of like stare

13defd96-46d1-4840-93da-a20b6d4ef396-2
00:06:53.650 --> 00:06:57.315
and compare in that you have a
piece of information that you

13defd96-46d1-4840-93da-a20b6d4ef396-3
00:06:57.315 --> 00:07:00.320
need to substantiate from
provided documentation.

329f06a2-0a8f-4c31-bb93-e8d74043e92c-0
00:07:01.280 --> 00:07:04.992
And sometimes that's easier than
others because sometimes

329f06a2-0a8f-4c31-bb93-e8d74043e92c-1
00:07:04.992 --> 00:07:09.088
documents come on standardized
forms where the documentation is

329f06a2-0a8f-4c31-bb93-e8d74043e92c-2
00:07:09.088 --> 00:07:10.560
really easy to discern.

51520f5c-02ba-4910-babd-8d3329752f9c-0
00:07:10.560 --> 00:07:16.200
And then other times they don't
and, and folks have to go in and

51520f5c-02ba-4910-babd-8d3329752f9c-1
00:07:16.200 --> 00:07:21.320
figure it out from the document
that that's been provided.

ec3de5de-21b6-4e47-a6d2-d063b5d39b15-0
00:07:21.320 --> 00:07:27.155
And that the latter, the latter
sort of thing is where there's,

ec3de5de-21b6-4e47-a6d2-d063b5d39b15-1
00:07:27.155 --> 00:07:32.443
there's a, a, a potential large
yield of improvement from

ec3de5de-21b6-4e47-a6d2-d063b5d39b15-2
00:07:32.443 --> 00:07:33.720
generative AI.

e81574a2-493a-499e-8c2d-3c0350d5453a-0
00:07:34.680 --> 00:07:38.896
First is, is sort of a backup
for human underwriter or second

e81574a2-493a-499e-8c2d-3c0350d5453a-1
00:07:38.896 --> 00:07:43.180
opinion to make sure that the
human underwriter is getting the

e81574a2-493a-499e-8c2d-3c0350d5453a-2
00:07:43.180 --> 00:07:44.200
decision right.

5ee2938d-024f-4dde-abf3-caa241be8730-0
00:07:45.480 --> 00:07:49.865
And then eventually when we're
confident enough that the that

5ee2938d-024f-4dde-abf3-caa241be8730-1
00:07:49.865 --> 00:07:53.543
generative AI screws up less
than than humans to to

5ee2938d-024f-4dde-abf3-caa241be8730-2
00:07:53.543 --> 00:07:57.363
essentially go full reliance
with the acceptance that

5ee2938d-024f-4dde-abf3-caa241be8730-3
00:07:57.363 --> 00:08:01.253
occasionally, you know, the
model will hallucinate and

5ee2938d-024f-4dde-abf3-caa241be8730-4
00:08:01.253 --> 00:08:03.800
you'll just have to get the
result.

d279c6eb-6b2f-466f-843c-dc68fe8e1882-0
00:08:03.800 --> 00:08:08.664
But but so, yeah, I think, I
think the areas where where,

d279c6eb-6b2f-466f-843c-dc68fe8e1882-1
00:08:08.664 --> 00:08:13.864
where there's kind of greater
areas for improvement are those

d279c6eb-6b2f-466f-843c-dc68fe8e1882-2
00:08:13.864 --> 00:08:19.147
patterns where it's not clear
cut and where and where you have

d279c6eb-6b2f-466f-843c-dc68fe8e1882-3
00:08:19.147 --> 00:08:24.347
to figure it out from the from,
from, you know, documentation

d279c6eb-6b2f-466f-843c-dc68fe8e1882-4
00:08:24.347 --> 00:08:29.379
that takes various forms to to
essentially substantiate the

d279c6eb-6b2f-466f-843c-dc68fe8e1882-5
00:08:29.379 --> 00:08:31.560
same piece of information.

dba89c49-12e5-4945-aaa9-3432a611aa4f-0
00:08:32.080 --> 00:08:34.360
So maybe like a pay stub would
be an example of that.

91bd11f2-ff34-4ca1-abcb-90ea4ef1fdb8-0
00:08:35.440 --> 00:08:37.120
Pay stub is a perfect example of
that.

bb223ec8-0182-49f5-89cb-6aefa1b9d0f5-0
00:08:37.120 --> 00:08:39.560
There's all sorts of pay stubs
out there.

48a5f6e8-5130-4ff2-9833-fc2a03f69dd9-0
00:08:39.560 --> 00:08:39.680
OK.

7efe6f23-66ac-42b1-b368-cbbcd580d410-0
00:08:44.280 --> 00:08:49.231
Is there a so from an interface
standpoint, from a a user

7efe6f23-66ac-42b1-b368-cbbcd580d410-1
00:08:49.231 --> 00:08:54.439
endpoint, do you see it as a
screen presented to an end user

7efe6f23-66ac-42b1-b368-cbbcd580d410-2
00:08:54.439 --> 00:08:59.817
and saying we think that these
are 90% accurate, these are 10%

7efe6f23-66ac-42b1-b368-cbbcd580d410-3
00:08:59.817 --> 00:09:02.720
accurate or you can ignore
these.

cfe37214-a3e0-4e30-8946-23b07ce21d21-0
00:09:04.080 --> 00:09:08.887
What do you think the end user
would see the end user being the

cfe37214-a3e0-4e30-8946-23b07ce21d21-1
00:09:08.887 --> 00:09:10.240
underwriter today?

c24c98e4-ede2-479d-b500-bf4daf7cff5b-0
00:09:10.400 --> 00:09:13.240
Yes, being essentially we're
trying to enhanced underwriter.

1f511182-8ac2-44de-861b-1fa8d26dcdf4-0
00:09:13.400 --> 00:09:13.880
Yeah, yeah.

f02e94e6-d457-4449-afa9-42ad952a3c1c-0
00:09:14.240 --> 00:09:15.400
How do we yeah.

5397df48-55ff-4681-bbde-ff6ca6fc1764-0
00:09:16.520 --> 00:09:24.436
So I think, I think the, the 1st
generation of, of these sort of

5397df48-55ff-4681-bbde-ff6ca6fc1764-1
00:09:24.436 --> 00:09:32.109
products will involve, similar
to what you suggested, they'll,

5397df48-55ff-4681-bbde-ff6ca6fc1764-2
00:09:32.109 --> 00:09:39.781
they'll involve a screen of a
screen of data points broken out

5397df48-55ff-4681-bbde-ff6ca6fc1764-3
00:09:39.781 --> 00:09:47.332
by whether or not the AI thinks
that it, it supports the, the

5397df48-55ff-4681-bbde-ff6ca6fc1764-4
00:09:47.332 --> 00:09:54.640
documentation supports the data
point that's been captured.

d38f2eef-115c-46b6-ad3b-ebfa5b26b9f1-0
00:09:56.440 --> 00:10:01.440
And, and then some sort of a,
some sort of a confidence score.

b6997a66-b09a-4de0-b5b9-9b449f8ae1d7-0
00:10:02.600 --> 00:10:05.916
And then if it doesn't, then
what the, what the AI thinks

b6997a66-b09a-4de0-b5b9-9b449f8ae1d7-1
00:10:05.916 --> 00:10:09.005
that the document, the
documentation does support for

b6997a66-b09a-4de0-b5b9-9b449f8ae1d7-2
00:10:09.005 --> 00:10:09.920
that data point.

4e45ed4a-9b9b-4f51-ba71-9984c6673218-0
00:10:10.720 --> 00:10:15.106
I think you'll see screens like
that and then, you know, UX

4e45ed4a-9b9b-4f51-ba71-9984c6673218-1
00:10:15.106 --> 00:10:19.784
designers will kind of slice and
dice them in a way to make, to

4e45ed4a-9b9b-4f51-ba71-9984c6673218-2
00:10:19.784 --> 00:10:23.951
make the human interaction with
them as efficient as, as

4e45ed4a-9b9b-4f51-ba71-9984c6673218-3
00:10:23.951 --> 00:10:25.560
efficient as possible.

3d201d05-55b1-4df1-891f-fd01806c5378-0
00:10:25.560 --> 00:10:30.817
But it'll effectively,
effectively be a, a presentation

3d201d05-55b1-4df1-891f-fd01806c5378-1
00:10:30.817 --> 00:10:36.639
of, of what generative AI thinks
and, you know, highlighting,

3d201d05-55b1-4df1-891f-fd01806c5378-2
00:10:36.639 --> 00:10:42.554
highlighting areas where, where
there's difference involved or

3d201d05-55b1-4df1-891f-fd01806c5378-3
00:10:42.554 --> 00:10:46.967
where that it reaches a
different, a different

3d201d05-55b1-4df1-891f-fd01806c5378-4
00:10:46.967 --> 00:10:47.999
conclusion.

4123b728-ee99-45a4-a4a0-d0cedf6d57e1-0
00:10:50.360 --> 00:10:55.168
And you know, information
presented where the, where the

4123b728-ee99-45a4-a4a0-d0cedf6d57e1-1
00:10:55.168 --> 00:11:00.483
underwriter can evaluate those
areas where the AI agrees with,

4123b728-ee99-45a4-a4a0-d0cedf6d57e1-2
00:11:00.483 --> 00:11:05.966
with the assessment that was or
with the where the document, the

4123b728-ee99-45a4-a4a0-d0cedf6d57e1-3
00:11:05.966 --> 00:11:11.112
AI agrees that the documentation
matches the matches the the

4123b728-ee99-45a4-a4a0-d0cedf6d57e1-4
00:11:11.112 --> 00:11:12.799
captured data point.

013ea435-8dec-4b49-bb1d-71c3193b2257-0
00:11:17.960 --> 00:11:24.114
But ultimately, ultimately the
underwriter is making the making

013ea435-8dec-4b49-bb1d-71c3193b2257-1
00:11:24.114 --> 00:11:29.980
the end call as to whether or
not to take what the AI thinks

013ea435-8dec-4b49-bb1d-71c3193b2257-2
00:11:29.980 --> 00:11:32.000
about substantiation.

3cf83e5e-20f9-4d0d-989f-3163f774ce0a-0
00:11:32.280 --> 00:11:35.191
And if it disagree or if it
thinks it doesn't, then what it

3cf83e5e-20f9-4d0d-989f-3163f774ce0a-1
00:11:35.191 --> 00:11:38.200
does, it'll be on the, on the
underwriter make that decision.

95acebbb-7a47-40d0-9351-6823aa2fb273-0
00:11:38.200 --> 00:11:41.235
I think that's what you're gonna
see in the first generation of

95acebbb-7a47-40d0-9351-6823aa2fb273-1
00:11:41.235 --> 00:11:41.520
these.

50a0019e-08d9-4c3e-8018-731aae239abf-0
00:11:41.960 --> 00:11:46.162
And then eventually I, I think
you'll remove, you'll remove

50a0019e-08d9-4c3e-8018-731aae239abf-1
00:11:46.162 --> 00:11:49.945
those or maybe you'll, you'll
take those down to only

50a0019e-08d9-4c3e-8018-731aae239abf-2
00:11:49.945 --> 00:11:54.568
presenting to a few underwriters
areas of extreme disagreement or

50a0019e-08d9-4c3e-8018-731aae239abf-3
00:11:54.568 --> 00:11:56.600
really material disagreement.

b2fffc5c-0463-4a96-935d-d39768fc1521-0
00:11:57.120 --> 00:12:00.554
But then otherwise, otherwise
just accept on the things that

b2fffc5c-0463-4a96-935d-d39768fc1521-1
00:12:00.554 --> 00:12:01.680
that that it agrees.

f1f8c410-ee90-4b14-9ea1-0e3035617b9a-0
00:12:02.840 --> 00:12:06.237
Do you think we'll need
capabilities to give you

f1f8c410-ee90-4b14-9ea1-0e3035617b9a-1
00:12:06.237 --> 00:12:10.744
feedback or give us feedback on
why a underwriter says that this

f1f8c410-ee90-4b14-9ea1-0e3035617b9a-2
00:12:10.744 --> 00:12:14.280
is good or or or bad or approves
or don't approve?

c871527c-2899-47b9-8d04-d9176545a7e7-0
00:12:14.960 --> 00:12:18.760
Do we need or should we have
that kind of capability?

33b89df0-e4be-4f21-9b4a-74dcca48a0f0-0
00:12:21.120 --> 00:12:21.920
I'm not sure I followed.

2b12a965-551f-4b9a-b83a-56edb20440a1-0
00:12:22.080 --> 00:12:27.669
When you say approve or not
approved, do you mean well, I'm

2b12a965-551f-4b9a-b83a-56edb20440a1-1
00:12:27.669 --> 00:12:33.165
suggesting that we have a
capability for us to for the LLM

2b12a965-551f-4b9a-b83a-56edb20440a1-2
00:12:33.165 --> 00:12:35.960
to present its recommendation.

045e813a-4943-4596-9b17-0620a20687fe-0
00:12:35.960 --> 00:12:40.547
We have an underwriter looking
at this recommendation and

045e813a-4943-4596-9b17-0620a20687fe-1
00:12:40.547 --> 00:12:45.372
they're, they're obviously going
to approve it or, or or not

045e813a-4943-4596-9b17-0620a20687fe-2
00:12:45.372 --> 00:12:49.643
approve it, write a new
condition or, or or what have

045e813a-4943-4596-9b17-0620a20687fe-3
00:12:49.643 --> 00:12:49.960
you.

1bb2351b-b389-47e1-9d0f-89b66a4f0c5c-0
00:12:51.400 --> 00:12:54.853
Would you like, would it be
helpful for us to capture that

1bb2351b-b389-47e1-9d0f-89b66a4f0c5c-1
00:12:54.853 --> 00:12:58.598
feedback loop to say that they
disagreed with the LLM that this

1bb2351b-b389-47e1-9d0f-89b66a4f0c5c-2
00:12:58.598 --> 00:13:02.344
wasn't good or they or they did
agree with the LLM and this was

1bb2351b-b389-47e1-9d0f-89b66a4f0c5c-3
00:13:02.344 --> 00:13:02.519
OK?

1989c607-e4b1-4327-91e4-7db28ec51921-0
00:13:02.960 --> 00:13:03.720
I think I see.

277e5315-623d-412d-831a-d35ab1b3926f-0
00:13:04.000 --> 00:13:05.360
So just two quick points.

bcad45ce-2492-41b1-b321-d28208ee1f2a-0
00:13:05.360 --> 00:13:08.000
The first one is it's less about
approving.

a004d113-075b-4632-b3fa-36eb70262d20-0
00:13:11.160 --> 00:13:14.903
I mean it's only about
substantiating information

a004d113-075b-4632-b3fa-36eb70262d20-1
00:13:14.903 --> 00:13:18.422
that's been provided or
otherwise changing the

a004d113-075b-4632-b3fa-36eb70262d20-2
00:13:18.422 --> 00:13:22.840
information of record to what
can be substantiated, right.

1992dbe3-9242-4d96-b024-6648e56ac5fb-0
00:13:24.720 --> 00:13:30.050
But to the extent that the the
that the AI is highlighting or

1992dbe3-9242-4d96-b024-6648e56ac5fb-1
00:13:30.050 --> 00:13:35.037
finding finding like lack of
substantiation or maybe it's

1992dbe3-9242-4d96-b024-6648e56ac5fb-2
00:13:35.037 --> 00:13:39.336
finding substantiation for
datapoint and then the

1992dbe3-9242-4d96-b024-6648e56ac5fb-3
00:13:39.336 --> 00:13:43.120
underwriter eventually disagrees
with that.

de17c123-3159-4384-8f3b-de96693e5b05-0
00:13:44.200 --> 00:13:49.331
Yes, feedback mechanism in order
to capture what the what the

de17c123-3159-4384-8f3b-de96693e5b05-1
00:13:49.331 --> 00:13:52.560
underwriter ultimately decided,
right?

a7ce9d49-b08f-4d9f-8be5-9986825a269d-0
00:13:52.800 --> 00:13:57.701
And what became of record for
the loan that was going to close

a7ce9d49-b08f-4d9f-8be5-9986825a269d-1
00:13:57.701 --> 00:13:59.880
or maybe be rejected, right.

bdea544b-31d2-43d5-b89d-7c4e5c506212-0
00:13:59.880 --> 00:14:04.404
If being able, you know, a
mechanism to capture that and

bdea544b-31d2-43d5-b89d-7c4e5c506212-1
00:14:04.404 --> 00:14:09.642
then a mechanism to to feed that
back into additional training of

bdea544b-31d2-43d5-b89d-7c4e5c506212-2
00:14:09.642 --> 00:14:13.849
the models is is definitely
important and definitely

bdea544b-31d2-43d5-b89d-7c4e5c506212-3
00:14:13.849 --> 00:14:19.008
something that that will want to
would want to be considered and

bdea544b-31d2-43d5-b89d-7c4e5c506212-4
00:14:19.008 --> 00:14:24.088
and products that that present
this so that you can continue to

bdea544b-31d2-43d5-b89d-7c4e5c506212-5
00:14:24.088 --> 00:14:28.850
improve and then train, train
the LLMS outside of just what

bdea544b-31d2-43d5-b89d-7c4e5c506212-6
00:14:28.850 --> 00:14:33.375
you're going to get from
foundation model evolution from

bdea544b-31d2-43d5-b89d-7c4e5c506212-7
00:14:33.375 --> 00:14:35.279
a technical perspective.

a55f0f4d-90d3-43c1-9557-f9cd978e1755-0
00:14:35.400 --> 00:14:37.760
So we're talking about LLMS
helping us underwrite.

cf59c813-638a-4721-9189-bff2a9054a49-0
00:14:37.760 --> 00:14:40.760
Is there is LLMS handle at all
or do we need other things?

9c64a56a-5f7f-43aa-8a27-27f6a011a214-0
00:14:40.760 --> 00:14:45.023
Do we need yeah, OCR engines or
any of that type of capabilities

9c64a56a-5f7f-43aa-8a27-27f6a011a214-1
00:14:45.023 --> 00:14:48.040
or is that all kit and kaboodle
with the LLM?

84b5d251-5b71-4f0f-9c74-45bde8acbffb-0
00:14:49.080 --> 00:14:51.240
It's kind of kit and caboodle
with the LLMS.

ffc50701-2355-4212-b16b-55865c25431f-0
00:14:51.280 --> 00:14:57.906
I mean literally, literally like
when you're calling open AIS

ffc50701-2355-4212-b16b-55865c25431f-1
00:14:57.906 --> 00:15:03.785
LLMS, they're like you're
getting essentially OCR with

ffc50701-2355-4212-b16b-55865c25431f-2
00:15:03.785 --> 00:15:04.319
that.

882942eb-c410-483a-b689-44f610abdab0-0
00:15:05.560 --> 00:15:09.200
They're I don't think they're
doing it in the same call.

fca42118-544d-4b9f-a201-02c27a7e0ca9-0
00:15:09.200 --> 00:15:14.622
I don't know exactly what they
are doing under the hood, but

fca42118-544d-4b9f-a201-02c27a7e0ca9-1
00:15:14.622 --> 00:15:20.044
they're, but they're the OCR
that you're getting essentially

fca42118-544d-4b9f-a201-02c27a7e0ca9-2
00:15:20.044 --> 00:15:25.733
out-of-the-box with with those
LLM seems seems pretty good, not

fca42118-544d-4b9f-a201-02c27a7e0ca9-3
00:15:25.733 --> 00:15:31.333
impossible that you would want
to use a very well trained more

fca42118-544d-4b9f-a201-02c27a7e0ca9-4
00:15:31.333 --> 00:15:36.755
bespoke LLM based OCR engine
from maybe a different provider

fca42118-544d-4b9f-a201-02c27a7e0ca9-5
00:15:36.755 --> 00:15:41.822
than your general purpose
foundation foundation LLM for,

fca42118-544d-4b9f-a201-02c27a7e0ca9-6
00:15:41.822 --> 00:15:44.400
for your assessments of that.

d0f0e8e0-8925-4fa9-8f5d-14666ef7ecf7-0
00:15:46.080 --> 00:15:50.824
The I mean there's various ways
that that could that, that could

d0f0e8e0-8925-4fa9-8f5d-14666ef7ecf7-1
00:15:50.824 --> 00:15:54.693
set up and a product will
probably want to offer the

d0f0e8e0-8925-4fa9-8f5d-14666ef7ecf7-2
00:15:54.693 --> 00:15:59.000
option to to integrate out with
with potentially different

d0f0e8e0-8925-4fa9-8f5d-14666ef7ecf7-3
00:15:59.000 --> 00:16:01.920
vendors technologies in order to
do it.

80c6ea79-8667-432a-a4a0-049e647e95e4-0
00:16:01.920 --> 00:16:06.803
But in truth, you could just go
with you could just go with one

80c6ea79-8667-432a-a4a0-049e647e95e4-1
00:16:06.803 --> 00:16:11.076
and use the UCR capabilities
that are that are resident

80c6ea79-8667-432a-a4a0-049e647e95e4-2
00:16:11.076 --> 00:16:15.960
because they're pretty they're
pretty good within at least with

80c6ea79-8667-432a-a4a0-049e647e95e4-3
00:16:15.960 --> 00:16:16.800
an open AI.

7c6c832e-13f4-4468-9a0d-8372e1af4da2-0
00:16:18.360 --> 00:16:20.520
OK, all right.

89145972-0200-4355-b72d-b362be8a696b-0
00:16:20.520 --> 00:16:23.685
So one of the tasks that we have
to do is we have to map this

89145972-0200-4355-b72d-b362be8a696b-1
00:16:23.685 --> 00:16:24.400
back to value.

cdc8681f-a528-4ec5-9153-e98d1a42b084-0
00:16:26.880 --> 00:16:31.564
Obviously it sounds like we are
trying to make the underwriter

cdc8681f-a528-4ec5-9153-e98d1a42b084-1
00:16:31.564 --> 00:16:33.200
faster, more accurate.

c5eb3510-50f4-4ef6-8465-7f9a952c6b8f-0
00:16:33.680 --> 00:16:38.787
Is there is there aspects of
business value that that you

c5eb3510-50f4-4ef6-8465-7f9a952c6b8f-1
00:16:38.787 --> 00:16:44.510
think that we should be tracking
down the two that you named are

c5eb3510-50f4-4ef6-8465-7f9a952c6b8f-2
00:16:44.510 --> 00:16:50.058
the are really it right, Making
them faster, being able to get

c5eb3510-50f4-4ef6-8465-7f9a952c6b8f-3
00:16:50.058 --> 00:16:55.870
more loans per human underwriter
involved and then having them be

c5eb3510-50f4-4ef6-8465-7f9a952c6b8f-4
00:16:55.870 --> 00:16:58.600
having them be accurate, right.

c2222e0d-8b07-4af6-9a78-59d8f3b7db7c-0
00:16:58.760 --> 00:17:02.471
Like when one speaks to the cost
of doing this, at least the

c2222e0d-8b07-4af6-9a78-59d8f3b7db7c-1
00:17:02.471 --> 00:17:06.000
human cost or the cost and
labor, human labor to do this.

f27193bd-6a9d-429b-99da-a27c388cf0fe-0
00:17:06.960 --> 00:17:14.375
And then the second, the second
one, yeah, speaks to how good

f27193bd-6a9d-429b-99da-a27c388cf0fe-1
00:17:14.375 --> 00:17:19.040
and accurate their
determinations are.

357c9135-e6ab-49bc-967a-1a105342cc1e-0
00:17:19.040 --> 00:17:23.164
And of course, there's a
financial element to if you are

357c9135-e6ab-49bc-967a-1a105342cc1e-1
00:17:23.164 --> 00:17:27.795
inaccurate with this information
and it ends up you originate a

357c9135-e6ab-49bc-967a-1a105342cc1e-2
00:17:27.795 --> 00:17:32.065
loan that doesn't conform
because the real information was

357c9135-e6ab-49bc-967a-1a105342cc1e-3
00:17:32.065 --> 00:17:36.334
different than, you know, you
potentially as an originator

357c9135-e6ab-49bc-967a-1a105342cc1e-4
00:17:36.334 --> 00:17:40.386
have to buy back the loan, which
has negative financial

357c9135-e6ab-49bc-967a-1a105342cc1e-5
00:17:40.386 --> 00:17:41.399
ramifications.

9f77b79c-e1bf-45c5-89c8-395963a56495-0
00:17:41.400 --> 00:17:45.355
So, yeah, those those are the
two those are the two key

9f77b79c-e1bf-45c5-89c8-395963a56495-1
00:17:45.355 --> 00:17:48.040
metrics that that need to be
tracked.

d1be6633-ad25-48f3-8245-c6ffaeac44d5-0
00:17:48.880 --> 00:17:53.174
Do you think it could also help
us with follow up like creating

d1be6633-ad25-48f3-8245-c6ffaeac44d5-1
00:17:53.174 --> 00:17:56.931
a new condition, a user, a
customer forgot to sign page

d1be6633-ad25-48f3-8245-c6ffaeac44d5-2
00:17:56.931 --> 00:17:59.280
three or didn't include page
five.

852dcb85-6da4-423e-a355-31ba1da91f93-0
00:18:01.240 --> 00:18:04.396
Could it identify and
potentially write a new

852dcb85-6da4-423e-a355-31ba1da91f93-1
00:18:04.396 --> 00:18:08.856
condition and maybe even e-mail
that condition with human in the

852dcb85-6da4-423e-a355-31ba1da91f93-2
00:18:08.856 --> 00:18:09.199
loop?

4c4ec912-8d64-4d0b-873f-336a2fc64a19-0
00:18:09.200 --> 00:18:09.920
I would assume.

ce0a3952-871d-4d5d-9934-2b9b2dc1981d-0
00:18:09.920 --> 00:18:18.644
But yeah, it could in the in
that look you depending on what

ce0a3952-871d-4d5d-9934-2b9b2dc1981d-1
00:18:18.644 --> 00:18:25.080
the rules for substantiating
information is.

1d8c5be4-0462-4932-b170-d93dc5e974c8-0
00:18:25.400 --> 00:18:30.013
If you have a data point and you
know that data point is being

1d8c5be4-0462-4932-b170-d93dc5e974c8-1
00:18:30.013 --> 00:18:34.699
substantiated by a document that
requires a human signature and

1d8c5be4-0462-4932-b170-d93dc5e974c8-2
00:18:34.699 --> 00:18:38.947
that's not there, then at the
ultimately you don't have a

1d8c5be4-0462-4932-b170-d93dc5e974c8-3
00:18:38.947 --> 00:18:43.633
substantiated, you don't have a
substantiated data point, which

1d8c5be4-0462-4932-b170-d93dc5e974c8-4
00:18:43.633 --> 00:18:48.247
in and of itself, you know, it
was a would should be a blocker

1d8c5be4-0462-4932-b170-d93dc5e974c8-5
00:18:48.247 --> 00:18:52.494
to to closing and trips some
sort of condition that would

1d8c5be4-0462-4932-b170-d93dc5e974c8-6
00:18:52.494 --> 00:18:56.961
that would remove the block,
right, Because that that has to

1d8c5be4-0462-4932-b170-d93dc5e974c8-7
00:18:56.961 --> 00:18:57.839
be provided.

9f05ab1e-239b-41bd-a060-4e4a8efbc8c0-0
00:18:57.840 --> 00:19:01.778
That's information that has to
be provided by the by the

9f05ab1e-239b-41bd-a060-4e4a8efbc8c0-1
00:19:01.778 --> 00:19:03.160
borrower themselves.

5b6382df-e559-47b1-9f5f-e69994cfbecb-0
00:19:04.760 --> 00:19:09.200
If it's, you know, if it's just
a well and really you can think

5b6382df-e559-47b1-9f5f-e69994cfbecb-1
00:19:09.200 --> 00:19:13.572
of all data points, even if it's
not something that's material

5b6382df-e559-47b1-9f5f-e69994cfbecb-2
00:19:13.572 --> 00:19:17.041
to, to the, you know, the
creditworthiness of the

5b6382df-e559-47b1-9f5f-e69994cfbecb-3
00:19:17.041 --> 00:19:21.343
borrower, but it is something
that needs to, that needs to be

5b6382df-e559-47b1-9f5f-e69994cfbecb-4
00:19:21.343 --> 00:19:21.760
there.

0a83c358-a8ee-4661-8824-c232ab548493-0
00:19:21.760 --> 00:19:27.832
I mean, it basically works the
same way it it would kind of

0a83c358-a8ee-4661-8824-c232ab548493-1
00:19:27.832 --> 00:19:30.160
exactly as you suggest.

c11ac1c1-b266-40a5-8a21-49699a72cdba-0
00:19:30.440 --> 00:19:34.452
If you don't have some, I don't
know, say it's a release

c11ac1c1-b266-40a5-8a21-49699a72cdba-1
00:19:34.452 --> 00:19:38.253
statement bar the borrower on
something or another or

c11ac1c1-b266-40a5-8a21-49699a72cdba-2
00:19:38.253 --> 00:19:42.547
attestation by the borrower on
something or another and it's

c11ac1c1-b266-40a5-8a21-49699a72cdba-3
00:19:42.547 --> 00:19:47.123
and it's absent, then you've got
a data point that is false that

c11ac1c1-b266-40a5-8a21-49699a72cdba-4
00:19:47.123 --> 00:19:50.079
needs to be true in order to go
to close.

c2b0d570-648c-44be-b981-cbf4b599dcdd-0
00:19:50.080 --> 00:19:52.640
So yeah, you trip a condition
and go.

81c4b5c1-abb7-435d-a1a7-af4eef46c770-0
00:19:52.640 --> 00:19:53.960
So the O Ms.

e71c6f14-1f6c-44d9-bd23-7f0ed2a42ff1-0
00:19:53.960 --> 00:19:57.727
role is made in determining that
in fact this condition or not

e71c6f14-1f6c-44d9-bd23-7f0ed2a42ff1-1
00:19:57.727 --> 00:20:00.120
condition, this data point is
not true.

b95ffde1-e7fc-4e56-8190-2177137900b2-0
00:20:00.120 --> 00:20:03.373
That needs to be the borrower as
it has attested to such and

b95ffde1-e7fc-4e56-8190-2177137900b2-1
00:20:03.373 --> 00:20:03.640
such.

de382042-ad33-4db7-a09b-797a72e1466e-0
00:20:03.680 --> 00:20:07.239
And then so long as that remains
false, then it would it would

de382042-ad33-4db7-a09b-797a72e1466e-1
00:20:07.239 --> 00:20:08.200
trip a condition.

26fb039a-2741-4241-940e-cc118fabd5c7-0
00:20:13.160 --> 00:20:18.136
Could the LLM prompt the lol the
the lint origination system to

26fb039a-2741-4241-940e-cc118fabd5c7-1
00:20:18.136 --> 00:20:20.080
bring up certain screens?

a53688c9-e6d6-4746-ade3-0cc796a4917a-0
00:20:21.240 --> 00:20:25.655
The the address is different on
the the the bar page than it is

a53688c9-e6d6-4746-ade3-0cc796a4917a-1
00:20:25.655 --> 00:20:28.760
on the property page or
something like that.

fe5e8172-0afd-4170-94c6-64833451d625-0
00:20:28.760 --> 00:20:32.889
Could it could it prompt the Los
to bring up those types of

fe5e8172-0afd-4170-94c6-64833451d625-1
00:20:32.889 --> 00:20:33.440
screens?

fa843c4f-a484-47ee-a22d-95dc502c6216-0
00:20:33.440 --> 00:20:36.640
It wouldn't prompt it directly.

fdf480ea-2875-427a-8285-4f1e43e1c8d7-0
00:20:36.640 --> 00:20:44.226
It's more, I mean it, I don't
know, I suppose you could, I'm

fdf480ea-2875-427a-8285-4f1e43e1c8d7-1
00:20:44.226 --> 00:20:51.813
not sure that that's really the
the I'm not sure that that's

fdf480ea-2875-427a-8285-4f1e43e1c8d7-2
00:20:51.813 --> 00:20:59.400
really the best use of of LLMS
in the in the in the process.

ec182975-a87b-4260-af65-777b7f133e07-0
00:20:59.400 --> 00:21:05.816
I think it's more, I think about
the LLM's role is more of making

ec182975-a87b-4260-af65-777b7f133e07-1
00:21:05.816 --> 00:21:11.065
a more making decisions about
about things that would

ec182975-a87b-4260-af65-777b7f133e07-2
00:21:11.065 --> 00:21:14.760
otherwise require human
intelligence.

d764d78a-a051-4317-b690-1956231ea393-0
00:21:14.760 --> 00:21:19.301
I mean, whether or not a
property address is the same on

d764d78a-a051-4317-b690-1956231ea393-1
00:21:19.301 --> 00:21:24.083
2 pages within the Los is not
really the sort of thing that

d764d78a-a051-4317-b690-1956231ea393-2
00:21:24.083 --> 00:21:28.385
you would use an LLM to do
because you can solve that

d764d78a-a051-4317-b690-1956231ea393-3
00:21:28.385 --> 00:21:33.326
problem without, without the,
the cost or expense of LLMS now

d764d78a-a051-4317-b690-1956231ea393-4
00:21:33.326 --> 00:21:34.920
in an automated way.

8dbe1ee5-3e06-46aa-af0b-dfc556ebdf5c-0
00:21:36.960 --> 00:21:40.494
But if it's something where,
hey, it is the property, I mean,

8dbe1ee5-3e06-46aa-af0b-dfc556ebdf5c-1
00:21:40.494 --> 00:21:41.520
it, it's possible.

ca2416a5-7510-4eaf-af30-1b0d4627eff9-0
00:21:41.520 --> 00:21:47.910
It's just the property addresses
and matching property addresses

ca2416a5-7510-4eaf-af30-1b0d4627eff9-1
00:21:47.910 --> 00:21:53.417
are, are a little more
straightforward things to do And

ca2416a5-7510-4eaf-af30-1b0d4627eff9-2
00:21:53.417 --> 00:21:56.759
there's like than than trying
to.

296a40a9-755e-46b6-aeec-0dd2147ea85c-0
00:21:56.760 --> 00:21:59.840
I, I don't know, it just, it
just depends, right?

362472f1-c579-404b-90db-945e7643405f-0
00:21:59.840 --> 00:22:03.289
Like how much do you need
semantic similarity versus

362472f1-c579-404b-90db-945e7643405f-1
00:22:03.289 --> 00:22:06.869
versus kind of literal after
it's been translated into

362472f1-c579-404b-90db-945e7643405f-2
00:22:06.869 --> 00:22:11.164
something else, which is the way
that address matching works when

362472f1-c579-404b-90db-945e7643405f-3
00:22:11.164 --> 00:22:15.199
you kind of normalize it against
what the Postal Service has?

9aec2ca1-3de0-473d-9bb6-928391ee98fc-0
00:22:16.800 --> 00:22:18.880
OK, well, we're down there our
last few minutes.

2e676797-e0c1-4b8a-b86b-d4d9e878b0fb-0
00:22:18.880 --> 00:22:24.223
Is there anything that we should
be focused on or following up

2e676797-e0c1-4b8a-b86b-d4d9e878b0fb-1
00:22:24.223 --> 00:22:28.888
with or from a feature set or
from a problem statement

2e676797-e0c1-4b8a-b86b-d4d9e878b0fb-2
00:22:28.888 --> 00:22:34.316
perspective like things that you
think that we should spend the

2e676797-e0c1-4b8a-b86b-d4d9e878b0fb-3
00:22:34.316 --> 00:22:37.199
most energy trying to figure
out?

3a2120b8-ab30-466a-b489-775d858c2879-0
00:22:44.000 --> 00:22:55.564
I think I think probably
attacking the data points that

3a2120b8-ab30-466a-b489-775d858c2879-1
00:22:55.564 --> 00:23:08.162
are the most time consuming for
for underwriters today to to

3a2120b8-ab30-466a-b489-775d858c2879-2
00:23:08.162 --> 00:23:21.172
substantiate along with the data
points that that are wrong or

3a2120b8-ab30-466a-b489-775d858c2879-3
00:23:21.172 --> 00:23:34.596
found to be wrong from like post
close quality reviews times the

3a2120b8-ab30-466a-b489-775d858c2879-4
00:23:34.596 --> 00:23:45.954
financial impact of those of
being wrong on those data

3a2120b8-ab30-466a-b489-775d858c2879-5
00:23:45.954 --> 00:23:47.400
points.

b42ceaa1-2f8e-45d0-81e0-b904ba32405d-0
00:23:47.880 --> 00:23:52.702
You know, and attacking,
attacking the LLM usage in a

b42ceaa1-2f8e-45d0-81e0-b904ba32405d-1
00:23:52.702 --> 00:23:58.418
prioritized way based on that is
probably the recommendation or

b42ceaa1-2f8e-45d0-81e0-b904ba32405d-2
00:23:58.418 --> 00:24:03.777
the would be my recommendation
for how to how to figure out

b42ceaa1-2f8e-45d0-81e0-b904ba32405d-3
00:24:03.777 --> 00:24:09.046
figure out where to start
deploying like what what do what

b42ceaa1-2f8e-45d0-81e0-b904ba32405d-4
00:24:09.046 --> 00:24:12.440
should we be prompting the LLMS
with?

9083c46d-e933-4aec-842a-f41c2f6116f5-0
00:24:13.400 --> 00:24:16.153
Like what are the scenarios
where we should be prompting the

9083c46d-e933-4aec-842a-f41c2f6116f5-1
00:24:16.153 --> 00:24:16.560
LMS with?

aeeb14f0-9e89-4c14-868a-2c6daea66180-0
00:24:16.560 --> 00:24:20.615
Because probably going to get
better answers if you ask the

aeeb14f0-9e89-4c14-868a-2c6daea66180-1
00:24:20.615 --> 00:24:24.468
LLMSA series of targeted
questions with a lot of context

aeeb14f0-9e89-4c14-868a-2c6daea66180-2
00:24:24.468 --> 00:24:28.388
versus, hey, here's all the
documentation, here's all the

aeeb14f0-9e89-4c14-868a-2c6daea66180-3
00:24:28.388 --> 00:24:29.200
data points.

217fcae4-21e8-4786-a985-c297836b2135-0
00:24:29.480 --> 00:24:31.880
Does the documentation
substantiate everything?

56257417-449b-4963-83eb-bcbb3e45c55b-0
00:24:32.360 --> 00:24:34.760
You get a lot less good answers
going that way.

c9a7018e-0a66-48c8-a50d-82ed63e3a11b-0
00:24:34.760 --> 00:24:38.718
So you kind of want to
prioritize how you attack asking

c9a7018e-0a66-48c8-a50d-82ed63e3a11b-1
00:24:38.718 --> 00:24:42.040
those underlying, underlying
questions, right.

080acb28-1983-4b6e-be8e-e8767eb81a57-0
00:24:42.040 --> 00:24:45.618
So we want to look at specific
documents, specific use cases,

080acb28-1983-4b6e-be8e-e8767eb81a57-1
00:24:45.618 --> 00:24:48.907
prioritize them by how much,
which ones consume the most

080acb28-1983-4b6e-be8e-e8767eb81a57-2
00:24:48.907 --> 00:24:49.600
human labor.

41de0906-776c-4e10-93ce-fa4b4655cccd-0
00:24:51.000 --> 00:24:51.200
Yeah.

25b84acb-e82d-46df-bd0a-98340083ecd8-0
00:24:51.200 --> 00:24:54.229
And it's more more data point
centric than document, right,

25b84acb-e82d-46df-bd0a-98340083ecd8-1
00:24:54.229 --> 00:24:56.400
because you have to the value
you have to.

004966d9-1582-45d5-a574-c6f5a6bbf811-0
00:24:56.440 --> 00:24:56.800
Yeah.

8334059f-2827-4daa-92a7-ecf2c7735446-0
00:24:58.320 --> 00:24:58.640
All right.

5bca8dcf-ffd1-406a-8792-3e058d7a5518-0
00:24:58.640 --> 00:25:01.701
Because there could be a broad
set of documents that actually,

5bca8dcf-ffd1-406a-8792-3e058d7a5518-1
00:25:01.701 --> 00:25:04.228
you know, ultimately
substantiate a particular data

5bca8dcf-ffd1-406a-8792-3e058d7a5518-2
00:25:04.228 --> 00:25:04.519
point.

79ff0e42-8fb9-4c9c-9cfb-112c202106c6-0
00:25:05.600 --> 00:25:05.880
OK.

ea834796-fbca-446e-a097-a357274fbc5c-0
00:25:07.120 --> 00:25:07.480
All right.

d3266011-f017-4721-813f-6ab05b18d188-0
00:25:07.480 --> 00:25:09.240
Well, that's all the time we
have for today.

5ed2e0c1-ec61-4773-838d-192af639b044-0
00:25:09.640 --> 00:25:13.320
Appreciate your time and of
course, we'll talk again.

a800fa11-1a42-4842-85d3-a4c230cf5c07-0
00:25:14.440 --> 00:25:14.680
All right.

df8d11cf-0522-49ba-b19e-0738199e9c73-0
00:25:14.680 --> 00:25:16.040
All right, thanks.

03b08161-a23f-46d8-a87d-80a6be4f9d5d-0
00:25:18.840 --> 00:25:19.440
All right, take care.

b47e35eb-777e-4694-aa2a-e28600ef568d-0
00:25:19.440 --> 00:25:21.160
It's not recording.

1cdce1de-5041-46a1-bbb3-081a7ab3409e-0
00:25:21.160 --> 00:25:22.440
All right.