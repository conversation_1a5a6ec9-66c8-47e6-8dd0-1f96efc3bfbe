"""
VTT Node Parser

This module parses a VTT document into structured text nodes using 
spacy for NLP. It extracts text and metadata, and calculates statistical 
summaries of the parsed nodes.
"""

import logging
import json
from typing import List, Dict, Tuple, IO, Any
import spacy
import numpy as np
from llama_index.core.schema import TextNode
from llama_index.core.node_parser import NodeParser
from engine.parsing.llama_index_extensions.parser.vtt_parser.vtt_text_extractor import VTTTextExtractor
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import Document, TranscriptBlock

nlp = spacy.load("en_core_web_sm")

class VTTNodeParser(NodeParser):
    """
    A class to parse VTT documents into structured text nodes using spacy
    for NLP. This class extracts text and metadata, and calculates statistical
    summaries of the parsed nodes.
    """

    def parse_nodes(
        self,
        document: IO[bytes],
        show_progress: bool = False,
        **kwargs: Any
    ) -> List[TextNode]:
        decoded_document = document.decode('utf-8')
        return self._parse_nodes(decoded_document, show_progress, **kwargs)


    def _parse_nodes(
        self,
        document: str,
        show_progress: bool = False,
        **kwargs: Any
    ) -> List[TextNode]:
        """
        Parses nodes from the given VTT document.

        Args:
            document (IO[str]): The VTT document as a string.
            show_progress (bool): Whether to show progress during parsing.
            **kwargs: Additional keyword arguments.

        Returns:
            List[TextNode]: A list of parsed TextNode objects.
        """
        logging.info(
            "%s - Starting to parse nodes from document",
            self.__class__.__name__
        )
        extractor = VTTTextExtractor(document)
        document_blocks = extractor.extract_text_from_document()
        nodes = self._create_vtt_nodes(document_blocks)
        logging.info(
            "%s - Finished parsing nodes from document",
            self.__class__.__name__
        )
        return nodes

    def _create_vtt_nodes(self, document: Document) -> List[TextNode]:
        """
        Create nodes from a VTT Document instance by counting words and grouping blocks into nodes.
        """
        nodes = []
        current_text = ""
        current_words = 0
        current_blocks = []
        node_word_ceiling = 1000

        for block in document.blocks:
            if isinstance(block, TranscriptBlock):
                block_text = block.parsed_line.strip()
                block_words = len(block_text.split())

                if current_words > node_word_ceiling:
                    # Create node with current data
                    node_text = " ".join(b.parsed_line for b in current_blocks)
                    node_metadata = self._create_metadata(current_blocks, document)
                    node = TextNode(metadata=node_metadata, text=node_text)
                    nodes.append(node)

                    # Reset current data
                    current_text = block_text
                    current_words = block_words
                    current_blocks = [block]
                else:
                    current_text += " " + block_text
                    current_words += block_words
                    current_blocks.append(block)

        if current_blocks:
            # Create the last node with remaining data
            node_text = " ".join(b.parsed_line for b in current_blocks)
            node_metadata = self._create_metadata(current_blocks, document)
            node = TextNode(metadata=node_metadata, text=node_text)
            nodes.append(node)

        return nodes

    def _create_metadata(self, blocks: List[TranscriptBlock], document: Document) -> Dict[str, Any]:
        """
        Create metadata for a node from a list of TranscriptBlocks.
        """
        starting_time = blocks[0].start_time
        ending_time = blocks[-1].end_time
        aggregated_text = " ".join(block.parsed_line for block in blocks)

        doc = nlp(aggregated_text)
        tokens = [token.text for token in doc]

        metadata = {
            "character_count": len(aggregated_text),
            "word_count": len(aggregated_text.split()),
            "token_count": len(tokens),
            "starting_time": starting_time,
            "ending_time": ending_time
        }
        return metadata

    def calculate_node_statistics(self, nodes: List[Dict]) -> Dict[str, float]:
        """
        Calculates statistical summaries of token lengths in nodes.

        Args:
            nodes (List[Dict]): The nodes to calculate statistics for.

        Returns:
            Dict[str, float]: A dictionary containing statistics.
        """
        logging.info("%s - Calculating node statistics", self.__class__.__name__)
        parsed_characters = [node.metadata['character_count'] for node in nodes]
        parsed_words = [node.metadata['word_count'] for node in nodes]
        parsed_tokens = [node.metadata["token_count"] for node in nodes]

        if not parsed_tokens:
            return {
                "min_tokens_per_node": 0,
                "max_tokens_per_node": 0,
                "avg_tokens_per_node": 0,
                "10th_percentile": 0,
                "25th_percentile": 0,
                "50th_percentile": 0,
                "75th_percentile": 0,
                "90th_percentile": 0,
                "parsed_nodes": 0,
                "parsed_characters": 0,
                "parsed_words": 0,
                "parsed_tokens": 0,
            }

        min_length = min(parsed_tokens)
        max_length = max(parsed_tokens)
        avg_length = sum(parsed_tokens) / len(parsed_tokens)
        p10 = np.percentile(parsed_tokens, 10)
        p25 = np.percentile(parsed_tokens, 25)
        p50 = np.percentile(parsed_tokens, 50)
        p75 = np.percentile(parsed_tokens, 75)
        p90 = np.percentile(parsed_tokens, 90)

        stats = {
            "min_tokens_per_node": min_length,
            "max_tokens_per_node": max_length,
            "avg_tokens_per_node": avg_length,
            "10th_percentile": p10,
            "25th_percentile": p25,
            "50th_percentile": p50,
            "75th_percentile": p75,
            "90th_percentile": p90,
            "parsed_nodes": len(nodes),
            "parsed_characters": sum(parsed_characters),
            "parsed_words": sum(parsed_words),
            "parsed_tokens": sum(parsed_tokens)
        }
        logging.info(
            "%s - Calculated node statistics: %s",
            self.__class__.__name__, json.dumps(stats, indent=2)
        )
        return stats
