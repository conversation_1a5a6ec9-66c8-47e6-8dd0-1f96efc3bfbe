import os
import logging
import webvtt
import tiktoken
from typing import List
from engine.parsing.llama_index_extensions.parser.pdf_parser.document_models import Document, TranscriptBlock

class VTTTextExtractor:
    def __init__(self, vtt_content: str):
        logging.info("Initializing VTTTextExtractor")
        self.vtt_content = vtt_content
        self.document = Document()

    def extract_text_from_document(self) -> Document:
        logging.info("Starting text extraction from VTT content")
        blocks_data = self.parse_vtt_content(self.vtt_content)

        for order_number, block_data in enumerate(blocks_data):
            block = TranscriptBlock(
                block_number = order_number,
                order_number = order_number,
                start_time = block_data['start'],
                end_time = block_data['end'],
                speaker = block_data.get('speaker', ''),
                text = block_data['text'],
                parsed_line = block_data['parsed_line'],
                identifier = block_data['identifier']
            )
            self.document.blocks.append(block)
        logging.info("Completed text extraction from VTT content")
        return self.document

    def extract_text_from_document(self) -> Document:
        logging.info("Starting text extraction from VTT content")
        blocks_data = self.parse_vtt_content(self.vtt_content)

        transcript_blocks = []
        for order_number, block_data in enumerate(blocks_data):
            block = TranscriptBlock(
                block_number=order_number,
                order_number=order_number,
                start_time=block_data['start'],
                end_time=block_data['end'],
                speaker=block_data.get('speaker', ''),
                text=self.clean_text(block_data['text']),
                identifier=block_data.get('identifier', '')
            )
            block.generate_parsed_line()  # Call the instance method to generate parsed_line
            transcript_blocks.append(block)

        merged_blocks = self.merge_blocks(transcript_blocks)

        self.document.blocks.extend(merged_blocks)
        logging.info("Completed text extraction from VTT content")
        return self.document

    def parse_vtt_content(self, vtt_content: str) -> List[dict]:
        parsed_blocks = []
        encoding = tiktoken.encoding_for_model("gpt-4")
        input_tokens = input_words = input_lines = input_chars = 0

        for caption in webvtt.read_buffer(vtt_content.splitlines(True)):
            clean_text = self.clean_text(caption.text)
            parsed_blocks.append({
                'start': caption.start,
                'end': caption.end,
                'identifier': caption.identifier,
                'speaker': caption.voice,
                'text': clean_text,
                'raw_text': caption.raw_text,
                'parsed_line': f"<{caption.start}> {caption.voice}: {clean_text}"
            })

        for item in parsed_blocks:
            text = item['parsed_line']
            input_tokens += len(encoding.encode(text))
            input_lines += 1
            input_words += len(text.split())
            input_chars += len(text)

        logging.info(f"Parsed VTT content: {input_words} words, {input_tokens} tokens, "
                     f"{input_lines} lines, {input_chars} characters")
        
        return parsed_blocks

    def clean_text(self, text: str) -> str:
        """
        Remove newline characters from the text and replace them with spaces.
        """
        return text.replace('\n', ' ')

    def merge_blocks(self, blocks: List[TranscriptBlock]) -> List[TranscriptBlock]:
        """
        Merge blocks that have the same speaker.
        """
        if not blocks:
            return []

        merged_blocks = [blocks[0]]

        for i in range(1, len(blocks)):
            current_block = merged_blocks[-1]
            next_block = blocks[i]

            # Check if the speaker of the current block is the same as the next block
            if current_block.speaker == next_block.speaker:
                # Merge the next block with the current block
                current_block.merge_with(next_block)
            else:
                # Add the next block as a new block
                merged_blocks.append(next_block)

        return merged_blocks