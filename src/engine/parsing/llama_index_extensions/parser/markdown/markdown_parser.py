from llama_index.core import Document
from llama_index.core.node_parser import MarkdownNodeParser


class MarkdownParser:
    """
    A simple parser for converting Markdown text into nodes using Llama Index.
    """

    def parse_nodes(self, markdown_text: str):
        """
        Converts Markdown text into nodes.

        Parameters:
            markdown_text (str): The Markdown text to parse.

        Returns:
            list: A list of nodes extracted from the Markdown document.
        """
        # Create a Document instance with the provided markdown text.
        document = Document(text=markdown_text)
        # Prepare a list of documents; this example uses a single document.
        markdown_docs = [document]
        # Instantiate the MarkdownNodeParser.
        parser = MarkdownNodeParser()
        # Extract nodes from the provided document(s).
        nodes = parser.get_nodes_from_documents(markdown_docs)
        return nodes
