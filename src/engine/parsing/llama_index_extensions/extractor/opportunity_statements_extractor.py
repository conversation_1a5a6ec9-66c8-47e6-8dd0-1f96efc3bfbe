"""
This module defines the OpportunityStatementsExtractor class, which extends BaseExtractor to 
implement the extraction of opportunity statements from text nodes. The class uses a language 
model (LLM) to generate responses based on a specifically designed prompt template.

The extractor is tailored to identify and extract statements that define an opportunity within the text. 
An opportunity in this context is characterized not by a requirement to perform an action, but rather by an 
authorization and encouragement to perform that action. The class aims to capture opportunities along with 
any predicate conditions or contextual information essential for understanding the opportunity.

Attributes:
    llm (LLMPredictorType): Specifies the language model used for generating opportunity statements, capable 
        of asynchronous predictions.
    prompt_template (str): A structured template that directs the language model in how to analyze the text and 
        what kind of responses to generate, ensuring that the responses focus on identifying opportunities.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new instance 
        of the extractor, configuring the language model, template, and number of workers for concurrent processing.
    class_name() -> str: Provides the name of the class, useful for logging and identification within larger systems.
    _aextract_opportunities_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts opportunity 
        statements from a single text node, processes the content to align with the specified requirements, and 
        returns it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of nodes, extracting 
        opportunity statements from each and compiling the results into a list of dictionaries, each representing 
        the extracted data from one node.

This class is particularly valuable in environments where understanding and extracting potential opportunities 
from textual data is crucial, such as in business analytics, strategic planning, or market analysis.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode

class OpportunityStatementsExtractor(BaseExtractor):
    DEFAULT_OPP_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Please analyze the provided text and extract all statements that explicitly define an opportunity. An opportunity is similar to a requirement but instead of being required to perform an action, the entity is both authorized and encouraged to perform that action. Phrases like "encouraged to" and "could benefit from" are often found in opportunities. Capture the opportunity and include include any predicate conditions or situational contexts found in the text that are essential to comprehending the opportunity. Important! do not create your own context, only include useful context already present in the text.

    Output the opportunities as an array of values, with each value being a distinct opportunity statement identified in the text, including all relevant context. Each opportunity statement must be comprised of full words not tokens. Ensure that the values are delimited by "|||" and that string values are not enclosed in quotes.  If no opportunity statements are found, include only "No Opportunity statements found" in your output. The response should strictly follow the format guidelines and include no extraneous content or formatting.
    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_OPP_GEN_TMPL,
        description="Prompt template to use when generating Opportunities.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_OPP_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "OpportunityStatementsExtractor"

    async def _aextract_opportunities_from_node(self, node: BaseNode) -> Dict[str, List[str]]:
        """Extract software opportunities from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}
        
        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = ["page_label","file_name","file_path","file_type","file_size","creation_date","last_modified_date"]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)
            
            # Use the machine learning model to predict data elements
            opportunities = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )
            
            if isinstance(opportunities, str):
                opportunity_statements = [opportunity.strip() for opportunity in opportunities.split('|||')]
            else:
                opportunity_statements = []  # Default to empty list if not a string

            return {"opportunity_statements": opportunity_statements}
        
        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        Opportunities_jobs = []
        for node in nodes:
            Opportunities_jobs.append(self._aextract_opportunities_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            Opportunities_jobs, show_progress=self.show_progress, workers=self.num_workers
        )

        return metadata_list
