"""
This module defines the DefinitionStatementsExtractor class, which extends the BaseExtractor 
to implement the extraction of definition statements from text nodes. It utilizes a language model
(LLM) to generate outputs based on a specific prompt template.

The extractor focuses on identifying and extracting clear definitions of terms, concepts, or processes
within a given text. These definitions are typically framed with phrases like "is defined as" or "means",
and include any necessary context for understanding. The output is formatted strictly as an array of
"Term - Definition" pairs, or a single entry indicating no definitions were found.

Attributes:
    llm (LLMPredictorType): The language model to use for generation, specified by a type that expects
        an asynchronous predict method (`apredict`).
    prompt_template (str): A predefined template that guides the language model in generating responses,
        focusing on extracting precise definition statements.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new 
        instance of the extractor with optional parameters for the language model, template, and concurrency.
    class_name() -> str: Returns the name of the class.
    _aextract_requirements_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts definition
        statements from a single text node and returns them in a metadata dictionary.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously extracts definition statements from
        a sequence of nodes, managing concurrency and aggregation of results.

This extractor is designed to integrate seamlessly within systems that adhere to asynchronous operations
and require precise linguistic analysis for content extraction.
"""


from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode

class DefinitionStatementsExtractor(BaseExtractor):
    DEFAULT_DS_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Please analyze the provided text and extract all statements that explicitly define a term, concept, 
    or process, including any critical context necessary to fully understand the definition. Definitions 
    typically include phrases like "is defined as," "means," or involve the explanation of what a 
    specific term or concept entails. Capture the term being defined and include any surrounding 
    context found in the text that contributes to a complete understanding of the definition. Important! 
    do not create your own context, only include useful context already present in the text.

    Here's what you should focus on in identifying definitions:

    - Explicit explanations of terms or concepts, ensuring to capture the term and its definition 
    comprehensively.
    - Descriptions that clarify what a term entails or how a process works, including any conditions 
    or contexts that enhance understanding.
    - Statements that start with a term followed by verbs like "refers to," "is," "constitutes," or 
    similar phrasing, ensuring to include the complete statement for clarity.

    Output the definitions as an array of values, with each value presented in the format "Term - Definition". 
    Each definition must be comprised of full words not tokens. Ensure that the values are delimited 
    by "|||" and that string values are not enclosed in quotes.  If no definition statements are found, 
    include only "[]" without the quotations in your output. The response should strictly follow the format 
    guidelines and include no extraneous content or formatting.
    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_DS_GEN_TMPL,
        description="Prompt template to use when generating definitions.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_DS_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "DefinitionStatementsExtractor"

    async def _aextract_definitions_from_node(self, node: BaseNode) -> Dict[str, List[str]]:
        """Extract definitions statements from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}
        
        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = ["page_label","file_name","file_path","file_type","file_size","creation_date","last_modified_date"]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)
            
            # Use the machine learning model to predict data elements
            definitions = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )
            
            if isinstance(definitions, str):
                definition_statements = [definition.strip() for definition in definitions.split('|||')]
            else:
                definition_statements = []  # Default to empty list if not a string

            return {"definition_statements": definition_statements}
        
        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        definitions_jobs = []
        for node in nodes:
            definitions_jobs.append(self._aextract_definitions_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            definitions_jobs, show_progress=self.show_progress, workers=self.num_workers
        )

        return metadata_list
