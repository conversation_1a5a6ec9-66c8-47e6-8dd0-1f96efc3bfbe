"""
This module defines the ProcedureChangeStatementsExtractor class, which extends BaseExtractor to 
implement the extraction of procedure change statements from text nodes. The class utilizes a language 
model (LLM) to generate responses based on a structured prompt template.
The extractor is specifically designed to identify and extract statements that are requirements
specific to changes in how the organization conducts their business. It focuses on capturing 
keywords such as "procedure", "policy", "process", and "audit", which indicate how work is done 
without necessarily altering the structure of the organization. The extractor also ensures that 
any predicate conditions or situational contexts that are crucial for understanding the procedure 
change are included in the output.
Attributes:
    llm (LLMPredictorType): Specifies the language model used for generating procedure change 
        statements. This model supports asynchronous predictions, enhancing the efficiency of the 
        data processing.
    prompt_template (str): A pre-defined template that instructs the language model on how to 
        analyze the text and structure the output, ensuring that the responses accurately reflect 
        the nuances of procedure change statements.
Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a 
        new instance of the extractor, setting up the language model, template, and the number of 
        workers for concurrent operations, among other configurations.
    class_name() -> str: Provides the name of the class, useful for logging and identification 
        within larger systems.
    _aextract_procedure_changes_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts 
        procedure change statements from a single text node, processes the content to align with the 
        specified requirements, and returns it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of 
        nodes, extracting procedure change statements from each and compiling the results into a 
        list of dictionaries, each representing the extracted data from one node.
This class is especially useful in contexts such as legal document analysis, compliance checks, or 
any setting where understanding the scope and boundaries of permissions granted by text is crucial.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode


class ProcedureChangeStatementsExtractor(BaseExtractor):
    DEFAULT_AUTH_GEN_TMPL = """\
    Here is the context:
    {context_str}
    Please analyze the provided text and extract all procedure change statements that explicitly 
    identify a modification, addition, removal, or clarification of steps, procedures, or workflows 
    within a system, operation, or set of tasks (consider specific changes in how the organization 
    conducts their business and focus on workflows, tools, procedures. Procedure changes often indicate 
    new or updated ways of doing things and can include specific actions, timelines, roles, or 
    conditions (look for keywords like "procedure," "policy," "aduit," or "process"). Capture the 
    procedure change and include any predicate conditions or situational contexts found in the text 
    that are essential to comprehending the procedure change. Important! do not create your own 
    context, only include useful context already present in the text. Output the procedure changes 
    as an array of values, with each value being a distinct procedure change statement identified in 
    the text, including all relevant context. Each procedure change statement must be comprised of 
    full words not tokens. Ensure that the values are delimited by "|||" and that string values are 
    not enclosed in quotes. If no procedure change statements are found, include only "[]" in your 
    output without quotations. The response should strictly follow the format guidelines and include 
    no extraneous content or formatting.
    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_AUTH_GEN_TMPL,
        description="Prompt template to use when generating Procedure Changes.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_AUTH_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "ProcedureChangeStatementsExtractor"

    async def _aextract_procedure_changes_from_node(
        self, node: BaseNode
    ) -> Dict[str, List[str]]:
        """Extract software procedure changes from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}

        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = [
                "page_label",
                "file_name",
                "file_path",
                "file_type",
                "file_size",
                "creation_date",
                "last_modified_date",
            ]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)

            # Use the machine learning model to predict data elements
            procedure_changes = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )

            if isinstance(procedure_changes, str):
                procedure_change_statements = [
                    procedure_change.strip()
                    for procedure_change in procedure_changes.split("|||")
                ]
            else:
                procedure_change_statements = []  # Default to empty list if not a string

            return {"procedure_change_statements": procedure_change_statements}

        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        procedure_changes_jobs = []
        for node in nodes:
            procedure_changes_jobs.append(self._aextract_procedure_changes_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            procedure_changes_jobs,
            show_progress=self.show_progress,
            workers=self.num_workers,
        )

        return metadata_list
