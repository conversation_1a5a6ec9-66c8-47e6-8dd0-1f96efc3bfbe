"""
This module defines the OrganizationalChangeStatementsExtractor class, which extends BaseExtractor 
to implement the extraction of organizational change statements from text nodes. The class 
utilizes a language model (LLM) to generate responses based on a structured prompt template.
The extractor is specifically designed to identify and extract statements that are requirements
tailored towards ensuring that the organization surrounding the tech change is also “ready” for 
the upcoming change. It focuses on capturing keywords like "staff/associate/analyst (etc.)", "xx 
team 'is notified/informed' (etc.)", and "train/training", which indicates who does the work and 
how the organization is structured or managed while considering change management, staff impact, 
and training impacts. The extractor also ensures that any predicate conditions or situational 
contexts that are crucial for understanding the organizational change are included in the output.
Attributes:
    llm (LLMPredictorType): Specifies the language model used for generating organizational change 
        statements. This model supports asynchronous predictions, enhancing the efficiency of the 
        data processing.
    prompt_template (str): A pre-defined template that instructs the language model on how to 
        analyze the text and structure the output, ensuring that the responses accurately reflect 
        the nuances of organizational change statements.
Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a 
        new instance of the extractor, setting up the language model, template, and the number of 
        workers for concurrent operations, among other configurations.
    class_name() -> str: Provides the name of the class, useful for logging and identification 
        within larger systems.
    _aextract_organizational_changes_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously 
        extracts organizational change statements from a single text node, processes the content 
        to align with the specified requirements, and returns it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of 
        nodes, extracting organizational change statements from each and compiling the results 
        into a list of dictionaries, each representing the extracted data from one node.
This class is especially useful in contexts such as legal document analysis, compliance checks, or
any setting where understanding the scope and boundaries of permissions granted by text is crucial.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode


class OrganizationalChangeStatementsExtractor(BaseExtractor):
    DEFAULT_AUTH_GEN_TMPL = """\
    Here is the context:
    {context_str}
    Please analyze the provided text and extract all organizational change statements that explicitly 
    identify any modification in the structure, roles, responsibilities, hierarchy, departments, teams, 
    or policies affecting how an organization operates or is managed. This includes new roles, mergers, 
    reassignments, team restructures, or policy changes impacting organizational workflows or relationships.
    The organizational changes I'm looking for are requirements tailored towards ensuring that the 
    organization surrounding the tech change is also “ready” for the upcoming change. An organizational 
    change is similar to a requirement but focuses on WHO does the work and how the organization is 
    structured or managed (e.g., roles, departments, reporting lines). Consider change management, 
    staff/people impact, training/development impacts. Phrases and keywords like "staff/associate/analyst (etc.)", 
    "xx team 'is notified/informed' (etc.)", and "train/training" are often associated with organizational 
    changes. Capture the organizational change and include any predicate conditions or situational 
    contexts found in the text that are essential to comprehending the organizational change. 
    Important! do not create your own context, only include useful context already present in the text.
    Output the organizational changes as an array of values, with each value being a distinct process 
    change statement identified in the text, including all relevant context. Each organizational change 
    statement must be comprised of full words not tokens. Ensure that the values are delimited by 
    "|||" and that string values are not enclosed in quotes.  If no organizational change statements are 
    found, include only "[]" without the quotations in your output. The response should 
    strictly follow the format guidelines and include no extraneous content or formatting.
    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_AUTH_GEN_TMPL,
        description="Prompt template to use when generating Organizational Changes.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_AUTH_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "OrganizationalChangeStatementsExtractor"

    async def _aextract_organizational_changes_from_node(
        self, node: BaseNode
    ) -> Dict[str, List[str]]:
        """Extract software organizational changes from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}

        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = [
                "page_label",
                "file_name",
                "file_path",
                "file_type",
                "file_size",
                "creation_date",
                "last_modified_date",
            ]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)

            # Use the machine learning model to predict data elements
            organizational_changes = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )

            if isinstance(organizational_changes, str):
                organizational_change_statements = [
                    organizational_change.strip()
                    for organizational_change in organizational_changes.split("|||")
                ]
            else:
                organizational_change_statements = (
                    []
                )  # Default to empty list if not a string

            return {
                "organizational_change_statements": organizational_change_statements
            }

        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        organizational_changes_jobs = []
        for node in nodes:
            organizational_changes_jobs.append(
                self._aextract_organizational_changes_from_node(node)
            )

        metadata_list: List[Dict] = await run_jobs(
            organizational_changes_jobs,
            show_progress=self.show_progress,
            workers=self.num_workers,
        )

        return metadata_list
