"""
This module defines the RequirementStatementsExtractor class, which extends BaseExtractor to 
implement the extraction of software requirement statements from text nodes. The class leverages 
a language model (LLM) to generate requirement statements based on a structured prompt template.

The extractor specifically targets statements that express a requirement, condition, or necessity 
within the text, adhering to strict formatting guidelines to produce an output array of requirement 
statements. The aim is to capture not only direct instructions or rules but also the contextual 
elements essential for understanding these requirements, such as conditions or obligations.

Attributes:
    llm (LLMPredictorType): The language model to be used for generating requirement statements. The model 
        is expected to support asynchronous predictions.
    prompt_template (str): A pre-defined template guiding the language model on how to structure its analysis 
        and output. The template ensures the responses are focused on requirement extraction and comply with 
        specified formatting rules.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new 
        instance of the extractor, setting up the language model, template, and number of workers for concurrent 
        operations.
    class_name() -> str: Returns the name of the class, used for identification and logging purposes.
    _aextract_requirements_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts requirement 
        statements from a single text node, processing the text to match the specified requirements and returning 
        them in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of nodes, extracting 
        requirement statements from each and compiling the results into a list of dictionaries, each representing 
        the extracted data from one node.

This extractor is particularly useful in environments where precise and structured extraction of requirement 
statements is necessary, such as in the analysis of software documentation or legal texts.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode

class RequirementStatementsExtractor(BaseExtractor):
    """Software requirements extractor. Node-level extractor.
    Extracts `software_requirements` metadata field.

    Args:
        llm (Optional[LLM]): LLM used for generation.
        prompt_template (str): Template for software requirements extraction.
    """
    DEFAULT_SR_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Please analyze the provided text and extract all statements that explicitly require an entity to act/behave in a certain manner, including any critical context necessary to fully understand the requirement. A requirement statement typically includes verbs like "must,"  "required," or phrases that indicate obligation or necessity. Additionally, include any predicate conditions or situational contexts found in the text that are essential to fully comprehending the requirement, but only together with the requirement, do not include those predicate conditions or situational contexts seperately. Important! do not create your own context, only include useful context already present in the text. Exclude all statements that authorize or encourage a course of action but that do not mandate or require it. However, if a statement says an entity must take one of the following courses of action, followed by statements that allow those possible courses of action, the statements should be read holistically as one requirement statement encompassing the statement that one of the courses of action must be taken along with all of the courses of action.

    Here's what you should focus on in identifying requirement statements:

    Direct instructions or rules along with any conditional language that frames these rules.
    Conditions that must be met before or while complying with a directive, including the directive itself.
    Obligations or duties specified in the context, including any clauses that trigger these obligations.

    Output the requirements as an array of values, with each value being a distinct requirement statement identified in the text, including all relevant context. Each requirement statement must be comprised of full words not tokens. Ensure that the values are delimited by "|||" and that string values are not enclosed in quotes.  If no requirement statements are found, include only "No Requirement statements found" in your output. The response should strictly follow the format guidelines and include no extraneous content or formatting.
    """
    
    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_SR_GEN_TMPL,
        description="Prompt template to use when generating software requirements.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_SR_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "SoftwareRequirementsExtractor"

    async def _aextract_requirements_from_node(self, node: BaseNode) -> Dict[str, List[str]]:
        """Extract software requirements from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}
        
        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = ["page_label","file_name","file_path","file_type","file_size","creation_date","last_modified_date"]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)
            
            # Use the machine learning model to predict data elements
            requirements = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )
            
            if isinstance(requirements, str):
                requirement_statements = [requirement.strip() for requirement in requirements.split('|||')]
            else:
                requirement_statements = []  # Default to empty list if not a string

            return {"requirement_statements": requirement_statements}
        
        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        requirements_jobs = []
        for node in nodes:
            requirements_jobs.append(self._aextract_requirements_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            requirements_jobs, show_progress=self.show_progress, workers=self.num_workers
        )

        return metadata_list
