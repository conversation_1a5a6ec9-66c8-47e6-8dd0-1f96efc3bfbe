"""
This module defines the ReferenceInformationStatementsExtractor class, which extends BaseExtractor to 
implement the extraction of reference information statements from text nodes. This class utilizes a 
language model (LLM) to generate responses based on a sophisticated prompt template.

The extractor is designed to identify and extract various types of reference statements within textual 
content. These can include cross-references, hyperlinks or footnotes, contextual placements, document 
and legislation names, use of abbreviations, lists of sources, and instructional references. The aim 
is to capture these references along with any necessary contextual information that aids in understanding 
the reference.

Attributes:
    llm (LLMPredictorType): Specifies the language model used for generating reference statements. 
        This model supports asynchronous predictions, facilitating efficient data processing.
    prompt_template (str): A pre-defined template that guides the language model on how to analyze 
        the text and structure the output, focusing specifically on the extraction of reference statements.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new 
        instance of the extractor, setting up the language model, template, and the number of workers 
        for concurrent operations, among other configurations.
    class_name() -> str: Returns the name of the class, useful for identification and logging purposes 
        within larger systems.
    _aextract_references_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts reference 
        statements from a single text node and processes the content to align with the specified requirements, 
        returning it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of nodes, extracting 
        reference statements from each and compiling the results into a list of dictionaries, each representing 
        the extracted data from one node.

This class is especially useful in settings such as academic research, legal document processing, and corporate 
documentation where accurate identification and extraction of reference information are crucial.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode
    
class ReferenceInformationStatementsExtractor(BaseExtractor):
    DEFAULT_REF_INF_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Please analyze the provided text and extract all reference statements.

    A reference statement can be:

    Cross-references: Look for phrases like "see," "refer to," "as described in," or "according to." These phrases often precede the title of another section, a table, a figure, or an appendix within the guide. For example: "Refer to Section B-2-01 for detailed property insurance requirements."

    Hyperlinks or Footnotes: In digital versions of the guide, hyperlinks may be used to connect directly to other sections or external documents. In printed materials, footnotes might be used similarly.

    Contextual Placement: References are typically found where further elaboration is needed that the current section does not cover comprehensively, or where supporting information can augment the primary content.

    Document and Legislation Names: References to external sources often include names of other documents, laws, or regulatory bodies. For example: "According to the Dodd-Frank Wall Street Reform and Consumer Protection Act…" and "As outlined in the Selling Guide…"

    Use of Abbreviations: After the first mention, documents or important concepts might be abbreviated. A prior or subsequent mention might explain what the abbreviation stands for, thereby acting as a reference. For example: "The LTV ratios are explained further in the ARM (Adjustable Rate Mortgage) section."

    List of Sources: At the end of sections or chapters, there might be a list of cited works or further readings, which includes other parts of the guide or external sources that offer additional details or context.

    Instructional References: These might include directions that guide the reader on how to perform specific tasks or where to find necessary tools or forms within the guide or on an external website.

    Output the reference statements as an array of values, with each value being a distinct reference statement identified in the text, include any predicate conditions or situational contexts found in the text that are essential to comprehending the reference statement. Important! do not create your own context, only include useful context already present in the text. Each reference statement must be comprised of full words not tokens. Ensure that the values are delimited by "|||" and that string values are not enclosed in quotes.  If no reference statements are found, include only "No Reference statements found" in your output. The response should strictly follow the format guidelines and include no extraneous content or formatting.
    """
    
    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_REF_INF_GEN_TMPL,
        description="Prompt template to use when generating reference statements.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_REF_INF_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "ReferenceInformationStatementsExtractor"

    async def _aextract_references_from_node(self, node: BaseNode) -> Dict[str, List[str]]:
        """Extract software references from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}
        
        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = ["page_label","file_name","file_path","file_type","file_size","creation_date","last_modified_date"]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)
            
            # Use the machine learning model to predict data elements
            references = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )
            
            if isinstance(references, str):
                reference_statements = [reference.strip() for reference in references.split('|||')]
            else:
                reference_statements = []  # Default to empty list if not a string

            return {"reference_statements": reference_statements}
        
        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        References_jobs = []
        for node in nodes:
            References_jobs.append(self._aextract_references_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            References_jobs, show_progress=self.show_progress, workers=self.num_workers
        )

        return metadata_list
