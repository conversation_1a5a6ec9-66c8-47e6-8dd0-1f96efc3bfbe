"""
This module defines the DataElementsExtractor class, which extends BaseExtractor to implement 
the extraction of data elements necessary for software implementation from text nodes. This 
class utilizes a language model (LLM) to generate responses based on a specified prompt template.

The extractor is specifically designed to identify and extract unique data elements that are 
required to implement functional software requirements within the provided text. It focuses 
on analyzing the text to determine what specific data inputs, configurations, or databases are 
necessary for software functionalities described in the text.

Attributes:
    llm (LLMPredictorType): Specifies the language model used for generating data elements. This model 
        is capable of asynchronous predictions, facilitating efficient and parallel data processing.
    prompt_template (str): A structured template that instructs the language model on how to analyze the 
        text and what type of data elements to identify, ensuring that the responses are relevant and 
        precise to the needs of software implementation.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new 
        instance of the extractor, configuring the language model, template, and number of workers for 
        concurrent operations, among other configurations.
    class_name() -> str: Returns the name of the class, useful for identification and logging purposes 
        within larger systems.
    _aextract_data_elements_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts data 
        elements from a single text node based on the requirements for software implementation and processes 
        the content to align with the specified requirements, returning it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of nodes, extracting 
        data elements necessary for software implementation from each and compiling the results into a list of 
        dictionaries, each representing the extracted data from one node.

This class is especially valuable in software development environments where accurate identification and 
extraction of necessary data elements are crucial for the design and implementation of software solutions.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings

class DataElementsExtractor(BaseExtractor):
    """Data elements extractor for software implementation. Node-level extractor.
    Extracts `data_elements` metadata field necessary for implementing software requirements.

    Args:
        llm (Optional[LLM]): LLM used for generation.
        prompt_template (str): Template for data elements extraction.
    """
    
    DEFAULT_DE_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Extract out as an python array of values all the unique data elements required to implement any functional software requirements present in the context.

    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_DE_GEN_TMPL,
        description="Prompt template to use when identifying necessary data elements.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_DE_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "DataElementsExtractor"

    async def _aextract_data_elements_from_node(self, node: BaseNode) -> Dict[str, str]:
        """Extract data elements required for software implementation from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}

        context_str = node.get_content(metadata_mode=self.metadata_mode)
        data_elements = await self.llm.apredict(
            PromptTemplate(template=self.prompt_template),
            context_str=context_str,
        )
        print(self.prompt_template)
        return {"data_elements": data_elements.strip()}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        data_elements_jobs = []
        for node in nodes:
            data_elements_jobs.append(self._aextract_data_elements_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            data_elements_jobs, show_progress=self.show_progress, workers=self.num_workers
        )

        return metadata_list