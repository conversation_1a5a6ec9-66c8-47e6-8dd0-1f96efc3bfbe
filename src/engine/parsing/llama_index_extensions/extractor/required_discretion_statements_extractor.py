"""
This module defines the RequiredDiscretionStatementsExtractor class, which extends BaseExtractor to 
implement the extraction of required discretion statements from text nodes. Utilizing a language 
model (LLM), this class generates responses based on a structured prompt template.

The extractor focuses on identifying and extracting statements where an entity is required to apply 
good business judgment rather than simply perform an action. This involves capturing phrases and 
contexts within the text that explicitly mandate the application of discretion in decision-making 
processes. The class ensures that each extracted statement includes necessary context to fully 
understand the discretion required.

Attributes:
    llm (LLMPredictorType): The language model used for generating responses. This model is capable 
        of asynchronous predictions, which is critical for processing multiple requests concurrently.
    prompt_template (str): A predefined template that directs the language model on how to analyze 
        the text and structure the output, focusing specifically on required discretions.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new 
        instance of the extractor, setting up the language model, template, and number of workers for 
        concurrent operations, among other configurations.
    class_name() -> str: Returns the name of the class, which is useful for identification and logging 
        purposes within larger systems.
    _aextract_discretions_from_node(node: BaseNode) -> Dict[str, str]: Asynchronously extracts discretion 
        statements from a single text node and processes the content to align with specified requirements, 
        returning it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of nodes, 
        extracting required discretion statements from each and compiling the results into a list of 
        dictionaries, each representing the extracted data from one node.

This class is particularly valuable in settings such as corporate governance, legal compliance, and 
business strategy development, where understanding the nuances of required discretions is crucial.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings
from llama_index.core.schema import MetadataMode

class RequiredDiscretionStatementsExtractor(BaseExtractor):
    DEFAULT_RQD_DSC_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Please analyze the provided text and extract all statements that explicitly require or explicitly involve the application of good business judgment.  A "Required Discretion" is similar to a requirement but instead of being required to perform an action, the entity is instead required to apply good business judgement to make a decision/perform an action.  Capture the required discretions and include include any predicate conditions or situational contexts found in the text that are essential to comprehending the required discretion. Important! do not create your own context, only include useful context already present in the text.

    Output the required discretions as an array of values, with each value being a distinct "required discretion" statement identified in the text, including all relevant context. Each "required discretion" statement must be comprised of full words not tokens. Ensure that the values are delimited by "|||" and that string values are not enclosed in quotes.  If no Required Discretion statements are found, include only "No Required Discretion statements found" in your output. The response should strictly follow the format guidelines and include no extraneous content or formatting.
    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_RQD_DSC_GEN_TMPL,
        description="Prompt template to use when generating Required Discretions.",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_RQD_DSC_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "RequiredDiscretionStatementsExtractor"

    async def _aextract_discretions_from_node(self, node: BaseNode) -> Dict[str, List[str]]:
        """Extract software discretions from a node and return its metadata dict."""
        if not isinstance(node, TextNode):
            return {}
        
        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = ["page_label","file_name","file_path","file_type","file_size","creation_date","last_modified_date"]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)
            
            # Use the machine learning model to predict data elements
            discretions = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )
            
            if isinstance(discretions, str):
                discretion_statements = [discretion.strip() for discretion in discretions.split('|||')]
            else:
                discretion_statements = []  # Default to empty list if not a string

            return {"discretion_statements": discretion_statements}
        
        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}

    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        Discretions_jobs = []
        for node in nodes:
            Discretions_jobs.append(self._aextract_discretions_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            Discretions_jobs, show_progress=self.show_progress, workers=self.num_workers
        )

        return metadata_list
