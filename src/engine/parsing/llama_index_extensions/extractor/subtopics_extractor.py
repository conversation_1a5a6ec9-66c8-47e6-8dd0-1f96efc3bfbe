"""
This module defines the SubtopicsExtractor class, which extends BaseExtractor to implement the extraction
of mortgage-related subtopics from text nodes. This class utilizes a language model (LLM) to generate responses
based on a detailed prompt template.

The extractor is designed to identify and list only the mortgage topics directly discussed within the provided 
context, adhering strictly to a predefined list of topics. This ensures that the output is highly relevant and 
focused, excluding any topics not specified in the list. The class is particularly useful in contexts such as 
document analysis for mortgage servicing or legal compliance, where precise identification of discussed topics 
is necessary.

Attributes:
    llm (LLMPredictorType): Specifies the language model used for generating topic lists. This model supports 
        asynchronous predictions, enabling efficient processing of multiple text nodes concurrently.
    prompt_template (str): A structured template that guides the language model in analyzing the text and 
        structuring the output to include only the specified subtopics.

Methods:
    __init__(llm: Optional[LLM], prompt_template: str, num_workers: int, **kwargs): Initializes a new instance 
        of the extractor, configuring the language model, template, and the number of workers for concurrent 
        operations, among other settings.
    class_name() -> str: Returns the name of the class, useful for identification and logging within larger systems.
    _aextract_subtopics_from_node(node: BaseNode) -> Dict[str, List[str]]: Asynchronously extracts subtopics 
        from a single text node based on the predefined topics list and processes the content to align with 
        the specified requirements, returning it in a dictionary format.
    aextract(nodes: Sequence[BaseNode]) -> List[Dict]: Asynchronously processes a sequence of nodes, extracting 
        subtopics from each and compiling the results into a list of dictionaries, each representing the extracted 
        data from one node.

This class is invaluable for efficiently parsing and extracting specific topic information from large volumes of 
textual data in mortgage servicing and compliance documentation, ensuring that outputs are strictly aligned with 
organizational or regulatory standards.
"""

from typing import Any, Dict, List, Optional, Sequence
from llama_index.core.async_utils import DEFAULT_NUM_WORKERS, run_jobs
from llama_index.core.bridge.pydantic import Field
from llama_index.core.extractors.interface import BaseExtractor
from llama_index.core.llms.llm import LLM
from llama_index.core.prompts import PromptTemplate
from llama_index.core.schema import BaseNode, TextNode
from llama_index.core.schema import MetadataMode
from llama_index.core.service_context_elements.llm_predictor import LLMPredictorType
from llama_index.core.settings import Settings

class SubtopicsExtractor(BaseExtractor):
    DEFAULT_SUBTOPIC_GEN_TMPL = """\
    Here is the context:
    {context_str}

    Given the provided context and information, please list only the mortgage topics directly discussed. Your response should exclusively include topics from the following predefined list. This list is organized as "Topic" - Topic Definition. Do not include any topics or content outside of this list in your response. VERY IMPORTANT! Do NOT include topic definitions in the output.  The topics are:

    "Bankruptcy Process" - Handling tasks related to loans in bankruptcy status, ensuring compliance with legal requirements and effective management of loan processes during bankruptcy proceedings.
    "Bankruptcy Concurrent Filing" - Addressing concurrent filings for bankruptcy, managing simultaneous filings to streamline processes and ensure regulatory compliance.
    "Bankruptcy Letters" - Managing correspondence and notifications related to bankruptcy, ensuring accurate and timely communication with relevant parties during the bankruptcy process.
    "Bankruptcy Notifications" - Handling notifications specific to bankruptcy cases, providing timely updates and information to stakeholders involved in the bankruptcy proceedings.
    "Bankruptcy Case Conversion" - Managing the conversion of loan cases during bankruptcy, ensuring accurate and compliant transition of cases between different bankruptcy stages.
    "Bankruptcy Cramdown" - Dealing with legal processes like reducing debt in bankruptcy, ensuring compliance with legal requirements and managing debt reduction procedures.
    "Bankruptcy Plan Setup" - Setting up repayment plans for bankruptcy cases, defining terms and conditions for borrowers to fulfill during the bankruptcy repayment period.
    "Cash Administration Banking" - Handling tasks related to the financial transactions of the servicing entity, ensuring accurate and secure management of financial activities within the system.
    "Cash Administration Fees and Costs" - Managing and processing fees and costs associated with loans, ensuring accurate assessment and collection of fees in accordance with established policies and regulations.
    "Cash Administration Operations" - Administering cash-related functions in loan servicing, including the processing and reconciliation of cash transactions for optimal financial management.
    "Inbound Bankruptcy Payments" - Handling payments received during bankruptcy, ensuring accurate recording and allocation of funds in compliance with bankruptcy regulations.
    "Inbound Loss Draft Payment" - Managing payments related to loss drafts, ensuring proper handling and allocation of funds to address property damage.
    "Inbound Loss Mitigation Payments" - Processing payments for loss mitigation, facilitating the efficient and accurate receipt of funds related to loss mitigation efforts.
    "Inbound Mortgage Payments" - Handling general inbound payments, ensuring accurate recording and allocation of funds to the appropriate loan accounts.
    "Inbound Payoff Funds" - Managing funds received for loan payoffs, ensuring timely and accurate processing of funds for loan satisfaction.
    "Inbound Cash Other" - A miscellaneous category for inbound cash transactions, providing flexibility for handling unique or unidentified transactions.
    "Inbound Cash Payment Amounts" - Processing and recording payment amounts, ensuring accurate and timely recording of payment information within the servicing system.
    "Inbound Cash Reversals" - Managing the reversal of cash transactions, addressing instances where a previously recorded transaction needs to be reversed or corrected.
    "Suspense Sweeps" - Addressing suspense account transactions, ensuring the efficient resolution and allocation of funds held in suspense accounts.
    "Borrower Refunds" - Handling refund processes for borrowers, ensuring timely and accurate processing of refunds in compliance with policies and regulations.
    "Corporate Advance" - Managing corporate advance disbursements, ensuring accurate and timely processing of corporate funds for various loan-related expenses.
    "Escrow Line Disbursements" - Disbursing funds from escrow accounts, ensuring accurate and timely disbursement of funds for escrow-related expenses.
    "Loss Draft Disbursements" - Administering disbursements related to loss drafts, ensuring proper handling and allocation of funds to address property damage.
    "MI Disbursements" - Handling disbursements for mortgage insurance, ensuring accurate processing and allocation of funds for mortgage insurance-related expenses.
    "Property Preservation" - Managing disbursements for property preservation, ensuring proper handling and allocation of funds for preserving property conditions.
    "Collateral Documents" - Handling and managing documents related to loan collateral, ensuring accurate documentation and compliance with collateral requirements.
    "Electronic Registrations" - Managing electronically registered documents, ensuring proper registration and documentation of electronic records related to loan collateral.
    "Property Monitoring" - Administering tasks related to loan collateral properties, ensuring proper management and oversight of properties securing loans.
    "Property Preservation" - Tasks related to preserving the condition of mortgaged properties, including activities to maintain and protect the value of collateral.
    "Loan Satisfactions" - Administering tasks related to loan satisfactions, ensuring accurate and timely processing of documents and activities related to loan payoffs.
    "Title Issue Resolution" - Resolving issues related to property titles, ensuring accurate and timely resolution of title-related challenges.
    "Claim/Loss Analysis" - Analyzing and processing claims and losses, ensuring accurate assessment and handling of claims and losses associated with loans.
    "Collections" - Servicer contacts with borrowers who are delinquent on their mortgage payments, actions to bring delinquent accounts current (such as payment plans, negotiations to resolve outstanding debts, assessments of eligibility for referral to loss mitigation or referral to foreclosure, reporting to internal and external stakeholders.
    "Delinquency" - Managing tasks related to overdue payments, ensuring effective handling of delinquent accounts and compliance with delinquency management processes.
    "Eviction" - Handling eviction processes for delinquent properties, ensuring compliance with legal and regulatory requirements for property eviction.
    "Foreclosure" - Administering tasks related to foreclosure proceedings, ensuring compliance with legal and regulatory requirements for foreclosure processes.
    "Loss Mitigation" - Managing efforts to mitigate losses on delinquent loans, including activities aimed at preventing or minimizing financial losses.
    "Pre-Foreclosure" - Administering tasks before foreclosure proceedings, ensuring compliance with pre-foreclosure requirements and effective management of pre-foreclosure activities.
    "Prior Lien Foreclosure" - Handling foreclosure of prior liens, ensuring compliance with legal and regulatory requirements for foreclosure on prior liens.
    "Reinstatement Quote" - Providing quotes for loan reinstatement, ensuring accurate and timely provision of reinstatement quotes for borrowers.
    "REO" - Administering Real Estate Owned (REO) properties, including tasks related to the management and disposition of properties owned by the servicer.
    "Escrow Analysis" - Analyzing and managing escrow accounts, ensuring accurate assessment and management of funds in escrow.
    "Escrow Line Management" - Managing specific escrow-related tasks, including the administration of escrow lines and related activities.
    "Escrow Status" - Monitoring and updating the status of escrow accounts, ensuring accurate and timely communication regarding the status of escrow accounts.
    "Escrow Type Setup" - Configuring types of escrow accounts, ensuring accurate setup and management of various escrow account types.
    "Interest On Escrow" - Administering interest on escrow balances, ensuring accurate calculation and processing of interest on funds held in escrow.
    "Fire Insurance" - Insurance provided by the borrower which protects the dwelling and structures for loss or damage .
    "Flood Insurance" - Insurance protecting the dwelling and structures from flood damage, usually as required by flood zone determination. 
    "Force-Placed Insurance" - Insurance protecting the dwelling and structures in favor of the lender, which is placed by the lender (servicer) when the homeowner has not provided their own insurance.
    "Homeowners Insurance" - Insurance provided by the borrower which protects the dwelling and structures for loss or damage to the structures and loss of contents.
    "Loss Drafts" - Handling loss drafts from escrow, ensuring proper management and allocation of funds related to loss drafts.
    "Disinterested Third Party" - An external entity or individual not directly associated with the loan but involved in related transactions, such as vendors or service providers.
    "Interested Third Party" - Entities or individuals, excluding the borrower and lender, involved in aspects of the loan process, such as legal representatives, insurance providers, or inspectors.
    "Loan Audit" - Examination and verification of loan records, transactions, and documentation to ensure accuracy and compliance with established standards.
    "Loan Search" - The process of locating and retrieving specific information related to a loan within a database or records system.
    "Loan Summary" - A concise overview outlining key details and terms of a loan, providing a snapshot of its essential information.
    "Servicing Acquisition" - Taking over servicing of an existing loan from another servicer. Sometimes called a Servicing Transfer, the can mean either an inbound loan or an outbound loan.
    "Service Release" - Transfer out of an existing loan to a new servicer. Sometimes called a Servicing Transfer, the can mean either an inbound loan or an outbound loan.
    "Servicing Transfer" - An ambiguous term, which can mean either an inbound loan or an outbound loan.
    "Loan Set-up" - The initial "boarding" of a loan to the servicing system, ensuring the accurate and timely notifications to the borrower and the accurate set-up of all internal information required to service the loan. 
    "Deed-in-Lieu" - The process of accepting conveyance of a distressed borrower's secured property in lieu of foreclosing the property.
    "Loan Modification" - The mutual agreement between the lender and the borrower to modify the terms of the loan, typically to mitigate the burden on a distressed borrower and to avoid foreclosure of the property. 
    "Short Sale" - The process of securing the lender's agreement to accept a lower payoff than the outstanding mortgage balance, in order to facilitate a distressed borrower's sale of their home and avoid foreclosure of the property. 
    "Lender Paid Mortgage Insurance" - Managing lender-paid mortgage insurance, including tasks related to the administration and processing of mortgage insurance payments.
    "MI Reporting" - Reporting on Mortgage Insurance, ensuring accurate and timely reporting of mortgage insurance-related information.
    "PMI Removal" - Administering the removal of Private Mortgage Insurance, ensuring compliance with requirements for removing private mortgage insurance.
    "PMI Pool Insurance" - Administering lender-paid private mortgage insurance which insures the guarantor of a mortgage backed security against financial loss.
    "Escrow Refund" - The refund to the borrower of all escrowed funds remaining after the full payoff of the mortgage. 
    "Final Payoff" - Processing the final payment of the full, outstanding loan balance, accrued interest, and fees.
    "Lien Release" - The creation of the proper legal document and its recordation to remove the mortgage lien from a property after the full payoff of the mortgage.
    "Payoff Quote" - Providing quotes for loan payoffs, ensuring accurate and timely provision of payoff quotes for borrowers.
    "Tax Bill" - The tax authority's invoice for the property tax installment(s). This is most commonly received through a "tax service" electronically, but it can also be a physical bill from the tax authority. 
    "Tax Sales" - Managing tasks related to the sale of properties for tax reasons, ensuring compliance with legal and regulatory requirements for tax sales.
    "Additional Special Loan Types" - Distinct loan variations beyond conventional categories, tailored to specific circumstances or borrower needs.
    "ARM Management" - Supervision and administration of loans with interest rates that can fluctuate based on market conditions.
    "Assumption / SII" - Handling processes related to loan assumptions and managing shared equity initiatives.
    "Contested / Litigation Loan" - Administration of loans entangled in legal disputes or litigation proceedings.
    "DSI Loans" - Oversight of loans provided to debtors during bankruptcy, allowing them to maintain control of their assets.
    "FHA 203K Loans" - Management of loans designed for property rehabilitation, combining purchase and renovation costs under an FHA-insured mortgage.
    "HELOCS" - Administration of loans allowing borrowers to draw funds as needed against the equity in their homes.
    "Interest Only Loans" - Handling loans where borrowers pay only the interest for a specified period, delaying principal payments.
    "Natural Disaster Loans" - Management of loans affected by unforeseen natural disasters, addressing associated challenges and adjustments.
    "SCRA" - Compliance with regulations ensuring financial protections for military personnel during active duty.
    "VA Land Boards" - Administration of loans involving Veterans Affairs Land Boards, which may include special considerations for veterans.
    "Variable Rate Loans" - Supervision of loans with interest rates subject to change based on specified financial indices or market conditions.
    "Borrower Tax Reporting" - The accounting required and production of the required 1098 and 1099 reporting to a borrower related to their mortgage loan. Includes the internal updates to enable search and viewing of the produced tax statements. 
    "Internal Financial Reporting" - The accounting and required output or transfer of data to the corporate finance or accounting area.
    "Third Party Reporting" - The accounting required and production of the required 1099 reporting to a third party (vendor) related to mortgage loans. Includes the internal updates to enable search and viewing of the produced tax statements. 


    Output the topics as an array of values. Ensure that the values are delimited by "|||" and that string values are not enclosed in quotes. If no topics from the list are discussed, include only "No relevant topics" in your output. The response should strictly follow the format guidelines and include no extraneous content or formatting.
    """

    llm: LLMPredictorType = Field(description="The LLM to use for generation.")
    prompt_template: str = Field(
        default=DEFAULT_SUBTOPIC_GEN_TMPL,
        description="Prompt template to use when identifying subtopics found in the chunk",
    )

    def __init__(
        self,
        llm: Optional[LLM] = None,
        prompt_template: str = DEFAULT_SUBTOPIC_GEN_TMPL,
        num_workers: int = DEFAULT_NUM_WORKERS,
        **kwargs: Any,
    ):
        """Init parameters."""
        super().__init__(
            llm=llm or Settings.llm,
            prompt_template=prompt_template,
            num_workers=num_workers,
            **kwargs,
        )

    @classmethod
    def class_name(cls) -> str:
        return "SubtopicsExtractor"


    async def _aextract_subtopics_from_node(self, node: BaseNode) -> Dict[str, List[str]]:
        if not isinstance(node, TextNode):
            return {}
        
        try:
            # Extract content from the node
            node.excluded_llm_metadata_keys = ["page_label","file_name","file_path","file_type","file_size","creation_date","last_modified_date"]
            context_str = node.get_content(metadata_mode=MetadataMode.NONE)
            
            # Use the machine learning model to predict data elements
            data_elements = await self.llm.apredict(
                PromptTemplate(template=self.prompt_template),
                context_str=context_str,
            )
            
            # Ensure data_elements is a string and split it into an array of strings
            if isinstance(data_elements, str):
                keywords = [keyword.strip() for keyword in data_elements.split('|||')]
            else:
                keywords = []  # Default to empty list if not a string
            #print(context_str)
            #print("|||||||||")
            #print(keywords)
            return {"found_subtopics": keywords}
        
        except Exception as e:
            # Log the error or handle it appropriately
            print(f"Error processing node: {e}")
            return {}


    async def aextract(self, nodes: Sequence[BaseNode]) -> List[Dict]:
        data_elements_jobs = []
        for node in nodes:
            data_elements_jobs.append(self._aextract_subtopics_from_node(node))

        metadata_list: List[Dict] = await run_jobs(
            data_elements_jobs, show_progress=self.show_progress, workers=self.num_workers
        )
        #print("foo")
        #print(metadata_list)
        return metadata_list