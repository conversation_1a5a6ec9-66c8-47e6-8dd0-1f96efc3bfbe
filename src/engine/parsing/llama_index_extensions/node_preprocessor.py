import tiktoken
import logging
from llama_index.core.schema import TextNode
from llama_index.core.node_parser import SentenceSplitter

# Ensure logging is configured
logging.basicConfig(level=logging.ERROR)

class NodePreprocessor:
    def __init__(self, chunk_size=7000, overlap=500):
        self.chunk_size = chunk_size
        self.overlap = overlap
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        if chunk_size <= overlap:
            raise ValueError("chunk_size must be greater than overlap")

    def split_text(self, node):
        try:
            splitting_parser = SentenceSplitter(chunk_size=self.chunk_size, chunk_overlap=self.overlap)
            chunks = splitting_parser([node])
            return chunks
        except Exception as e:
            logging.error("Error in split_text: %s", e, exc_info=True)
            raise

    def preprocess_nodes(self, nodes):
        logging.info("Preprocessing nodes")
        new_nodes = []
        try:
            for node in nodes:
                content = node.get_content()
                tokens = self.tokenizer.encode(content)
                if len(tokens) > self.chunk_size:
                    chunks = self.split_text(node)
                    for chunk in chunks:
                        new_nodes.append(chunk)
                else:
                    new_nodes.append(node)
            return new_nodes
        except Exception as e:
            logging.error("Error in preprocess_nodes for node %s: %s", node, e, exc_info=True)
            raise
