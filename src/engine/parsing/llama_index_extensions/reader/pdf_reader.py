from pathlib import Path
from typing import List, Dict, Optional, Union
import pypdf
from llama_index.legacy.readers.base import BaseReader
from llama_index.legacy.schema import Document


class PDFReader(BaseReader):
    """PDF parser."""

    def __init__(self, return_full_document: Optional[bool] = False) -> None:
        """
        Initialize PDFReader.
        """
        self.return_full_document = return_full_document

    def load_data(
        self, pdf_data: Union[Path, bytes], extra_info: Optional[Dict] = None
    ) -> List[Document]:
        """Parse PDF data."""
        if isinstance(pdf_data, Path):
            # Open the file if a file path is provided
            with open(pdf_data, "rb") as fp:
                pdf_bytes = fp.read()
        elif isinstance(pdf_data, bytes):
            # Directly use the binary data if provided
            pdf_bytes = pdf_data
        else:
            raise ValueError("Invalid input: pdf_data must be a Path or bytes")

        # Create a PDF object from bytes
        pdf = pypdf.PdfReader(pdf_bytes)

        # Get the number of pages in the PDF document
        num_pages = len(pdf.pages)
        docs = []

        # This block returns a whole PDF as a single Document
        if self.return_full_document:
            text = ""
            metadata = {"file_name": getattr(pdf_data, 'name', 'unknown')}

            for page in range(num_pages):
                # Extract the text from the page
                page_text = pdf.pages[page].extract_text()
                text += page_text

            docs.append(Document(text=text, metadata=metadata))

        # This block returns each page of a PDF as its own Document
        else:
            for page in range(num_pages):
                # Extract the text from the page
                page_text = pdf.pages[page].extract_text()
                page_label = pdf.page_labels[page]

                metadata = {"page_label": page_label, "file_name": getattr(pdf_data, 'name', 'unknown')}
                if extra_info is not None:
                    metadata.update(extra_info)

                docs.append(Document(text=page_text, metadata=metadata))

        return docs
