from llama_index.core import VectorStoreIndex
import os
import logging
from llama_index_extensions.reader.pdf_reader import PDFReader
from pinecone import Pinecone, ServerlessSpec
from openai import OpenAI
from llama_index_extensions.extractor import (
    AuthorizationStatementsExtractor,
    DataElementsExtractor,
    DefinitionStatementsExtractor,
    OpportunityStatementsExtractor,
    ReferenceInformationStatementsExtractor,
    RequiredDiscretionStatementsExtractor,
    RequirementStatementsExtractor,
    SubtopicsExtractor,
    ProcessChangeStatementsExtractor,
    OrganizationalChangeStatementsExtractor
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.vector_stores.pinecone import PineconeVectorStore
from llama_index.core.ingestion import IngestionPipeline


class LlamaIndexHelper:
    def __init__(self):
        # Initialize any necessary components of the llama index framework
        pass

    def process_document(self, document_id, path, index_name):
        logging.info(
            f"Starting processing with Llama Index for document {document_id}.")
        embedding_model_name = "text-embedding-3-large"
        pc = Pinecone(os.environ["PINECONE_API_KEY"])
        index_list_response = pc.list_indexes()
        index_names = [index_info["name"]
                       for index_info in index_list_response.get("indexes", [])]
        if index_name not in index_names:
            pc.create_index(
                name=index_name,
                dimension=3072,
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-east-2"),
            )
        pinecone_index = pc.Index(index_name)

        pdf_reader = PDFReader()
        with open(path, "rb") as file:
            pdf_bytes = file.read()
        documents = pdf_reader.load_data(pdf_bytes)

        llm = OpenAI(temperature=0.1,
                     model="gpt-4-turbo-preview", max_tokens=512)
        vector_store = PineconeVectorStore(pinecone_index=pinecone_index)
        pipeline = IngestionPipeline(
            transformations = [
                RequirementStatementsExtractor(llm=llm),
                SubtopicsExtractor(llm=llm),
                OpportunityStatementsExtractor(llm=llm),
                AuthorizationStatementsExtractor(llm=llm),
                ProcessChangeStatementsExtractor(llm=llm),
                OrganizationalChangeStatementsExtractor(llm=llm),
                RequiredDiscretionStatementsExtractor(llm=llm),
                ReferenceInformationStatementsExtractor(llm=llm),
                OpenAIEmbedding(),
            ],
            vector_store=vector_store,
        )
        pipeline.run(documents=documents)

        # Here you would include the actual interaction with the llama index framework
        logging.info(
            f"Completed processing document {document_id} with Llama Index.")
        return {"document_id": document_id, "status": "processed"}
