"""
This module provides the TransformEngine class, which implements the IPdfTransformer interface.
The TransformEngine class supports transforming PDF documents into various output formats,
including cropped image and Excel formats, based on specific document structure and metadata.

Classes:
    - TransformEngine: Handles transformations for PDFs, including rendering to a cropped
      image based on bounding box data and exporting structured data to Excel format.
"""

import logging
import os
import re
import tempfile
import uuid
from typing import Dict, List

import fitz
import pypandoc

from accessor.image.image_accessor import ImageAccessor
from accessor.image.interface.i_image_accessor import IImageAccessor
from engine.transformer.dto import ImageData, TransformConfig
from engine.transformer.excel.excel_formatter import ExcelFormatter
from engine.transformer.interface.i_pdf_transformer import IPdfTransformer
from engine.transformer.pdf import PdfTransformer

LUA_FILTER_LOG_MSG = "Using Lua filter: %s"


class TransformEngine(IPdfTransformer):
    """
    A transformer engine that provides methods to render and transform PDF documents into
    specified formats, such as cropped images and Excel files.

    Methods:
        - transform_to_excel: Converts structured data into an Excel format with headers and tables.
        - transform_pdf_to_cropped_image: Transforms a PDF document into a combined, cropped image
          based on bounding box data from multiple pages, with options for high-resolution
          rendering.
    """

    def __init__(self, logger_name: str = "TransformEngineLogger"):
        self._pdf_transformer: IPdfTransformer = PdfTransformer(logger_name)
        self._image_accessor: IImageAccessor = ImageAccessor(logger_name)
        self.logger = logging.getLogger(logger_name)

    @staticmethod
    def transform_to_excel(
        export_type: str, header_data: Dict[str, str], table_data: List[Dict[str, str]]
    ) -> bytes:
        """
        Transforms structured data into an Excel file format with headers and table rows.

        This method takes in structured data with specified headers and rows, formats it into
        an Excel file, and returns the file as a bytes object. It uses the ExcelFormatter to
        handle the actual data transformation based on the specified export type.

        Parameters:
            - export_type (str): The type or category of export, which defines specific formatting
              options for the Excel file.
            - header_data (Dict[str, str]): A dictionary containing header information for
              the Excel file. Each key-value pair represents a column name and its
              associated value for the header.
            - table_data (List[Dict[str, str]]): A list of dictionaries representing the rows of
              table data. Each dictionary corresponds to a row, with keys matching the headers
              and values as cell data.

        Returns:
            - bytes: The generated Excel file content in bytes format, ready
              for saving or streaming.

        Raises:
            - ValueError: If an error occurs during the Excel transformation process.

        Example:
            >>> transformer = TransformEngine()
            >>> excel_bytes = transformer.transform_to_excel(
                    "default", {"Name": "Value"}, [{"Name": "Alice"}, {"Name": "Bob"}]
                )
            >>> with open("output.xlsx", "wb") as f:
            >>>     f.write(excel_bytes)
        """
        try:
            return ExcelFormatter.transform_data_to_excel(export_type, header_data, table_data)
        except Exception as e:
            raise ValueError(f"An error occurred during Excel transformation: {e}")

    def transform_pdf_to_cropped_image(
        self, doc: fitz.Document, document_blocks: List[dict], config: TransformConfig
    ) -> ImageData:
        return self._pdf_transformer.transform_pdf_to_cropped_image(
            doc=doc, document_blocks=document_blocks, config=config
        )

    def transform_docx_to_markdown(self, docx_bytes: bytes, document_id: int) -> str:
        """
        Converts DOCX bytes to Markdown and uploads all extracted media via the image accessor.

        Parameters:
            docx_bytes (bytes): The raw bytes of the DOCX document.
            document_id (int): The document ID used in constructing storage keys.

        Returns:
            str: The resulting Markdown text with media references
            replaced by image id placeholders.
        """
        self.logger.info("Starting DOCX to Markdown conversion for document_id: %d", document_id)
        self.logger.debug("Input DOCX size: %d bytes", len(docx_bytes))

        with tempfile.TemporaryDirectory() as tmp_dir:
            self.logger.debug("Created temporary directory: %s", tmp_dir)

            unique_id = uuid.uuid4().hex
            tmp_docx_path = os.path.join(tmp_dir, f"input_{document_id}_{unique_id}.docx")
            self.logger.debug("Temporary DOCX path: %s", tmp_docx_path)

            with open(tmp_docx_path, "wb") as docx_file:
                docx_file.write(docx_bytes)
            self.logger.debug("Wrote DOCX bytes to temporary file")

            extraction_dir = os.path.join(tmp_dir, "extracted_media")
            os.makedirs(extraction_dir, exist_ok=True)
            self.logger.debug("Created media extraction directory: %s", extraction_dir)

            lua_filter_path = os.path.join(
                os.path.dirname(__file__),
                "filters",
                "conditional-image-filter.lua",
            )
            self.logger.debug(LUA_FILTER_LOG_MSG, lua_filter_path)

            extra_args = [
                f"--extract-media={extraction_dir}",
                "--wrap=none",
                f"--lua-filter={lua_filter_path}",
            ]
            self.logger.debug("Pandoc conversion arguments: %s", extra_args)

            try:
                markdown_text = pypandoc.convert_file(
                    tmp_docx_path,
                    to="markdown-raw_html+grid_tables+multiline_tables+simple_tables",
                    format="docx",
                    extra_args=extra_args,
                )
                self.logger.debug(
                    "Initial markdown conversion successful. Text length: %d", len(markdown_text)
                )
            except Exception as e:
                self.logger.error("Pandoc conversion failed: %s", str(e), exc_info=True)
                raise

            # Check extraction directory contents
            self.logger.debug("Checking extraction directory contents")
            extraction_dir_contents = list(os.scandir(extraction_dir))
            self.logger.debug(
                "Extraction directory contents: %s",
                [entry.name for entry in extraction_dir_contents],
            )

            # Sometimes Pandoc places images in a nested "media" folder
            if not any(extraction_dir_contents):
                nested_dir = os.path.join(extraction_dir, "media")
                self.logger.debug("Empty extraction dir, checking nested media dir: %s", nested_dir)

                if os.path.isdir(nested_dir) and any(os.scandir(nested_dir)):
                    self.logger.info("Found nested media directory, updating extraction_dir")
                    extraction_dir = nested_dir
                    self.logger.debug("New extraction_dir: %s", extraction_dir)

            # Walk the extracted directory and process images
            extracted_mapping = {}
            self.logger.info("Starting to process extracted media files")

            for root, dirs, files in os.walk(extraction_dir):
                self.logger.debug("Walking directory: %s", root)
                self.logger.debug("Found subdirectories: %s", dirs)
                self.logger.debug("Found files: %s", files)

                for filename in files:
                    abs_file_path = os.path.join(root, filename)
                    rel_path = os.path.relpath(abs_file_path, extraction_dir).replace("\\", "/")
                    storage_key = f"images/document/{document_id}/{rel_path}".replace("\\", "/")

                    self.logger.debug("Processing file: %s", filename)
                    self.logger.debug("Absolute path: %s", abs_file_path)
                    self.logger.debug("Relative path: %s", rel_path)
                    self.logger.debug("Storage key: %s", storage_key)

                    try:
                        with open(abs_file_path, "rb") as f:
                            file_content = f.read()
                        self.logger.debug("Read file content, size: %d bytes", len(file_content))

                        image_dto = self._image_accessor.upload_image(
                            file_content,
                            file_name=storage_key,
                            metadata={},
                        )
                        extracted_mapping[rel_path] = str(image_dto.image_id)
                        self.logger.debug(
                            "Uploaded image. Path %s -> ID %s", rel_path, image_dto.image_id
                        )
                    except Exception as e:
                        self.logger.error(
                            "Failed to process file %s: %s", abs_file_path, str(e), exc_info=True
                        )
                        raise

            # Process markdown text with image replacements
            if extracted_mapping:
                self.logger.info("Starting image path replacement in markdown")
                self.logger.debug("Image mapping: %s", extracted_mapping)

                image_pattern = re.compile(r"(!\[[^\]]*\]\()([^)]*)(\))")

                def replace_image_path(match):
                    prefix, img_path, suffix = match.groups()
                    norm_path = os.path.normpath(img_path).replace("\\", "/")
                    self.logger.debug("Processing image reference: %s", img_path)
                    self.logger.debug("Normalized path: %s", norm_path)

                    # If absolute, check if it's under extraction_dir
                    if os.path.isabs(norm_path):
                        try:
                            rel_inside = os.path.relpath(norm_path, extraction_dir).replace(
                                "\\", "/"
                            )
                            self.logger.debug(
                                "Absolute path detected, relative path: %s", rel_inside
                            )
                            if not rel_inside.startswith("..") and rel_inside in extracted_mapping:
                                result = prefix + extracted_mapping[rel_inside] + suffix
                                self.logger.debug("Replaced with: %s", result)
                                return result
                        except ValueError as e:
                            self.logger.warning("Failed to process absolute path: %s", str(e))

                    # If relative, check mapping directly
                    if norm_path in extracted_mapping:
                        result = prefix + extracted_mapping[norm_path] + suffix
                        self.logger.debug("Replaced relative path with: %s", result)
                        return result

                    self.logger.warning("No replacement found for path: %s", img_path)
                    return match.group(0)

                markdown_text = image_pattern.sub(replace_image_path, markdown_text)
                self.logger.debug("Final markdown text length: %d", len(markdown_text))

            self.logger.info("DOCX to Markdown conversion completed successfully")
            return markdown_text

    def transform_markdown_to_docx(self, markdown_text: str) -> bytes:
        """
        Converts Markdown with numeric image IDs to a DOCX, retrieving and embedding
        each image from the image service exactly once per ID.
        """
        self.logger.info("Starting Markdown to DOCX conversion")
        self.logger.debug("Input markdown length: %d characters", len(markdown_text))

        # Regex to capture Markdown image references like ![alt text](1234).
        image_pattern = re.compile(r"(!\[[^\]]*\]\()(\d+)(\))")
        self.logger.debug("Using image pattern: %s", image_pattern.pattern)

        id_to_image_info = {}

        def replace_id_with_filename(match):
            """
            Whenever we find a numeric ID, retrieve (or reuse) image info,
            and produce a reference like (media/filename.png).
            """
            prefix, image_id_str, suffix = match.groups()
            image_id = int(image_id_str)
            self.logger.debug("Processing image ID: %d", image_id)

            # If we've already retrieved this image ID, just reuse the same file name.
            if image_id in id_to_image_info:
                file_name, _content = id_to_image_info[image_id]
                self.logger.debug("Reusing cached image info for ID %d: %s", image_id, file_name)
                return f"{prefix}media/{file_name}{suffix}"

            # Otherwise, fetch from image_accessor exactly ONCE
            self.logger.debug("Fetching new image content for ID: %d", image_id)
            image_content_dto = self._image_accessor.get_image_content(image_id)
            file_name = image_content_dto.file_name
            content = image_content_dto.content
            self.logger.debug("Retrieved image %s, size: %d bytes", file_name, len(content))

            # Store it so we don't fetch again
            id_to_image_info[image_id] = (file_name, content)
            self.logger.debug("Cached image info for ID %d", image_id)

            # Return updated reference in the Markdown
            return f"{prefix}media/{file_name}{suffix}"

        self.logger.info("Starting image reference replacement")
        updated_markdown = image_pattern.sub(replace_id_with_filename, markdown_text)
        self.logger.debug("Processed %d unique images", len(id_to_image_info))

        with tempfile.TemporaryDirectory() as tmp_dir:
            self.logger.debug("Created temporary directory: %s", tmp_dir)

            md_file_path = os.path.join(tmp_dir, "converted.md")
            self.logger.debug("Writing markdown to: %s", md_file_path)
            with open(md_file_path, "w", encoding="utf-8") as f:
                f.write(updated_markdown)

            local_media_dir = os.path.join(tmp_dir, "media")
            os.makedirs(local_media_dir, exist_ok=True)
            self.logger.debug("Created media directory: %s", local_media_dir)

            self.logger.info("Writing %d images to media directory", len(id_to_image_info))
            for image_id, (file_name, content) in id_to_image_info.items():
                abs_file_path = os.path.join(local_media_dir, file_name)
                os.makedirs(os.path.dirname(abs_file_path), exist_ok=True)
                self.logger.debug("Writing image ID %d to: %s", image_id, abs_file_path)
                with open(abs_file_path, "wb") as img_file:
                    img_file.write(content)

            unique_id = uuid.uuid4().hex
            docx_file_path = os.path.join(tmp_dir, f"output_{unique_id}.docx")
            self.logger.debug("Output DOCX path: %s", docx_file_path)

            lua_filter_path = os.path.join(
                os.path.dirname(__file__),
                "filters",
                "conditional-image-filter.lua",
            )
            self.logger.debug(LUA_FILTER_LOG_MSG, lua_filter_path)

            extra_args = [
                f"--resource-path={tmp_dir}",
                f"--lua-filter={lua_filter_path}",
            ]
            self.logger.debug("Pandoc conversion arguments: %s", extra_args)

            try:
                self.logger.info("Starting pandoc conversion")
                pypandoc.convert_file(
                    md_file_path,
                    to="docx",
                    format="markdown-raw_html+grid_tables+multiline_tables+simple_tables",
                    outputfile=docx_file_path,
                    extra_args=extra_args,
                )
                self.logger.debug("Pandoc conversion completed successfully")

                self.logger.debug("Reading generated DOCX file: %s", docx_file_path)
                with open(docx_file_path, "rb") as docx_file:
                    docx_bytes = docx_file.read()
                self.logger.debug("Read DOCX file, size: %d bytes", len(docx_bytes))

            except Exception as e:
                self.logger.error("Pandoc conversion failed: %s", str(e), exc_info=True)
                raise

        self.logger.info("Markdown to DOCX conversion completed successfully")
        return docx_bytes

    def transform_docx_to_pdf(self, docx_bytes: bytes) -> bytes:
        """
        Convert a DOCX byte stream to a PDF byte stream.

        Parameters:
            docx_bytes (bytes): The input DOCX file as bytes.

        Returns:
            bytes: The converted PDF file as bytes.
        """
        self.logger.info("Starting DOCX to PDF conversion")
        self.logger.debug("Input DOCX size: %d bytes", len(docx_bytes))
        try:
            with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as docx_file:
                docx_file.write(docx_bytes)
                docx_file_path = docx_file.name
                self.logger.debug("Temporary DOCX file created: %s", docx_file_path)

            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as pdf_file:
                pdf_file_path = pdf_file.name
                self.logger.debug("Temporary PDF file path: %s", pdf_file_path)

            try:
                lua_filter_path = os.path.join(
                    os.path.dirname(__file__),
                    "filters",
                    "pdf-filters.lua",
                )
                self.logger.debug(LUA_FILTER_LOG_MSG, lua_filter_path)

                extra_args = ["--pdf-engine=lualatex", f"--lua-filter={lua_filter_path}"]
                extra_args.extend(
                    [
                        "-V",
                        "mainfont=Latin Modern Roman",
                        "-V",
                        "header-includes=\\usepackage{enumitem}",
                        "-V",
                        "header-includes=\\setlistdepth{9}",
                        "-V",
                        "header-includes=\\renewlist{itemize}{itemize}{9}",
                        "-V",
                        "header-includes=\\renewlist{enumerate}{enumerate}{9}",
                        "-V",
                        "header-includes=\\setlist[itemize]{label=\\textbullet}",
                        "-V",
                        "header-includes=\\setlist[enumerate]{label=\\arabic*.}",
                        "-V",
                        "header-includes=\\usepackage{xcolor}",
                        "-V",
                        "colorlinks:true",
                        "-V",
                        "linkcolor:blue",
                    ]
                )
                self.logger.debug("Pandoc extra arguments: %s", extra_args)

                self.logger.info("Starting pandoc conversion")
                pypandoc.convert_file(
                    docx_file_path, to="pdf", outputfile=pdf_file_path, extra_args=extra_args
                )
                self.logger.info("Pandoc conversion completed successfully")

                self.logger.debug("Reading generated PDF file: %s", pdf_file_path)
                with open(pdf_file_path, "rb") as f:
                    pdf_bytes = f.read()
                self.logger.debug("Read PDF file, size: %d bytes", len(pdf_bytes))
            finally:
                if os.path.exists(docx_file_path):
                    os.remove(docx_file_path)
                    self.logger.debug("Removed temporary DOCX file: %s", docx_file_path)
                if os.path.exists(pdf_file_path):
                    os.remove(pdf_file_path)
                    self.logger.debug("Removed temporary PDF file: %s", pdf_file_path)
        except Exception as e:
            self.logger.error("DOCX to PDF conversion failed: %s", str(e), exc_info=True)
            raise

        self.logger.info("DOCX to PDF conversion completed successfully")
        return pdf_bytes
