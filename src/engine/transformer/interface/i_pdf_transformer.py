"""
Module: pdf_transformer_interface

This module defines the `IPdfTransformer` interface, an abstract base class for transforming 
PDF documents into cropped images. The interface specifies a method for generating a single, 
combined image from multiple pages in a PDF, based on bounding box data. The resulting image 
can be output in various formats, such as PNG or JPEG.

Classes:
    IPdfTransformer: An abstract base class that defines a method for transforming PDF documents 
    into cropped images according to page-specific bounding boxes.

Dependencies:
    - fitz (PyMuPDF): For handling PDF document objects.
    - Block: A TypedDict representing each bounding box entry with page number and bounding box 
      coordinates.
    - ImageData: A data class encapsulating the image format and bytes for the transformed image.
    - ImageFormatEnum: An enumeration specifying supported image formats (e.g., PNG, JPEG).
"""

from abc import ABC, abstractmethod
from typing import List

import fitz  # PyMuPDF

from engine.transformer.dto import ImageData, TransformConfig


class IPdfTransformer(ABC):
    """
    Interface for transforming PDF documents into various output formats.

    This abstract base class defines a method for transforming a PDF document and a collection
    of bounding box data into a single cropped image, supporting multiple output formats such
    as PNG and JPEG. Implementations of this interface handle the specifics of cropping and
    combining images from PDF pages based on bounding box information.
    """

    @abstractmethod
    def transform_pdf_to_cropped_image(
        self, doc: fitz.Document, document_blocks: List[dict], config: TransformConfig
    ) -> ImageData:
        """
        Transforms a PDF document into a single cropped image based on bounding boxes
        from multiple pages.

        This method takes a PDF document and a collection of bounding boxes, then generates
        a combined image where each page is cropped according to the bounding box specifications.
        The image can be returned in different formats, such as PNG or JPEG.

        Parameters:
        - doc (fitz.Document): A PyMuPDF Document object representing the PDF to transform.
        - document_blocks (List[Dict[str, float]]): A list of bounding box dictionaries for each
          page. Each dictionary contains page-specific cropping details, including:
            - 'page_number' (int): Page index within the document.
            - 'bbox' (List[float]): Bounding box coordinates [x_min, y_min, x_max, y_max].
        - image_format (ImageFormatEnum): The desired image format for the output (e.g., PNG, JPEG).
          Default is ImageFormatEnum.PNG.

        Returns:
        - ImageData: An object containing the image format and the bytes of the cropped image.

        Raises:
        - NotImplementedError: Indicates that this method is abstract and requires implementation
          in a subclass.
        """
