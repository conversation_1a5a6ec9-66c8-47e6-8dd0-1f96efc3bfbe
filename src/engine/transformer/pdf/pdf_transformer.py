"""
This module defines the PdfTransformer class, which is responsible for transforming
PDF documents into cropped images based on bounding box data. It provides methods 
to process PDF pages, crop based on bounding boxes, and combine the resulting images 
vertically to generate a single output image.

Classes:
- PdfTransformer: Transforms PDF documents into cropped images by processing bounding 
  box data, allowing output in specified image formats and DPI settings.

Dependencies:
- fitz (PyMuPDF): For handling PDF document objects.
- Image (PIL): For creating and manipulating image objects.
- ImageData: A data class encapsulating the image format and bytes for the transformed image.
- TransformConfig: A data class encapsulating transformation settings, including image format, 
  scaling factor, and DPI.
- ImageFormatEnum: An enumeration specifying supported image formats (e.g., PNG, JPEG).
"""

import io
import logging
from typing import List, Tuple

import fitz  # PyMuPDF
from PIL import Image

from engine.transformer.dto import ImageData, TransformConfig
from engine.transformer.enum.image_format_enum import ImageFormatEnum
from engine.transformer.interface.i_pdf_transformer import IPdfTransformer


class PdfTransformer(IPdfTransformer):
    """
    A transformer class for converting PDF documents into cropped images.

    The PdfTransformer class provides functionality to process PDF pages using
    bounding box data, crop sections of pages, and combine these into a single
    vertically stacked image. Configurable settings such as output format, scaling
    factor, and DPI are managed through the TransformConfig object, allowing flexible
    output options.

    Attributes:
        logger (logging.Logger): Logger instance for logging messages within the transformer.

    Methods:
        transform_pdf_to_cropped_image(doc, document_blocks, config):
            Transforms a PDF document into a single cropped image using bounding boxes
            from multiple pages, based on settings in the provided TransformConfig.
        _calculate_crop_bounds(page_number, first_page, last_page, min_y, max_y, img_height):
            Calculates the top and bottom crop bounds for a page based on bounding box data.
        _combine_images_vertically(page_images):
            Combines a list of cropped images vertically into a single output image.
        _save_image_with_dpi(image, output_buffer, img_format, dpi):
            Saves an image to a buffer with the specified DPI if supported by the format.
    """

    def __init__(self, logger_name: str):
        self.logger = logging.getLogger(logger_name)

    def transform_pdf_to_cropped_image(
        self, doc: fitz.Document, document_blocks: List[dict], config: TransformConfig
    ) -> ImageData:
        """
        Transforms a PDF document into a single cropped image based on bounding boxes
        from multiple fragments, preserving indents and formatting.

        Args:
            doc (fitz.Document): The PDF document to process.
            document_blocks (List[Dict[str, object]]): List of blocks, each containing "page_number"
                and "bbox" (bounding box with four values).
            config (TransformConfig): Configuration object with settings for transformation,
                including:
                - image_format (ImageFormatEnum): Desired output image format.
                - scaling_factor (float): Scaling factor for image resolution, must be positive.
                - dpi (Tuple[int, int]): DPI settings for output image, must be positive integers.

        Returns:
            ImageData: Object containing the final image format and bytes.

        Raises:
            ValueError: If any bbox is missing or incorrectly formatted, or if invalid parameters
            are given.
        """
        self._validate_config(config)

        pages_in_fragment = {}
        for block in document_blocks:
            page_number = block.get("page_number", 0) - 1
            bbox = block.get("bbox")
            if bbox is None or len(bbox) != 4:
                raise ValueError("Each bounding box must be a list of exactly four values.")
            if page_number not in pages_in_fragment:
                pages_in_fragment[page_number] = []
            pages_in_fragment[page_number].append(bbox)
        page_images = []

        for page_number in sorted(pages_in_fragment.keys()):
            page = doc.load_page(page_number)
            mat = fitz.Matrix(config.scaling_factor, config.scaling_factor).prerotate(page.rotation)
            page_rect = fitz.Rect()
            for bbox in pages_in_fragment[page_number]:
                rect = fitz.Rect(bbox)
                page_rect.include_rect(rect)
            device_rect = page_rect.transform(mat)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            img_bytes = pix.tobytes(config.image_format.value.lower())
            full_image = Image.open(io.BytesIO(img_bytes))
            crop_box = (
                max(0, int(device_rect.x0)),
                max(0, int(device_rect.y0)),
                min(full_image.width, int(device_rect.x1)),
                min(full_image.height, int(device_rect.y1)),
            )
            cropped_image = full_image.crop(crop_box)
            page_images.append(cropped_image)

            self.logger.debug(
                "Processed page %d with crop box: %s",
                page_number + 1,
                crop_box,
            )

        combined_image = self._combine_images_vertically(page_images)
        output_buffer = io.BytesIO()
        self._save_image_with_dpi(combined_image, output_buffer, config.image_format, config.dpi)
        output_buffer.seek(0)

        return ImageData(image_format=config.image_format, image_bytes=output_buffer.getvalue())

    def _validate_config(self, config):
        """
        Validates the given configuration for the PDF transformer.

        Args:
            config (Config): The configuration object to validate.
            It must have the attributes:
                - scaling_factor (float): The scaling factor for the
                  transformation. Must be greater than zero.
                - dpi (tuple): A tuple containing two positive integers
                  representing the DPI (dots per inch) values.
        Raises:
            ValueError: If the scaling factor is not greater than zero
            or if any of the DPI values are not positive integers.
        """
        if config.scaling_factor <= 0:
            raise ValueError("Scaling factor must be greater than zero.")
        if config.dpi[0] <= 0 or config.dpi[1] <= 0:
            raise ValueError("DPI values must be positive integers.")

    def _combine_images_vertically(self, page_images: List[Image.Image]) -> Image.Image:
        """Combine a list of images vertically into one single image."""
        total_height = sum(img.height for img in page_images)
        max_width = max(img.width for img in page_images)
        combined_image = Image.new("RGB", (max_width, total_height), color=(255, 255, 255))

        y_offset = 0
        for img in page_images:
            combined_image.paste(img, (0, y_offset))
            y_offset += img.height

        self.logger.debug("Combined image dimensions: width=%d, height=%d", max_width, total_height)

        return combined_image

    def _save_image_with_dpi(
        self,
        image: Image.Image,
        output_buffer: io.BytesIO,
        img_format: ImageFormatEnum,
        dpi: Tuple[int, int],
    ):
        """Save an image to a buffer with specified DPI, if supported by the format."""
        try:
            if img_format is ImageFormatEnum.PNG:
                image.save(output_buffer, format=img_format.value.lower(), dpi=dpi)
            else:
                image.save(output_buffer, format=img_format.value.lower())
        except ValueError as e:
            self.logger.exception("Error saving image with DPI: %s", e)
            raise
