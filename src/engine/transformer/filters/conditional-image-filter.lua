-- List of common image file extensions (add more if needed)
local image_extensions = {
  ".png", ".jpg", ".jpeg", ".gif", ".bmp", ".svg", ".webp", ".tif", ".tiff", ".emf"
}

-- Function to check if a path ends with any known image extension
local function has_image_extension(path)
  local lower_path = path:lower()
  for _, ext in ipairs(image_extensions) do
    if lower_path:match(ext .. "$") then
      return true
    end
  end
  return false
end

local function inlines_to_string(inlines)
  local chunks = {}
  for i, inline in ipairs(inlines) do
    if inline.t == "Str" then
      table.insert(chunks, inline.text)
    elseif inline.t == "Space" then
      table.insert(chunks, " ")
    else
      local fallback = pandoc.utils.stringify(inline)
      table.insert(chunks, fallback)
    end
  end
  return table.concat(chunks)
end

-- Utility: create inlines from a single string
local function string_to_inlines(str)
  return { pandoc.Str(str) }
end

function Image(el)
  -- If the source ends with ".shtml", treat it as a link
  if el.src:match("%.shtml$") then
    local raw_caption = inlines_to_string(el.caption)
    raw_caption = raw_caption:gsub("^%s*!", ""):gsub("^%s+", "")
    local fixed_caption = string_to_inlines(raw_caption)
    return pandoc.Link(fixed_caption, el.src)
  end

  -- If the src does NOT match a known image extension, or if it starts with a UNC/file link, treat it as a link
  if (not has_image_extension(el.src)) or el.src:match("^file://") then
    local raw_caption = inlines_to_string(el.caption)
    raw_caption = raw_caption:gsub("^%s*!", ""):gsub("^%s+", "")
    local fixed_caption = string_to_inlines(raw_caption)
    return pandoc.Link(fixed_caption, el.src)
  end

  -- Otherwise, leave genuine images alone
  return el
end
