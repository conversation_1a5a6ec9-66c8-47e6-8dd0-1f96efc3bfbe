import logging
import io
import pandas as pd
import xlsxwriter
from typing import Dict, List


class ExcelFormatter:
    @staticmethod
    def get_config(export_type: str) -> dict:
        """
        Return the configuration based on the export type.
        """
        configs = {
            "unique_statement_excel": {
                "column_widths": {
                    "Requirement ID": 25,
                    "Topic": 15,
                    "Subtopic": 30,
                    "Primary Subtopic": 15,
                    "Description": 50,
                    "Rating": 15,
                    "Source Identifier": 25,
                    "Source Statement": 50,
                },
                "sheet_name": "BurstAI Requirements",
                "header_text": "Phoenix Burst Requirements Export",
            },
            "requirements_bundle_excel": {
                "column_widths": {
                    "Requirement ID": 25,
                    "User Story ID": 25,
                    "Acceptance Criteria ID": 25,
                    "Test Case ID": 25,
                    "Topic": 15,
                    "Subtopic": 30,
                    "Primary Subtopic": 15,
                    "Artifact Type": 20,
                    "Description": 50,
                    "Rating": 15,
                },
                "sheet_name": "Requirements Bundle",
                "header_text": "Phoenix Requirements Bundle Export",
            },
            "requirements_explorer_excel": {
                "column_widths": {
                    "Document Name": 25,
                    "Business": 25,
                    "Domain": 25,
                    "Statement Type": 25,
                    "Statement ID": 25,
                    "Topic": 15,
                    "Subtopic": 30,
                    "Primary Subtopic": 15,
                    "Description": 50,
                    "Rating": 15,
                },
                "sheet_name": "Requirements Explorer",
                "header_text": "Phoenix Burst Explorer Global Knowledge Requirements Export",
            },
            "change_statement_curator_excel": {
                "column_widths": {
                    "Statement ID": 25,
                    "Identifier": 25,
                    "Original Text": 30,
                    "Updated Text": 30,
                    "Changed Text": 30,
                    "Change Statement": 30,
                    "Impact": 15,
                    "Rating": 15,
                },
                "sheet_name": "Change Statement Curator",
                "header_text": "Phoenix Burst Change Statement Curator Export",
            },
        }
        return configs.get(
            export_type, {"column_widths": {}, "sheet_name": "Sheet1", "header_text": ""}
        )

    @staticmethod
    def transform_data_to_excel(
        export_type: str, header_data: Dict[str, str], table_data: List[Dict[str, str]]
    ) -> bytes:
        """
        Transform the given DataFrame into an Excel file with additional static columns,
        text, and specified cell formats, then return the bytestream.

        Parameters:
        - export_type (str): The type of export configuration to use.
        - header_data (Dict[str, str]): Dictionary containing key-value pairs for additional header data.
        - table_data (List[Dict[str, str]]): The data to be written to the Excel file.

        Returns:
        - bytes: The bytestream of the generated Excel file.
        """
        config = ExcelFormatter.get_config(export_type)
        column_widths = config["column_widths"]
        sheet_name = config["sheet_name"]
        header_text = config["header_text"]

        data_table_with_data = pd.DataFrame(table_data)

        # Constants for row positions
        HEADER_ROW_INDEX = 1  # Excel is 1-based, but the python library is 0-based
        if header_data:
            COLUMN_HEADER_ROW_INDEX = 8
        else:
            COLUMN_HEADER_ROW_INDEX = 3

        # Transform the DataFrame into an Excel file and return the bytestream
        with io.BytesIO() as output:
            with pd.ExcelWriter(output, engine="xlsxwriter") as writer:
                # Create a blank DataFrame to start with START_DATA_ROW_INDEX (row 8)
                blank_df = pd.DataFrame(columns=data_table_with_data.columns)
                blank_df.to_excel(
                    writer, sheet_name=sheet_name, index=False, startrow=COLUMN_HEADER_ROW_INDEX - 1
                )  # Adjust for 0-indexed rows

                # Get the xlsxwriter workbook and worksheet objects
                workbook = writer.book
                worksheet = writer.sheets[sheet_name]

                # Add header text with blue background and thick border
                ExcelFormatter.add_header_text(
                    workbook, worksheet, header_text, len(data_table_with_data.columns)
                )

                # Add header data with gray background and bold text, and thick borders if header_data is provided
                if header_data:
                    ExcelFormatter.write_header_data(
                        workbook, worksheet, header_data, HEADER_ROW_INDEX
                    )

                wrap_format = workbook.add_format({"text_wrap": True, "border": 2})

                # Write column headers and data to the worksheet
                ExcelFormatter.write_data_to_worksheet(
                    workbook, worksheet, data_table_with_data, COLUMN_HEADER_ROW_INDEX, wrap_format
                )

                # Set the column widths and apply text wrap
                for column, width in column_widths.items():
                    col_idx = data_table_with_data.columns.get_loc(column)
                    worksheet.set_column(col_idx, col_idx, width, wrap_format)

                # Remove horizontal borders for visual separator
                separator_format = workbook.add_format({"border": 0, "bg_color": "white"})
                for col_num in range(len(data_table_with_data.columns)):
                    worksheet.write(COLUMN_HEADER_ROW_INDEX - 2, col_num, "", separator_format)

                # Apply filters to all columns
                worksheet.autofilter(
                    COLUMN_HEADER_ROW_INDEX - 1,
                    0,
                    COLUMN_HEADER_ROW_INDEX - 1 + len(data_table_with_data),
                    len(data_table_with_data.columns) - 1,
                )

            return output.getvalue()

    @staticmethod
    def add_header_text(workbook, worksheet, header_text: str, num_columns: int):
        end_column = xlsxwriter.utility.xl_col_to_name(num_columns - 1)
        header_format = workbook.add_format(
            {
                "bold": True,
                "font_size": 16,
                "align": "center",
                "valign": "vcenter",
                "border": 2,
                "bg_color": "#4F81BD",
                "font_color": "white",
            }
        )
        worksheet.merge_range(f"A1:{end_column}1", header_text, header_format)

    @staticmethod
    def write_header_data(workbook, worksheet, header_data: Dict[str, str], start_row: int):
        """
        Write header data with specified formatting to the worksheet.

        Parameters:
        - workbook: The xlsxwriter Workbook object.
        - worksheet: The xlsxwriter Worksheet object.
        - header_data (Dict[str, str]): Dictionary containing key-value pairs for additional header data.
        - start_row (int): The row index to start writing the header data.
        """
        key_format = workbook.add_format(
            {"bold": True, "bg_color": "#D9D9D9", "border": 2, "align": "left", "valign": "vcenter"}
        )
        value_format = workbook.add_format({"align": "left", "valign": "vcenter", "border": 2})

        row = start_row
        for key, value in header_data.items():
            worksheet.write(row, 0, key, key_format)
            worksheet.merge_range(row, 1, row, 4, value, value_format)
            row += 1

    @staticmethod
    def write_data_to_worksheet(
        workbook, worksheet, data_table_with_data: pd.DataFrame, start_row: int, wrap_format
    ):
        """
        Write data from a DataFrame to the Excel worksheet with specified formatting.

        Parameters:
        - workbook: The xlsxwriter Workbook object.
        - worksheet: The xlsxwriter Worksheet object.
        - data_table_with_data (pd.DataFrame): The DataFrame containing the data to be written.
        - start_row (int): The row index to start writing the data (headers will be written at start_row - 1).
        """
        # Create header cell format
        header_cell_format = workbook.add_format(
            {
                "bold": True,
                "border": 2,
                "align": "center",
                "valign": "vcenter",
                "bg_color": "#D9D9D9",
            }
        )

        # Write column headers to the worksheet
        for col_num, column in enumerate(data_table_with_data.columns):
            worksheet.write(start_row - 1, col_num, column, header_cell_format)

        # Write data rows to the worksheet
        for row_num, row_data in data_table_with_data.iterrows():
            for col_num, cell_data in enumerate(row_data):
                worksheet.write(row_num + start_row, col_num, cell_data, wrap_format)
