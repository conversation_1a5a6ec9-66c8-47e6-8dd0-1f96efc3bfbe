import os
from engine.transformer.excel.excel_formatter import ExcelFormatter

# Example usage from another module
if __name__ == "__main__":

    # Header data for unique_statement_excel
    unique_statement_header_data = {
        "Job Name": "FNMA Servicing Guide",
        "File Name": "fnma_servicing_guide.pdf",
        "Enterprise": "Phoenix Internal USDA",
        "Business": "Servicing",
        "Domain": "FNMA"
    }

    # Table data for unique_statement_excel
    unique_statement_table_data = [
        {
            "Requirement ID": "USDA-REQ-0001",
            "Topic": "Escrow",
            "Subtopic": "Shortage and Surplus Management",
            "Description": "The institution must send a written notice to the borrower..",
            "Rating": "Positive"
        },
        {
            "Requirement ID": "USDA-REQ-0002",
            "Topic": "Escrow",
            "Subtopic": "Shortage and Surplus Management",
            "Description": "The institution must tweet a special message to the borrower..",
            "Rating": "Positive"
        },
        {
            "Requirement ID": "USDA-REQ-0003",
            "Topic": "Escrow",
            "Subtopic": "Shortage and Surplus Management",
            "Description": "The institution must subscribe to the borrower's TikTok channel..",
            "Rating": "Positive"
        }
    ]

    # Header data for requirements_bundle_excel
    requirements_bundle_header_data = {
        "Job Name": "USDA Requirements Bundle",
        "File Name": "usda_requirements_bundle.pdf",
        "Enterprise": "Phoenix Internal USDA",
        "Business": "Origination",
        "Domain": "USDA"
    }

    # Table data for requirements_bundle_excel
    requirements_bundle_table_data = [
        {
            "Requirement ID": "REQ-001",
            "User Story ID": "US-001",
            "Acceptance Criteria ID": "AC-001",
            "Test Case ID": "TC-001",
            "Topic": "Compliance",
            "Subtopic": "Regulations",
            "Artifact Type": "Requirement",
            "Description": "Ensure compliance with all regulations."
        },
        {
            "Requirement ID": "REQ-002",
            "User Story ID": "US-002",
            "Acceptance Criteria ID": "AC-002",
            "Test Case ID": "TC-002",
            "Topic": "Security",
            "Subtopic": "Data Protection",
            "Artifact Type": "User Story",
            "Description": "Implement data protection measures."
        },
        {
            "Requirement ID": "REQ-003",
            "User Story ID": "US-003",
            "Acceptance Criteria ID": "AC-003",
            "Test Case ID": "TC-003",
            "Topic": "Performance",
            "Subtopic": "Speed Optimization",
            "Artifact Type": "Test Case",
            "Description": "Optimize system for speed."
        }
    ]

    # Define the export types
    export_types = ["unique_statement_excel", "requirements_bundle_excel"]

    # Create instances of the ExcelFormatter and generate bytestreams for both configurations
    formatters = [
        ExcelFormatter(export_types[0], unique_statement_header_data, unique_statement_table_data),
        ExcelFormatter(export_types[1], requirements_bundle_header_data, requirements_bundle_table_data)
    ]

    header_data_list = [unique_statement_header_data, requirements_bundle_header_data]

    # Generate and save the Excel files
    for formatter, header_data in zip(formatters, header_data_list):
        bytestream = formatter.transform_data_to_excel()

        original_file_name = header_data['File Name']

        # Replace all periods with underscores
        sanitized_file_name = original_file_name.replace(".", "_").replace("/", "_").replace("\\", "_")

        # Construct the final filename by adding _output.xlsx
        output_file_name = f"{sanitized_file_name}_output.xlsx"

        # Save the file
        with open(output_file_name, 'wb') as f:
            f.write(bytestream)
