"""
This module defines the TransformConfig data class, which holds configuration
settings for PDF transformation operations. It encapsulates settings related
to output format, image resolution, and DPI, providing a single object to
simplify the handling of transformation parameters across the system.

Classes:
    TransformConfig: Configuration settings for PDF transformation tasks.
"""

from dataclasses import dataclass
from typing import <PERSON><PERSON>

from engine.transformer.enum import ImageFormatEnum


@dataclass
class TransformConfig:
    """
    Configuration settings for PDF transformation operations.

    This class is used to store and manage settings related to PDF transformation,
    such as the desired output image format, scaling factor, and DPI. By encapsulating
    these settings in a single object, TransformConfig simplifies the passing of
    parameters to transformation functions.

    Attributes:
        image_format (ImageFormatEnum): The desired output format for the transformed image.
            Defaults to ImageFormatEnum.PNG.
        scaling_factor (float): Scaling factor applied to increase or decrease the image resolution.
            Higher values result in higher resolution images. Defaults to 2.0.
        dpi (Tuple[int, int]): The DPI (dots per inch) setting for the output image,
            affecting image clarity and quality. Defaults to (300, 300).
    """

    image_format: ImageFormatEnum = ImageFormatEnum.PNG
    scaling_factor: float = 2.0
    dpi: Tuple[int, int] = (300, 300)

    def __post_init__(self):
        if self.scaling_factor < 0:
            raise ValueError("scaling_factor must be greater than or equal to 0.")
        if any(d <= 0 for d in self.dpi):
            raise ValueError("All DPI values must be greater than 0.")
