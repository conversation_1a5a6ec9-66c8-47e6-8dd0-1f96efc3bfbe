"""
This module defines the Block TypedDict, which specifies the structure for individual 
bounding box data entries used in PDF transformations.

Classes:
- Block: A TypedDict defining the fields for each block of data, including page number 
  and bounding box coordinates.
"""

from typing import Optional, Tuple, TypedDict


class Block(TypedDict):
    """
    Represents a single block of bounding box data for a specific page in a PDF document.

    This TypedDict is used to define the structure for each block of data involved in
    cropping operations. Each block includes a page number and an optional bounding box
    specifying the region to crop.

    Attributes:
    - page_number (int): The page number in the PDF document where this bounding box is located.
    - bbox (Optional[Tuple[float, float, float, float]]): A tuple defining the bounding box
      coordinates in the format (x_min, y_min, x_max, y_max), where:
        - x_min (float): The leftmost X-coordinate of the bounding box.
        - y_min (float): The topmost Y-coordinate of the bounding box.
        - x_max (float): The rightmost X-coordinate of the bounding box.
        - y_max (float): The bottommost Y-coordinate of the bounding box.
      This attribute is optional and can be None if no bounding box is specified.
    """

    page_number: int
    bbox: Optional[Tuple[float, float, float, float]]
