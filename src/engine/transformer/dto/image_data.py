"""
This module defines the ImageData class, which encapsulates image information, including 
the format and binary data, for consistent handling across transformations and output processes.
"""

from dataclasses import dataclass

from engine.transformer.enum import ImageFormatEnum


@dataclass
class ImageData:
    """
    A data class for encapsulating image format and binary data.

    This class holds information about an image's format and its byte representation, providing
    a unified way to handle images across different parts of an application where format and
    binary data need to be retained together.

    Attributes:
    - image_format (ImageFormatEnum): The format of the image (e.g., PNG, JPEG) as specified
      by the ImageFormatEnum.
    - image_bytes (bytes): The binary data of the image in the specified format.
    """

    image_format: ImageFormatEnum
    image_bytes: bytes

    def __post_init__(self):
        if not self.image_bytes:
            raise ValueError("image_bytes cannot be empty.")
