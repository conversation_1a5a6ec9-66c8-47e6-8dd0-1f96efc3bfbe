"""
This module defines the ImageFormatEnum, an enumeration for specifying 
supported image formats used in PDF transformations. By standardizing 
image format options, this enum enables consistent handling and output 
of image data across various modules that interact with PDF files.

Classes:
- ImageFormatEnum: An enumeration representing the supported output formats 
  for images generated from PDF transformations.
"""

from enum import Enum


class ImageFormatEnum(Enum):
    """
    Enumeration of supported image formats for PDF transformations.

    This enum provides a standardized set of image formats, such as PNG and JPEG,
    which can be used to specify the desired output format for images generated
    from PDF documents. By using this enum, transformations can be requested with
    a clear and consistent format parameter.

    Attributes:
    - PNG (str): PNG format, offering lossless compression for high-quality images.
    - JPEG (str): JPEG format, providing lossy compression with smaller file sizes.
    """

    PNG = "PNG"
    JPEG = "JPEG"
