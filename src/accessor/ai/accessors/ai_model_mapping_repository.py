"""Repository for managing AI model mappings in the database.

This module provides functionality to create, read, update, and delete
AI model mappings that associate friendly names with provider-specific model names.
"""
from typing import List, Optional

from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError

from accessor.ai.interfaces import IAiModelMappingRepository
from accessor.content.exceptions import ContentAccessorException
from shared.models import AiModelMapping


class AiModelMappingRepository(IAiModelMappingRepository):
    """Repository for AI model mappings database operations.
    
    Implements the IAiModelMappingRepository interface to provide CRUD
    operations for AI model mappings in the database.
    """

    def __init__(self, session_factory):
        """Initialize the repository with a session factory.
        
        Args:
            session_factory: Factory function that creates database sessions.
        """
        self.session_factory = session_factory

    def get_all_mappings(self) -> List[AiModelMapping]:
        """Retrieve all AI model mappings from the database.
        
        Returns:
            List[AiModelMapping]: A list of all AI model mappings.
            
        Raises:
            ContentAccessorException: If a database error occurs.
        """
        with self.session_factory() as session:
            try:
                mappings = session.query(AiModelMapping).all()
                return mappings
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching all AI model mappings: {str(e)}") from e

    def get_mapping_by_friendly_name(self, friendly_name: str) -> Optional[AiModelMapping]:
        """Retrieve an AI model mapping by its friendly name.
        
        Args:
            friendly_name (str): The friendly name to search for (case-insensitive).
            
        Returns:
            Optional[AiModelMapping]: The matching AI model mapping or None if not found.
            
        Raises:
            ContentAccessorException: If a database error occurs.
        """
        with self.session_factory() as session:
            try:
                friendly_name = friendly_name.lower()
                mapping = session.query(AiModelMapping).filter(
                    func.lower(AiModelMapping.friendly_name) == friendly_name
                ).first()
                return mapping
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching AI model mapping by friendly name: {str(e)}") from e

    def add_mapping(self, friendly_name: str, provider_model_name: str) -> AiModelMapping:
        """Add a new AI model mapping to the database.
        
        Args:
            friendly_name (str): The friendly name for the model (will be converted to lowercase).
            provider_model_name (str): The provider-specific model name.
            
        Returns:
            AiModelMapping: The newly created AI model mapping.
            
        Raises:
            ContentAccessorException: If a database error occurs.
        """
        with self.session_factory() as session:
            try:
                friendly_name = friendly_name.lower()
                new_mapping = AiModelMapping(
                    friendly_name=friendly_name,
                    provider_model_name=provider_model_name
                )
                session.add(new_mapping)
                session.commit()
                session.refresh(new_mapping)
                return new_mapping
            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error adding AI model mapping: {str(e)}") from e

    def update_mapping(self, friendly_name: str, provider_model_name: str) -> Optional[AiModelMapping]:
        """Update an existing AI model mapping.
        
        Args:
            friendly_name (str): The friendly name of the mapping to update (case-insensitive).
            provider_model_name (str): The new provider-specific model name.
            
        Returns:
            Optional[AiModelMapping]: The updated mapping or None if not found.
            
        Raises:
            ContentAccessorException: If a database error occurs.
        """
        with self.session_factory() as session:
            try:
                friendly_name = friendly_name.lower()
                mapping = session.query(AiModelMapping).filter(
                    func.lower(AiModelMapping.friendly_name) == friendly_name
                ).first()
                if mapping:
                    mapping.provider_model_name = provider_model_name
                    session.commit()
                    session.refresh(mapping)
                return mapping
            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating AI model mapping: {str(e)}") from e

    def delete_mapping(self, friendly_name: str) -> bool:
        """Delete an AI model mapping by its friendly name.
        
        Args:
            friendly_name (str): The friendly name of the mapping to delete (case-insensitive).
            
        Returns:
            bool: True if a mapping was deleted, False if no matching mapping was found.
            
        Raises:
            ContentAccessorException: If a database error occurs.
        """
        with self.session_factory() as session:
            try:
                friendly_name = friendly_name.lower()
                mapping = session.query(AiModelMapping).filter(
                    func.lower(AiModelMapping.friendly_name) == friendly_name
                ).first()
                if mapping:
                    session.delete(mapping)
                    session.commit()
                    return True
                return False
            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting AI model mapping: {str(e)}") from e 