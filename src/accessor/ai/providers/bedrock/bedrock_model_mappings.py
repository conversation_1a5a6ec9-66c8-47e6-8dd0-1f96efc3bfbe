"""
Mapping of user-friendly model names to their corresponding AWS Bedrock model identifiers.
This module is separate to make updates easier and keep the accessor code cleaner.
"""

# Mapping from user-friendly model names to Bedrock API model IDs
MODEL_NAME_TO_ID_MAPPING = {
    # Claude 3 Opus
    "claude-3-opus": "us.anthropic.claude-3-opus-20240229-v1:0",
    "claude3opus": "us.anthropic.claude-3-opus-20240229-v1:0",
    "claude3.5opus": "us.anthropic.claude-3-opus-20240229-v1:0",
    "claude-3.5-opus": "us.anthropic.claude-3-opus-20240229-v1:0",
    "opus": "us.anthropic.claude-3-opus-20240229-v1:0",
    
    # Claude 3.7 Sonnet
    "claude-3.7-sonnet": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
    "claude3.7sonnet": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",

    # Claude 3.5 Sonnet v2
    "claude-3.5-sonnet-v2": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
    "claude3.5sonnetv2": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",

    # Claude 3.5 Sonnet
    "claude-3.5-sonnet": "us.anthropic.claude-3-5-sonnet-20240620-v1:0", 
    "claude3.5sonnet": "us.anthropic.claude-3-5-sonnet-20240620-v1:0",

    # Claude 3 Sonnet
    "claude-3-sonnet": "anthropic.claude-3-sonnet-20240229-v1:0",
    "claude3sonnet": "anthropic.claude-3-sonnet-20240229-v1:0", 
    "sonnet": "anthropic.claude-3-sonnet-20240229-v1:0",
    "claude": "anthropic.claude-3-sonnet-20240229-v1:0",  # Default to Sonnet
    
    # Meta Llama 3.2 models
    "llama-3.2-90b": "us.meta.llama3-2-90b-instruct-v1:0",
    "llama3.2-90b": "us.meta.llama3-2-90b-instruct-v1:0",
    "llama3-2-90b": "us.meta.llama3-2-90b-instruct-v1:0",
    "llama-3.2-11b": "us.meta.llama3-2-11b-instruct-v1:0",
    "llama3.2-11b": "us.meta.llama3-2-11b-instruct-v1:0",
    "llama3-2-11b": "us.meta.llama3-2-11b-instruct-v1:0",
    "llama-3.2-3b": "us.meta.llama3-2-3b-instruct-v1:0",
    "llama3.2-3b": "us.meta.llama3-2-3b-instruct-v1:0",
    "llama3-2-3b": "us.meta.llama3-2-3b-instruct-v1:0",
    "llama-3.2-1b": "us.meta.llama3-2-1b-instruct-v1:0",
    "llama3.2-1b": "us.meta.llama3-2-1b-instruct-v1:0",
    "llama3-2-1b": "us.meta.llama3-2-1b-instruct-v1:0",
    
    # Meta Llama 3.3 models
    "llama-3.3-70b": "us.meta.llama3-3-70b-instruct-v1:0",
    "llama3.3-70b": "us.meta.llama3-3-70b-instruct-v1:0",
    "llama3-3-70b": "us.meta.llama3-3-70b-instruct-v1:0",

    # Deepseek R1
    "deepseek": "us.deepseek.r1-v1:0",
    "deepseek-r1": "us.deepseek.r1-v1:0",
    "deepseekr1": "us.deepseek.r1-v1:0",
} 