"""
Configuration file for AWS Bedrock models.
Contains token limits, valid parameters, and other model-specific configurations.
This centralized approach makes it easier to update model configurations as AWS adds new models.
"""

# Model token limits (output token capacities)
# These are verified values from AWS Bedrock
MODEL_TOKEN_LIMITS = {
    "anthropic.claude-3-sonnet-20240229-v1:0": 4096,
    "us.meta.llama3-2-11b-instruct-v1:0": 8192,
    "us.meta.llama3-2-90b-instruct-v1:0": 8192,
    "us.meta.llama3-3-70b-instruct-v1:0": 8192,
    "us.anthropic.claude-3-5-sonnet-20240620-v1:0": 8192,
    "us.anthropic.claude-3-5-sonnet-20241022-v2:0": 8192,
    "us.anthropic.claude-3-7-sonnet-20250219-v1:0": 131072,
    "us.anthropic.claude-3-opus-20240229-v1:0": 4096,
    "us.meta.llama3-2-1b-instruct-v1:0": 8192,
    "us.meta.llama3-2-3b-instruct-v1:0": 8192,
    "amazon.nova-lite-v1:0": 10000,
    "amazon.nova-pro-v1:0": 10000,
    "bedrock/us.meta.llama4-maverick-17b-instruct-v1:0": 8192,
    "bedrock/us.meta.llama4-scout-17b-instruct-v1:0": 8192,
    "us.deepseek.r1-v1:0": 32768,
}

# Valid parameters for each model family
MODEL_VALID_PARAMETERS = {
    "anthropic.claude": {
        "temperature": "Float (0-1)",
        "top_p": "Float (0-1)",
        "top_k": "Integer", 
        "max_tokens": "Integer",
        "stop_sequences": "List of strings"
    },
    "meta.llama": {
        "temperature": "Float (0-1)",
        "top_p": "Float (0-1)",
        "max_gen_len": "Integer"
    },
    "amazon.titan": {
        "temperature": "Float (0-1)",
        "top_p": "Float (0-1)",
        "max_token_count": "Integer",
        "stop_sequences": "List of strings"
    },
    "amazon.nova": {
        "temperature": "Float (0-1)",
        "top_p": "Float (0-1)",
        "max_tokens": "Integer",
        "stop_sequences": "List of strings"
    },
    "cohere.command": {
        "temperature": "Float (0-2)",
        "p": "Float (0-1)",
        "k": "Integer (0-500)",
        "max_tokens": "Integer",
        "stop_sequences": "List of strings"
    },
    "ai21.j2": {
        "temperature": "Float (0-1)",
        "topP": "Float (0-1)",
        "maxTokens": "Integer",
        "stopSequences": "List of strings",
        "countPenalty": {"scale": "Float"},
        "presencePenalty": {"scale": "Float"},
        "frequencyPenalty": {"scale": "Float"}
    },
    "stability.stable-diffusion": {
        "cfg_scale": "Float (0-35)",
        "seed": "Integer (0-4294967295)",
        "steps": "Integer (10-150)",
        "style_preset": "String"
    }
}

# Model capabilities - additional metadata about what each model can do
MODEL_CAPABILITIES = {
    "anthropic.claude-3-sonnet-20240229-v1:0": {
        "supports_function_calling": True,
        "supports_vision": True,
        "supports_streaming": True,
        "context_window": 200000
    },
    "us.anthropic.claude-3-5-sonnet-20240620-v1:0": {
        "supports_function_calling": True,
        "supports_vision": True,
        "supports_streaming": True,
        "context_window": 200000
    },
    "us.anthropic.claude-3-5-sonnet-20241022-v2:0": {
        "supports_function_calling": True,
        "supports_vision": True,
        "supports_streaming": True,
        "context_window": 200000
    },
    "us.anthropic.claude-3-7-sonnet-20250219-v1:0": {
        "supports_function_calling": True,
        "supports_vision": True,
        "supports_streaming": True,
        "context_window": 200000
    },
    "us.meta.llama3-2-11b-instruct-v1:0": {
        "supports_function_calling": False,
        "supports_vision": False,
        "supports_streaming": True,
        "context_window": 8192
    },
    "us.meta.llama3-2-90b-instruct-v1:0": {
        "supports_function_calling": False,
        "supports_vision": False,
        "supports_streaming": True,
        "context_window": 8192
    },
    "us.meta.llama3-3-70b-instruct-v1:0": {
        "supports_function_calling": True,
        "supports_vision": True,
        "supports_streaming": True,
        "context_window": 8192
    }
}

# Default recommended models for different use cases
RECOMMENDED_MODELS = {
    "default": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
    "high_accuracy": "us.anthropic.claude-3-opus-20240229-v1:0",
    "balanced": "us.anthropic.claude-3-5-sonnet-20241022-v2:0",
    "economical": "us.meta.llama3-2-11b-instruct-v1:0",
    "vision": "us.anthropic.claude-3-7-sonnet-20250219-v1:0",
    "long_context": "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
} 