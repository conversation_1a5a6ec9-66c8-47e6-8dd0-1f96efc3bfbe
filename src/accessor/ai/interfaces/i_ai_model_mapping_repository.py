from abc import ABC, abstractmethod
from typing import List, Optional

from shared.models import AiModelMapping


class IAiModelMappingRepository(ABC):

    @abstractmethod
    def get_all_mappings(self) -> List[AiModelMapping]:
        pass

    @abstractmethod
    def get_mapping_by_friendly_name(self, friendly_name: str) -> Optional[AiModelMapping]:
        pass

    @abstractmethod
    def add_mapping(self, friendly_name: str, provider_model_name: str) -> AiModelMapping:
        pass

    @abstractmethod
    def update_mapping(self, friendly_name: str, provider_model_name: str) -> Optional[AiModelMapping]:
        pass

    @abstractmethod
    def delete_mapping(self, friendly_name: str) -> bool:
        pass 