"""
Utilities for working with AWS Bedrock models via LiteLLM.

This module contains helper functions for model selection, parameter validation,
and other Bedrock-specific functionality used by the AiAccessor.
"""

import logging
from typing import Dict, Any, Optional

from accessor.ai.providers.bedrock.bedrock_model_mappings import MODEL_NAME_TO_ID_MAPPING
from accessor.ai.providers.bedrock.bedrock_model_config import (
    MODEL_TOKEN_LIMITS,
    MODEL_VALID_PARAMETERS,
    RECOMMENDED_MODELS
)
from engine.synthesis.config import Config

# Initialize logger
logger = logging.getLogger(__name__)


def get_bedrock_model_id(model_name: str, max_tokens: Optional[int] = None, 
                        default_model: Optional[str] = None) -> str:
    """
    Converts a user-friendly model name to a LiteLLM-compatible Bedrock model ID.
    
    Args:
        model_name (str): User-friendly model name (e.g., "claude-3-sonnet", "gpt-4").
        max_tokens (int, optional): Maximum tokens required for generation.
        default_model (str, optional): Default model to use if needed.
        
    Returns:
        str: The potentially mapped Bedrock model ID or a LiteLLM compatible ID.
    """
    if default_model is None:
        default_model = RECOMMENDED_MODELS.get("default", Config.MODEL_DEFAULT)
        
    normalized_name = model_name.lower().strip()
    
    # If it already looks like a provider/model format, use it directly
    if "/" in normalized_name and not normalized_name.startswith("arn:"):
        logger.debug("Using provided model name directly: %s", normalized_name)
        return normalized_name  # e.g., "bedrock/anthropic.claude-3-sonnet-20240229-v1:0"
    
    # Handle common non-Bedrock names or defaults needing Bedrock mapping
    # Map 'default' or 'gpt-*' names to appropriate Bedrock models
    if "gpt" in normalized_name or "default" in normalized_name:
        logger.warning("Mapping requested model '%s' to a recommended Bedrock model.", model_name)
        if max_tokens is not None:
            bedrock_model_id = select_model_by_token_limit(max_tokens, normalized_name, default_model)
            logger.info("Selected Bedrock model %s based on token limit %s for requested '%s'",
                        bedrock_model_id, max_tokens, model_name)
            return bedrock_model_id
        else:
            # Return the configured default Bedrock model
            logger.info("Using default Bedrock model %s for requested '%s'", default_model, model_name)
            return default_model
    
    # Check mappings (e.g., "claude-3-haiku" -> "bedrock/...")
    if normalized_name in MODEL_NAME_TO_ID_MAPPING:
        mapped_id = MODEL_NAME_TO_ID_MAPPING[normalized_name]
        logger.debug("Mapped '%s' to Bedrock ID: %s", normalized_name, mapped_id)
        return mapped_id
    
    # Check recommended models (e.g., "economical" -> "bedrock/...")
    if normalized_name in RECOMMENDED_MODELS:
        recommended_id = RECOMMENDED_MODELS[normalized_name]
        logger.debug("Mapped recommended model '%s' to Bedrock ID: %s", normalized_name, recommended_id)
        return recommended_id
    
    # Handle direct Bedrock model names (e.g., "anthropic.claude-3-sonnet-...")
    # Prepend with "bedrock/" for LiteLLM if not already present
    if any(normalized_name.startswith(p) for p in ["anthropic.", "meta.", "amazon.", "cohere.", "ai21."]):
        bedrock_id = f"bedrock/{normalized_name}"
        logger.debug("Assuming Bedrock model, formatting for LiteLLM: %s", bedrock_id)
        return bedrock_id
    
    # Handle Bedrock ARN
    if normalized_name.startswith("arn:aws:bedrock:"):
        logger.debug("Using Bedrock ARN directly: %s", normalized_name)
        return normalized_name  # LiteLLM might support ARNs directly
    
    # If no mapping found, return the name as is for LiteLLM to resolve
    logger.warning("Could not confidently map model name '%s'. Returning as is for LiteLLM.", model_name)
    return model_name


def select_model_by_token_limit(max_tokens: int, model_name_hint: str, default_model: str) -> str:
    """
    Selects an appropriate Bedrock model based on required token limit.
    
    Args:
        max_tokens (int): Maximum tokens required for generation.
        model_name_hint (str): Hint about the desired model type (e.g., 'gpt-4o-mini').
        default_model (str): The default model to use as a baseline.
        
    Returns:
        str: The Bedrock model ID that can handle the token limit.
    """
    # Adjust default based on hint if needed (e.g., prefer economical for smaller GPT models)
    if "mini" in model_name_hint or "economical" in model_name_hint:
        default_model = RECOMMENDED_MODELS.get("economical", default_model)
    
    default_model_limit = MODEL_TOKEN_LIMITS.get(default_model, 4096)
    
    if max_tokens <= default_model_limit:
        logger.debug("Token limit %s fits within default model %s limit %s.",
                     max_tokens, default_model, default_model_limit)
        return default_model
    
    # Find the smallest capacity Bedrock model that meets the requirement
    # Sort by token limit ascending
    sorted_limits = sorted(MODEL_TOKEN_LIMITS.items(), key=lambda item: item[1])
    for model_id, limit in sorted_limits:
        # Ensure we only consider actual Bedrock model IDs from the limits dict
        if limit >= max_tokens and ("bedrock/" in model_id or any(model_id.startswith(p) for p in ["anthropic.", "meta.", "amazon.", "cohere.", "ai21."])):
            logger.info("Selected model %s (limit %s) for required tokens %s.",
                         model_id, limit, max_tokens)
            return model_id
    
    # Fallback if no suitable model found (should be rare with large context models)
    highest_limit_model = max(MODEL_TOKEN_LIMITS.items(), key=lambda item: item[1])[0]
    logger.error("No model found in config for token limit %s. Falling back to highest capacity model: %s",
                  max_tokens, highest_limit_model)
    return highest_limit_model


def validate_and_filter_params(model_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validates and filters parameters against known valid parameters for a given Bedrock model ID.
    
    Args:
        model_id (str): The Bedrock model identifier (e.g., bedrock/anthropic...).
        params (dict): Dictionary of parameters to validate.
        
    Returns:
        dict: Filtered dictionary containing only valid parameters for the model family.
    """
    if not params:
        return {}
    
    valid_param_dict = get_valid_parameters(model_id)  # Gets {param_name: description}
    valid_keys = set(valid_param_dict.keys())
    
    filtered_params = {}
    invalid_params = []
    
    for key, value in params.items():
        if key in valid_keys:
            filtered_params[key] = value
        else:
            invalid_params.append(key)
    
    if invalid_params:
        logger.warning(
            "Removed invalid parameters for model family derived from %s: %s. Valid keys are: %s",
            model_id, ", ".join(invalid_params), ", ".join(valid_keys)
        )
    
    return filtered_params


def get_valid_parameters(model_id: str) -> Dict[str, str]:
    """
    Returns valid parameters for a Bedrock model family based on the model ID.
    
    Args:
        model_id (str): Bedrock model identifier (e.g., bedrock/anthropic...).
        
    Returns:
        dict: Dictionary mapping valid parameter names to descriptions.
    """
    # Extract model family hint (e.g., 'anthropic', 'meta') from the ID
    model_family_hint = None
    if "bedrock/" in model_id:
        parts = model_id.split('/')[1].split('.')
        if len(parts) > 1:
            model_family_hint = parts[0]  # e.g., 'anthropic' from 'anthropic.claude...'
    elif any(model_id.startswith(p + ".") for p in MODEL_VALID_PARAMETERS.keys()):
        model_family_hint = model_id.split('.')[0]
    
    if model_family_hint and model_family_hint in MODEL_VALID_PARAMETERS:
        return MODEL_VALID_PARAMETERS[model_family_hint]
    
    logger.warning("Model family not recognized for '%s'. Cannot validate parameters.", model_id)
    return {}  # Return empty dict if family not found 