import logging

from accessor.ai.accessors import AiModelMappingRepository
from accessor.ai.interfaces import IAiModelMappingRepository
from shared.models import AiModelMapping
from util.database import Session


class AiModelAccessor(IAiModelMappingRepository):
    def __init__(self, logger_name):
        self.session_factory = Session
        self.logger = logging.getLogger(logger_name)

        self.ai_model_mapping_repository = AiModelMappingRepository(self.session_factory)

    def get_all_mappings(self):
        return self.ai_model_mapping_repository.get_all_mappings()

    def get_mapping_by_friendly_name(self, friendly_name: str):
        return self.ai_model_mapping_repository.get_mapping_by_friendly_name(friendly_name)

    def add_mapping(self, friendly_name: str, provider_model_name: str):
        return self.ai_model_mapping_repository.add_mapping(friendly_name, provider_model_name)

    def update_mapping(self, friendly_name: str, provider_model_name: str):
        return self.ai_model_mapping_repository.update_mapping(friendly_name, provider_model_name)

    def delete_mapping(self, friendly_name: str):
        return self.ai_model_mapping_repository.delete_mapping(friendly_name) 