"""
High-level AI accessor using LiteLLM, integrating with Langfuse for prompts.

This module provides a unified interface to interact with various AI models
via LiteLLM, leveraging Langfuse for prompt management and observability.
"""

import os
import logging
import requests
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

# Third-party libraries
import litellm
# from litellm.utils import get_valid_models
from langfuse.decorators import langfuse_context, observe
from langfuse.model import PromptClient  # Assuming PromptClient is used within BurstLangfuseHelpers

# Local application/library specific imports
from engine.synthesis.config import Config
from util.config import (
    AWS_REGION_NAME,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY
)
from util.langfuse.helpers.burst_langfuse_helpers import BurstLangfuseHelpers
from engine.synthesis.prompt_definitions import FallbackPrompts
from accessor.ai.bedrock_utils import (
    get_bedrock_model_id,
    validate_and_filter_params
)
from accessor.ai.ai_model_accessor import AiModelAccessor

# Configure LiteLLM for Langfuse callback (alternative to @observe decorator)
# This typically happens once at application startup, but can be included here
# for clarity or if this module initializes it.
#litellm.success_callback = ["langfuse"]
litellm.failure_callback = ["langfuse"]
# if LANGFUSE_HOST:
#    os.environ["LANGFUSE_HOST"] = LANGFUSE_HOST

class AiAccessor:
    """
    High-level accessor using LiteLLM to interact with AI models,
    integrating Langfuse for prompt management and observability.
    """

    # Initialize logger and helpers as static members
    _logger = logging.getLogger(__name__)
    _logger.setLevel(logging.DEBUG) # Or level from config
    _burst_langfuse_helper = BurstLangfuseHelpers()
    _default_model = Config.MODEL_DEFAULT
    _ai_model_accessor = AiModelAccessor(__name__)

    @classmethod
    @observe(as_type="generation", name="LiteLLM Completion") # Updated decorator name
    def get_completion(
        cls,
        prompt_name: str,
        prompt_variables: Optional[Dict[str, Any]] = None,
        messages: Optional[List[Dict[str, Any]]] = None,
        additional_model_request_fields: Optional[Dict[str, Any]] = None,
        inference_config: Optional[Dict[str, Any]] = None,
        tool_config: Optional[Dict[str, Any]] = None,
        trace_data: Optional[Dict[str, Any]] = None,
        retry: Optional[bool] = True
    ) -> str:
        """
        Invokes an AI model via LiteLLM using a prompt fetched from Langfuse.

        Handles fetching prompts from Langfuse, falling back to predefined prompts
        if Langfuse is unavailable, determines the appropriate model, prepares
        parameters for LiteLLM, and makes the call.

        Args:
            prompt_name (str): Name of the Langfuse prompt to use.
            prompt_variables (dict, optional): Variables to substitute in the prompt.
            messages (list, optional): Conversation history messages.
            additional_model_request_fields (dict, optional): Additional parameters specific
                to the underlying model provider (passed via litellm's `metadata` or specific kwargs).
            inference_config (dict, optional): Inference parameters like temperature, max_tokens.
                These will be mapped to LiteLLM parameters.
            tool_config (dict, optional): Configuration for tools/function calling (passed via litellm).
            trace_data (dict, optional): Trace data for request tracking (passed via litellm metadata).

        Returns:
            str: Response text from the foundation model.

        Raises:
            ValueError: If prompt content is empty or not found (and no fallback exists).
            RuntimeError: If the LiteLLM model invocation fails.
            Exception: For underlying issues with Langfuse or LiteLLM.
        """
        prompt_variables = prompt_variables or {}
        messages = messages or []
        additional_model_request_fields = additional_model_request_fields or {}
        inference_config = inference_config or {}
        tool_config = tool_config or {}
        trace_data = trace_data or {}

        system_prompt_content: str = ""
        user_prompt_content: Optional[str] = None
        model_config: Dict[str, Any] = {}
        langfuse_prompt: Optional[PromptClient] = None # Store the fetched Langfuse prompt object

        try:
            # Fetch the prompt from Langfuse
            langfuse_prompt, compiled_prompt = cls._burst_langfuse_helper.fetch_langfuse_prompt(
                prompt_name, **prompt_variables
            )

            # Update Langfuse observation (if using @observe)
            if hasattr(langfuse_context, 'update_current_observation') and langfuse_prompt:
                langfuse_context.update_current_observation(prompt=langfuse_prompt)

            # Extract system and user prompts
            if not langfuse_prompt:
                cls._logger.error("No prompt content found for '%s'", prompt_name)
                raise ValueError(f"No prompt content found for '{prompt_name}'")

            if compiled_prompt:
                # Use compiled prompt if available
                system_prompt_content = compiled_prompt[0]["content"]
                user_prompt_content = compiled_prompt[1]["content"] if len(compiled_prompt) >= 2 else None
            else:
                # Fallback if no compiled prompt (e.g., no variables)
                system_prompt_content = langfuse_prompt.prompt[0]["content"]
                # Assuming user prompt might not exist in the base prompt definition without variables
                user_prompt_content = None

            # Get model configuration from Langfuse
            model_config = cls._burst_langfuse_helper.get_model_config(langfuse_prompt)
            cls._logger.debug("Fetched Langfuse config: %s", cls._truncate_for_logging(model_config))

        except (ConnectionError, KeyError, ValueError, requests.RequestException) as error:
            cls._logger.error("Error fetching Langfuse prompt '%s', using fallback: %s", prompt_name, error)
            fallback_method = getattr(FallbackPrompts, prompt_name, None)
            if not fallback_method:
                raise ValueError(f"No fallback prompt found for '{prompt_name}'") from error

            system_prompt_content, user_prompt_content = cls._handle_fallback_prompt(fallback_method, **prompt_variables)
            # Use default model config for fallback
            model_config = {"model": cls._default_model}
            cls._logger.warning("Using fallback prompt and default model config for '%s'", prompt_name)


        try:
            # --- Prepare LiteLLM call parameters ---

            # Determine Model ID
            model_name_from_config = model_config.get("model", cls._default_model)
            # Check for friendly model name mapping
            model_name_from_config = cls._get_model_from_mapping(model_name_from_config)
            max_tokens_from_config = model_config.get("max_tokens") or inference_config.get("maxTokens") # Check both Langfuse and direct inference config

            # Check if FORCE_BEDROCK env var is set to true
            force_bedrock = os.getenv("FORCE_BEDROCK", "").lower() == "true"
            if force_bedrock:
                cls._logger.info("Forcing Bedrock Usage.")
                # Use the bedrock_utils helper to get the appropriate model ID
                model_id = get_bedrock_model_id(
                    model_name_from_config, 
                    max_tokens_from_config,
                    cls._default_model
                )
                cls._logger.info("Using Bedrock model ID: %s", model_id)
            else:
                model_id = model_name_from_config
                cls._logger.info("Using model ID: %s", model_name_from_config)

            # TODO: I wanted to use this helper function to validate the model name against available models in the environment
            # but it's not working as expected.
            # Validate the model name against available models in the environment
            # valid_models = litellm.get_valid_models()
            # cls._logger.info("Valid models: %s", valid_models)
            # if model_name_from_config not in valid_models:
            #     cls._logger.warning(
            #         "Model '%s' not available in the current environment. Using default model '%s'.",
            #         model_name_from_config, cls._default_model
            #     )
            #     model_id = cls._default_model
            # else:
            #     model_id = model_name_from_config
            #     cls._logger.debug("Using configured model: %s", model_id)

            # Construct LiteLLM messages
            litellm_messages = []
            if system_prompt_content:
                litellm_messages.append({"role": "system", "content": system_prompt_content})
            # Prepend history messages
            litellm_messages.extend(messages)
            # Append the current user prompt (if any)
            if user_prompt_content:
                litellm_messages.append({"role": "user", "content": user_prompt_content})


            # Map Langfuse/Inference Config to LiteLLM parameters
            litellm_params = {}
            langfuse_to_litellm_map = {
                "temperature": "temperature",
                "max_tokens": "max_tokens",
                "top_p": "top_p",
                "stop_sequences": "stop", # Map stop_sequences to litellm's stop
                # Add other mappings as needed (e.g., frequency_penalty, presence_penalty)
            }

            # Prioritize inference_config passed directly to the method
            merged_configs = {**model_config, **inference_config}

            for conf_key, litellm_key in langfuse_to_litellm_map.items():
                if merged_configs.get(conf_key) is not None:
                    litellm_params[litellm_key] = merged_configs[conf_key]

            # Handle tools/functions
            if tool_config:
                litellm_params["tools"] = tool_config.get("tools") # Adjust based on LiteLLM's expected format
                litellm_params["tool_choice"] = tool_config.get("toolChoice") # Adjust as needed

            # Handle additional provider-specific fields (pass via metadata or specific kwargs)
            # Filter params using the bedrock_utils helper
            filtered_params = validate_and_filter_params(
                model_id,
                additional_model_request_fields
            )

            # Build metadata for tracing and provider-specific params
            litellm_metadata = {
                "langfuse_prompt_name": prompt_name,
                "langfuse_prompt_version": langfuse_prompt.version if langfuse_prompt else None,
                **(trace_data or {}),
                "additional_provider_fields": filtered_params
            }

            cls._logger.debug(
                "Calling litellm.completion with model: %s, params: %s, metadata: %s, messages: %s",
                model_id,
                cls._truncate_for_logging(litellm_params),
                cls._truncate_for_logging(litellm_metadata),
                cls._truncate_for_logging(litellm_messages)
            )

            start_time = datetime.now()
            if retry:
                response, used_fallback = cls._execute_with_retry_and_fallback(
                    model_id=model_id,
                    messages=litellm_messages,
                    metadata=litellm_metadata,
                    params=litellm_params,
                    prompt_name=prompt_name
                )
            else:
                response = litellm.completion(
                    model=model_id,
                    messages=litellm_messages,
                    metadata=litellm_metadata,
                    **litellm_params
                )

            # Update model_id if fallback was used
            if used_fallback:
                model_id = cls._default_model

            elapsed_time = (datetime.now() - start_time).total_seconds()
            cls._logger.info(
                "LiteLLM completion call took %.2f seconds for prompt '%s' using model %s%s",
                elapsed_time, prompt_name, model_id, " (fallback)" if used_fallback else ""
            )

            # --- Process Response ---
            # Assuming response structure follows OpenAI format typically returned by LiteLLM
            response_text = ""
            if response.choices and response.choices[0].message:
                response_text = response.choices[0].message.content or ""
            # TODO: Handle potential tool calls in the response if tool_config was used

            cls._logger.debug("LiteLLM raw response object: %s", cls._truncate_for_logging(response))

            # --- Update Langfuse Observation ---
            # (Assuming @observe handles basic input/output automatically)
            # Manually add usage and metadata if needed and not handled by callback/decorator
            input_tokens = response.usage.prompt_tokens if response.usage else 0
            output_tokens = response.usage.completion_tokens if response.usage else 0
            total_tokens = response.usage.total_tokens if response.usage else 0

            if hasattr(langfuse_context, 'update_current_observation'):
                langfuse_context.update_current_observation(
                    # output=response_text, # Usually handled by @observe
                    usage_details={ # Langfuse expects "input", "output", "total"
                        "input": input_tokens,
                        "output": output_tokens,
                        "total": total_tokens,
                    },
                    metadata={ # Add response metadata if available and useful
                        "litellm_id": response.id,
                        "litellm_created": response.created,
                        "litellm_model_used": response.model,
                        "prompt_name": prompt_name,
                        # "_response": cls._truncate_for_logging(response.dict()) # Optionally log full response dict
                    },
                    model=model_id.split("/")[-1].split("us.")[-1]
                )

            return response_text

        except litellm.exceptions.APIConnectionError as e:
            cls._logger.error("LiteLLM API connection error for prompt '%s': %s", prompt_name, e)
            raise RuntimeError(f"LiteLLM API connection error: {e}") from e
        except litellm.exceptions.APIError as e:
            cls._logger.error("LiteLLM API error for prompt '%s': %s", prompt_name, e)
            raise RuntimeError(f"LiteLLM API error: {e}") from e
        except litellm.exceptions.Timeout as e:
            cls._logger.error("LiteLLM request timed out for prompt '%s': %s", prompt_name, e)
            raise RuntimeError(f"LiteLLM request timed out: {e}") from e
        except Exception as e:
            cls._logger.error("Unexpected error during LiteLLM invocation for prompt '%s': %s", prompt_name, e)
            # Optionally update Langfuse trace with error details here
            raise # Re-raise the original exception


    # --- Helper Methods ---
    @classmethod
    def _execute_with_retry_and_fallback(
        cls,
        model_id: str,
        messages: List[Dict[str, Any]],
        metadata: Dict[str, Any],
        params: Dict[str, Any],
        prompt_name: str
    ) -> Tuple[Any, bool]:
        """
        Executes LiteLLM completion with retry logic and fallback to default model.
        
        Args:
            model_id (str): The model ID to use for completion.
            messages (List[Dict[str, Any]]): The messages to send for completion.
            metadata (Dict[str, Any]): Metadata for the completion request.
            params (Dict[str, Any]): Additional parameters for the completion request.
            prompt_name (str): The name of the prompt being used (for logging).
            
        Returns:
            Tuple: (response, used_fallback) where:
                - response: The response from the successful completion call.
                - used_fallback: Boolean indicating whether the fallback model was used.
            
        Raises:
            RuntimeError: If all retry attempts fail with both models.
        """
        max_retries = 2  # Additional retries (3 attempts total)
        retry_count = 0
        last_exception = None
        used_fallback = False

        # Try with chosen model first
        while retry_count <= max_retries:
            try:
                response = litellm.completion(
                    model=model_id,
                    messages=messages,
                    metadata=metadata,
                    **params
                )
                # Success - return response
                return response, used_fallback
            except (litellm.exceptions.APIConnectionError, 
                    litellm.exceptions.APIError, 
                    litellm.exceptions.Timeout,
                    Exception) as e:
                last_exception = e
                retry_count += 1
                cls._logger.warning(
                    "LiteLLM completion failed (attempt %d/%d) with model '%s': %s",
                    retry_count, max_retries + 1, model_id, str(e)
                )
                if retry_count <= max_retries:
                    continue

                # All retries failed with chosen model, try default model
                if model_id != cls._default_model:
                    cls._logger.info(
                        "All attempts with model '%s' failed, falling back to default model '%s'",
                        model_id, cls._default_model
                    )
                    model_id = cls._default_model
                    used_fallback = True
                    # Reset retry count for default model attempts
                    retry_count = 0
                    continue

                # We've exhausted all retries with both models
                cls._logger.error(
                    "All completion attempts failed for prompt '%s' with both chosen and default models",
                    prompt_name
                )
                raise RuntimeError(f"LiteLLM completion failed after multiple retry attempts: {last_exception}") from last_exception

    @staticmethod
    def _handle_fallback_prompt(fallback_prompt_func, **kwargs) -> Tuple[str, Optional[str]]:
        """
        Handles fallback prompt generation using a function from FallbackPrompts.

        Args:
            fallback_prompt_func (callable): The fallback function to generate prompts.
            **kwargs: Variables to pass to the fallback function.

        Returns:
            tuple: (system_prompt, user_prompt) generated from fallback logic.
                 User prompt might be None if not generated by fallback.
        """
        AiAccessor._logger.warning("Generating prompt using fallback logic.")

        # Generate system prompt using the fallback method, passing relevant kwargs
        system_prompt = fallback_prompt_func(**kwargs) 

        # Construct user prompt dynamically from kwargs
        user_prompt = "\n".join(f"{key}: {value}" for key, value in kwargs.items())

        return system_prompt, user_prompt

    @staticmethod
    def _truncate_for_logging(obj: Any, max_length: int = 250, max_content_length: int = 50) -> Any:
        """
        Truncates large objects/strings/lists/dicts for cleaner logging.

        Args:
            obj: The object to truncate.
            max_length: Max length for full string representation.
            max_content_length: Max length for individual string values within collections.

        Returns:
            Truncated representation suitable for logging.
        """
        if isinstance(obj, bytes):
            return "<bytes content>"

        try:
            if isinstance(obj, str):
                if len(obj) > max_length:
                    return obj[:max_length] + f"... [truncated, total length: {len(obj)}]"
                return obj

            if isinstance(obj, dict):
                truncated_dict = {}
                for k, v in obj.items():
                    # Truncate keys if they are long strings
                    key_repr = AiAccessor._truncate_for_logging(k, max_content_length // 2, max_content_length // 2)
                    # Truncate values recursively
                    truncated_dict[key_repr] = AiAccessor._truncate_for_logging(v, max_length, max_content_length)
                # Check overall dict string representation length
                dict_repr = str(truncated_dict)
                if len(dict_repr) > max_length:
                    return f"{{...}} [truncated dict, {len(obj)} items]"
                return truncated_dict


            if isinstance(obj, list):
                truncated_list = [AiAccessor._truncate_for_logging(item, max_length, max_content_length) for item in obj]
                # Check overall list string representation length
                list_repr = str(truncated_list)
                if len(list_repr) > max_length:
                    return f"[...] [truncated list, {len(obj)} items]"
                return truncated_list

            # For other types, convert to string and truncate if necessary
            obj_repr = str(obj)
            if len(obj_repr) > max_length:
                return obj_repr[:max_length] + f"... [truncated {type(obj).__name__}]"
            return obj

        except Exception as e:
            # Fallback in case of unexpected errors during truncation (e.g., complex objects)
            AiAccessor._logger.error("Error during logging truncation: %s", e, exc_info=False)
            return f"<Error truncating {type(obj).__name__}>"

    @classmethod
    def _get_model_from_mapping(cls, model_name: str) -> str:
        """
        Tries to get the provider model name from the database mapping table.
        Falls back to the original model name if no mapping exists.

        Args:
            model_name (str): The friendly model name to lookup

        Returns:
            str: The provider model name or the original name if no mapping exists
        """
        try:
            mapping = cls._ai_model_accessor.get_mapping_by_friendly_name(model_name)
            if mapping:
                cls._logger.debug(f"Found model mapping: {model_name} -> {mapping.provider_model_name}")
                return mapping.provider_model_name
            else:
                cls._logger.warning(f"No model mapping found for {model_name}")
                return model_name
        except Exception as e:
            cls._logger.error(f"Error fetching model mapping for {model_name}: {str(e)}")
            return model_name

# Example of how to use the accessor (assuming Langfuse callbacks are set elsewhere):
# if __name__ == "__main__":
#    logging.basicConfig(level=logging.INFO)
#    try:
#        response = AiAccessor.get_completion(
#            prompt_name="YourLangfusePromptName",
#            prompt_variables={"input_variable": "some value"},
#            messages=[{"role": "user", "content": "Previous user message"}],
#            trace_data={"user_id": "user-123", "session_id": "session-abc"}
#        )
#        print("Model Response:", response)
#    except Exception as e:
#        print(f"An error occurred: {e}")
