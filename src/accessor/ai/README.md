# AI Accessor Module

This module provides a high-level abstraction for accessing AI models using LiteLLM, integrating with Langfuse for prompts and observability.

## AiAccessor

The `AiAccessor` is a high-level wrapper that hides provider details from consumers. It leverages LiteLLM for model access and Langfuse for prompt management and observability.

### Usage

```python
from accessor.ai import AiAccessor

# The AiAccessor can be used directly without instantiation
response = AiAccessor.get_completion(
    prompt_name="my_prompt", 
    prompt_variables={"my_var": "value"},
    messages=[{"role": "user", "content": "previous message"}],
    inference_config={"temperature": 0.7, "max_tokens": 500},
    tool_config={"tools": [...]}
)
```

### Configuration

The AiAccessor automatically determines which model to use based on the prompt configuration in Langfuse, with fallbacks to default models.

The implementation supports both direct model specification and intelligent model selection based on token limits and other requirements.

## Internal Architecture

The AiAccessor uses LiteLLM as the foundation for making AI model requests, supporting a variety of AI providers through LiteLLM's unified API.

### Bedrock Integration

The module includes specialized support for AWS Bedrock models through:

- Model name mapping (e.g., "claude-3-sonnet" → appropriate Bedrock model ID)
- Token limit-based model selection
- Parameter validation for different model families

## Features

- **Prompt Management**: Uses Langfuse to manage prompts with versioning
- **Fallback Mechanism**: Falls back to predefined prompts if Langfuse is unavailable
- **Observability**: Integrates with Langfuse for tracking and monitoring
- **Provider Abstraction**: Simplifies working with different AI providers
- **Model Selection**: Intelligent model selection based on requirements
- **Tool Support**: Support for function calling / tools with compatible models 