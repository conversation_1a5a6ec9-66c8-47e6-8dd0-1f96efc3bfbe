from abc import ABC, abstractmethod
from typing import Any

from accessor.reference.dto import UpdatedRegulationSectionDTO, OriginalRegulationSectionDTO


class IFederalRegisterAccessor(ABC):
    """
    Interface for accessing Federal Register data.
    """

    @abstractmethod
    def get_updated_sections_from_federal_register(
        self, document_number: str
    ) -> list[UpdatedRegulationSectionDTO]:
        """
        Retrieve Federal Register document sections based on the document number provided.

        Args:
            document_number (str): The document number.

        Returns:
            list[Job]: A list of Job objects with updated related data counts.
        """
        pass

    @abstractmethod
    def get_original_sections_from_federal_register(
        self, new_sections: list[UpdatedRegulationSectionDTO]
    ) -> list[OriginalRegulationSectionDTO]:
        pass
