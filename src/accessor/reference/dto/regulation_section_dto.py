from dataclasses import dataclass

from accessor.reference.dto.reference_version_enum import (
    ReferenceVersion,
)  # Direct import to avoid cyclic error


@dataclass
class BaseRegulationSectionDTO:
    title: str
    section: str
    subject: dict
    content: str
    ecfr_url: str
    version: ReferenceVersion


@dataclass
class OriginalRegulationSectionDTO(BaseRegulationSectionDTO):
    version: ReferenceVersion = ReferenceVersion.ORIGINAL


@dataclass
class UpdatedRegulationSectionDTO(BaseRegulationSectionDTO):
    version: ReferenceVersion = ReferenceVersion.UPDATED
