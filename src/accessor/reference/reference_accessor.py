import logging
import re
import xml.etree.ElementTree as ET
from typing import Any, Dict, Tuple

import requests

from accessor.reference.dto import UpdatedRegulationSectionDTO, OriginalRegulationSectionDTO
from accessor.reference.dto.reference_version_enum import ReferenceVersion
from accessor.reference.exceptions.federal_citation_exceptions import (
    CitationServiceInternalError,
    CitationNumberNotFoundException,
    MalformedCitationNumberException,
    MissingRequiredTagsException,
)
from accessor.reference.interfaces import IFederalRegisterAccessor
from util.config import CODE_OF_FEDERAL_REGULATIONS_API, FEDERAL_REGISTER_API


class ReferenceAccessor(IFederalRegisterAccessor):
    def __init__(self, logger_name):
        self.logger = logging.getLogger(logger_name)

    def get_updated_sections_from_federal_register(
        self, document_number: str
    ) -> list[UpdatedRegulationSectionDTO]:
        """
        Fetch and extract updated sections from the Federal Register for a given document number.

        Args:
            document_number (str): The unique identifier for the Federal Register document.

        Returns:
            list[Dict[str, Any]]: A list of sections with their details.

        Raises:
            ValueError: If the document number is invalid or required data is missing.
            requests.RequestException: If there is an issue with the API calls.
        """
        if not document_number or not isinstance(document_number, str):
            self.logger.error("Invalid document number provided.")
            raise ValueError("Document number must be a non-empty string.")

        self.logger.info("Fetching updated sections for document: %s", document_number)

        # Step 1: Fetch document data from Federal Register API
        try:
            document_data = self._get_federal_register_document(document_number)
        except Exception as e:
            self.logger.exception("Error fetching document data: %s", e)
            raise

        # Step 2: Extract required document information
        try:
            publication_date, full_text_xml_url = self._extract_document_info(
                document_data, document_number
            )
        except ValueError as e:
            self.logger.exception("Error extracting document info: %s", e)
            raise

        # Step 3: Download and process the XML content
        try:
            xml_content = self._download_full_text_xml(full_text_xml_url)
        except Exception as e:
            self.logger.exception("Error downloading XML content: %s", e)
            raise

        # Step 4: Extract REGTEXT sections from the XML content
        try:
            sections = self._extract_regtext_sections(xml_content, publication_date)
        except ET.ParseError as e:
            self.logger.exception("Error parsing XML content: %s", e)
            raise
        except Exception as e:
            self.logger.exception("Unexpected error while extracting sections: %s", e)
            raise

        self.logger.info(
            "Successfully extracted %s sections for document %s.", len(sections), document_number
        )
        return sections

    def get_original_sections_from_federal_register(
        self, updated_sections: list[UpdatedRegulationSectionDTO]
    ) -> list[OriginalRegulationSectionDTO]:
        original_sections = []

        for updated_section in updated_sections:
            self.logger.info(
                "Retrieving original version of Title %s CFR %s",
                updated_section.title,
                updated_section.section,
            )

            # API call 3: Get eCFR section
            original_section_xml_content = self._get_ecfr_section(updated_section.ecfr_url)

            if original_section_xml_content and not self._is_amendment_reference(
                original_section_xml_content
            ):
                sanitized_content = self._sanitize_legal_text(original_section_xml_content)

                original_sections.append(
                    OriginalRegulationSectionDTO(
                        title=updated_section.title,
                        content=sanitized_content,
                        section=updated_section.section,
                        subject=updated_section.subject,
                        version=ReferenceVersion.ORIGINAL,
                        ecfr_url=updated_section.ecfr_url,
                    )
                )
            else:
                self.logger.warning(
                    "Skipping section %s: No previous version exists or contains only a reference to an amendment.",
                    updated_section.section,
                )

        return original_sections

    def validate_citation_number(self, citation_number: str):
        """
        Validates a given Federal Register citation number by performing a series of
        data extraction and parsing steps.

        This method performs the following steps:
        1. Verifies that the citation number is a non-empty string.
        2. Retrieves the Federal Register document associated with the citation number.
        3. Extracts the publication date and full-text XML URL from the document data.
        4. Downloads the full-text XML content.
        5. Parses the XML to extract relevant REGTEXT sections.
        6. Validates that the expected REGTEXT sections are present.

        If any step fails due to malformed input or missing/invalid data, appropriate
        exceptions are raised and logged. If all steps succeed, the method completes
        silently without returning a value.

        Args:
            citation_number (str): The Federal Register citation number to validate.

        Raises:
            MalformedCitationNumberException: If the citation number is missing,
                incorrectly formatted, or if document information cannot be extracted.
            MissingRequiredTagsException: If no REGTEXT sections are found in the XML content.
            ET.ParseError: If the XML content is malformed and cannot be parsed.
            Exception: For unexpected errors during XML download or processing.
        """

        # Step 1: Verify citation number exists
        if not citation_number or not isinstance(citation_number, str):
            self.logger.error("Malformed citation number: %s", citation_number)
            raise MalformedCitationNumberException()

        document_data = self._get_federal_register_document(citation_number)

        # Step 2: Extract required document information
        try:
            publication_date, full_text_xml_url = self._extract_document_info(
                document_data, citation_number
            )
        except ValueError as e:
            self.logger.exception("Error extracting document info: %s", e)
            raise MalformedCitationNumberException()

        # Step 3: Download and process the XML content
        try:
            xml_content = self._download_full_text_xml(full_text_xml_url)
        except Exception as e:
            self.logger.exception("Error downloading XML content: %s", e)
            raise

        # Step 4: Extract REGTEXT sections from the XML content
        try:
            sections = self._extract_regtext_sections(xml_content, publication_date)
        except ET.ParseError as e:
            self.logger.exception("Error parsing XML content: %s", e)
            raise
        except Exception as e:
            self.logger.exception("Unexpected error while extracting sections: %s", e)
            raise

        # Step 5: Validate that sections were found
        if not sections:
            self.logger.error("No REGTEXT sections found for citation number: %s", citation_number)
            raise MissingRequiredTagsException()

        # No return needed—successful validation means the method ends silently

    def extract_section_titles(self, xml_text: str) -> list[str]:
        """
        Extracts section titles (e.g., 3555.303, 3555.304) from the given XML content.

        Args:
            xml_text (str): The XML content as a string.

        Returns:
            list: A list of section titles.
        """
        section_titles = re.findall(r'<DIV8 N="(.*?)"', xml_text)
        return section_titles

    def _is_amendment_reference(self, xml_content: str) -> bool:
        """
        Determines if the retrieved XML content contains only an amendment reference.

        Args:
            xml_content (str): The XML content returned from eCFR.

        Returns:
            bool: True if the content is just a reference to an amendment, otherwise False.
        """
        try:
            root = ET.fromstring(xml_content)

            # Check if the root tag is <DIV8> (Common in eCFR responses)
            if root.tag == "DIV8":
                # Ensure it has a <HEAD> element (section heading)
                head = root.find("HEAD")
                xref = root.find("XREF")

                # If the content only consists of a HEAD and XREF reference, it's just a link to an amendment
                if head is not None and xref is not None and len(root) == 2:
                    return True

            return False
        except ET.ParseError:
            self.logger.error("Error parsing XML content.")
            return False  # If parsing fails, assume it's valid content

    def _get_ecfr_section(self, url: str) -> str:
        """Retrieve a section from eCFR. The response contains only the requested section text."""
        self.logger.debug("Fetching eCFR section from: %s", url)

        try:
            response = requests.get(url)
            response.raise_for_status()

            # Explicitly set encoding to UTF-8
            response.encoding = "utf-8"

            # Replace problematic characters if needed
            cleaned_text = response.text.replace("Â§", "§").replace("â", "")

            return cleaned_text
        except Exception as e:
            self.logger.error("Failed to get eCFR section: %s", e)
            return None

    def _sanitize_legal_text(self, raw_text: str) -> str:
        # 1. Remove unwanted tags and their contents entirely
        raw_text = re.sub(r"<(XREF|CITA)[^>]*?>.*?</\1>", "", raw_text, flags=re.DOTALL)

        # 2. Remove inline formatting tags (keep inner content)
        raw_text = re.sub(r"</?(I|E)[^>]*?>", "", raw_text)

        # 3. Extract content from <HEAD> and <P> tags
        head_and_p = re.findall(r"<(HEAD|P)>(.*?)</\1>", raw_text, flags=re.DOTALL)

        # 4. Normalize whitespace and flatten to single line
        cleaned_segments = []
        for _, content in head_and_p:
            text = re.sub(r"\s+", " ", content.strip())
            cleaned_segments.append(text)

        return " ".join(cleaned_segments)

    def _get_federal_register_document(self, document_number: str) -> Dict[str, Any]:
        """
        There are 4 different scenarios to handle here:

        1. Valid citation number: e.g., "89 FR 99705"
            -> 200 response and returns back document info
        2. Invalid citation number: e.g., "invalid-citation" or "89 FR 99-705"
            -> 500 response and raise CitationServiceInternalError
        3. Citation number with additional spacing: e.g., "89 FR 99 7 0 5"
            -> 200 response and returns back document info (spaces don't seem to phase the federal endpoint)
        4. Citation number that doesn't exist: e.g., "999 FR 99999"
            -> 200 response with errors ({'not_found': ['999 FR 99999']}) and raise CitationNumberNotFoundException
        """

        formatted_document_number = document_number.upper().replace(" ", "%20")
        base_url = f"{FEDERAL_REGISTER_API}{formatted_document_number}.json"

        self.logger.debug("Requesting Federal Register document: %s", base_url)

        try:
            response = self._make_request(base_url)
            data = response.json()

            if "errors" in data and "not_found" in data["errors"]:
                self.logger.warning("Citation not found: %s", data["errors"]["not_found"])
                raise CitationNumberNotFoundException()

            return data

        except requests.RequestException as e:
            self.logger.error("RequestException: %s", e)
            raise CitationServiceInternalError()

        except ValueError as e:
            self.logger.error("Invalid JSON response for document '%s': %s", document_number, e)
            raise CitationServiceInternalError()

    def _extract_document_info(self, document_data, citation_number: str) -> Tuple[str, str]:
        """
        Extracts the publication date and XML URL for the document with the matching citation.

        Args:
            document_data (dict): The JSON response containing document information.
            citation_number (str): The citation number to match (e.g., "89 FR 99705").

        Returns:
            Tuple[str, str]: The publication date and XML URL if found.

        Raises:
            ValueError: If no matching document is found or required data is missing.
        """
        if not isinstance(document_data, dict) or "results" not in document_data:
            raise ValueError("Invalid document data received")

        # Iterate over all returned results and find the matching citation
        for document_info in document_data["results"]:
            if document_info.get("citation", "").upper() == citation_number.upper():
                publication_date = document_info.get("publication_date")
                full_text_xml_url = document_info.get("full_text_xml_url")

                if not publication_date or not full_text_xml_url:
                    raise ValueError(
                        f"Required document information missing for citation: {citation_number}"
                    )

                self.logger.info("Matching citation found: %s", citation_number)
                self.logger.info(
                    "Publication date: %s, XML URL: %s", publication_date, full_text_xml_url
                )

                return publication_date, full_text_xml_url

        # If no matching citation is found
        raise ValueError("No document found with citation: %s", citation_number)

    def _download_full_text_xml(self, url: str) -> str:
        self.logger.debug("Downloading XML from: %s", url)
        try:
            response = self._make_request(url)

            # Clean up and return the content
            cleaned_text = response.text.replace("Â§", "§").replace("â", "")
            return cleaned_text
        except requests.RequestException as e:
            self.logger.error("Failed to download XML from '%s' : %s", url, e)
            raise

    def _extract_regtext_sections(
        self, xml_content: str, publication_date: str
    ) -> list[UpdatedRegulationSectionDTO]:
        """
        Extract title, section, and content information from REGTEXT sections.
        """
        self.logger.debug("Parsing XML content for REGTEXT sections.")
        root = ET.fromstring(xml_content)
        sections = []

        for regtext in root.findall(".//REGTEXT"):
            title = self._extract_title(regtext)
            if not title:
                self.logger.warning("Skipping REGTEXT without a title.")
                continue

            sections.extend(self._extract_sections(regtext, title, publication_date))

        self.logger.info("Extracted %s sections from REGTEXT.", len(sections))
        return sections

    def _extract_title(self, regtext: ET.Element) -> str:
        """
        Extract the title from a REGTEXT element.
        """
        title = regtext.get("TITLE")
        if not title:
            self.logger.warning("REGTEXT element missing TITLE attribute.")
        return title

    def _extract_sections(
        self, regtext: ET.Element, title: str, publication_date: str
    ) -> list[UpdatedRegulationSectionDTO]:
        """
        Extract and process sections from a REGTEXT node.
        """
        sections = []
        for section in regtext.findall(".//SECTION"):
            section_data = self._construct_updated_section_data(section, title, publication_date)
            if section_data:
                sections.append(section_data)
        return sections

    def _construct_updated_section_data(
        self, section: ET.Element, title: str, publication_date: str
    ) -> UpdatedRegulationSectionDTO:
        """
        Construct a dictionary containing section details.
        """
        sectno = section.find(".//SECTNO")
        subject = section.find(".//SUBJECT")
        content = ET.tostring(section, encoding="unicode", method="text").strip()

        if sectno is not None:
            section_number = "".join(
                char for char in (sectno.text or "") if char.isdigit() or char == "."
            )
            ecfr_url = f"{CODE_OF_FEDERAL_REGULATIONS_API}{publication_date}/title-{title}.xml?section={section_number}"

            return UpdatedRegulationSectionDTO(
                title=title,
                section=section_number,
                subject=subject.text if subject is not None else None,
                content=content,
                ecfr_url=ecfr_url,
                version=ReferenceVersion.UPDATED,
            )

        self.logger.warning("Skipping section due to missing SECTNO.")
        return {}

    def _make_request(self, url: str) -> requests.Response:
        self.logger.debug("Making request to: %s", url)
        try:
            response = requests.get(url)
            response.raise_for_status()
            response.encoding = "utf-8"  # Ensure consistent encoding
            return response
        except requests.RequestException as e:
            self.logger.exception("Request failed for URL '%s': %s", url, e)
            raise
