class CitationNumberNotFoundException(Exception):
    """Raised when the citation number format is valid, but it doesnt exist."""

    pass


class MissingRequiredTagsException(Exception):
    """Raised when REGTEXT/SECTION tags are missing in a valid citation."""

    pass


class CitationServiceInternalError(Exception):
    """Raised for unexpected API/system errors."""

    pass


class MalformedCitationNumberException(Exception):
    """Raised when the citation number either isn't in the correct format or is invalid."""

    pass
