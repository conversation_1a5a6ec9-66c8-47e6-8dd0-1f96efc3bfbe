from dataclasses import dataclass, field
from datetime import datetime
from typing import Optional

from shared.enums.job_enums import JobStatusEnum


@dataclass
class DocumentCreateDTO:
    domain_id: int
    name: str
    filename: str
    document_bytes: bytes
    business_id: int
    effective_datetime: datetime = field(default_factory=datetime.now)
    start_page: int = 1
    end_page: int = -1
    original_document_id: Optional[int] = None


@dataclass
class JobCreateDTO:
    job_type_id: int
    job_status: JobStatusEnum = JobStatusEnum.INITIAL_PROCESSING.value


@dataclass
class DocumentJobCreateDTO:
    document: DocumentCreateDTO
    job: JobCreateDTO
