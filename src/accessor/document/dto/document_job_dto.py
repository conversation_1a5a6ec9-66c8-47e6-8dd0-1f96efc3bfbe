from dataclasses import dataclass
from typing import Optional


@dataclass
class DocumentJobDTO:
    document_id: int
    document_name: str
    document_start_page: int
    document_end_page: int
    document_business_id: int
    document_filename: str
    job_id: int
    job_type_id: int
    job_status: str
    domain_id: int
    domain_name: str
    domain_vector_index_name: str
    document_bytes: Optional[bytes]
