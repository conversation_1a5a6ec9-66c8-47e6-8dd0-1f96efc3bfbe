"""
job_dto.py

This module defines the Data Transfer Object (DTO) for representing 
a job and its related information. The JobDTO class encapsulates all 
the attributes necessary for transferring job data across different 
layers of the application, such as the service and presentation layers.

Attributes:
    JobDTO (class): Represents a job, including its ID, type, status details, submission date, and
                    various counts for related entities such as unique statements, user stories, 
                    acceptance criteria, and test cases.

    to_dict (method): Converts the JobDTO instance into a dictionary for easy serialization.

Example usage:
    job_dto = JobDTO(
        job_id=1,
        reference_id=101,
        job_type="Processing",
        job_status_id=2,
        job_status_name="In Progress",
        job_status_description="The job is currently being processed.",
        job_state="Active",
        submitted_datetime=datetime.now(),
        job_name="Data Processing Job",
        global_knowledge=True,
        unique_statement_count=10,
        user_story_count=5,
        acceptance_criteria_count=3,
        test_case_count=7
    )

    job_dict = job_dto.to_dict()
"""

from typing import Optional
from dataclasses import asdict, dataclass
from datetime import datetime

from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO


@dataclass
class JobDTO:
    """
    Data Transfer Object for representing a job and its related information.

    Attributes:
        job_id (int): The unique identifier for the job.
        reference_id (int): The reference ID associated with the job (e.g., document ID).
        job_type (str): The type of the job (e.g., processing, analysis).
        job_status_id (int): The ID representing the current status of the job.
        job_status_name (str): The name of the current job status.
        job_status_description (str): A human-readable description of the job's status.
        job_state (str): The state of the job, which may represent the progress or stage.
        submitted_datetime (datetime): The date and time when the job was submitted.
        job_name (str): The name of the job.
        global_knowledge (bool): Indicates if the job's results contribute to global knowledge.
        unique_statement_count (int): The count of unique statements related to the job.
        user_story_count (int): The count of user stories associated with the job.
        acceptance_criteria_count (int): The count of acceptance criteria items for the job.
        test_case_count (int): The count of test cases related to the job.

    Methods:
        to_dict() -> dict:
            Convert the JobDTO to a dictionary representation.
    """

    job_id: int
    reference_id: int
    job_type_id: int
    job_status_id: int
    job_status_name: str
    job_status_description: str
    job_state: str
    submitted_datetime: datetime
    job_name: str
    global_knowledge: bool
    unique_statement_count: int
    user_story_count: int
    acceptance_criteria_count: int
    test_case_count: int
    created_at: datetime
    updated_at: datetime
    business_id: int
    domain_id: int
    deleted: bool
    business_name: str
    domain_name: str
    curation_complete_datetime: Optional[datetime] = None
    document_statistics: Optional[DocumentStatisticsDTO] = None

    def to_dict(self) -> dict:
        """
        Convert the JobDTO to a dictionary.

        Returns:
            dict: A dictionary representation of the JobDTO.
        """
        return asdict(self)
