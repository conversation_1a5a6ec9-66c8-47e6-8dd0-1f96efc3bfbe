from dataclasses import dataclass
from datetime import datetime
from shared.models.workflow_type_enum import WorkflowTypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum

@dataclass
class JobWorkflowDTO:
    job_workflow_id: int
    job_id: int
    workflow_type: WorkflowTypeEnum
    status: JobWorkflowStatusEnum
    run_count: int
    created_at: datetime
    updated_at: datetime
