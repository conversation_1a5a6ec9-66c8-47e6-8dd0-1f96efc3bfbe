from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class DocumentStatisticsDTO:
    """A Data Transfer Object (DTO) representing document statistics and readability metrics.

    This class encapsulates various text analysis metrics including word counts,
    readability scores, and timestamps for document statistics tracking.

    Attributes:
        word_count (int): Total number of words in the document.
        difficult_word_count (int): Number of words considered difficult based on syllable count.
        average_sentence_length (float): Mean number of words per sentence (rounded to decimal_places).
        average_word_length (float): Mean number of characters per word (rounded to decimal_places).
        flesch_grade_level (int): Flesch-Kincaid Grade Level score.
        flesch_score (int): Raw Flesch Reading Ease score.
        flesch_reading_ease (str): Descriptive category of reading ease (e.g., "Easy", "Difficult").
        created_at (datetime): Timestamp when the statistics were first generated.
        updated_at (datetime): Timestamp of the last update to the statistics.
        decimal_places (int): Number of decimal places to round the float values (default: 2).
    """
    word_count: int
    difficult_word_count: int
    average_sentence_length: float
    average_word_length: float
    flesch_grade_level: int
    flesch_score: int
    flesch_reading_ease: str
    created_at: datetime
    updated_at: datetime

    decimal_places: int = field(default=2)

    def __post_init__(self):
        self.average_sentence_length = round(
            self.average_sentence_length, self.decimal_places
        )
        self.average_word_length = round(self.average_word_length, self.decimal_places)
