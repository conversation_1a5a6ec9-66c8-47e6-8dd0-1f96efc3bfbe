from dataclasses import dataclass


@dataclass
class TestCaseGenerationDTO:
    acceptance_criteria_id: int
    user_story_id: int
    unique_statement_id: int
    job_id: int
    document_id: int
    subtopic_id: int
    unique_statement_text: str
    acceptance_criteria_description: str
    user_story_description: str
    subtopic_name: str
    subtopic_description: str
    domain_vector_database_name: str
