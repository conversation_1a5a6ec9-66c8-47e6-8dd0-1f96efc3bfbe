import json
import logging
import re
from datetime import datetime
from typing import List, Optional, Sequence

from sqlalchemy import and_
from sqlalchemy.exc import NoResultFound
from sqlalchemy.orm import joinedload
from sqlalchemy.sql import case, distinct, func, select, text

from accessor.document.dto import (
    AcceptanceCriteriaGenerationDTO,
    DocumentJobDTO,
    DocumentStatisticsDTO,
    JobDTO,
    JobStatusDTO,
    JobWorkflowDTO,
    PolicyRequirementComparisonRequirementDTO,
    TestCaseGenerationDTO,
)
from accessor.document.dto.document_comparison_job_dto import DocumentComparisonJobDTO
from accessor.document.dto.document_job_create_dto import (
    DocumentCreateDTO,
    DocumentJobCreateDTO,
)
from accessor.document.enum.job_sort_order import JobSortOrder
from accessor.document.interfaces import IJobWorkflowAccessor
from accessor.document.interfaces.i_job_accessor import IJobAccessor
from accessor.document.queries.params import DocumentJobQueryParams
from shared.enums.job_enums import JobStatusEnum, JobTypeEnum
from shared.models import (
    AcceptanceCriteria,
    Document,
    Domain,
    Job,
    JobStatus,
    JobStatusSortOrder,
    JobWorkflow,
    JobWorkflowStatusEnum,
    Subtopic,
    Topic,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    UserStory,
    WorkflowType,
    WorkflowTypeEnum,
)
from shared.models.business import Business
from shared.models.test_case import TestCase
from util.config import AWS_REGION, AWS_S3_BUCKET_NAME
from util.database import Session, engine
from util.s3_helper import S3Helper
from util.logging_config import setup_logging
from util.job_logging import JobLogger
from accessor.document.interfaces.i_job_item_workflow import IJobItemWorkflow
from accessor.document.job_item_workflow_accessor import JobItemWorkflow

# Leave this. Must ensure relationships are loaded
# import accessor.document.model.relationships # pylint: disable=unused-import


class DocumentAccessor(IJobWorkflowAccessor, IJobAccessor, IJobItemWorkflow):
    def __init__(self, access_key, secret_key, logger_name):
        self.logger = setup_logging(logger_name)
        self.job_logger = JobLogger(self.logger)
        self.session_factory = Session
        self.s3_service = S3Helper(access_key, secret_key, AWS_REGION, AWS_S3_BUCKET_NAME)
        self.logger.debug("Using database URL", database_url=str(engine.url))
        self._job_item_workflow = JobItemWorkflow(self.session_factory)

    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitizes the given filename by allowing only alphanumerics, underscores, hyphens, and dots.
        """
        return re.sub(r"[^\w\.-]", "_", filename)

    def create_document(self, document_dto: DocumentCreateDTO) -> dict:
        """
        Creates a new document record from a DocumentCreateDTO.

        :param document_dto: An instance of DocumentCreateDTO.
        :return: A dict with success message and the created document_id.
        :raises Exception: If there is any error during the process.
        """
        session = self.session_factory()
        try:
            self.logger.debug("Starting document creation")

            if not (
                document_dto.domain_id
                and document_dto.name
                and document_dto.filename
                and document_dto.document_bytes
            ):
                raise ValueError(
                    "Missing required document fields: "
                    "domain_id, name, filename, and document_bytes are required"
                )

            sanitized_filename = self._sanitize_filename(document_dto.filename)
            self.logger.debug("Sanitized filename: %s", sanitized_filename)

            try:
                self.s3_service.upload_document_to_s3(
                    file_content=document_dto.document_bytes, s3_key=sanitized_filename
                )
                s3_location = f"s3://{self.s3_service.bucket_name}/{sanitized_filename}"
            except Exception as e:
                self.logger.error("Failed to upload document to S3: %s", str(e))
                raise Exception(f"Failed to upload document to S3: {str(e)}") from e

            new_document = Document(
                domain_id=document_dto.domain_id,
                name=document_dto.name,
                business_id=document_dto.business_id,
                start_page=document_dto.start_page,
                end_page=document_dto.end_page,
                effective_datetime=document_dto.effective_datetime or datetime.now(),
                s3_location=s3_location,
                original_document_id=document_dto.original_document_id,
            )
            session.add(new_document)
            session.commit()
            self.logger.debug("Document record created with ID: %s", new_document.document_id)

            response = {
                "success": "File uploaded and document created successfully",
                "document_id": new_document.document_id,
            }
            return response

        except Exception as e:
            self.logger.error("Error processing document: %s", str(e))
            session.rollback()
            raise

    def create_document_and_job(self, document_job_dto: DocumentJobCreateDTO):
        """
        Creates a new document and an associated job concurrently.

        :param document_job_dto: A composite DTO containing both
        document and job creation properties.
        :return: A dict with the created document and job IDs.
        """
        session = self.session_factory()
        try:
            self.logger.debug("Starting document processing for new document and job creation")

            document_dto = document_job_dto.document
            job_dto = document_job_dto.job

            if not (
                document_dto.domain_id
                and document_dto.name
                and document_dto.filename
                and document_dto.document_bytes
            ):
                raise ValueError(
                    "Missing required document fields: "
                    "domain_id, name, filename, and document_bytes are required"
                )

            sanitized_filename = self._sanitize_filename(document_dto.filename)
            self.logger.debug("Sanitized filename: %s", sanitized_filename)

            try:
                self.s3_service.upload_document_to_s3(
                    file_content=document_dto.document_bytes, s3_key=sanitized_filename
                )
                s3_location = f"s3://{self.s3_service.bucket_name}/{sanitized_filename}"
            except Exception as e:
                self.logger.error("Failed to upload document to S3: %s", str(e))
                raise Exception(f"Failed to upload document to S3: {str(e)}") from e

            new_document = Document(
                domain_id=document_dto.domain_id,
                name=document_dto.name,
                business_id=document_dto.business_id,
                start_page=document_dto.start_page,
                end_page=document_dto.end_page,
                effective_datetime=document_dto.effective_datetime or datetime.now(),
                s3_location=s3_location,
                original_document_id=document_dto.original_document_id,
            )
            session.add(new_document)
            session.flush()
            self.logger.debug("Document record created with ID: %s", new_document.document_id)

            new_job = Job(
                reference_id=new_document.document_id,
                job_status_id=(
                    job_dto.job_status
                    if isinstance(job_dto.job_status, int)
                    else job_dto.job_status.value
                ),
                job_type_id=(
                    job_dto.job_type_id.value
                    if hasattr(job_dto.job_type_id, "value")
                    else job_dto.job_type_id
                ),
            )
            session.add(new_job)
            session.commit()
            self.logger.debug("Job record created with ID: %s", new_job.job_id)

            response = {
                "success": "File uploaded and records created successfully",
                "document_id": new_document.document_id,
                "job_id": new_job.job_id,
            }
            return response

        except Exception as e:
            self.logger.error("Error processing document and job: %s", str(e))
            session.rollback()
            raise

    def _build_document_job_dto(
        self, query_params: DocumentJobQueryParams
    ) -> Optional[DocumentJobDTO]:
        """
        Builds a `DocumentJobDTO` instance for a given document and job based on specified criteria.

        This method queries the database to retrieve information about a document and its associated
        job, domain, and job status. If the query is successful, it returns a `DocumentJobDTO`
        containing the retrieved data. The document's binary data can also be included if specified.

        Args:
            query_params (DocumentJobQueryParams): An instance of `DocumentJobQueryParams`
            containing the parameters for querying the document and job information.

        Returns:
            DocumentJobDTO: A DTO containing the document, job, and domain details if the query
            is successful. Includes information such as document ID, name, start and end pages,
            job ID, job type, job status, and domain details. Returns None if no matching record
            is found.

        Raises:
            Exception: Logs any exceptions encountered during the query and DTO creation process.

        Logging:
            Logs various stages of the DTO building process, including:
                - Start of the operation with input parameters.
                - Generated SQL query and its parameters.
                - Query results, if found.
                - Any errors encountered.

        Notes:
            - This method uses a SQLAlchemy session to query the database and closes the session
            after use.
            - Document bytes retrieval from S3 is optional, as it may be an expensive operation.

        """
        self.logger.debug(
            "Starting to build DocumentJobDTO for document_id: %s, job_id: %s, "
            "job_type_ids: %s, job_status_names: %s",
            query_params.document_id,
            query_params.job_id,
            query_params.job_type_ids,
            query_params.job_status_names,
        )

        session = self.session_factory()
        try:
            job_type_ids = (
                [JobTypeEnum(j).value for j in query_params.job_type_ids]
                if isinstance(query_params.job_type_ids, list)
                else [query_params.job_type_ids.value]
            )

            # Step 1: Get the Job and JobStatus
            job_result = (
                session.query(Job, JobStatus)
                .join(JobStatus, Job.job_status_id == JobStatus.job_status_id)
                .filter(
                    and_(
                        Job.job_id == query_params.job_id,
                        Job.job_type_id.in_(job_type_ids),
                        JobStatus.job_status_id.in_(
                            [status.value for status in query_params.job_status_names]
                        ),
                    )
                )
                .first()
            )

            if not job_result:
                self.logger.debug("No matching job found for job_id: %s", query_params.job_id)
                return None

            job, job_status = job_result

            # Step 2: Decide which document to use (updated or original)
            doc_id_to_use = query_params.document_id
            if job.reference_id != query_params.document_id:
                self.logger.debug(
                    "VERSION_COMPARISON scenario — using original document_id: %s", doc_id_to_use
                )

            # Step 3: Fetch Document + Domain in a single query
            doc_result = (
                session.query(Document, Domain)
                .join(Domain, Document.domain_id == Domain.domain_id)
                .filter(Document.document_id == doc_id_to_use)
                .first()
            )

            if not doc_result:
                self.logger.warning("Document+Domain not found for document_id: %s", doc_id_to_use)
                return None

            document, domain = doc_result

            # Step 4: Optionally fetch document bytes
            document_bytes = None
            if query_params.include_document_bytes:
                document_bytes = self.s3_service.get_document_from_s3(document.s3_location)

            if job.job_type_id == JobTypeEnum.VERSION_COMPARISON.value:
                # Fetch the original document object if available
                original_document = session.query(Document).get(document.original_document_id)

                if not original_document:
                    self.logger.warning(
                        "Original document not found for document_id: %s",
                        document.original_document_id,
                    )
                    return None

                original_document_bytes = None
                if query_params.include_document_bytes:
                    original_document_bytes = self.s3_service.get_document_from_s3(
                        original_document.s3_location
                    )

                return DocumentComparisonJobDTO(
                    document_id=document.document_id,
                    document_bytes=document_bytes,
                    document_name=document.name,
                    document_start_page=document.start_page,
                    document_end_page=document.end_page,
                    document_business_id=document.business_id,
                    document_filename=document.s3_location.split("/")[-1],
                    job_id=job.job_id,
                    job_type_id=job.job_type_id,
                    job_status=job_status.name,
                    domain_id=domain.domain_id,
                    domain_name=domain.name,
                    domain_vector_index_name=domain.vector_database_name,
                    original_document_id=original_document.document_id,
                    original_document_bytes=original_document_bytes,
                )
            else:
                return DocumentJobDTO(
                    document_id=document.document_id,
                    document_bytes=document_bytes,
                    document_name=document.name,
                    document_start_page=document.start_page,
                    document_end_page=document.end_page,
                    document_business_id=document.business_id,
                    document_filename=document.s3_location.split("/")[-1],
                    job_id=job.job_id,
                    job_type_id=job.job_type_id,
                    job_status=job_status.name,
                    domain_id=domain.domain_id,
                    domain_name=domain.name,
                    domain_vector_index_name=domain.vector_database_name,
                )

        except Exception as e:
            self.logger.exception("Failed to build DocumentJobDTO: %s", e)
        finally:
            session.close()

        return None

    def build_acceptance_criteria_generation_dto(
        self,
        user_story_id: int,
    ) -> Optional[AcceptanceCriteriaGenerationDTO]:
        """
        Builds an `AcceptanceCriteriaGenerationDTO` instance for the specified user story.

        This method queries the database to retrieve information related to the given
        user story, including details about the job, document, subtopic, unique statement,
        and associated domain. If the query is successful, an `AcceptanceCriteriaGenerationDTO`
        is returned, containing the retrieved data. If no matching record is found,
        the method returns `None`.

        Args:
            user_story_id (int): The unique identifier of the user story for which to
                build the `AcceptanceCriteriaGenerationDTO`.

        Returns:
            AcceptanceCriteriaGenerationDTO: A DTO containing the user story details,
            job ID, document ID, subtopic information, unique statement text, user story
            description, subtopic name and description, and domain vector database name.
            Returns `None` if no matching record is found.

        Raises:
            Exception: Logs any exceptions encountered during the query and DTO creation process.

        Logging:
            Logs various stages of the DTO building process, including:
                - Start of the operation with the input `user_story_id`.
                - The generated SQL query and its parameters.
                - Query results, if found, including relevant fields.
                - Any errors encountered during the process.

        Notes:
            - This method uses a SQLAlchemy session to query the database and closes
            the session after use.
            - The database query joins several tables to gather all necessary details
            for the `AcceptanceCriteriaGenerationDTO`, including `UserStory`, `UniqueStatement`,
            `Subtopic`, `Document`, `Domain`, and `Job`.
        """
        self.logger.debug(
            "Starting to build AcceptanceCriteriaGenerationDTO for user_story_id: %s",
            user_story_id,
        )
        session = self.session_factory()
        try:
            query = (
                session.query(
                    UserStory.user_story_id,
                    Job.job_id,
                    UniqueStatement.unique_statement_id,
                    Document.document_id,
                    UniqueStatementSubtopicAssociation.subtopic_id,
                    UniqueStatement.text.label("unique_statement_text"),
                    UserStory.description.label("user_story_description"),
                    Subtopic.name.label("subtopic_name"),
                    Subtopic.description.label("subtopic_description"),
                    Domain.vector_database_name.label("domain_vector_database_name"),
                )
                .join(
                    UniqueStatement,
                    UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                )
                .join(
                    UniqueStatementSubtopicAssociation,  # Join via association table
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .join(
                    Subtopic,
                    UniqueStatementSubtopicAssociation.subtopic_id
                    == Subtopic.subtopic_id,  # Use association table for Subtopic join
                )
                .join(Document, UniqueStatement.document_id == Document.document_id)
                .join(Domain, Document.domain_id == Domain.domain_id)
                .join(Job, Document.document_id == Job.reference_id)
                .filter(UserStory.user_story_id == user_story_id)
            )

            result = query.first()
            if result:
                (
                    user_story_id,
                    job_id,
                    unique_statement_id,
                    document_id,
                    subtopic_id,
                    unique_statement_text,
                    user_story_description,
                    subtopic_name,
                    subtopic_description,
                    domain_vector_db_name,
                ) = result

                self.logger.debug(
                    "Query result - user_story_id: %s, unique_statement_text: %s, "
                    "job_id: %s, document_id: %s, "
                    "user_story_description: %s, subtopic_name: %s, "
                    "subtopic_description: %s, domain_vector_database_name: %s",
                    user_story_id,
                    unique_statement_text,
                    job_id,
                    document_id,
                    user_story_description,
                    subtopic_name,
                    subtopic_description,
                    domain_vector_db_name,
                )

                # Construct and return the DTO
                return AcceptanceCriteriaGenerationDTO(
                    user_story_id=user_story_id,
                    job_id=job_id,
                    unique_statement_id=unique_statement_id,
                    document_id=document_id,
                    subtopic_id=subtopic_id,
                    unique_statement_text=unique_statement_text,
                    user_story_description=user_story_description,
                    subtopic_name=subtopic_name,
                    subtopic_description=subtopic_description,
                    domain_vector_database_name=domain_vector_db_name,
                )

            self.logger.debug(
                "No query results found for user_story_id: %s",
                user_story_id,
            )

        except Exception as e:
            self.logger.exception(
                "Failed to retrieve AcceptanceCriteriaGenerationDTO for "
                "user_story_id: %s. Exception: %s",
                user_story_id,
                e,
            )
        finally:
            session.close()
        return None

    def build_policy_requirement_comparison_requirement_dto(
        self,
        document_id: int,
        domain_id: int,
        unique_statement_id: int,
        subtopic_id: int,
        job_id: int,
    ) -> Optional[PolicyRequirementComparisonRequirementDTO]:

        self.logger.debug(
            "Starting to build PolicyRequirementComparisonRequirementDTO for document_id: %s, domain_id: %s, unique_statement_id: %s, subtopic_id: %s, job_id: %s",
            document_id,
            domain_id,
            unique_statement_id,
            subtopic_id,
            job_id,
        )
        session = self.session_factory()
        try:
            # Main query for document, subtopic, and unique statement
            query = (
                session.query(
                    Document.document_id,
                    Subtopic.subtopic_id,
                    Subtopic.name.label("subtopic_name"),
                    Subtopic.description.label("subtopic_description"),
                    UniqueStatement.unique_statement_id,
                    UniqueStatement.text.label("requirement_text"),
                    Domain.domain_id,
                    Domain.name.label("domain_name"),
                    Domain.vector_database_name.label("domain_vector_index_name"),
                    Job.job_id,
                    Job.job_type_id,
                    JobStatus.description.label(
                        "job_status"
                    ),  # Fetch JobStatus.description as job_status
                    Topic.topic_id,
                )
                .select_from(Document)  # Explicit starting point
                .join(
                    UniqueStatement, UniqueStatement.unique_statement_id == unique_statement_id
                )  # Explicit ON condition
                .join(Subtopic, Subtopic.subtopic_id == subtopic_id)  # Join Subtopic explicitly
                .join(Topic, Topic.topic_id == Subtopic.topic_id)
                .join(Domain, Domain.domain_id == domain_id)  # Join Subtopic explicitly
                .join(Job, Job.job_id == job_id)  # Join Subtopic explicitly
                .join(
                    JobStatus, Job.job_status_id == JobStatus.job_status_id
                )  # Join JobStatus explicitly
                .filter(Document.document_id == document_id)  # Filter by document_id
            )

            result = query.first()
            if result:

                (
                    document_id,
                    subtopic_id,
                    subtopic_name,
                    subtopic_description,
                    unique_statement_id,
                    requirement_text,
                    domain_id,
                    domain_name,
                    domain_vector_index_name,
                    job_id,
                    job_type_id,
                    job_status,
                    topic_id,
                ) = result

                self.logger.debug(
                    "Query result - document_id: %s, domain_id: %s, domain_name: %s, "
                    "domain_vector_index_name: %s, subtopic_id: %s, subtopic_name: %s, "
                    "subtopic_description: %s, job_id: %s, job_type_id: %s, job_status: %s, "
                    "unique_requirement_id: %s, unique_requirement_text: %s",
                    document_id,
                    domain_id,
                    domain_name,
                    domain_vector_index_name,
                    subtopic_id,
                    subtopic_name,
                    subtopic_description,
                    job_id,
                    job_type_id,
                    job_status,
                    unique_statement_id,
                    requirement_text,
                )

                # Construct and return the DTO
                return PolicyRequirementComparisonRequirementDTO(
                    document_id=document_id,
                    domain_id=domain_id,
                    domain_name=domain_name,
                    domain_vector_index_name=domain_vector_index_name,
                    subtopic_id=subtopic_id,
                    subtopic_name_description=subtopic_name + " - " + subtopic_description,
                    job_id=job_id,
                    job_type_id=job_type_id,
                    job_status=job_status,
                    unique_statement_id=unique_statement_id,
                    requirement_text=requirement_text,
                    topic_id=topic_id,
                )

            self.logger.debug(
                "No query results found for document_id: %s, domain_id: %s, unique_statement_id: %s, subtopic_id: %s",
                document_id,
                domain_id,
                unique_statement_id,
                subtopic_id,
            )

        except Exception as e:
            self.logger.exception(
                "Failed to retrieve DocumentDomainComparisonRequirementDTO for "
                "document_id: %s, domain_id: %s, unique_statement_id: %s, subtopic_id: %s. Exception: %s",
                document_id,
                domain_id,
                unique_statement_id,
                subtopic_id,
                e,
            )
        finally:
            session.close()
        return None

    def build_test_case_generation_dto(
        self,
        acceptance_criteria_id: int,
    ) -> Optional[TestCaseGenerationDTO]:
        """
        Builds a `TestCaseGenerationDTO` instance for the specified acceptance criteria.

        This method queries the database to retrieve information related to the given
        acceptance criteria, including details about the associated user story, job, document,
        subtopic, unique statement, and domain. If the query is successful, a
        `TestCaseGenerationDTO` is returned, containing the retrieved data. If no matching
        record is found, the method returns `None`.

        Args:
            acceptance_criteria_id (int): The unique identifier of the acceptance criteria
                for which to build the `TestCaseGenerationDTO`.

        Returns:
            TestCaseGenerationDTO: A DTO containing the acceptance criteria details, user story
            ID, job ID, document ID, subtopic information, acceptance criteria description,
            unique statement text, user story description, subtopic name and description,
            and domain vector database name. Returns `None` if no matching record is found.

        Raises:
            Exception: Logs any exceptions encountered during the query and DTO creation process.

        Logging:
            Logs various stages of the DTO building process, including:
                - Start of the operation with the input `acceptance_criteria_id`.
                - The generated SQL query and its parameters.
                - Query results, if found, including relevant fields.
                - Any errors encountered during the process.

        Notes:
            - This method uses a SQLAlchemy session to query the database and closes
            the session after use.
            - The database query joins several tables to gather all necessary details
            for the `TestCaseGenerationDTO`, including `AcceptanceCriteria`, `UserStory`,
            `UniqueStatement`, `Subtopic`, `Document`, `Domain`, and `Job`.
        """
        self.logger.debug(
            "Starting to build TestCaseGenerationDTO for acceptance_criteria_id: %s",
            acceptance_criteria_id,
        )
        session = self.session_factory()
        try:
            query = (
                session.query(
                    AcceptanceCriteria.acceptance_criteria_id,
                    UserStory.user_story_id,
                    UniqueStatement.unique_statement_id,
                    Job.job_id,
                    Document.document_id,
                    UniqueStatementSubtopicAssociation.subtopic_id,
                    AcceptanceCriteria.description.label("acceptance_criteria_description"),
                    UniqueStatement.text.label("unique_statement_text"),
                    UserStory.description.label("user_story_description"),
                    Subtopic.name.label("subtopic_name"),
                    Subtopic.description.label("subtopic_description"),
                    Domain.vector_database_name.label("domain_vector_database_name"),
                )
                .select_from(AcceptanceCriteria)
                .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
                .join(
                    UniqueStatement,
                    UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                )
                .join(
                    UniqueStatementSubtopicAssociation,  # Join via association table
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .join(
                    Subtopic,
                    UniqueStatementSubtopicAssociation.subtopic_id
                    == Subtopic.subtopic_id,  # Use association table for Subtopic join
                )
                .join(Document, UniqueStatement.document_id == Document.document_id)
                .join(Domain, Document.domain_id == Domain.domain_id)
                .join(Job, Document.document_id == Job.reference_id)
                .filter(AcceptanceCriteria.acceptance_criteria_id == acceptance_criteria_id)
            )

            result = query.first()

            if result:
                (
                    acceptance_criteria_id,
                    user_story_id,
                    unique_statement_id,
                    job_id,
                    document_id,
                    subtopic_id,
                    acceptance_criteria_description,
                    unique_statement_text,
                    user_story_description,
                    subtopic_name,
                    subtopic_description,
                    domain_vector_db_name,
                ) = result

                self.logger.debug(
                    "Query result - acceptance_criteria_id: %s, "
                    "user_story_id: %s, "
                    "job_id: %s, document_id: %s, "
                    "acceptance_criteria_description: %s, "
                    "unique_statement_text: %s, "
                    "user_story_description: %s, subtopic_name: %s, "
                    "subtopic_description: %s, domain_vector_database_name: %s",
                    acceptance_criteria_id,
                    user_story_id,
                    job_id,
                    document_id,
                    acceptance_criteria_description,
                    unique_statement_text,
                    user_story_description,
                    subtopic_name,
                    subtopic_description,
                    domain_vector_db_name,
                )

                return TestCaseGenerationDTO(
                    acceptance_criteria_id=acceptance_criteria_id,
                    user_story_id=user_story_id,
                    unique_statement_id=unique_statement_id,
                    job_id=job_id,
                    document_id=document_id,
                    subtopic_id=subtopic_id,
                    acceptance_criteria_description=acceptance_criteria_description,
                    unique_statement_text=unique_statement_text,
                    user_story_description=user_story_description,
                    subtopic_name=subtopic_name,
                    subtopic_description=subtopic_description,
                    domain_vector_database_name=domain_vector_db_name,
                )

            self.logger.debug(
                "No query results found for acceptance_criteria_id: %s",
                acceptance_criteria_id,
            )

        except Exception as e:
            self.logger.exception(
                "Failed to retrieve TestCaseGenerationDTO for "
                "acceptance_criteria_id: %s. Exception: %s",
                acceptance_criteria_id,
                e,
            )
        finally:
            session.close()
        return None

    def prepare_document_in_initial_processing(
        self, document_id: int, job_id: int
    ) -> Optional[DocumentJobDTO]:
        """
        Prepares a `DocumentJobDTO` for a document in the initial processing stage.

        This method sets up query parameters to filter for a document and job associated
        with the initial processing status. It constructs and returns a DTO containing
        relevant details about the document and job, including the document's binary data.

        Args:
            document_id (int): The unique identifier of the document.
            job_id (int): The unique identifier of the job.

        Returns:
            DocumentJobDTO: A data transfer object with details about the document and job.
        """
        query_params = DocumentJobQueryParams(
            document_id=document_id,
            job_id=job_id,
            job_type_ids=[
                JobTypeEnum.PROCESS_DOCUMENT.value,
                JobTypeEnum.ARTIFACT_GENERATION.value,
                JobTypeEnum.PROCEDURE_IMPORT.value,
                JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value,
                JobTypeEnum.OPTIMIZE_PROCEDURE.value,
                JobTypeEnum.VERSION_COMPARISON.value,
            ],
            job_status_names=[JobStatusEnum.INITIAL_PROCESSING],
            include_document_bytes=True,
        )
        return self._build_document_job_dto(query_params)

    def prepare_document_in_chunk_curation(self, document_id: int, job_id: int) -> DocumentJobDTO:
        """
        Prepares a `DocumentJobDTO` for a document transitioning to the chunk curation stage.

        This method sets up query parameters to filter for a document and job associated
        with a specific status related to chunk curation. It constructs and returns a DTO
        containing relevant details about the document and job.

        Args:
            document_id (int): The unique identifier of the document.
            job_id (int): The unique identifier of the job.

        Returns:
            DocumentJobDTO: A data transfer object with details about the document and job.
        """
        query_params = DocumentJobQueryParams(
            document_id=document_id,
            job_id=job_id,
            job_type_ids=[
                JobTypeEnum.PROCESS_DOCUMENT.value,
                JobTypeEnum.ARTIFACT_GENERATION.value,
                JobTypeEnum.FEDERAL_REGISTER_CHANGE_IDENTIFICATION.value,
                JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value,
            ],
            job_status_names=[JobStatusEnum.READY_FOR_CHUNK_CURATION],
            include_document_bytes=False,
        )
        return self._build_document_job_dto(query_params)

    def prepare_document_in_requirement_curation(
        self, document_id: int, job_id: int
    ) -> DocumentJobDTO:
        """
        Prepares a `DocumentJobDTO` for a document in various stages of requirement curation.

        This method sets up query parameters to filter for a document and job associated
        with multiple statuses related to the requirement curation process. It constructs
        and returns a DTO containing relevant details about the document and job.

        Args:
            document_id (int): The unique identifier of the document.
            job_id (int): The unique identifier of the job.

        Returns:
            DocumentJobDTO: A data transfer object with details about the document and job.
        """
        query_params = DocumentJobQueryParams(
            document_id=document_id,
            job_id=job_id,
            job_type_ids=[
                JobTypeEnum.PROCESS_DOCUMENT.value,
                JobTypeEnum.ARTIFACT_GENERATION.value,
                JobTypeEnum.FEDERAL_REGISTER_CHANGE_IDENTIFICATION.value,
                JobTypeEnum.MARKUP_CHANGE_IDENTIFICATION.value,
                JobTypeEnum.OPTIMIZE_PROCEDURE.value,
            ],
            job_status_names=[
                JobStatusEnum.READY_FOR_REQUIREMENT_CURATION,
                JobStatusEnum.GENERATING_USER_STORIES,
            ],
            include_document_bytes=False,
        )
        return self._build_document_job_dto(query_params)

    def job_error(self, job_id: int):
        """
        Updates the job status to "Error" and logs detailed information about the failed job.

        Args:
            job_id (int): The unique identifier of the job.
        """
        try:
            self.job_logger.log_job_error(job_id)
            self.update_job_status(job_id, JobStatusEnum.JOB_FAILURE)
        except Exception as e:
            self.logger.error("Failed to process job error", job_id=job_id, exc_info=e)
            raise

    def job_ready_for_chunk_curation(self, job_id: int):
        """
        Updates the job status to "Ready for Chunk Curation."

        Args:
            job_id (int): The unique identifier of the job.
        """
        self.update_job_status(job_id, JobStatusEnum.READY_FOR_CHUNK_CURATION)

    def job_ready_for_subtopic_curation(self, job_id: int):
        """
        Updates the job status to "Ready For Subtopic Curation"

        Args:
            job_id (int): The unique identifier of the job.
        """
        self.update_job_status(job_id, JobStatusEnum.SUBTOPICS_READY_FOR_CURATION)

    def job_ready_for_requirement_curation(self, job_id: int):
        """
        Updates the job status to "Ready for Requirement Curation."

        Args:
            job_id (int): The unique identifier of the job.
        """
        self.update_job_status(job_id, JobStatusEnum.READY_FOR_REQUIREMENT_CURATION)

    def job_generating_requirements(self, job_id: int):
        """
        Updates the job status to "Generating Requirements."

        Args:
            job_id (int): The unique identifier of the job.
        """
        self.update_job_status(job_id, JobStatusEnum.GENERATING_REQUIREMENTS)

    def job_complete(self, job_id: int):
        """
        Updates the job status to "Ready for Final Review and Export."

        Args:
            job_id (int): The unique identifier of the job.
        """
        self.update_job_status(job_id, JobStatusEnum.JOB_COMPLETE)

    def update_job_status(self, job_id: int, job_status: JobStatusEnum):
        """
        Updates the status of the specified job to the given status.

        This method sets the `job_status_id` of the specified job to the corresponding
        value from the provided `JobStatusEnum`. It retrieves the job using the given
        job ID, and if the job is found, updates its status. If the job is not found,
        a `ValueError` is raised. If an exception occurs during the process, the
        transaction is rolled back, and the error is logged.

        Args:
            job_id (int): The unique identifier of the job to be updated.
            job_status (JobStatusEnum): The new status for the job.

        Raises:
            ValueError: If the job with the specified ID is not found.

        Logging:
            - Logs an informational message if the job status is successfully updated.
            - Logs an exception message if an error occurs during the update process.
        """
        session = self.session_factory()
        try:
            job_status_id = job_status.value
            job = session.query(Job).filter_by(job_id=job_id).first()
            if job is None:
                raise ValueError(f"Job with id {job_id} not found")
            job.job_status_id = job_status_id
            session.commit()
            self.logger.info(f'Job with id %s updated to status "{job_status.name}"', job_id)
        except Exception:
            session.rollback()
            self.logger.exception("Error updating job status.")
        finally:
            session.close()

    def generate_requirement_identifiers(self, document_id):
        session = self.session_factory()
        try:
            sql = text("CALL generate_requirement_identifiers(:document_id)")
            session.execute(sql, {"document_id": document_id})
            session.commit()
            self.logger.info(
                "Document with id %s had requirement identifiers generated", document_id
            )
        except Exception:
            session.rollback()
            self.logger.exception("Error generating document requirement identifiers.")
        finally:
            session.close()

    def generate_artifact_identifiers(self, document_id):
        session = self.session_factory()
        try:
            sql = text("CALL generate_artifact_identifiers(:document_id)")
            session.execute(sql, {"document_id": document_id})
            session.commit()

            self.logger.info("Document with id %s had artifact identifiers generated", document_id)
        except Exception:
            session.rollback()
            self.logger.exception("Error generating document artifact identifiers.")
        finally:
            session.close()

    def generate_user_story_identifiers(self, document_id):
        session = self.session_factory()
        try:
            sql = text("CALL generate_user_story_identifiers(:document_id)")
            session.execute(sql, {"document_id": document_id})
            session.commit()

            self.logger.info(
                "Document with id %s had user story identifiers generated", document_id
            )
        except Exception:
            session.rollback()
            self.logger.exception("Error generating user story identifiers.")
        finally:
            session.close()

    def generate_acceptance_criteria_identifiers(self, document_id):
        session = self.session_factory()
        try:
            sql = text("CALL generate_acceptance_criteria_identifiers(:document_id)")
            session.execute(sql, {"document_id": document_id})
            session.commit()

            self.logger.info(
                "Document with id %s had acceptance criteria identifiers generated", document_id
            )
        except Exception:
            session.rollback()
            self.logger.exception("Error generating acceptance criteria identifiers.")
        finally:
            session.close()

    def generate_test_case_identifiers(self, document_id):
        session = self.session_factory()
        try:
            sql = text("CALL generate_test_case_identifiers(:document_id)")
            session.execute(sql, {"document_id": document_id})
            session.commit()

            self.logger.info("Document with id %s had test case identifiers generated", document_id)
        except Exception:
            session.rollback()
            self.logger.exception("Error generating test case identifiers.")
        finally:
            session.close()

    def create_job_workflow(
        self, job_id: int, workflow_type: WorkflowTypeEnum, run_count: int
    ) -> JobWorkflowDTO:
        with self.session_factory() as session:
            job_workflow = JobWorkflow(
                job_id=job_id, workflow_type_id=workflow_type.workflow_type_id, run_count=run_count
            )
            session.add(job_workflow)
            session.commit()
            return JobWorkflowDTO(
                job_workflow_id=job_workflow.job_workflow_id,  # type: ignore
                job_id=job_workflow.job_id,  # type: ignore
                workflow_type=workflow_type,
                status=JobWorkflowStatusEnum.STARTING,
                run_count=job_workflow.run_count,  # type: ignore
                created_at=job_workflow.created_at,  # type: ignore
                updated_at=job_workflow.updated_at,  # type: ignore
            )

    def update_job_workflow(self, job_workflow: JobWorkflowDTO, new_status: JobWorkflowStatusEnum):
        with self.session_factory() as session:
            session.query(JobWorkflow).filter_by(
                job_workflow_id=job_workflow.job_workflow_id
            ).update(
                {
                    JobWorkflow.status: new_status,  # type: ignore
                    JobWorkflow.run_count: job_workflow.run_count,
                }
            )
            session.commit()

    def get_job_workflow(
        self, job_id: int, workflow_type: WorkflowTypeEnum
    ) -> Optional[JobWorkflowDTO]:
        with self.session_factory() as session:
            try:
                result = (
                    session.query(JobWorkflow, WorkflowType)
                    .join(
                        WorkflowType, JobWorkflow.workflow_type_id == WorkflowType.workflow_type_id
                    )
                    .filter(JobWorkflow.job_id == job_id)
                    .filter(WorkflowType.workflow_type_id == workflow_type.workflow_type_id)
                    .one()
                )
                job_workflow = result[0]
                workflow_type_name = result[1].name
                job_workflow_dto = JobWorkflowDTO(
                    job_workflow_id=job_workflow.job_workflow_id,
                    job_id=job_workflow.job_id,
                    workflow_type=WorkflowTypeEnum.from_name(workflow_type_name),
                    status=JobWorkflowStatusEnum(job_workflow.status),
                    run_count=job_workflow.run_count,
                    created_at=job_workflow.created_at,
                    updated_at=job_workflow.updated_at,
                )
                return job_workflow_dto
            except NoResultFound:
                return None

    def get_job_type_id_by_document(self, document_id: int) -> Optional[int]:
        with self.session_factory() as session:
            try:
                job_type_id = (
                    session.query(Job.job_type_id).filter(Job.reference_id == document_id).scalar()
                )
                if not job_type_id:
                    return None
                try:
                    return job_type_id
                except ValueError as e:
                    self.logger.error(
                        "Invalid job_type_id '%s' for document_id '%s': %s",
                        job_type_id,
                        document_id,
                        e,
                    )
                    return None
            except Exception as e:
                self.logger.exception(
                    "Error fetching job type for document_id '%s': %s", document_id, e
                )
                return None

    def _load_test_case_generation_dto_from_result(self, result) -> Optional[TestCaseGenerationDTO]:
        """
        Creates a `TestCaseGenerationDTO` from the given query result.

        Args:
            result (tuple): A tuple containing the query result values in the expected order.

        Returns:
            TestCaseGenerationDTO: The constructed DTO with values populated from the query result.
        """
        fields = {
            "acceptance_criteria_id": result[0],
            "user_story_id": result[1],
            "unique_statement_id": result[2],
            "job_id": result[3],
            "document_id": result[4],
            "subtopic_id": result[5],
            "acceptance_criteria_description": result[6],
            "unique_statement_text": result[7],
            "user_story_description": result[8],
            "subtopic_name": result[9],
            "subtopic_description": result[10],
            "domain_vector_database_name": result[11],
        }

        self.logger.debug("Loaded TestCaseGenerationDTO - %s", fields)
        return TestCaseGenerationDTO(**fields)

    def get_jobs(
        self,
        business_id: Optional[int] = None,
        domain_id: Optional[int] = None,
        job_status_id: Optional[int] = None,
        sort_by: JobSortOrder = JobSortOrder.DEFAULT,
        ascending: bool = True,
    ) -> List[JobDTO]:
        """
        Retrieve jobs and update related data counts based on the business ID,
        domain ID, and job status ID. If no business_id is provided, all businesses
        will be included. Additional filters can be applied for domain_id and job_status_id.
        The sort order is determined by the sort_by parameter, and the order can be
        ascending or descending.

        Args:
            business_id (int, optional): The ID of the business for filtering jobs. If None,
                                        jobs from all businesses will be included. Defaults to None.
            domain_id (int, optional): The ID of the domain for filtering jobs. If None,
                no domain filter will be applied. Defaults to None.
            job_status_id (int, optional): The ID of the job status for filtering jobs.
                If None, no status filter will be applied. Defaults to None.
            sort_by (JobSortOrder, optional): The field to sort by. Options include various
                                            sort orders such as "DEFAULT", "CREATED_DATE",
                                            "NAME", and "STATUS". Defaults to "DEFAULT".
            ascending (bool, optional): Whether to sort in ascending order. Defaults to True.

        Returns:
            List[JobDTO]: A list of JobDTO objects with updated related data counts.
        """
        # Define subqueries once to avoid duplication
        unique_statement_subq = (
            # pylint: disable=not-callable
            select(func.count(distinct(UniqueStatement.unique_statement_id)))
            .where(UniqueStatement.document_id == Document.document_id)
            .scalar_subquery()
        )
        user_story_subq = (
            # pylint: disable=not-callable
            select(func.count(distinct(UserStory.user_story_id)))
            .join(
                UniqueStatement,
                UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
            )
            .where(UniqueStatement.document_id == Document.document_id)
            .scalar_subquery()
        )
        acceptance_criteria_subq = (
            # pylint: disable=not-callable
            select(func.count(distinct(AcceptanceCriteria.acceptance_criteria_id)))
            .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
            .join(
                UniqueStatement,
                UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
            )
            .where(UniqueStatement.document_id == Document.document_id)
            .scalar_subquery()
        )
        test_case_subq = (
            # pylint: disable=not-callable
            select(func.count(distinct(TestCase.test_case_id)))
            .join(
                AcceptanceCriteria,
                TestCase.acceptance_criteria_id == AcceptanceCriteria.acceptance_criteria_id,
            )
            .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
            .join(
                UniqueStatement,
                UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
            )
            .where(UniqueStatement.document_id == Document.document_id)
            .scalar_subquery()
        )

        with self.session_factory() as session:
            jobs_query = (
                session.query(Job)
                .join(Document, Job.reference_id == Document.document_id)
                .join(Business, Document.business_id == Business.business_id)
                .join(Domain, Document.domain_id == Domain.domain_id)  # Explicitly join Domain
                .filter(Job.deleted.is_(False))
                # Load all needed relationships eagerly
                .options(
                    joinedload(Job.document).joinedload(Document.business),
                    joinedload(Job.document).joinedload(Document.domain),
                    joinedload(Job.document).joinedload(Document.document_statistics),
                    joinedload(Job.job_status),
                )
                .add_columns(
                    Business.name.label("business_name"),  # Extract in main query
                    Domain.name.label("domain_name"),  # Extract in main query
                    case(
                        (
                            Job.job_status_id == JobStatusEnum.JOB_COMPLETE.value,
                            unique_statement_subq,
                        ),
                        else_=0,
                    ).label("unique_statement_count"),
                    case(
                        (
                            Job.job_status_id == JobStatusEnum.JOB_COMPLETE.value,
                            user_story_subq,
                        ),
                        else_=0,
                    ).label("user_story_count"),
                    case(
                        (
                            Job.job_status_id == JobStatusEnum.JOB_COMPLETE.value,
                            acceptance_criteria_subq,
                        ),
                        else_=0,
                    ).label("acceptance_criteria_count"),
                    case(
                        (
                            Job.job_status_id == JobStatusEnum.JOB_COMPLETE.value,
                            test_case_subq,
                        ),
                        else_=0,
                    ).label("test_case_count"),
                )
            )

            if business_id is not None:
                jobs_query = jobs_query.filter(Business.business_id == business_id)

            if domain_id is not None:
                jobs_query = jobs_query.filter(Document.domain_id == domain_id)

            if job_status_id is not None:
                jobs_query = jobs_query.filter(Job.job_status_id == job_status_id)

            if sort_by in {JobSortOrder.DEFAULT, JobSortOrder.STATUS}:
                # Sort by the custom job status sort order
                jobs_query = jobs_query.join(
                    JobStatusSortOrder,  # Use the model class here
                    Job.job_status_id == JobStatusSortOrder.job_status_id,
                ).order_by(
                    JobStatusSortOrder.sort_order.asc()
                    if ascending
                    else JobStatusSortOrder.sort_order.desc()
                )
            elif sort_by == JobSortOrder.CREATED_DATE:
                jobs_query = jobs_query.order_by(
                    Job.created_at.asc() if ascending else Job.created_at.desc()
                )
            elif sort_by == JobSortOrder.NAME:
                jobs_query = jobs_query.order_by(
                    Document.name.asc() if ascending else Document.name.desc()
                )

            results = jobs_query.all()

            job_dto_list = []

            # Preprocess document statistics in bulk
            for (
                job,
                business_name,
                domain_name,
                unique_statement_count,
                user_story_count,
                acceptance_criteria_count,
                test_case_count,
            ) in results:
                document_stats = None
                if job.document.document_statistics is not None:
                    stats = job.document.document_statistics
                    document_stats = DocumentStatisticsDTO(
                        word_count=stats.word_count,
                        difficult_word_count=stats.difficult_word_count,
                        average_sentence_length=stats.average_sentence_length,
                        average_word_length=stats.average_word_length,
                        flesch_grade_level=stats.flesch_grade_level,
                        flesch_score=stats.flesch_score,
                        flesch_reading_ease=stats.flesch_reading_ease,
                        created_at=stats.created_at,
                        updated_at=stats.updated_at,
                    )

                job_dto = JobDTO(
                    job_id=job.job_id,
                    reference_id=job.reference_id,
                    job_type_id=job.job_type_id,
                    job_status_id=job.job_status_id,
                    job_status_name=job.job_status.name if job.job_status else None,
                    job_status_description=(job.job_status.description if job.job_status else None),
                    job_state=job.job_status.state if job.job_status else None,
                    submitted_datetime=job.submitted_datetime,
                    job_name=job.job_name,
                    global_knowledge=job.global_knowledge,
                    unique_statement_count=unique_statement_count,
                    user_story_count=user_story_count,
                    acceptance_criteria_count=acceptance_criteria_count,
                    test_case_count=test_case_count,
                    created_at=job.created_at,
                    updated_at=job.updated_at,
                    business_id=job.document.business_id,
                    domain_id=job.document.domain_id,
                    deleted=job.deleted,
                    business_name=business_name,  # Use data from the main query
                    domain_name=domain_name,  # Use data from the main query
                    curation_complete_datetime=job.curation_complete_datetime,
                    document_statistics=document_stats,
                )

                job_dto_list.append(job_dto)

            return job_dto_list

    def get_job_statuses_with_sort_order(self) -> List[JobStatusDTO]:
        """
        Retrieve all job statuses with their sort order.

        Returns:
            List[JobStatusDTO]: A list of JobStatusDTO objects with their associated sort order.
        """
        with self.session_factory() as session:
            job_statuses = (
                session.query(JobStatus, JobStatusSortOrder.sort_order)
                .join(
                    JobStatusSortOrder, JobStatus.job_status_id == JobStatusSortOrder.job_status_id
                )
                .order_by(JobStatusSortOrder.sort_order)
                .all()
            )

            return [
                JobStatusDTO(
                    job_status_id=job_status.job_status_id,
                    name=job_status.name,
                    description=job_status.description,
                    state=job_status.state,
                    sort_order=sort_order,
                )
                for job_status, sort_order in job_statuses
            ]

    # ---- IJobItemWorkflow implementation ----------------------------------
    def seed_items(
        self,
        job_workflow_id: int,
        item_keys: Sequence[str],
        item_type: JobItemTypeEnum = JobItemTypeEnum.CHUNK,
    ) -> None:
        return self._job_item_workflow.seed_items(job_workflow_id, item_keys, item_type)

    def reserve_next_pending(self, job_workflow_id: int) -> Optional[str]:
        return self._job_item_workflow.reserve_next_pending(job_workflow_id)

    def mark_done(self, job_workflow_id: int, item_key: str) -> None:
        return self._job_item_workflow.mark_done(job_workflow_id, item_key)

    def mark_failed(
        self,
        job_workflow_id: int,
        item_key: str,
        error_txt: str | None = None,
    ) -> None:
        return self._job_item_workflow.mark_failed(job_workflow_id, item_key, error_txt)

    def list_incomplete(self, job_workflow_id: int) -> List[str]:
        return self._job_item_workflow.list_incomplete(job_workflow_id)
