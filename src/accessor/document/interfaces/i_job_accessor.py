"""
job_service.py

This module provides functions for accessing and managing job-related data.
The main function, `get_jobs`, retrieves jobs from the database and calculates
related data counts based on the provided business ID. The function also includes
logic to handle cases where all businesses should be included if no specific
business ID is provided.

Functions:
    get_jobs(business_id: int = None) -> List[Job]:
        Retrieves a list of jobs with calculated related data counts such as
        unique statements, user stories, acceptance criteria, and test cases.

Usage:
    # Retrieve jobs for a specific business
    jobs = get_jobs(business_id=123)

    # Retrieve jobs for all businesses
    all_jobs = get_jobs()

Notes:
    - The `Session` is created and managed internally within the function, ensuring
      that resources are properly released.
    - The function relies on SQLAlchemy for querying the database and performing
      data calculations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional

from accessor.document.dto import JobStatusDTO
from accessor.document.dto.job_dto import JobDTO


class IJobAccessor(ABC):
    """
    Interface for accessing job-related data.
    """

    @abstractmethod
    def get_jobs(self, business_id: Optional[int] = None) -> List[JobDTO]:
        """
        Retrieve jobs and update related data counts based on the business ID.
        If no business_id is provided, all businesses will be included.

        Args:
            business_id (int, optional): The ID of the business for filtering jobs. If None,
            jobs from all businesses will be included. Defaults to None.

        Returns:
            List[Job]: A list of Job objects with updated related data counts.
        """

    @abstractmethod
    def get_job_statuses_with_sort_order(self) -> List[JobStatusDTO]:
        """
        Retrieve all job statuses with their sort order.

        Returns:
            List[JobStatus]: A list of JobStatus objects with their associated sort order.
        """
