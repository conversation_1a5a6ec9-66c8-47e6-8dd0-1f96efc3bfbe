"""Module containing interface for job item workflow access."""

from abc import ABC, abstractmethod
from typing import List, Optional, Sequence

from shared.enums.job_enums import JobItemTypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum


class IJobItemWorkflow(ABC):
    """CRUD + reservation operations for per-item workflow rows."""

    # ---- creation / seeding ------------------------------------------------
    @abstractmethod
    def seed_items(
        self,
        job_workflow_id: int,
        item_keys: Sequence[str],
        item_type: JobItemTypeEnum = JobItemTypeEnum.CHUNK,
    ) -> None:
        """Seed workflow items for a job workflow."""
        ...

    # ---- worker side -------------------------------------------------------
    @abstractmethod
    def reserve_next_pending(self, job_workflow_id: int) -> Optional[str]:
        """Reserve the next pending item for processing."""
        ...

    @abstractmethod
    def mark_done(self, job_workflow_id: int, item_key: str) -> None:
        """Mark an item as completed."""
        ...

    @abstractmethod
    def mark_failed(
        self, job_workflow_id: int, item_key: str, error_txt: str | None = None
    ) -> None:
        """Mark an item as failed with optional error text."""
        ...

    @abstractmethod
    def list_incomplete(self, job_workflow_id: int) -> List[str]:
        """List all incomplete items for a job workflow."""
        ... 