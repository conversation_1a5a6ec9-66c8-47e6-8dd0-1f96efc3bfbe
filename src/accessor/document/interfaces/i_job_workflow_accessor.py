"""Module containing interface(s) for workflow access."""

from abc import ABC, abstractmethod
from typing import Optional

from accessor.document.dto.job_workflow_dto import JobWorkflowDTO
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from shared.models.workflow_type_enum import WorkflowTypeEnum


class IJ<PERSON><PERSON>orkflowAccessor(ABC):
    """Interface for accessing JobWorkflow"""

    @abstractmethod
    def create_job_workflow(
        self, job_id: int, workflow_type: WorkflowTypeEnum, run_count: int
    ) -> JobWorkflowDTO:
        """Creates a JobWorkflow"""

    @abstractmethod
    def update_job_workflow(self, job_workflow: JobWorkflowDTO, new_status: <PERSON>WorkflowStatusEnum):
        """Updates a JobWorkflow's status"""

    @abstractmethod
    def get_job_workflow(
        self, job_id: int, workflow_type: WorkflowTypeEnum
    ) -> Optional[JobWorkflowDTO]:
        """Gets a JobWorkflow with the matching type and job id."""
