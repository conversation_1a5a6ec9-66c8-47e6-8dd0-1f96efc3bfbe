"""Module containing implementation of job item workflow access."""

import datetime
from typing import List, Optional, Sequence

from sqlalchemy import select, update
from sqlalchemy.exc import NoResultFound

from accessor.document.interfaces.i_job_item_workflow import IJobItemWorkflow
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.job_item_workflow import JobItemWorkflow as JobItemWorkflowModel
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from util.logging_config import setup_logging


class JobItemWorkflow(IJobItemWorkflow):
    """Implements the reservation pattern with a DB-level atomic UPDATE … RETURNING."""

    def __init__(self, session_factory):
        self.session_factory = session_factory
        self.logger = setup_logging("phoenix-burst-api.accessor.job-item-workflow")

    # ------------- seeding --------------------------------------------------
    def seed_items(
        self,
        job_workflow_id: int,
        item_keys: Sequence[str],
        item_type: JobItemTypeEnum = JobItemTypeEnum.CHUNK,
    ) -> None:
        with self.session_factory() as s, s.begin():
            existing = {
                row[0]
                for row in s.execute(
                    select(JobItemWorkflowModel.item_key)
                    .where(JobItemWorkflowModel.job_workflow_id == job_workflow_id)
                )
            }
            for key in item_keys:
                if key in existing:
                    continue
                s.add(
                    JobItemWorkflowModel(
                        job_workflow_id=job_workflow_id,
                        item_type=item_type,
                        status=JobWorkflowStatusEnum.STARTING,
                        run_count=0,
                        item_key=key,
                    )
                )

    # ------------- reservation (atomic) ------------------------------------
    def reserve_next_pending(self, job_workflow_id: int) -> Optional[str]:
        with self.session_factory() as s, s.begin():
            stmt = (
                update(JobItemWorkflowModel)
                .where(
                    JobItemWorkflowModel.job_workflow_id == job_workflow_id,
                    JobItemWorkflowModel.status == JobWorkflowStatusEnum.STARTING,
                )
                .order_by(JobItemWorkflowModel.updated_at)  # oldest first
                .limit(1)
                .values(
                    status=JobWorkflowStatusEnum.IN_PROGRESS,
                    run_count=JobItemWorkflowModel.run_count + 1,
                    updated_at=datetime.datetime.utcnow(),
                )
                .returning(JobItemWorkflowModel.item_key)
            )
            res = s.execute(stmt).first()
            if res:
                self.logger.debug("Reserved item %s for job_run %s", res[0], job_workflow_id)
                return res[0]
            return None

    # ------------- completion / failure ------------------------------------
    def mark_done(self, job_workflow_id: int, item_key: str) -> None:
        self._set_status(job_workflow_id, item_key, JobWorkflowStatusEnum.COMPLETED)

    def mark_failed(
        self,
        job_workflow_id: int,
        item_key: str,
        error_txt: str | None = None,
    ) -> None:
        self._set_status(
            job_workflow_id,
            item_key,
            JobWorkflowStatusEnum.FAILED,
            extra={"last_error": error_txt},
        )

    # ------------- list unfinished -----------------------------------------
    def list_incomplete(self, job_workflow_id: int) -> List[str]:
        with self.session_factory() as s:
            rows = s.execute(
                select(JobItemWorkflowModel.item_key).where(
                    JobItemWorkflowModel.job_workflow_id == job_workflow_id,
                    JobItemWorkflowModel.status.in_(
                        (
                            JobWorkflowStatusEnum.IN_PROGRESS,
                            JobWorkflowStatusEnum.FAILED,
                        )
                    ),
                )
            )
            return [r[0] for r in rows]

    # ------------- internal helper -----------------------------------------
    def _set_status(
        self,
        job_workflow_id: int,
        item_key: str,
        new_status: JobWorkflowStatusEnum,
        extra: dict | None = None,
    ) -> None:
        extra = extra or {}
        with self.session_factory() as s, s.begin():
            stmt = (
                update(JobItemWorkflowModel)
                .where(
                    JobItemWorkflowModel.job_workflow_id == job_workflow_id,
                    JobItemWorkflowModel.item_key == item_key,
                )
                .values(status=new_status, updated_at=datetime.datetime.utcnow(), **extra)
            )
            if s.execute(stmt).rowcount == 0:
                raise NoResultFound(
                    f"No job_item_workflow row for ({job_workflow_id}, '{item_key}')"
                ) 