"""
Parameter classes for document-related operations.

This module defines parameter classes used for querying or executing commands
related to document operations within the application. These classes encapsulate
the various criteria and parameters needed for performing document-specific 
operations, such as querying job details associated with a document.

Classes:
    DocumentJobQueryParams: Encapsulates the parameters used for querying a 
        document and its associated job, including document ID, job ID, job type,
        job status filters, and an option to include document bytes.
        
Usage:
    These parameter classes are intended to be used as input for services or 
    methods that require structured criteria for querying documents or executing 
    document-related commands.
"""

from dataclasses import dataclass
from typing import List


@dataclass
class DocumentJobQueryParams:
    """
    Encapsulates the parameters used for querying a document and its associated job.

    This class is used to provide a structured way to specify the criteria for
    retrieving information about a document and its related job details from the
    database. It includes parameters such as document ID, job ID, job type,
    and a list of acceptable job statuses for filtering results. Optionally,
    it can also specify whether to include the document's binary data.

    Attributes:
        document_id (int): The unique identifier for the document being queried.
        job_id (int): The unique identifier for the job associated with the document.
        job_type (str): The type of the job being queried, used for filtering.
        job_status_names (List[str]): A list of job status names used to filter the
            query results. Only jobs with a matching status will be returned.
        include_document_bytes (bool): If True, the query will also retrieve the
            document's binary data. Defaults to False.
    """

    document_id: int
    job_id: int
    job_type_ids: List[int]
    job_status_names: List[str]
    include_document_bytes: bool = False
