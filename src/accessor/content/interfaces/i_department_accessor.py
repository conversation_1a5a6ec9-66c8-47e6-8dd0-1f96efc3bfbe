from abc import ABC, abstractmethod
from typing import List

from shared.models import Department


class IDepartmentAccessor(ABC):

    @abstractmethod
    def get_department_by_id(self, department_id: int) -> Department:
        pass

    @abstractmethod
    def save_department(self, insert_fields: dict) -> Department:
        pass

    @abstractmethod
    def update_department(self, department_id: int, updated_fields: dict) -> Department:
        pass

    @abstractmethod
    def delete_department(self, department_id: int):
        pass

    @abstractmethod
    def get_departments_by_enterprise(self, enterprise_id: int) -> List[Department]:
        pass

    @abstractmethod
    def get_departments_by_business(self, business_id: int) -> List[Department]:
        pass
