from abc import ABC, abstractmethod
from datetime import datetime
from typing import List

from accessor.content.accessors.dto import DocumentInsertDTO, DocumentUpdateDTO
from shared.models import Document


class IDocumentAccessor(ABC):
    @abstractmethod
    def save_document(self, insert_dto: DocumentInsertDTO) -> Document:
        pass

    @abstractmethod
    def update_document(self, document_id: int, update_dto: DocumentUpdateDTO) -> Document:
        pass

    @abstractmethod
    def get_document_by_id(self, document_id: int) -> Document:
        pass

    @abstractmethod
    def delete_document(self, document_id: int):
        pass

    # @abstractmethod
    # def get_all_document(self) -> List[Document]:
    #     pass
