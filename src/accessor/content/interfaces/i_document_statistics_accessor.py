# file: accessor/content/interfaces/i_document_statistics_accessor.py

from abc import ABC, abstractmethod
from typing import Optional, <PERSON><PERSON>

from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO


class IDocumentStatisticsAccessor(ABC):
    @abstractmethod
    def create_document_statistics(
        self, document_id: int, stats_dto: DocumentStatisticsDTO
    ) -> Optional[DocumentStatisticsDTO]:
        """
        Inserts a new record into document_statistics based on the given DTO.

        Args:
            document_id: The associated document_id for the statistics.
            stats_dto: The DTO containing statistics data to be inserted.

        Returns:
            A DocumentStatisticsDTO of the newly created record, or None if insertion failed.
        """
        pass

    @abstractmethod
    def get_document_statistics_by_document_id(
        self, document_id: int
    ) -> Tuple[Optional[DocumentStatisticsDTO], Optional[DocumentStatisticsDTO]]:
        """
        Get document statistics for a document and its original document (if exists).

        Args:
            document_id: The ID of the document to query statistics for

        Returns:
            Tuple[DocumentStatisticsDTO for the document, DocumentStatisticsDTO for the original document]
        """
        pass
