from abc import ABC, abstractmethod
from typing import List

from shared.models import UniqueStatement


class IUniqueStatementAccessor(ABC):

    @abstractmethod
    def get_unique_statement_by_id(self, unique_statement_id: int) -> UniqueStatement:
        pass

    @abstractmethod
    def get_new_unique_statement_identifier(
        self, document_id, subtopic_id, statement_type_id, topic_id
    ) -> str:
        pass
