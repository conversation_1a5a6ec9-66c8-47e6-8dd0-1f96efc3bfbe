from abc import ABC, abstractmethod
from typing import List

from accessor.content.accessors.dto import AuditActivityReadDTO
from shared.models import AuditActivity


class IAuditActivityAccessor(ABC):

    @abstractmethod
    def get_audit_activity_by_id(self, acceptance_criteria_id: int) -> AuditActivityReadDTO:
        pass

    @abstractmethod
    def get_all_audit_log_entries_per_user(self, user_story_id: int) -> List[AuditActivityReadDTO]:
        pass

    @abstractmethod
    def get_all_audit_activity_per_object(
        self, audit_object_type_id: int, object_key: int
    ) -> List[AuditActivityReadDTO]:
        pass
