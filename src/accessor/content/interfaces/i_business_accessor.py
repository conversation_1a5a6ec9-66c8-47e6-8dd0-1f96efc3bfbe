from abc import ABC, abstractmethod
from typing import List

from shared.models import Business


class IBusinessAccessor(ABC):

    @abstractmethod
    def get_business(self, business_id: int) -> Business:
        pass

    @abstractmethod
    def get_all_business(self) -> Business:
        """
        Retrieves the "All" business entry from the database.

        Returns:
            Business: The "All" business entry

        Raises:
            ContentAccessorException: If there is an error retrieving the business
        """
        pass
