from abc import ABC, abstractmethod
from typing import List

from shared.models import AcceptanceCriteria


class IAcceptanceCriteriaAccessor(ABC):
    @abstractmethod
    def save_acceptance_criteria(self, insert_fields: dict) -> AcceptanceCriteria:
        pass

    @abstractmethod
    def get_acceptance_criteria_by_id(self, acceptance_criteria_id: int) -> AcceptanceCriteria:
        pass

    @abstractmethod
    def update_acceptance_criteria(
        self, acceptance_criteria_id: int, updated_fields: dict[str, str]
    ) -> AcceptanceCriteria:
        pass

    @abstractmethod
    def delete_acceptance_criteria(self, acceptance_criteria_id: int):
        pass

    @abstractmethod
    def get_all_acceptance_criteria_per_user_story(self, user_story_id) -> List[AcceptanceCriteria]:
        pass
