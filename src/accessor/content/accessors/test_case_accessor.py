"""
test_case_accessor

This module provides accessors for handling CRUD operations on test case entities
within a specified session context. It includes methods for saving, updating, and
retrieving test cases associated with acceptance criteria, user stories, and unique statements.

Classes:
    - TestCaseAccessor: Implements the ITestCaseAccessor interface to provide
      database operations for TestCase entities.

Exceptions:
    - ContentAccessorException: Raised for errors during database operations.
"""

from typing import List, Optional

from sqlalchemy import func

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import ITestCaseAccessor
from shared.models import (
    AcceptanceCriteria,
    RatingEnum,
    TestCase,
    UniqueStatement,
    UserStory,
)


class TestCaseAccessor(ITestCaseAccessor):
    """
    Provides database operations for managing TestCase entities.

    This accessor class implements the ITestCaseAccessor interface and allows for CRUD operations
    on TestCase objects, including saving, updating, and retrieving test cases related to acceptance
    criteria, user stories, and unique statements.

    Attributes:
        session_factory: A callable that returns a new database session, ensuring each operation
                         is performed in an independent session context.
    """

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def save_test_case(self, insert_fields: dict) -> TestCase:
        """
        Saves a new TestCase entity to the database.

        This method creates a new TestCase instance with the provided details and commits it to
        the database. If an error occurs during the database operation, the transaction is rolled
        back, and a ContentAccessorException is raised.

        Parameters:
            acceptance_criteria_id (int): The ID of the acceptance criteria to which this test case is linked.
            description (str): A description of the test case.
            identifier (str): A unique identifier for the test case.
            rating (str): A rating value for the test case.

        Returns:
            TestCase: The newly created TestCase instance with a generated primary key.

        Raises:
            ContentAccessorException: If there is an error during the database transaction.
        """
        with self.session_factory() as session:
            try:
                identifier = self._generate_test_case_identifier(
                    insert_fields["acceptance_criteria_id"]
                )

                test_case = TestCase(
                    acceptance_criteria_id=insert_fields["acceptance_criteria_id"],
                    description=insert_fields["description"],
                    rating=insert_fields.get("rating", RatingEnum.unrated),
                    user_entered=insert_fields.get("user_entered", False),
                    identifier=identifier,
                )
                session.add(test_case)
                session.commit()
                session.refresh(test_case)

                return test_case
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving test case: {str(e)}") from e

    def get_test_case_by_id(self, test_case_id: int) -> TestCase:
        """
        Retrieves a TestCase entity by its ID.

        This method queries the database to find a TestCase with the specified test_case_id.
        If no match is found, it returns None. If an error occurs during the query, a
        ContentAccessorException is raised.

        Parameters:
            test_case_id (int): The unique identifier of the TestCase to retrieve.

        Returns:
            TestCase: The TestCase instance with the given ID, or None if no match is found.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:

                ac = session.query(TestCase).filter_by(test_case_id=test_case_id).first()
                return ac

            except Exception as e:
                raise ContentAccessorException(f"Error fetching test case: {str(e)}") from e

    def delete_test_case(self, test_case_id: int):
        """
        Deletes a TestCase entity by its ID.

        This method removes the TestCase with the specified test_case_id from the database.
        If the deletion operation encounters an error, the transaction is rolled back, and
        a ContentAccessorException is raised.

        Parameters:
            test_case_id (int): The unique identifier of the TestCase to delete.

        Raises:
            ContentAccessorException: If there is an error during the database transaction.
        """
        with self.session_factory() as session:
            try:
                session.query(TestCase).filter_by(test_case_id=test_case_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting test case: {str(e)}") from e

    def get_all_test_cases_per_acceptance_criteria(
        self, acceptance_criteria_id: int
    ) -> List[TestCase]:
        """
        Retrieves all TestCase entities associated with a specific acceptance criteria.

        This method queries the database to find all test cases linked to the given acceptance_criteria_id.
        It joins related tables to also retrieve information about the associated unique statements and
        documents. Results are ordered by the test_case_id.

        Parameters:
            acceptance_criteria_id (int): The ID of the acceptance criteria for which test cases are to be retrieved.

        Returns:
            List[TestCase]: A list of TestCase instances associated with the specified acceptance criteria.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                test_cases = (
                    session.query(
                        TestCase.test_case_id,
                        TestCase.acceptance_criteria_id,
                        TestCase.description,
                        TestCase.identifier,
                        TestCase.rating,
                        TestCase.edited,
                        TestCase.user_entered,
                        UniqueStatement.unique_statement_id,
                        UniqueStatement.document_id,
                    )
                    .join(
                        AcceptanceCriteria,
                        AcceptanceCriteria.acceptance_criteria_id
                        == TestCase.acceptance_criteria_id,
                    )
                    .join(UserStory, UserStory.user_story_id == AcceptanceCriteria.user_story_id)
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id == UserStory.unique_statement_id,
                    )
                    .filter(TestCase.acceptance_criteria_id == acceptance_criteria_id)
                    .order_by(TestCase.test_case_id)
                    .all()
                )

                return test_cases
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching all acceptance criteria per user story: {str(e)}"
                ) from e

    def verify_test_case_exists(self, test_case_id: int) -> bool:
        """
        Checks if a TestCase entity exists in the database by its ID.

        This method queries the database to determine if a TestCase with the specified
        test_case_id exists. It returns True if the test case is found, and False otherwise.

        Parameters:
            test_case_id (int): The unique identifier of the TestCase to verify.

        Returns:
            bool: True if a TestCase with the given ID exists, False otherwise.
        """
        with self.session_factory() as session:
            return session.query(
                session.query(TestCase).filter_by(test_case_id=test_case_id).exists()
            ).scalar()

    def update_test_case(self, test_case_id: int, updated_fields: dict[str, str]) -> TestCase:
        """
        Updates fields of an existing TestCase entity in the database.

        This method updates specified fields for a TestCase identified by test_case_id.
        The updated fields are passed in as a dictionary, and only provided fields are modified.
        If the update is successful, the method commits the changes and returns the updated
        TestCase attributes as a dictionary.

        Parameters:
            test_case_id (int): The unique identifier of the TestCase to update.
            updated_fields (dict[str, str]): A dictionary of fields to update and their new values.
                                            Possible keys are "description", "identifier", and "rating".

        Returns:
            dict[str, str]: A dictionary with the updated TestCase attributes:
                            - "test_case_id": The ID of the TestCase.
                            - "description": The updated description of the TestCase.
                            - "identifier": The updated identifier of the TestCase.
                            - "rating": The updated rating of the TestCase, or None if not set.

        Raises:
            ContentAccessorException: If there is an error during the database transaction.
        """
        with self.session_factory() as session:

            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_test_case_by_id here because it uses a different session
            test_case = session.query(TestCase).get(test_case_id)

            if test_case is None:
                raise NotFoundException(f"Test Case with ID {test_case_id} does not exist.")

            try:
                # Update fields if provided
                if "description" in updated_fields:
                    test_case.description = updated_fields["description"]
                    # Set `edited` to True only if `description` is being updated
                    test_case.edited = True
                if "identifier" in updated_fields:
                    test_case.identifier = updated_fields["identifier"]
                if "rating" in updated_fields:
                    test_case.rating = RatingEnum(updated_fields["rating"])

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(test_case)

                return test_case

            except Exception as e:
                raise ContentAccessorException(f"Error updating test case: {str(e)}") from e

    def get_document_id_by_test_case_id(self, test_case_id: int) -> int:
        """
        Retrieves the document ID associated with a specific TestCase.

        This method queries the database to find the document_id linked to the given test_case_id
        by joining related tables, including UserStory, AcceptanceCriteria, and UniqueStatement.
        If the document_id is found, it is returned. If an error occurs during the query,
        a ContentAccessorException is raised.

        Parameters:
            test_case_id (int): The unique identifier of the TestCase for which to retrieve the document ID.

        Returns:
            int: The document_id associated with the specified TestCase.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                return (
                    session.query(UniqueStatement.document_id)
                    .join(UserStory)
                    .join(AcceptanceCriteria)
                    .join(TestCase)
                    .filter(TestCase.test_case_id == test_case_id)
                    .scalar()
                )
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document_id using test_case_id: {str(e)}"
                ) from e

    # Helper methods
    def _generate_test_case_identifier(self, acceptance_criteria_id: int) -> Optional[str]:
        with self.session_factory() as session:
            try:
                # Retrieve the acceptance criteria identifier
                acceptance_criteria_info = (
                    session.query(AcceptanceCriteria.identifier)
                    .filter(AcceptanceCriteria.acceptance_criteria_id == acceptance_criteria_id)
                    .first()
                )

                if not acceptance_criteria_info:
                    print("Acceptance Criteria not found.")
                    return None  # Return None if the AcceptanceCriteria does not exist

                # Get the count of test cases for this AcceptanceCriteria to use as the next order_id
                test_case_count = (
                    session.query(func.count(TestCase.acceptance_criteria_id))
                    .filter(TestCase.acceptance_criteria_id == acceptance_criteria_id)
                    .scalar()
                )

                new_order_id = test_case_count + 1

                # Replace "AC" prefix with "TC" from the AC identifier and generate the new identifier
                tc_identifier_prefix = acceptance_criteria_info.identifier.replace("AC", "TC", 1)
                new_identifier = f"{tc_identifier_prefix}.{str(new_order_id).zfill(2)}"

                return new_identifier  # Return the generated identifier

            except Exception as e:
                print(f"Error generating identifier for test case: {e}")
                return None  # Return None on error
