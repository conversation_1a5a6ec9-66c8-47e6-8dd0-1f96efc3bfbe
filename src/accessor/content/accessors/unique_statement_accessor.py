from typing import List, Optional, Tuple

from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload, selectinload

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IUniqueStatementAccessor
from shared.enums import StatementTypeEnum, AuditObjectType, AuditOperationType
from shared.models import (
    RatingEnum,
    Subtopic,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    Topic,
)
from util.audit.dto import AuditActivityInsertDTO


class UniqueStatementAccessor(IUniqueStatementAccessor):

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def verify_unique_statement_exists(self, unique_statement_id: int) -> bool:
        with self.session_factory() as session:
            try:
                return session.query(
                    session.query(UniqueStatement)
                    .filter_by(unique_statement_id=unique_statement_id)
                    .exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if unique statement exists: {str(e)}"
                ) from e

    def get_unique_statement_by_id(self, unique_statement_id: int) -> Optional[UniqueStatement]:
        with self.session_factory() as session:
            try:
                # Query with eager loading for subtopic associations and topics
                unique_statement = (
                    session.query(UniqueStatement)
                    .options(
                        joinedload(UniqueStatement.subtopics).joinedload(Subtopic.topic),
                        joinedload(UniqueStatement.unique_statement_subtopic_associations),
                    )
                    .filter(UniqueStatement.unique_statement_id == unique_statement_id)
                    .one_or_none()
                )
                return unique_statement

            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching unique statement: {str(e)}") from e

    def get_all_unique_statements_by_domain_id(self, domain_id: int) -> List[UniqueStatement]:
        with self.session_factory() as session:
            unique_statements = session.query(UniqueStatement).filter_by(domain_id=domain_id).all()
            return unique_statements

    def create_unique_statement_with_subtopics(self, insert_fields: dict) -> UniqueStatement:
        with self.session_factory() as session:
            # Look for an existing unique statement that has the same text
            existing_unique_statement = (
                session.query(UniqueStatement)
                .filter_by(
                    document_id=insert_fields["document_id"],
                    text=insert_fields["text"],
                    statement_type_id=insert_fields["statement_type_id"],
                )
                .one_or_none()
            )

            # Only create the new unique statement if a match doesn't already exist
            if not existing_unique_statement:
                unique_statement = self._insert_unique_statement_record(insert_fields)
            else:
                unique_statement = existing_unique_statement

            subtopics = insert_fields.get("subtopics", [])

            if unique_statement and subtopics:
                self._create_subtopic_associations_for_unique_statement(
                    unique_statement.unique_statement_id, insert_fields["subtopics"]
                )

            return self._load_unique_statement_with_subtopics(unique_statement.unique_statement_id)

    def get_new_unique_statement_identifier(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> str:
        with self.session_factory() as session:
            # Count the number of unique_statements for the given document_id and subtopic_id
            statement_count = (
                session.query(UniqueStatement)
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )  # Proper join condition
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                )
                .count()
            )

            identifier_suffix = statement_count + 1

            # Join Subtopic → Topic to get topic_id
            topic_id = (
                session.query(Topic.topic_id)
                .join(Subtopic, Subtopic.topic_id == Topic.topic_id)
                .filter(Subtopic.subtopic_id == subtopic_id)
                .scalar()
            )

            # Construct the identifier using a helper function
            identifier = self._generate_identifier(
                document_id,
                topic_id,
                subtopic_id,
                identifier_suffix,
                statement_type_id,
            )

            return identifier

    def get_unique_statements_using_filter(
        self, domain_id: int, document_id: int, topic_id: int, subtopic_id: int
    ) -> List[UniqueStatement]:
        with self.session_factory() as session:
            try:
                query = session.query(UniqueStatement)

                # If document_id exists, let's use that over domain_id for now
                if document_id:
                    query = query.filter_by(document_id=document_id)
                elif domain_id:
                    query = query.filter_by(domain_id=domain_id)

                # Eagerly load subtopics to avoid DetachedInstanceError
                query = query.options(
                    joinedload(UniqueStatement.subtopics).joinedload(Subtopic.topic),
                    joinedload(UniqueStatement.unique_statement_subtopic_associations),
                )

                # If filtering by topic_id or subtopic_id, join with the association table
                if topic_id or subtopic_id:
                    query = query.join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    ).join(
                        Subtopic,  # Join with Subtopic through the association table
                        UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id,
                    )

                # Filter by topic_id if provided
                if topic_id:
                    query = query.filter(Subtopic.topic_id == topic_id)

                # Filter by subtopic_id if provided
                if subtopic_id:
                    query = query.filter(
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id
                    )

                # Sort the results by unique_statement_id
                query = query.order_by(UniqueStatement.unique_statement_id)

                unique_statements = query.all()

                return unique_statements

            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching unique statements: {str(e)}") from e

    def update_unique_statement(
        self, unique_statement_id: int, updated_fields: dict
    ) -> Tuple[UniqueStatement, List[AuditActivityInsertDTO]]:
        audit_activity: List[AuditActivityInsertDTO] = []

        with self.session_factory() as session:
            try:
                unique_statement = (
                    session.query(UniqueStatement)
                    .options(
                        selectinload(UniqueStatement.subtopics).selectinload(Subtopic.topic),
                        selectinload(UniqueStatement.unique_statement_subtopic_associations),
                    )
                    .filter_by(unique_statement_id=unique_statement_id)
                    .one_or_none()
                )

                if unique_statement is None:
                    raise NotFoundException(
                        f"Unique statement with ID {unique_statement_id} does not exist."
                    )

                updated_subtopics = updated_fields.pop("subtopics", None)
                if updated_subtopics is not None:
                    audit_activity += self._update_subtopics_for_unique_statement(
                        unique_statement, updated_subtopics
                    )

                if updated_fields:
                    unique_statement_has_changes = self._update_unique_statement_if_changed(
                        unique_statement, updated_fields
                    )

                unique_statement.edited = len(updated_fields) > 1 or "rating" not in updated_fields

                session.commit()
                session.refresh(unique_statement)

                # Log update to the unique statement itself
                if unique_statement_has_changes:
                    audit_activity.append(
                        AuditActivityInsertDTO(
                            object_type=AuditObjectType.UNIQUE_STATEMENT,
                            object_key=unique_statement_id,
                            snapshot=unique_statement.to_dict(),
                            operation_type=AuditOperationType.UPDATE,
                        )
                    )

                return unique_statement, audit_activity

            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating unique statement: {str(e)}") from e

    def update_unique_statements_to_positive_rating_by_document_id(self, document_id: int) -> str:
        with self.session_factory() as session:
            try:

                positive_rating = RatingEnum.positive.value

                # Execute a raw SQL update statement to update the rating of all unique statements to 'positive'
                update_query = text(
                    """
                    UPDATE unique_statement
                    SET rating = :positive_rating
                    WHERE document_id = :document_id
                """
                )

                session.execute(
                    update_query, {"document_id": document_id, "positive_rating": positive_rating}
                )
                session.commit()
                return "All unique statements updated to 'positive' rating successfully"
            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating unique statements: {str(e)}") from e

    def delete_unique_statement(self, unique_statement_id: int):
        with self.session_factory() as session:
            try:
                # Fetch the unique statement to delete
                unique_statement = (
                    session.query(UniqueStatement)
                    .filter_by(unique_statement_id=unique_statement_id)
                    .one_or_none()
                )

                # Delete the record
                session.delete(unique_statement)
                session.commit()

                return {"message": "Unique statement deleted successfully"}, 200
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting unique statement: {str(e)}") from e

    def get_unique_statements_ordered(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[UniqueStatement]:
        with self.session_factory() as session:
            try:
                return (
                    session.query(UniqueStatement)
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatement.rating != RatingEnum.negative.value,
                    )
                    .order_by(UniqueStatement.unique_statement_id)
                    .all()
                )
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching ordered unique statemens: {str(e)}"
                ) from e

    def get_unique_statements_for_document_and_subtopic(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[UniqueStatement]:
        with self.session_factory() as session:
            try:
                unique_statements = (
                    session.query(UniqueStatement)
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatement.rating != RatingEnum.negative.value,
                    )
                    .all()
                )
                return unique_statements
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching unique statemens for document and subtopic: {str(e)}"
                ) from e

    def update_unique_statement_chunk_id(self, unique_statement_id: int, chunk_id: int):
        with self.session_factory() as session:
            try:
                unique_statement = (
                    session.query(UniqueStatement)
                    .filter_by(unique_statement_id=unique_statement_id)
                    .first()
                )

                if not unique_statement:
                    raise ValueError(f"UniqueStatement with ID {unique_statement_id} not found.")

                # Update existing record
                unique_statement.chunk_id = chunk_id

                session.commit()
                return unique_statement

            except (SQLAlchemyError, ValueError) as e:
                # Rollback for both SQLAlchemy errors and ValueError
                session.rollback()
                raise ContentAccessorException(
                    f"Error updating chunk_id for unique statement: {str(e)}"
                ) from e

    def get_subtopics_for_unique_statement(self, unique_statement_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                subtopics = (
                    session.query(Subtopic)
                    .join(
                        UniqueStatementSubtopicAssociation,
                        Subtopic.subtopic_id == UniqueStatementSubtopicAssociation.subtopic_id,
                    )
                    .filter(
                        UniqueStatementSubtopicAssociation.unique_statement_id
                        == unique_statement_id
                    )
                    .all()
                )
                return subtopics
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching subtopics for unique statement: {str(e)}"
                ) from e

    def get_subtopic_associations_for_unique_statement(
        self, unique_statement_id: int, subtopic_ids: list[int]
    ) -> List[UniqueStatementSubtopicAssociation]:
        with self.session_factory() as session:
            try:
                # Fetch the unique statement
                unique_statement = (
                    session.query(UniqueStatement)
                    .filter_by(unique_statement_id=unique_statement_id)
                    .one_or_none()
                )

                if not unique_statement:
                    raise NotFoundException(
                        f"Unique statement with ID {unique_statement_id} not found"
                    )

                # Filter subtopics to get associations
                associations = [
                    assoc
                    for assoc in unique_statement.unique_statement_subtopic_associations
                    if assoc.subtopic_id in subtopic_ids
                ]

                return associations

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching subtopic associations: {str(e)}"
                ) from e

    def get_documents_with_change_statements(self, document_ids: list[int]) -> dict[int, bool]:
        """
        Checks which documents have change statements (statement_type_id = 11) in bulk.

        Args:
            document_ids (list[int]): List of document IDs to check

        Returns:
            dict[int, bool]: Dictionary mapping document IDs to boolean indicating presence of change statements

        Raises:
            ContentAccessorException: If there is an error checking for change statements
        """
        with self.session_factory() as session:
            try:
                # Query to get all documents that have change statements
                documents_with_changes = (
                    session.query(UniqueStatement.document_id)
                    .filter(
                        UniqueStatement.document_id.in_(document_ids),
                        UniqueStatement.statement_type_id
                        == StatementTypeEnum.PROCEDURE_CHANGE.value,
                    )
                    .distinct()
                    .all()
                )

                # Convert to set for O(1) lookup
                documents_with_changes_set = {doc[0] for doc in documents_with_changes}

                # Create result dictionary with all document IDs
                return {doc_id: doc_id in documents_with_changes_set for doc_id in document_ids}

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error checking for change statements in bulk: {str(e)}"
                ) from e

    # Helper methods
    def _remove_subtopic_associations_from_unique_statement(
        self, unique_statement_id, subtopic_ids_to_remove
    ):
        """
        Removes associations between a unique statement and specified subtopics.

        Args:
            unique_statement_id (int): The ID of the unique statement.
            subtopic_ids_to_remove (set[int]): The IDs of subtopics to be removed.

        Returns:
            bool: True if removal was successful, False otherwise.
        """
        with self.session_factory() as session:
            try:
                # Ensure the unique statement exists
                unique_statement = session.query(UniqueStatement).get(unique_statement_id)
                if not unique_statement:
                    raise NotFoundException(
                        f"Unique statement with ID {unique_statement_id} not found"
                    )

                # Perform bulk delete of associations
                session.query(UniqueStatementSubtopicAssociation).filter(
                    UniqueStatementSubtopicAssociation.unique_statement_id == unique_statement_id,
                    UniqueStatementSubtopicAssociation.subtopic_id.in_(subtopic_ids_to_remove),
                ).delete(
                    synchronize_session=False
                )  # set to 'fetch' if you need safety over performance

                session.commit()
                return True

            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error removing subtopics: {str(e)}")

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Unexpected error: {str(e)}")

    def _create_subtopic_associations_for_unique_statement(
        self, unique_statement_id, subtopics_to_add: list[dict]
    ):
        """
        Adds associations between a unique statement and specified subtopics. This method assumes the
        subtopics_to_add list has already been filtered and unchanged, current subtopics are not
        included in the list.

        Args:
            unique_statement_id (int): The ID of the unique statement.
            subtopics_to_add (list[dict]): List of subtopics (with `subtopic_id` and `is_primary`) to add.

        Returns:
            bool: True if addition was successful, False otherwise.
        """
        with self.session_factory() as session:
            try:
                # Prepare all new association objects
                new_associations = [
                    UniqueStatementSubtopicAssociation(
                        unique_statement_id=unique_statement_id,
                        subtopic_id=subtopic["subtopic_id"],
                        is_primary=subtopic["is_primary"],
                    )
                    for subtopic in subtopics_to_add
                ]

                # Use bulk insert
                session.bulk_save_objects(new_associations)

                session.commit()
                return True

            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error adding subtopics: {str(e)}")

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Unexpected error: {str(e)}")

    def _set_primary_flag_for_subtopic_association(
        self, unique_statement_id: int, subtopic_id: int, is_primary: bool
    ) -> bool:
        """
        Updates the 'is_primary' value for a specific subtopic association tied to a unique statement.

        Args:
            unique_statement_id (int): The ID of the unique statement.
            subtopic_id (int): The ID of the subtopic whose association needs updating.
            is_primary (bool): The new value for the 'is_primary' flag.

        Returns:
            bool: True if update was successful.

        Raises:
            ContentAccessorException: If the association is not found or update fails.
        """
        with self.session_factory() as session:
            try:
                association = (
                    session.query(UniqueStatementSubtopicAssociation)
                    .filter_by(unique_statement_id=unique_statement_id, subtopic_id=subtopic_id)
                    .one_or_none()
                )

                if not association:
                    raise ContentAccessorException(
                        f"No association found for unique_statement_id={unique_statement_id} and subtopic_id={subtopic_id}"
                    )

                # Update the 'is_primary' field
                association.is_primary = is_primary
                session.commit()
                return True

            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating subtopic association: {str(e)}")

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Unexpected error: {str(e)}")

    def _generate_identifier(
        self,
        document_id: int,
        topic_id: int,
        subtopic_id: int,
        identifier_suffix: str,
        statement_type_id: int,
    ) -> str:
        # Determine the prefix based on the statement_type_id
        prefixes = {
            StatementTypeEnum.REQUIREMENT.value: "REQ",
            StatementTypeEnum.OPPORTUNITY.value: "OPP",
            StatementTypeEnum.AUTHORIZATION.value: "AUT",
            StatementTypeEnum.PROCEDURE_CHANGE.value: "PRO",
            StatementTypeEnum.ORGANIZATIONAL_CHANGE.value: "ORG",
        }
        prefix = prefixes.get(
            statement_type_id, "UNK"
        )  # UNK is default or unknown prefix (if not 1, 2, 6, 11, or 12)

        # Pad topic_id, subtopic_id, and identifier_suffix to 3 digits
        topic_id_padded = f"{int(topic_id):03}"
        subtopic_id_padded = f"{int(subtopic_id):03}"
        identifier_suffix_padded = f"{int(identifier_suffix):03}"

        # Return the formatted identifier
        return f"{prefix}{document_id}.{topic_id_padded}.{subtopic_id_padded}.{identifier_suffix_padded}"

    def _update_identifier_by_statement_type(
        self, unique_statement: UniqueStatement, statement_type_id: int
    ):
        IDENTIFIER_PREFIX_LENGTH = 3
        if (
            unique_statement.identifier
            and len(unique_statement.identifier) >= IDENTIFIER_PREFIX_LENGTH
        ):
            unique_statement.statement_type_id = statement_type_id
            prefixes = {
                StatementTypeEnum.REQUIREMENT.value: "REQ",
                StatementTypeEnum.OPPORTUNITY.value: "OPP",
                StatementTypeEnum.AUTHORIZATION.value: "AUT",
                StatementTypeEnum.PROCEDURE_CHANGE.value: "PRO",
                StatementTypeEnum.ORGANIZATIONAL_CHANGE.value: "ORG",
            }
            prefix = prefixes.get(statement_type_id, "")
            if prefix:
                unique_statement.identifier = (
                    prefix + unique_statement.identifier[IDENTIFIER_PREFIX_LENGTH:]
                )

    def _update_unique_statement_if_changed(
        self, unique_statement: UniqueStatement, updated_fields
    ) -> bool:
        """Apply updates to the unique statement and return True if any fields were changed."""
        has_changes = False

        # Check and update 'text'
        if "text" in updated_fields:
            new_text = updated_fields["text"]
            if unique_statement.text != new_text:
                unique_statement.text = new_text
                has_changes = True

        # Check and update 'statement_type_id' (and update identifier if needed)
        if "statement_type_id" in updated_fields:
            new_type_id = updated_fields["statement_type_id"]
            if unique_statement.statement_type_id != new_type_id:
                self._update_identifier_by_statement_type(unique_statement, new_type_id)
                has_changes = True

        # Check and update 'rating'
        if "rating" in updated_fields:
            new_rating = updated_fields["rating"]
            if unique_statement.rating != new_rating:
                unique_statement.rating = new_rating
                has_changes = True

        return has_changes

    def _insert_unique_statement_record(self, insert_fields: dict) -> UniqueStatement:
        with self.session_factory() as session:
            try:
                # Use the first subtopic_id in the list if multiple exist
                identifier = self.get_new_unique_statement_identifier(
                    insert_fields["document_id"],
                    insert_fields["subtopics"][0]["subtopic_id"],
                    insert_fields["statement_type_id"],
                )

                new_unique_statement = UniqueStatement(
                    domain_id=insert_fields.get("domain_id", None),
                    document_id=insert_fields["document_id"],
                    statement_type_id=insert_fields["statement_type_id"],
                    text=insert_fields["text"],
                    identifier=identifier,
                    rating=insert_fields.get("rating", RatingEnum.unrated),
                    user_entered=insert_fields.get("user_entered", False),
                    edited=insert_fields.get("edited", False),
                    statement_id=insert_fields.get("statement_id"),
                    parent_requirement_id=insert_fields.get("parent_requirement_id"),
                )
                session.add(new_unique_statement)

                session.commit()
                session.refresh(new_unique_statement)

                return new_unique_statement

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving unique statement: {str(e)}") from e

    def _load_unique_statement_with_subtopics(self, unique_statement_id: int) -> UniqueStatement:
        with self.session_factory() as session:
            return (
                session.query(UniqueStatement)
                .options(
                    joinedload(UniqueStatement.subtopics).joinedload(Subtopic.topic),
                    joinedload(UniqueStatement.unique_statement_subtopic_associations)
                    .joinedload(UniqueStatementSubtopicAssociation.subtopic)
                    .joinedload(Subtopic.topic),
                    joinedload(UniqueStatement.statement_type),
                    joinedload(UniqueStatement.domain),
                )
                .filter_by(unique_statement_id=unique_statement_id)
                .one()
            )

    def _update_subtopics_for_unique_statement(
        self, current_unique_statement, updated_subtopics: list[dict]
    ) -> List[AuditActivityInsertDTO]:
        audit_activity: List[AuditActivityInsertDTO] = []

        audit_activity += self._update_primary_subtopic(current_unique_statement, updated_subtopics)
        audit_activity += self._update_non_primary_subtopics(
            current_unique_statement, updated_subtopics
        )

        return audit_activity

    def _update_primary_subtopic(
        self, current_unique_statement, updated_subtopics: list[dict]
    ) -> List[AuditActivityInsertDTO]:
        changelog = []

        # Get the current primary subtopic id and the updated primary subtopic id
        current_primary_subtopic_id = self._get_current_primary_subtopic_id(
            current_unique_statement
        )
        updated_primary_subtopic = next((s for s in updated_subtopics if s.get("is_primary")), None)
        updated_primary_subtopic_id = updated_primary_subtopic["subtopic_id"]

        # If they are the same, nothing to do here, return no audit activity
        if current_primary_subtopic_id == updated_primary_subtopic_id:
            return changelog  # No change

        # Does the new primary subtopic already exist in the current list?
        new_primary_subtopic_exists_in_current_list = (
            self._new_primary_subtopic_exists_in_current_list(
                current_unique_statement, updated_primary_subtopic_id
            )
        )

        if new_primary_subtopic_exists_in_current_list:
            # If it exists in the current list, we simply need to update the primary flag to be true
            self._set_primary_flag_for_subtopic_association(
                current_unique_statement.unique_statement_id, updated_primary_subtopic_id, True
            )
        else:
            # Otherwise create a new association
            self._create_subtopic_associations_for_unique_statement(
                current_unique_statement.unique_statement_id, [updated_primary_subtopic]
            )

        changelog.append(
            AuditActivityInsertDTO(
                object_type=AuditObjectType.UNIQUE_STATEMENT_SUBTOPIC_ASSOCIATION,
                object_key=current_unique_statement.unique_statement_id,
                snapshot={
                    "unique_statement_id": current_unique_statement.unique_statement_id,
                    "subtopic_id": updated_primary_subtopic_id,
                    "is_primary": True,
                },
                operation_type=(
                    AuditOperationType.UPDATE
                    if new_primary_subtopic_exists_in_current_list
                    else AuditOperationType.CREATE
                ),
            )
        )

        return changelog

    def _new_primary_subtopic_exists_in_current_list(
        self, current_unique_statement, updated_primary_subtopic_id
    ):
        new_primary_subtopic_in_current_list = next(
            (
                s
                for s in current_unique_statement.unique_statement_subtopic_associations
                if s.subtopic_id == updated_primary_subtopic_id
            ),
            None,
        )

        return new_primary_subtopic_in_current_list

    def _get_current_primary_subtopic_id(self, current_unique_statement):
        current_primary_id = next(
            (
                s.subtopic_id
                for s in current_unique_statement.unique_statement_subtopic_associations
                if getattr(s, "is_primary", False)
            ),
            None,
        )

        return current_primary_id

    def _update_non_primary_subtopics(
        self,
        current_unique_statement,
        updated_subtopics: list[dict],
    ) -> List[AuditActivityInsertDTO]:
        audit_activity = []
        unique_statement_id = current_unique_statement.unique_statement_id

        # Extract the updated primary subtopic ID
        updated_primary_subtopic_id = next(
            (sub["subtopic_id"] for sub in updated_subtopics if sub.get("is_primary")), None
        )

        # Filter out the primary from the updated list
        updated_non_primary_ids = {
            sub["subtopic_id"] for sub in updated_subtopics if not sub.get("is_primary", False)
        }

        # Get current associated non-primary subtopic IDs
        current_non_primary_ids = {
            assoc.subtopic_id
            for assoc in current_unique_statement.unique_statement_subtopic_associations
            if assoc.subtopic_id != updated_primary_subtopic_id
        }

        # Compute which non-primary subtopics need to be added or removed
        subtopic_ids_to_add = updated_non_primary_ids - current_non_primary_ids
        subtopic_ids_to_remove = current_non_primary_ids - updated_non_primary_ids

        if subtopic_ids_to_remove:
            self._update_audit_activity_with_removed_subtopics(
                audit_activity, unique_statement_id, subtopic_ids_to_remove
            )

        if subtopic_ids_to_add:
            self._update_audit_activity_with_added_subtopics(
                audit_activity, unique_statement_id, updated_subtopics, subtopic_ids_to_add
            )

        return audit_activity

    def _update_audit_activity_with_added_subtopics(
        self, changelog, unique_statement_id, updated_subtopics, subtopic_ids_to_add
    ):
        subtopics_to_add = [
            sub for sub in updated_subtopics if sub["subtopic_id"] in subtopic_ids_to_add
        ]
        self._create_subtopic_associations_for_unique_statement(
            unique_statement_id, subtopics_to_add
        )

        for subtopic in subtopics_to_add:
            changelog.append(
                AuditActivityInsertDTO(
                    object_type=AuditObjectType.UNIQUE_STATEMENT_SUBTOPIC_ASSOCIATION,
                    object_key=unique_statement_id,
                    snapshot={
                        "unique_statement_id": unique_statement_id,
                        "subtopic_id": subtopic["subtopic_id"],
                        "is_primary": subtopic.get("is_primary", False),
                    },
                    operation_type=AuditOperationType.CREATE,
                )
            )

    def _update_audit_activity_with_removed_subtopics(
        self, changelog, unique_statement_id, subtopic_ids_to_remove
    ):
        removed_assocs = self.get_subtopic_associations_for_unique_statement(
            unique_statement_id, subtopic_ids_to_remove
        )
        self._remove_subtopic_associations_from_unique_statement(
            unique_statement_id, subtopic_ids_to_remove
        )

        for assoc in removed_assocs:
            changelog.append(
                AuditActivityInsertDTO(
                    object_type=AuditObjectType.UNIQUE_STATEMENT_SUBTOPIC_ASSOCIATION,
                    object_key=assoc.unique_statement_id,
                    snapshot=assoc.to_dict(),
                    operation_type=AuditOperationType.DELETE,
                )
            )
