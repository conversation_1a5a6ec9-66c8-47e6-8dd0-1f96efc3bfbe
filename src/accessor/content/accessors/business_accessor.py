from sqlalchemy.exc import SQLAlchemyError

from accessor.content.exceptions import ContentAccessorException
from shared.models.business import Business


class BusinessAccessor:
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_business(self, business_id: int) -> Business:
        """
        Retrieves a business entry from the database by its ID.

        Args:
            business_id (int): The ID of the business to retrieve

        Returns:
            Business: The business entry

        Raises:
            ContentAccessorException: If there is an error retrieving the business or if the business is not found
        """
        with self.session_factory() as session:
            try:
                business = session.query(Business).filter_by(business_id=business_id).first()
                if not business:
                    raise ContentAccessorException(f"Business with ID {business_id} not found")
                return business
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error retrieving business: {str(e)}") from e

    def get_all_business(self) -> Business:
        """
        Retrieves the "All" business entry from the database.

        Returns:
            Business: The "All" business entry

        Raises:
            ContentAccessorException: If there is an error retrieving the business
        """
        with self.session_factory() as session:
            try:
                all_business = session.query(Business).filter_by(name="All").first()
                if not all_business:
                    raise ContentAccessorException("'All' business entry not found")
                return all_business
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error retrieving 'All' business: {str(e)}") from e
