from typing import List

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content.exceptions import ContentAccessorException
from accessor.content.interfaces import IChunkAccessor
from shared.enums import StatementTypeEnum
from shared.models import Chunk, Document, Statement, Subtopic
from shared.models.rating_enum import RatingEnum


class ChunkAccessor(IChunkAccessor):

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def verify_chunk_exists(self, chunk_id: int) -> bool:
        with self.session_factory() as session:
            try:
                return session.query(
                    session.query(Chunk).filter_by(chunk_id=chunk_id).exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if chunk exists: {str(e)}"
                ) from e

    def get_chunk_by_id(self, chunk_id: int) -> Chunk:
        with self.session_factory() as session:
            try:
                return session.query(Chunk).filter_by(chunk_id=chunk_id).one_or_none()
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching chunk: {str(e)}") from e

    def get_chunk_ids_for_subtopic_and_document(
        self, subtopic_id: int, document_id: int
    ) -> List[int]:
        """This method will return all chunk_ids that are associated with a specified subtopic
            for a specified document

        Args:
            subtopic_id (int): subtopic that we are looking to find chunk_ids with
            document_id (int): specified document to search within

        Raises:
            ContentAccessorException: _description_

        Returns:
            List[int]: List of chunk_ids for a document that are linked via identified subtopic
        """
        with self.session_factory() as session:
            try:
                # Query using ORM to join models
                chunk_ids = (
                    session.query(Chunk.chunk_id)
                    .join(Statement, Chunk.chunk_id == Statement.chunk_id)
                    .join(Subtopic, Statement.text == Subtopic.name)
                    .join(Document, Chunk.document_id == Document.document_id)
                    .filter(
                        Subtopic.subtopic_id == subtopic_id, Document.document_id == document_id
                    )
                    .distinct()
                    .all()
                )
                return [chunk_id[0] for chunk_id in chunk_ids]
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching chunk ids for subtopic and document: {str(e)}"
                ) from e

    def save_chunk(
        self, document_id: int, formatted_text: dict, text_content: dict, vector_id: str
    ) -> int:
        """
        Saves a chunk to the database.

        Args:
            document_id (int): The ID of the associated document.
            formatted_text (dict): The formatted text of the chunk.
            text_content (dict): The raw text content of the chunk.
            vector_id (str): The UUID of the vector associated with this chunk.
        """
        with self.session_factory() as session:
            try:
                chunk = Chunk(
                    document_id=document_id,
                    formatted_text=formatted_text,
                    text_content=text_content,
                    vector_id=vector_id,
                )
                session.add(chunk)
                session.commit()
                session.refresh(chunk)
                return chunk.chunk_id
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving chunk: {str(e)}") from e

    def get_chunks_by_document(self, document_id: int) -> list[Chunk]:
        with self.session_factory() as session:
            try:
                chunks = (
                    session.query(Chunk)
                    .filter(Chunk.document_id == document_id)
                    .order_by(Chunk.chunk_id)
                    .all()
                )
                return chunks
            except Exception as e:
                raise ContentAccessorException(f"Error getting chunks by document: {str(e)}") from e

    def get_document_id_by_chunk_id(self, chunk_id: int) -> int:
        with self.session_factory() as session:
            try:
                # Fetch the document_id from the associated Chunk
                return session.query(Chunk.document_id).filter_by(chunk_id=chunk_id).scalar()
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document_id using statement_id: {str(e)}"
                ) from e

    def delete_all_chunks_for_document(self, document_id: int):
        """
        Deletes all chunks associated with a specified document_id in a single database operation.

        Args:
            document_id (int): The ID of the document for which chunks need to be deleted.

        Raises:
            Exception: If an error occurs during the deletion process.
        """
        with self.session_factory() as session:
            try:
                # Perform a single DELETE operation using SQLAlchemy's delete() construct
                session.query(Chunk).filter_by(document_id=document_id).delete(
                    synchronize_session=False
                )

                # Commit the transaction
                session.commit()
            except Exception as e:
                # Roll back the transaction in case of an error
                session.rollback()
                raise Exception(f"Failed to delete chunks for document_id {document_id}: {e}")

    def update_chunk_subtopics(
        self,
        chunk_id: int,
        business_subtopics: List[Subtopic],
        subtopic_ids: List[int],
        chunk_current_subtopic_statements: List[Statement],
    ) -> Chunk:
        with self.session_factory() as session:
            try:
                # Need to manually pull back the chunk instead of
                # using get_by_id because of session issues
                chunk = (
                    session.query(Chunk)
                    .options(joinedload(Chunk.statements).joinedload(Statement.statement_type))
                    .filter_by(chunk_id=chunk_id)
                    .one_or_none()
                )

                if not chunk:
                    raise ContentAccessorException(f"Chunk with ID {chunk_id} not found")

                # Create a mapping of subtopic names to IDs for easy lookup
                subtopic_name_to_id = {
                    subtopic.name: subtopic.subtopic_id for subtopic in business_subtopics
                }

                # Extract current subtopic IDs by matching statement.text to subtopic.name
                current_subtopic_ids = [
                    subtopic_name_to_id[statement.text]
                    for statement in chunk_current_subtopic_statements
                    if statement.text in subtopic_name_to_id
                ]

                # Find differences between the provided and current subtopic IDs
                to_add = list(set(subtopic_ids) - set(current_subtopic_ids))

                # Determine the names of subtopics to remove
                # based on `current_subtopic_ids` and `subtopic_ids`
                to_remove_names = [
                    name
                    for name, subtopic_id in subtopic_name_to_id.items()
                    if subtopic_id in (set(current_subtopic_ids) - set(subtopic_ids))
                ]

                # Remove statements for subtopics that need to be removed
                if to_remove_names:
                    session.query(Statement).filter(
                        Statement.chunk_id == chunk_id,
                        Statement.text.in_(
                            to_remove_names
                        ),  # Match on `text`, which aligns with subtopic names
                        Statement.statement_type_id
                        == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,  # Ensure type matches
                    ).delete(synchronize_session=False)

                # Create a mapping of subtopic_id to name for easy lookup
                subtopic_id_to_name = {
                    subtopic.subtopic_id: subtopic.name for subtopic in business_subtopics
                }

                # Add new statements for subtopics in bulk
                if to_add:
                    new_statements = [
                        {
                            "chunk_id": chunk_id,
                            "statement_type_id": StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                            "text": subtopic_id_to_name.get(subtopic_id, "Unknown Subtopic"),
                            "rating": "unrated",
                        }
                        for subtopic_id in to_add
                    ]
                    session.bulk_insert_mappings(Statement, new_statements)

                # Commit the changes
                session.commit()

                # Refresh and return the updated chunk
                session.refresh(chunk)

                return chunk

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating chunk subtopics: {str(e)}") from e
