from typing import List

from sqlalchemy.exc import SQLAlchemyError

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import ITopicAccessor
from shared.models import Topic, Business


class TopicAccessor(ITopicAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_topic_by_id(self, topic_id: int) -> Topic:
        with self.session_factory() as session:
            try:
                topic = session.query(Topic).filter(Topic.topic_id == topic_id).one_or_none()
                return topic

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching topic with ID {topic_id}: {str(e)}"
                ) from e

    def save_topic(self, insert_fields: dict) -> Topic:
        with self.session_factory() as session:
            try:
                topic = Topic(
                    business_id=insert_fields["business_id"],
                    name=insert_fields["name"],
                    description=insert_fields["description"],
                )
                session.add(topic)
                session.commit()
                session.refresh(topic)
                return topic
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving topic: {str(e)}") from e

    def update_topic(self, topic_id: int, updated_fields: dict) -> Topic:
        with self.session_factory() as session:
            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_topic_by_id here because it uses a different session
            topic = session.query(Topic).get(topic_id)

            if topic is None:
                raise NotFoundException(f"Topic with ID {topic_id} does not exist.")

            try:

                if "name" in updated_fields:
                    topic.name = updated_fields["name"]
                if "description" in updated_fields:
                    topic.description = updated_fields["description"]

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(topic)

                return topic
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating topic: {str(e)}") from e

    def delete_topic(self, topic_id: int):
        with self.session_factory() as session:
            try:
                session.query(Topic).filter_by(topic_id=topic_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting topic: {str(e)}") from e

    def get_topics_by_enterprise(self, enterprise_id: int) -> List[Topic]:
        """
        Retrieves all topics associated with a given enterprise.

        Args:
            enterprise_id (int): The ID of the enterprise.

        Returns:
            List[Topic]: A list of Topic objects associated with the enterprise.
        """
        with self.session_factory() as session:
            try:
                topics = (
                    session.query(Topic)
                    .join(Business.business_id == Topic.business_id)
                    .filter(Business.enterprise_id == enterprise_id)
                    .filter(Topic.enterprise_id == enterprise_id)
                    .all()
                )
                return topics

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching topics with enterprise_id of {enterprise_id}: {str(e)}"
                ) from e

    def get_topics_by_business(self, business_id: int) -> List[Topic]:
        with self.session_factory() as session:
            try:
                topics = session.query(Topic).filter(Topic.business_id == business_id).all()
                return topics
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching topics by business: {str(e)}"
                ) from e
