from collections import defaultdict
from typing import List

from sqlalchemy.exc import SQLAlchemyError

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IDepartmentAccessor
from shared.models import Department, Business


class DepartmentAccessor(IDepartmentAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_department_by_id(self, department_id: int) -> Department:
        with self.session_factory() as session:
            try:
                department = (
                    session.query(Department)
                    .filter(Department.department_id == department_id)
                    .one_or_none()
                )
                return department

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching department with ID {department_id}: {str(e)}"
                ) from e

    def save_department(self, insert_fields: dict) -> Department:
        with self.session_factory() as session:
            try:
                department = Department(
                    business_id=insert_fields["business_id"],
                    name=insert_fields["name"],
                    description=insert_fields["description"],
                )
                session.add(department)
                session.commit()
                session.refresh(department)
                return department
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving department: {str(e)}") from e

    def update_department(self, department_id: int, updated_fields: dict) -> Department:
        with self.session_factory() as session:
            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_statement_by_id here because it uses a different session
            department = session.query(Department).get(department_id)

            if department is None:
                raise NotFoundException(f"Department with ID {department_id} does not exist.")

            try:

                if "name" in updated_fields:
                    department.name = updated_fields["name"]
                if "description" in updated_fields:
                    department.description = updated_fields["description"]

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(department)

                return department
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating department: {str(e)}") from e

    def delete_department(self, department_id: int):
        with self.session_factory() as session:
            try:
                session.query(Department).filter_by(department_id=department_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting department: {str(e)}") from e

    def get_departments_by_enterprise(self, enterprise_id: int) -> List[Department]:
        """
        Retrieves all departments associated with a given enterprise.

        Args:
            enterprise_id (int): The ID of the enterprise.

        Returns:
            List[Department]: A list of Department objects associated with the enterprise.
        """
        with self.session_factory() as session:
            try:
                departments = (
                    session.query(Department)
                    .join(Business.business_id == Department.business_id)
                    .filter(Business.enterprise_id == enterprise_id)
                    .all()
                )
                return departments

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching department with enterprise_id of {enterprise_id}: {str(e)}"
                ) from e

    def get_departments_by_business(self, business_id: int) -> List[Department]:
        """
        Retrieves all departments associated with a given business.

        Args:
            business_id (int): The ID of the business.

        Returns:
            List[Department]: A list of Department objects associated with the business.
        """
        with self.session_factory() as session:
            try:
                departments = (
                    session.query(Department).filter(Department.business_id == business_id).all()
                )
                return departments

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching department with business_id of {business_id}: {str(e)}"
                ) from e
