from dataclasses import dataclass, field
from datetime import datetime


@dataclass
class DocumentInsertDTO:
    domain_id: int
    name: str
    s3_location: str
    business_id: int = 1  # Default to 1 if not provided
    effective_datetime: datetime = field(
        default_factory=datetime.now
    )  # Default to current datetime when instance is created
    start_page: int = 0  # Default to 1
    end_page: int = 0  # Default to 100
