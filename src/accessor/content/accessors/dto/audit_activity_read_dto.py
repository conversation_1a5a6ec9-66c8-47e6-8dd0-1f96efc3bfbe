from dataclasses import dataclass
from datetime import datetime
from typing import Op<PERSON>

from shared.models import AuditActivity


@dataclass
class AuditActivityReadDTO:
    audit_activity_id: int
    audit_object_type_id: int
    audit_object_type_name: Optional[str]
    object_key: int
    data_snapshot: dict
    user_id: Optional[int]
    user_name: Optional[str]
    audit_operation_type_id: int
    audit_operation_type_name: Optional[str]
    recorded_at: datetime

    @classmethod
    def from_model(cls, audit_activity: "AuditActivity") -> "AuditActivityReadDTO":
        # This assumes you've joined the necessary relationships in your query!
        user_name = None

        first = audit_activity.user.first_name or ""
        last = audit_activity.user.last_name or ""
        user_name = f"{first} {last}".strip() or None

        audit_object_type_name = audit_activity.audit_object_type_name
        audit_operation_type_name = audit_activity.audit_operation_type_name

        return cls(
            audit_activity_id=audit_activity.audit_activity_id,
            audit_object_type_id=audit_activity.audit_object_type_id,
            audit_object_type_name=audit_object_type_name,
            object_key=audit_activity.object_key,
            data_snapshot=audit_activity.data_snapshot,
            user_id=audit_activity.user_id,
            user_name=user_name,
            audit_operation_type_id=audit_activity.audit_operation_type_id,
            audit_operation_type_name=audit_operation_type_name,
            recorded_at=audit_activity.recorded_at,
        )
