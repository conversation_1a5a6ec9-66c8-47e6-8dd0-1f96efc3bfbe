# file: accessor/content/accessors/document_statistics_accessor.py

from typing import <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import aliased

from accessor.content.interfaces.i_document_statistics_accessor import (
    IDocumentStatisticsAccessor,
)
from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO
from shared.models import Document, DocumentStatistics  # Adjust your import paths


class DocumentStatisticsAccessor(IDocumentStatisticsAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def create_document_statistics(
        self, document_id: int, stats_dto: DocumentStatisticsDTO
    ) -> Optional[DocumentStatisticsDTO]:
        """
        Inserts a new record into document_statistics based on the given DTO.
        """
        try:
            with self.session_factory() as session:
                doc_stats = DocumentStatistics(
                    document_id=document_id,
                    word_count=stats_dto.word_count,
                    difficult_word_count=stats_dto.difficult_word_count,
                    average_sentence_length=stats_dto.average_sentence_length,
                    average_word_length=stats_dto.average_word_length,
                    flesch_grade_level=stats_dto.flesch_grade_level,
                    flesch_score=stats_dto.flesch_score,
                    flesch_reading_ease=stats_dto.flesch_reading_ease,
                )

                session.add(doc_stats)
                session.commit()
                session.refresh(doc_stats)

                return DocumentStatisticsDTO(
                    word_count=doc_stats.word_count,
                    difficult_word_count=doc_stats.difficult_word_count,
                    average_sentence_length=doc_stats.average_sentence_length,
                    average_word_length=doc_stats.average_word_length,
                    flesch_grade_level=doc_stats.flesch_grade_level,
                    flesch_score=doc_stats.flesch_score,
                    flesch_reading_ease=doc_stats.flesch_reading_ease,
                    created_at=doc_stats.created_at,
                    updated_at=doc_stats.updated_at,
                )
        except SQLAlchemyError as e:
            # Log or handle exception
            print(f"Error inserting document statistics: {e}")
            return None

    def get_document_statistics_by_document_id(
        self, document_id: int
    ) -> Tuple[Optional[DocumentStatisticsDTO], Optional[DocumentStatisticsDTO]]:
        """
        Get document statistics for a document and its original document (if exists).
        """
        ds1 = aliased(DocumentStatistics)
        ds2 = aliased(DocumentStatistics)

        with self.session_factory() as session:
            result = (
                session.query(
                    Document,
                    ds1.word_count.label("ds1_word_count"),
                    ds1.difficult_word_count.label("ds1_difficult_word_count"),
                    ds1.average_sentence_length.label("ds1_avg_sentence_length"),
                    ds1.average_word_length.label("ds1_avg_word_length"),
                    ds1.flesch_grade_level.label("ds1_flesch_grade_level"),
                    ds1.flesch_score.label("ds1_flesch_score"),
                    ds1.flesch_reading_ease.label("ds1_flesch_reading_ease"),
                    ds1.created_at.label("ds1_created_at"),
                    ds1.updated_at.label("ds1_updated_at"),
                    ds2.word_count.label("ds2_word_count"),
                    ds2.difficult_word_count.label("ds2_difficult_word_count"),
                    ds2.average_sentence_length.label("ds2_avg_sentence_length"),
                    ds2.average_word_length.label("ds2_avg_word_length"),
                    ds2.flesch_grade_level.label("ds2_flesch_grade_level"),
                    ds2.flesch_score.label("ds2_flesch_score"),
                    ds2.flesch_reading_ease.label("ds2_flesch_reading_ease"),
                    ds2.created_at.label("ds2_created_at"),
                    ds2.updated_at.label("ds2_updated_at"),
                )
                .select_from(Document)
                .outerjoin(ds1, Document.document_id == ds1.document_id)
                .outerjoin(ds2, Document.original_document_id == ds2.document_id)
                .filter(Document.document_id == document_id)
                .first()
            )

            if not result:
                return None, None

            document_stats = (
                DocumentStatisticsDTO(
                    word_count=result.ds1_word_count,
                    difficult_word_count=result.ds1_difficult_word_count,
                    average_sentence_length=result.ds1_avg_sentence_length,
                    average_word_length=result.ds1_avg_word_length,
                    flesch_grade_level=result.ds1_flesch_grade_level,
                    flesch_score=result.ds1_flesch_score,
                    flesch_reading_ease=result.ds1_flesch_reading_ease,
                    created_at=result.ds1_created_at,
                    updated_at=result.ds1_updated_at,
                )
                if result.ds1_word_count is not None
                else None
            )

            original_stats = (
                DocumentStatisticsDTO(
                    word_count=result.ds2_word_count,
                    difficult_word_count=result.ds2_difficult_word_count,
                    average_sentence_length=result.ds2_avg_sentence_length,
                    average_word_length=result.ds2_avg_word_length,
                    flesch_grade_level=result.ds2_flesch_grade_level,
                    flesch_score=result.ds2_flesch_score,
                    flesch_reading_ease=result.ds2_flesch_reading_ease,
                    created_at=result.ds2_created_at,
                    updated_at=result.ds2_updated_at,
                )
                if result.ds2_word_count is not None
                else None
            )

            return document_stats, original_stats
