from typing import List, Optional
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.interfaces import IAcceptanceCriteriaAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException


from accessor.content.interfaces import IDomainAccessor
from shared.models import Domain


class DomainAccessor(IDomainAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_domain_by_id(self, domain_id: int) -> Optional[Domain]:
        """
        Retrieves a domain record by its unique ID.

        This method queries the database for an acceptance criteria record
        matching the provided `domain_id`. If a matching record
        is found, it is returned; otherwise, the method returns `None`.

        Args:
            domain_id (int): The unique identifier of the domain to retrieve.

        Returns:
            Optional[Domain]: The matching `Domain` instance if found,
                                          or `None` if no match exists.
        """
        with self.session_factory() as session:
            return session.query(Domain).filter_by(domain_id=domain_id).first()
