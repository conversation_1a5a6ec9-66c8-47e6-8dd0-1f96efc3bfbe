from collections import defaultdict
from typing import List

from sqlalchemy.exc import SQLAlchemyError

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IPersonaAccessor
from shared.models import (
    Persona,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    department_subtopic_association,
    Department,
    Business,
)


class PersonaAccessor(IPersonaAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_persona_by_id(self, persona_id: int) -> Persona:
        with self.session_factory() as session:
            try:
                persona = (
                    session.query(Persona).filter(Persona.persona_id == persona_id).one_or_none()
                )
                return persona

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching persona with ID {persona_id}: {str(e)}"
                ) from e

    def get_personas_for_unique_statements(self, document_id: int) -> dict:
        """
        Retrieves personas associated with all unique statements in one query,
        filtering by document_id. Also includes subtopic_id in the results.

        Args:
            document_id (int): The ID of the document to filter unique statements.

        Returns:
            dict: A dictionary where keys are unique_statement_ids, and values
            are lists of dictionaries containing subtopic_id, persona_name,
            and persona_description.
        """
        with self.session_factory() as session:
            try:
                # Query to fetch unique_statement_id, subtopic_id, and persona details
                result = (
                    session.query(
                        UniqueStatement.unique_statement_id,
                        UniqueStatementSubtopicAssociation.subtopic_id,
                        Persona.name.label("persona_name"),
                        Persona.description.label("persona_description"),
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .join(
                        department_subtopic_association,
                        UniqueStatementSubtopicAssociation.subtopic_id
                        == department_subtopic_association.c.subtopic_id,
                    )
                    .join(
                        Persona,
                        department_subtopic_association.c.department_id == Persona.department_id,
                    )
                    .filter(UniqueStatement.document_id == document_id)
                    .order_by(
                        UniqueStatement.unique_statement_id,
                        UniqueStatementSubtopicAssociation.subtopic_id,
                    )
                    .all()
                )

                # Use defaultdict(list) to automatically initialize lists
                persona_mapping = defaultdict(list)
                for unique_statement_id, subtopic_id, persona_name, persona_description in result:
                    persona_mapping[unique_statement_id].append(
                        {
                            "subtopic_id": subtopic_id,
                            "name": persona_name,
                            "description": persona_description,
                        }
                    )

                return dict(persona_mapping)

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(
                    f"Error fetching personas for unique statements: {str(e)}"
                ) from e

    def save_persona(self, insert_fields: dict) -> Persona:
        with self.session_factory() as session:
            try:
                persona = Persona(
                    department_id=insert_fields["department_id"],
                    name=insert_fields["name"],
                    description=insert_fields["description"],
                )
                session.add(persona)
                session.commit()
                session.refresh(persona)
                return persona
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving persona: {str(e)}") from e

    def update_persona(self, persona_id: int, updated_fields: dict) -> Persona:
        with self.session_factory() as session:
            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_statement_by_id here because it uses a different session
            persona = session.query(Persona).get(persona_id)

            if persona is None:
                raise NotFoundException(f"Persona with ID {persona_id} does not exist.")

            try:

                if "name" in updated_fields:
                    persona.name = updated_fields["name"]
                if "description" in updated_fields:
                    persona.description = updated_fields["description"]

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(persona)

                return persona
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating persona: {str(e)}") from e

    def delete_persona(self, persona_id: int):
        with self.session_factory() as session:
            try:
                session.query(Persona).filter_by(persona_id=persona_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting persona: {str(e)}") from e

    def get_personas_by_department(self, department_id: int) -> List[Persona]:
        """
        Retrieves all personas associated with a given department.

        Args:
            department_id (int): The ID of the department.

        Returns:
            List[Persona]: A list of Persona objects associated with the department.
        """
        with self.session_factory() as session:
            try:
                personas = (
                    session.query(Persona).filter(Persona.department_id == department_id).all()
                )
                return personas

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching persona with department_id of {department_id}: {str(e)}"
                ) from e

    def get_personas_by_enterprise(self, enterprise_id: int) -> List[Persona]:
        """
        Retrieves all personas associated with a given enterprise.

        Args:
            enterprise_id (int): The ID of the enterprise.

        Returns:
            List[Persona]: A list of Persona objects associated with the enterprise.
        """
        with self.session_factory() as session:
            try:
                personas = (
                    session.query(Persona)
                    .join(Department.department_id == Persona.department_id)
                    .join(Business.business_id == Department.business_id)
                    .filter(Business.enterprise_id == enterprise_id)
                )
                return personas

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching persona with enterprise_id of {enterprise_id}: {str(e)}"
                ) from e
