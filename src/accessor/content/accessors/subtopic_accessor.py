from typing import List, Tuple
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import ISubtopicAccessor

from shared.enums import StatementTypeEnum
from shared.models import (
    Subtopic,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    Statement,
    Chunk,
    Topic,
    Business,
    Enterprise,
)


class SubtopicAccessor(ISubtopicAccessor):

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def verify_subtopic_exists(self, subtopic_id: int) -> bool:
        with self.session_factory() as session:
            try:
                return session.query(
                    session.query(Subtopic).filter_by(subtopic_id=subtopic_id).exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if subtopic exists: {str(e)}"
                ) from e

    def get_subtopic_by_id(self, subtopic_id: int) -> Subtopic:
        with self.session_factory() as session:
            try:
                return session.query(Subtopic).filter_by(subtopic_id=subtopic_id).one_or_none()
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching subtopic: {str(e)}") from e

    def get_all_subtopics(self) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                return (
                    session.query(Subtopic).options(joinedload(Subtopic.topic)).all()
                )  # Eagerly load topic relationship

            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching all subtopics: {str(e)}") from e

    def get_subtopics_ordered(self, document_id: int, statement_type_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                return (
                    session.query(Subtopic)
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id,
                    )
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                    )
                    .order_by(Subtopic.subtopic_id)
                    .all()
                )
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching ordered subtopics: {str(e)}") from e

    def get_all_subtopics_ordered(self, document_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                return (
                    session.query(Subtopic)
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id,
                    )
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(UniqueStatement.document_id == document_id)
                    .order_by(Subtopic.subtopic_id)
                    .all()
                )
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching ordered subtopics: {str(e)}") from e

    def get_subtopic_statements_for_chunk(self, chunk_id: int):
        with self.session_factory() as session:
            try:
                # Query to get the chunk_id based on the vector_id
                subtopic_statements = (
                    session.query(Statement)
                    .filter(
                        Statement.chunk_id == chunk_id,
                        Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                    )
                    .all()
                )
                return subtopic_statements
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching subtopic statements for chunk: {str(e)}"
                ) from e

    def get_relevant_subtopics_for_unique_statement_generation(
        self, document_id: int
    ) -> List[Tuple[int, str]]:
        with self.session_factory() as session:
            try:
                # Query to get relevant subtopics based on the chunk_ids for related document_id
                relevant_subtopics = (
                    session.query(Subtopic.subtopic_id, Subtopic.name)
                    .join(Statement, Statement.text == Subtopic.name)
                    .join(Chunk, Statement.chunk_id == Chunk.chunk_id)
                    .filter(
                        Chunk.document_id == document_id,
                        Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                    )
                    .all()
                )
                return relevant_subtopics
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching relevant subtopics for document {document_id}: {str(e)}"
                ) from e

    def get_subtopics_by_topic_id(self, topic_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                subtopics = session.query(Subtopic).filter(Subtopic.topic_id == topic_id).all()
                return subtopics
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching subtopic by topid_id: {str(e)}"
                ) from e

    def get_subtopics_by_business(self, business_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                subtopics = (
                    session.query(Subtopic).filter(Subtopic.business_id == business_id).all()
                )
                return subtopics
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching subtopic by business: {str(e)}"
                ) from e

    def get_subtopics_by_document(self, document_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                # Join Subtopic with UniqueStatement via the association table and filter by document_id
                subtopics = (
                    session.query(Subtopic)
                    .distinct(Subtopic.subtopic_id)  # Ensure distinct subtopics
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id,
                    )
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(UniqueStatement.document_id == document_id)
                    .all()
                )
                return subtopics
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching subtopics by document: {str(e)}"
                ) from e

    def get_identified_subtopics_by_chunk(self, chunk_id: int) -> List[Subtopic]:
        with self.session_factory() as session:
            try:
                # Query to get relevant Subtopic objects based on the chunk_id
                relevant_subtopics = (
                    session.query(Subtopic)
                    .join(Statement, Statement.text == Subtopic.name)
                    .filter(
                        Statement.chunk_id == chunk_id,
                        Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                    )
                    .all()
                )
                return relevant_subtopics
            except Exception as e:
                self.logger.exception(
                    f"Error fetching relevant subtopics as objects for chunk {chunk_id}: {str(e)}"
                )
                raise ContentAccessorException(
                    f"Error fetching relevant subtopics as objects for chunk {chunk_id}"
                ) from e

    def get_subtopics_by_enterprise(self, enterprise_id: int) -> List[Subtopic]:
        """
        Retrieves all subtopics associated with a given enterprise.

        Args:
            enterprise_id (int): The ID of the enterprise.

        Returns:
            List[Subtopic]: A list of Subtopic objects associated with the enterprise.
        """
        with self.session_factory() as session:
            try:
                subtopics = (
                    session.query(Subtopic)
                    .join(Topic.topic_id == Subtopic.topic_id)
                    .join(Business.business_id == Topic.business_id)
                    .join(Enterprise.enterprise_id == Business.enterprise_id)
                    .filter(Enterprise.enterprise_id == enterprise_id)
                    .options(joinedload(Subtopic.topic))
                    .all()
                )
                return subtopics

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching subtopic with enterprise_id of {enterprise_id}: {str(e)}"
                ) from e

    def save_subtopic(self, insert_fields: dict) -> Subtopic:
        with self.session_factory() as session:
            try:
                subtopic = Subtopic(
                    topic_id=insert_fields["topic_id"],
                    business_id=insert_fields["business_id"],
                    name=insert_fields["name"],
                    description=insert_fields["description"],
                )
                session.add(subtopic)
                session.commit()
                session.refresh(subtopic)
                return subtopic
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving subtopic: {str(e)}") from e

    def update_subtopic(self, subtopic_id: int, updated_fields: dict) -> Subtopic:
        with self.session_factory() as session:
            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_statement_by_id here because it uses a different session
            subtopic = session.query(Subtopic).get(subtopic_id)

            if subtopic is None:
                raise NotFoundException(f"Subtopic with ID {subtopic_id} does not exist.")

            try:

                if "name" in updated_fields:
                    subtopic.name = updated_fields["name"]
                if "description" in updated_fields:
                    subtopic.description = updated_fields["description"]

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(subtopic)

                return subtopic
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating subtopic: {str(e)}") from e

    def delete_subtopic(self, subtopic_id: int):
        with self.session_factory() as session:
            try:
                session.query(Subtopic).filter_by(subtopic_id=subtopic_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting subtopic: {str(e)}") from e
