from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from typing import Optional
from util.database import Session

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IJobAccessor

from shared.models import Job, Statement, Chunk, Document
from shared.enums.statement_type_enum import StatementTypeEnum
from shared.enums.job_enums import JobStatusEnum, JobTypeEnum


class JobAccessor(IJobAccessor):

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_job_by_id(self, job_id: int) -> Job:
        with self.session_factory() as session:
            try:
                return session.query(Job).filter_by(job_id=job_id).one_or_none()
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching job: {str(e)}") from e

    @staticmethod
    def get_job_status_statement_filters(
        document_id: int, chunk_id: Optional[int] = None
    ) -> Optional[int]:
        session = Session()
        try:
            job_status_id = (
                session.query(Job.job_status_id)
                .join(Document, Job.reference_id == Document.document_id)
                .filter(Document.document_id == document_id)
                .scalar()
            )

            filters = []
            if chunk_id:
                filters.append(Statement.chunk_id == chunk_id)  # Use chunk_id if available
            else:
                filters.append(Chunk.document_id == document_id)  # Otherwise, filter by document_id

            if job_status_id == JobStatusEnum.SUBTOPICS_READY_FOR_CURATION.value:
                filters.append(
                    Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value
                )
            else:
                filters.append(
                    Statement.statement_type_id != StatementTypeEnum.IDENTIFIED_SUBTOPIC.value
                )

            return filters
        finally:
            session.close()

    def save_job(self, insert_fields: dict) -> Job:
        with self.session_factory() as session:
            try:
                job = Job(
                    reference_id=insert_fields["reference_id"],
                    job_status_id=insert_fields["job_status_id"],
                    job_type_id=insert_fields.get("job_type_id", JobTypeEnum.ARTIFACT_GENERATION.value),
                )
                session.add(job)
                session.commit()
                session.refresh(job)
                return job
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving job: {str(e)}") from e

    def update_job(self, job_id: int, updated_fields: dict) -> Job:
        with self.session_factory() as session:
            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_statement_by_id here because it uses a different session
            job = (
                session.query(Job)
                .options(joinedload(Job.job_status), joinedload(Job.document), joinedload(Job.job_type))
                .get(job_id)
            )

            if job is None:
                raise NotFoundException(f"Job with ID {job_id} does not exist.")

            try:

                if "job_status_id" in updated_fields:
                    job.job_status_id = updated_fields["job_status_id"]

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(job)

                return job
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating job: {str(e)}") from e
