from typing import List, Optional

from sqlalchemy import case, func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

from accessor.content.accessors.job_accessor import JobAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IStatementAccessor
from shared.models import Chunk, RatingEnum, Statement


class StatementAccessor(IStatementAccessor):

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def verify_statement_exists(self, statement_id: int) -> bool:
        with self.session_factory() as session:
            try:
                return session.query(
                    session.query(Statement).filter_by(statement_id=statement_id).exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if statement exists: {str(e)}"
                ) from e

    def get_statement_by_id(self, statement_id: int) -> Optional[Statement]:
        with self.session_factory() as session:
            try:
                return session.query(Statement).filter_by(statement_id=statement_id).one_or_none()
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching statement: {str(e)}") from e

    def get_statements_by_chunk_and_type(
        self, chunk_id: int, statement_type_id: int
    ) -> List[Statement]:
        with self.session_factory() as session:
            try:
                statements = (
                    session.query(Statement)
                    .options(joinedload(Statement.statement_type))  # Eager load statement_type
                    .join(Chunk, Statement.chunk_id == Chunk.chunk_id)
                    .filter(
                        Chunk.chunk_id == chunk_id,
                        Statement.statement_type_id == statement_type_id,
                        Statement.rating != RatingEnum.negative.value,
                    )
                    .all()
                )
                return statements
            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(
                    f"Error fetching statement by chunk and type: {str(e)}"
                ) from e

    def get_statements_by_document_and_type(
        self, document_id: int, statement_type_id: int
    ) -> List[Statement]:
        with self.session_factory() as session:
            try:
                statements = (
                    session.query(Statement)
                    .options(joinedload(Statement.statement_type))  # Eager load statement_type
                    .join(Chunk, Statement.chunk_id == Chunk.chunk_id)
                    .filter(
                        Chunk.document_id == document_id,
                        Statement.statement_type_id == statement_type_id,
                    )
                    .all()
                )
                return statements
            except SQLAlchemyError as e:
                session.rollback()
                raise ContentAccessorException(
                    f"Error fetching statement by document and type: {str(e)}"
                ) from e

    def save_statement(self, insert_fields: dict) -> Statement:
        with self.session_factory() as session:
            try:
                statement = Statement(
                    chunk_id=insert_fields["chunk_id"],
                    statement_type_id=insert_fields["statement_type_id"],
                    text=insert_fields["text"],
                    rating=insert_fields.get("rating", RatingEnum.unrated),
                    user_entered=insert_fields.get("user_entered", False),
                )
                session.add(statement)
                session.commit()
                session.refresh(statement)

                return statement
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving statement: {str(e)}") from e

    def get_statements_by_vector_and_type(self, vector_id: str, statement_type_id: int):
        with self.session_factory() as session:
            try:
                statements = (
                    session.query(Statement)
                    .join(Chunk, Statement.chunk_id == Chunk.chunk_id)
                    .filter(
                        Chunk.vector_id == vector_id,
                        Statement.statement_type_id == statement_type_id,
                    )
                    .all()
                )
                return statements
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(
                    f"Error getting statements by vector and type: {str(e)}"
                ) from e

    def update_statement(self, statement_id: int, updated_fields: dict) -> Statement:
        with self.session_factory() as session:
            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_statement_by_id here because it uses a different session
            statement = session.query(Statement).get(statement_id)

            if statement is None:
                raise NotFoundException(f"Statement with ID {statement_id} does not exist.")

            try:

                if "statement_type_id" in updated_fields:
                    statement.statement_type_id = updated_fields["statement_type_id"]
                if "text" in updated_fields:
                    statement.text = updated_fields["text"]
                if "rating" in updated_fields:
                    statement.rating = RatingEnum(
                        updated_fields["rating"]
                    )  # Convert to enum if provided

                # Set `edited` to True only if `statement_type_id` or `text` is being updated
                if "statement_type_id" in updated_fields or "text" in updated_fields:
                    statement.edited = True

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(statement)

                return statement
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating statement: {str(e)}") from e

    def get_statement_counts(self, document_id: int) -> dict:
        with self.session_factory() as session:
            try:
                # Use helper function for subtopic and non-subtopic filtering
                filters = JobAccessor.get_job_status_statement_filters(document_id)

                # Perform a single query to get:
                # - unrated statement count for the current chunk
                # - remaining chunks with at least one unrated statement
                # - grouped data for unrated, positive, and negative statements
                counts_query = (
                    session.query(
                        # Count unrated statements for the current chunk
                        func.count(Statement.statement_id)
                        .filter(Statement.rating == RatingEnum.unrated.value)
                        .label("unrated_statement_count"),
                        # Count the number of remaining chunks with at least one unrated statement
                        func.count(func.distinct(Chunk.chunk_id))
                        .filter(Statement.rating == RatingEnum.unrated.value)
                        .label("remaining_chunks"),
                        # Total unrated, positive, and negative statement counts using case
                        func.count(case((Statement.rating == RatingEnum.unrated.value, 1))).label(
                            "unrated_count"
                        ),
                        func.count(case((Statement.rating == RatingEnum.positive.value, 1))).label(
                            "positive_count"
                        ),
                        func.count(case((Statement.rating == RatingEnum.negative.value, 1))).label(
                            "negative_count"
                        ),
                    )
                    .join(Chunk, Chunk.chunk_id == Statement.chunk_id)
                    .filter(*filters)
                    .one_or_none()
                )

                # Convert the counts_query result to a dictionary
                if counts_query:
                    result = {
                        "unrated_statement_count": counts_query.unrated_statement_count,
                        "remaining_chunks": counts_query.remaining_chunks,
                        "unrated_count": counts_query.unrated_count,
                        "positive_count": counts_query.positive_count,
                        "negative_count": counts_query.negative_count,
                    }
                else:
                    result = {
                        "unrated_statement_count": 0,
                        "remaining_chunks": 0,
                        "unrated_count": 0,
                        "positive_count": 0,
                        "negative_count": 0,
                    }

                return result

            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error getting statement counts: {str(e)}") from e

    def delete_statement(self, statement_id: int):
        with self.session_factory() as session:
            try:
                session.query(Statement).filter_by(statement_id=statement_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting statement: {str(e)}") from e
