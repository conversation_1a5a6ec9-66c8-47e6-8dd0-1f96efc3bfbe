from typing import Optional

from sqlalchemy import case, func, or_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import aliased, joinedload

from accessor.content.accessors.dto import DocumentInsertDTO, DocumentUpdateDTO
from accessor.content.accessors.job_accessor import JobAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IDocumentAccessor
from accessor.document.dto import DocumentStatisticsDTO
from shared.enums.job_enums import JobTypeEnum
from shared.enums.statement_type_enum import StatementTypeEnum
from shared.models import (
    AcceptanceCriteria,
    Chunk,
    Document,
    DocumentStatistics,
    Job,
    RatingEnum,
    Statement,
    Subtopic,
    TestCase,
    Topic,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    UserStory,
)

# Document version keys
VERSION_ORIGINAL_WORD = "original_word"
VERSION_OPTIMIZED_WORD = "optimized_word"
VERSION_OPTIMIZED_PDF = "optimized_pdf"
VERSION_ORIGINAL_PDF = "original_pdf"

# Document ID field keys
ORIGINAL_WORD_DOC_ID = "original_word_document_id"
OPTIMIZED_WORD_DOC_ID = "optimized_word_document_id"
OPTIMIZED_PDF_DOC_ID = "optimized_pdf_document_id"
ORIGINAL_PDF_DOC_ID = "original_pdf_document_id"


class DocumentAccessor(IDocumentAccessor):

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def verify_document_exists(self, document_id: int) -> bool:
        if document_id is None:
            raise TypeError("document_id cannot be None")

        try:
            with self.session_factory() as session:
                return session.query(
                    session.query(Document).filter_by(document_id=document_id).exists()
                ).scalar()
        except Exception as e:
            raise ContentAccessorException(f"Error verifying that document exists: {str(e)}") from e

    def save_document(self, insert_dto: DocumentInsertDTO) -> Document:
        with self.session_factory() as session:
            try:
                # Dynamically unpacks the attributes of the dto into the Document class constructor
                new_document = Document(**insert_dto.__dict__)
                session.add(new_document)
                session.commit()

                # Eagerly load the `job` relationship
                document_with_relationships = (
                    session.query(Document)
                    .options(joinedload(Document.job))  # Replace with your actual relationship name
                    .get(new_document.document_id)
                )

                return document_with_relationships

            except Exception as e:
                # If there is an error, rollback the session
                session.rollback()
                raise e  # Re-raise the exception for handling by the caller

    def update_document(self, document_id: int, update_dto: DocumentUpdateDTO) -> Document:
        with self.session_factory() as session:
            try:
                query = (
                    session.query(Document)
                    .options(joinedload(Document.job))
                    .filter_by(document_id=document_id)
                )

                document_to_update = query.one_or_none()

                if not document_to_update:
                    raise ValueError(f"Document with ID {document_id} not found.")

                # Update fields using the DTO
                for field, value in update_dto.__dict__.items():
                    if value is not None:  # Skip None values
                        setattr(document_to_update, field, value)

                session.commit()

                # Refresh the instance to ensure it is fully loaded
                session.refresh(document_to_update)

                return document_to_update
            except Exception:
                session.rollback()
                raise

    def get_document_by_id(self, document_id: int) -> Document:
        with self.session_factory() as session:
            document = (
                session.query(Document)
                .options(joinedload(Document.job))
                .filter_by(document_id=document_id)
                .one_or_none()
            )
            return document

    def delete_document(self, document_id: int) -> str:
        document = self.get_document_by_id(document_id)

        if document is None:
            return "Document not found or access denied"

        with self.session_factory() as session:
            try:
                session.delete(document)
                session.commit()
                return "Document deleted successfully"
            except SQLAlchemyError as e:
                session.rollback()
                return "Error deleting document: " + str(e)
            finally:
                session.close()

    def get_document_statement_counts(self, document_id: int):
        with self.session_factory() as session:
            try:
                # Use helper function for subtopic and non-subtopic filtering
                filters = JobAccessor.get_job_status_statement_filters(document_id)

                query = (
                    session.query(
                        func.count()
                        .filter(Statement.rating == RatingEnum.unrated.value)
                        .label("unrated_count"),
                        func.count()
                        .filter(Statement.rating == RatingEnum.positive.value)
                        .label("positive_count"),
                        func.count()
                        .filter(Statement.rating == RatingEnum.negative.value)
                        .label("negative_count"),
                    )
                    .join(Chunk, Statement.chunk_id == Chunk.chunk_id)
                    .join(Document, Chunk.document_id == Document.document_id)
                    .filter(Chunk.document_id == document_id, *filters)
                )

                statement_counts_row = query.group_by(Chunk.document_id).one_or_none()

                if statement_counts_row:
                    return {
                        "unrated_count": getattr(statement_counts_row, "unrated_count", 0),
                        "positive_count": getattr(statement_counts_row, "positive_count", 0),
                        "negative_count": getattr(statement_counts_row, "negative_count", 0),
                    }

                return {
                    "unrated_count": 0,
                    "positive_count": 0,
                    "negative_count": 0,
                }

            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document statement counts: {str(e)}"
                ) from e

    def get_document_unique_requirement_counts(self, document_id: int):
        with self.session_factory() as session:
            try:
                query = (
                    session.query(
                        UniqueStatement.document_id,
                        func.count()
                        .filter(UniqueStatement.rating == RatingEnum.unrated.value)
                        .label("unrated_count"),
                        func.count()
                        .filter(UniqueStatement.rating == RatingEnum.positive.value)
                        .label("positive_count"),
                        func.count()
                        .filter(UniqueStatement.rating == RatingEnum.negative.value)
                        .label("negative_count"),
                    )
                    .join(Document, UniqueStatement.document_id == Document.document_id)
                    .filter(UniqueStatement.document_id == document_id)
                )

                grouped_data = query.group_by(UniqueStatement.document_id).one_or_none()

                return {
                    "unrated_count": grouped_data.unrated_count if grouped_data else 0,
                    "positive_count": grouped_data.positive_count if grouped_data else 0,
                    "negative_count": grouped_data.negative_count if grouped_data else 0,
                }

            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document unique requirement counts: {str(e)}"
                ) from e

    def get_document_count_chunks_with_unrated_statements(self, document_id: int):
        with self.session_factory() as session:
            try:
                filters = JobAccessor.get_job_status_statement_filters(document_id)

                query = (
                    session.query(func.count(Chunk.chunk_id.distinct()))
                    .join(Statement, Chunk.chunk_id == Statement.chunk_id)
                    .join(Document, Chunk.document_id == Document.document_id)
                    .filter(
                        Chunk.document_id == document_id,
                        Statement.rating == RatingEnum.unrated.value,
                        *filters,
                    )
                )

                return query.scalar()

            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error counting unrated statement chunks: {str(e)}"
                ) from e

    def get_document_bulk_generation_button_statuses(self, document_id: int):
        with self.session_factory() as session:
            try:

                # Subquery for user stories without acceptance criteria
                user_story_query = (
                    session.query(UserStory)
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id == UserStory.unique_statement_id,
                    )
                    .outerjoin(
                        AcceptanceCriteria,
                        AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        AcceptanceCriteria.acceptance_criteria_id == None,
                        or_(
                            UserStory.rating == RatingEnum.unrated.value,
                            UserStory.rating == RatingEnum.positive.value,
                        ),
                    )
                )

                # Subquery for ACs without test cases
                ac_query = (
                    session.query(AcceptanceCriteria)
                    .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id == UserStory.unique_statement_id,
                    )
                    .outerjoin(
                        TestCase,
                        TestCase.acceptance_criteria_id
                        == AcceptanceCriteria.acceptance_criteria_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        TestCase.test_case_id == None,
                        or_(
                            AcceptanceCriteria.rating == RatingEnum.unrated.value,
                            AcceptanceCriteria.rating == RatingEnum.positive.value,
                        ),
                    )
                )

                has_unrated_or_positive_user_story_without_ac = session.query(
                    user_story_query.exists()
                ).scalar()
                has_unrated_or_positive_ac_without_tc = session.query(ac_query.exists()).scalar()

                return {
                    "can_generate_ac": has_unrated_or_positive_user_story_without_ac,
                    "can_generate_tc": has_unrated_or_positive_ac_without_tc,
                }

            except Exception as e:
                raise ContentAccessorException(
                    f"Error determining bulk generation button statuses: {str(e)}"
                ) from e

    def get_document_counts_all_levels(self, document_id: int) -> dict[str, int]:
        if not isinstance(document_id, (int, str)):
            raise TypeError(
                f"Expected an integer or string document ID, but got {type(document_id).__name__}."
            )

        with self.session_factory() as session:
            unrated_count = positive_count = negative_count = 0

            def accumulate_counts(counts):
                return counts.unrated_count, counts.positive_count, counts.negative_count

            # Apply base UniqueStatement filter
            base_us_filter = [UniqueStatement.document_id == document_id]

            # UniqueStatement level
            unique_statement_counts = (
                session.query(
                    func.count()
                    .filter(UniqueStatement.rating == RatingEnum.unrated.value)
                    .label("unrated_count"),
                    func.count()
                    .filter(UniqueStatement.rating == RatingEnum.positive.value)
                    .label("positive_count"),
                    func.count()
                    .filter(UniqueStatement.rating == RatingEnum.negative.value)
                    .label("negative_count"),
                )
                .join(Document, UniqueStatement.document_id == Document.document_id)
                .filter(*base_us_filter)
                .one()
            )

            unrated_unique, positive_unique, negative_unique = accumulate_counts(
                unique_statement_counts
            )
            unrated_count += unrated_unique
            positive_count += positive_unique
            negative_count += negative_unique

            # UserStory level
            user_story_counts = (
                session.query(
                    func.count()
                    .filter(UserStory.rating == RatingEnum.unrated.value)
                    .label("unrated_count"),
                    func.count()
                    .filter(UserStory.rating == RatingEnum.positive.value)
                    .label("positive_count"),
                    func.count()
                    .filter(UserStory.rating == RatingEnum.negative.value)
                    .label("negative_count"),
                )
                .join(
                    UniqueStatement,
                    UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                )
                .join(Document, UniqueStatement.document_id == Document.document_id)
                .filter(*base_us_filter)
                .one()
            )

            unrated_user_story, positive_user_story, negative_user_story = accumulate_counts(
                user_story_counts
            )
            unrated_count += unrated_user_story
            positive_count += positive_user_story
            negative_count += negative_user_story

            # Count ratings for AcceptanceCriteria level under the same document_id
            acceptance_criteria_counts = (
                session.query(
                    func.count()  # pylint: disable=not-callable
                    .filter(AcceptanceCriteria.rating == RatingEnum.unrated.value)
                    .label("unrated_count"),
                    func.count()  # pylint: disable=not-callable
                    .filter(AcceptanceCriteria.rating == RatingEnum.positive.value)
                    .label("positive_count"),
                    func.count()  # pylint: disable=not-callable
                    .filter(AcceptanceCriteria.rating == RatingEnum.negative.value)
                    .label("negative_count"),
                )
                .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
                .join(
                    UniqueStatement,
                    UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                )
                .join(Document, UniqueStatement.document_id == Document.document_id)
                .filter(*base_us_filter)
                .one()
            )

            unrated_ac, positive_ac, negative_ac = accumulate_counts(acceptance_criteria_counts)
            unrated_count += unrated_ac
            positive_count += positive_ac
            negative_count += negative_ac

            # Count ratings for TestCase level under the same document_id
            test_case_counts = (
                session.query(
                    func.count()  # pylint: disable=not-callable
                    .filter(TestCase.rating == RatingEnum.unrated.value)
                    .label("unrated_count"),
                    func.count()  # pylint: disable=not-callable
                    .filter(TestCase.rating == RatingEnum.positive.value)
                    .label("positive_count"),
                    func.count()  # pylint: disable=not-callable
                    .filter(TestCase.rating == RatingEnum.negative.value)
                    .label("negative_count"),
                )
                .join(
                    AcceptanceCriteria,
                    TestCase.acceptance_criteria_id == AcceptanceCriteria.acceptance_criteria_id,
                )
                .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
                .join(
                    UniqueStatement,
                    UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                )
                .join(Document, UniqueStatement.document_id == Document.document_id)
                .filter(*base_us_filter)
                .one()
            )

            unrated_tc, positive_tc, negative_tc = accumulate_counts(test_case_counts)
            unrated_count += unrated_tc
            positive_count += positive_tc
            negative_count += negative_tc

            return {
                "unrated_count": unrated_count,
                "positive_count": positive_count,
                "negative_count": negative_count,
                "unrated_requirements_count": unrated_unique,
                "positive_requirements_count": positive_unique,
                "negative_requirements_count": negative_unique,
                "unrated_user_story_count": unrated_user_story,
                "positive_user_story_count": positive_user_story,
                "negative_user_story_count": negative_user_story,
                "unrated_acceptance_criteria_count": unrated_ac,
                "positive_acceptance_criteria_count": positive_ac,
                "negative_acceptance_criteria_count": negative_ac,
                "unrated_test_case_count": unrated_tc,
                "positive_test_case_count": positive_tc,
                "negative_test_case_count": negative_tc,
            }

    def get_document_by_acceptance_criteria_id(self, acceptance_criteria_id: int) -> Document:
        with self.session_factory() as session:
            try:

                document = (
                    session.query(Document)
                    .join(UniqueStatement, UniqueStatement.document_id == Document.document_id)
                    .join(
                        UserStory,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        AcceptanceCriteria,
                        AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                    )
                    .filter(AcceptanceCriteria.acceptance_criteria_id == acceptance_criteria_id)
                    .one_or_none()
                )

                return document
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document by acceptance criteria id: {str(e)}"
                ) from e

    def get_subtopic_counts(self, document_id: int) -> dict:
        with self.session_factory() as session:
            try:
                if not self.verify_document_exists(document_id):
                    raise NotFoundException(f"Document with ID {document_id} does not exist.")

                query = (
                    session.query(
                        func.count(
                            func.distinct(
                                case(
                                    (
                                        Statement.rating == RatingEnum.unrated.value,
                                        Statement.statement_type_id
                                        == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                                    ),
                                    else_=None,
                                )
                            )
                        ).label("unrated_requirements_count"),
                        func.count(
                            func.distinct(
                                case(
                                    (
                                        Statement.rating == RatingEnum.unrated.value,
                                        Statement.statement_type_id
                                        == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                                    ),
                                    else_=None,
                                )
                            )
                        ).label("unrated_artifact_counts"),
                        Subtopic.subtopic_id,
                        func.min(Topic.topic_id).label("topic_id"),
                        func.min(Subtopic.name).label("subtopic_name"),
                        func.min(Subtopic.description).label("subtopic_description"),
                        func.min(Document.business_id).label("business_id"),
                        func.min(Topic.name).label("topic_name"),
                    )
                    .join(Chunk, Statement.chunk_id == Chunk.chunk_id)
                    .join(Document, Chunk.document_id == Document.document_id)
                    .join(Subtopic, Statement.statement_type_id == Subtopic.subtopic_id)
                    .join(Topic, Subtopic.topic_id == Topic.topic_id)
                    .filter(
                        Chunk.document_id == document_id,
                        Statement.statement_type_id == StatementTypeEnum.IDENTIFIED_SUBTOPIC.value,
                    )
                )

                return query.group_by(Subtopic.subtopic_id).all()

            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document subtopic artifact counts: {str(e)}"
                ) from e

    def get_document_subtopic_counts(self, document_id: int):
        with self.session_factory() as session:
            try:
                if not self.verify_document_exists(document_id):
                    raise NotFoundException(f"Document with ID {document_id} does not exist.")

                query = (
                    session.query(
                        func.count(
                            func.distinct(
                                case(
                                    (
                                        UniqueStatement.rating == RatingEnum.unrated.value,
                                        UniqueStatement.unique_statement_id,
                                    ),
                                    else_=None,
                                )
                            )
                        ).label("unrated_requirements_count"),
                        (
                            func.count(
                                func.distinct(
                                    case(
                                        (
                                            UniqueStatement.rating == RatingEnum.unrated.value,
                                            UniqueStatement.unique_statement_id,
                                        ),
                                        else_=None,
                                    )
                                )
                            )
                            + func.count(
                                func.distinct(
                                    case(
                                        (
                                            UserStory.rating == RatingEnum.unrated.value,
                                            UserStory.user_story_id,
                                        ),
                                        else_=None,
                                    )
                                )
                            )
                            + func.count(
                                func.distinct(
                                    case(
                                        (
                                            AcceptanceCriteria.rating == RatingEnum.unrated.value,
                                            AcceptanceCriteria.acceptance_criteria_id,
                                        ),
                                        else_=None,
                                    )
                                )
                            )
                            + func.count(
                                func.distinct(
                                    case(
                                        (
                                            TestCase.rating == RatingEnum.unrated.value,
                                            TestCase.test_case_id,
                                        ),
                                        else_=None,
                                    )
                                )
                            )
                        ).label("unrated_artifact_counts"),
                        Subtopic.subtopic_id,
                        func.min(Topic.topic_id).label("topic_id"),
                        func.min(Subtopic.name).label("subtopic_name"),
                        func.min(Subtopic.description).label("subtopic_description"),
                        func.min(Document.business_id).label("business_id"),
                        func.min(Topic.name).label("topic_name"),
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .join(
                        Subtopic,
                        UniqueStatementSubtopicAssociation.subtopic_id == Subtopic.subtopic_id,
                    )
                    .join(Topic, Subtopic.topic_id == Topic.topic_id)
                    .join(Document, UniqueStatement.document_id == Document.document_id)
                    .outerjoin(
                        UserStory,
                        UniqueStatement.unique_statement_id == UserStory.unique_statement_id,
                    )
                    .outerjoin(
                        AcceptanceCriteria,
                        UserStory.user_story_id == AcceptanceCriteria.user_story_id,
                    )
                    .outerjoin(
                        TestCase,
                        AcceptanceCriteria.acceptance_criteria_id
                        == TestCase.acceptance_criteria_id,
                    )
                    .filter(UniqueStatement.document_id == document_id)
                )

                return query.group_by(Subtopic.subtopic_id).all()

            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document subtopic artifact counts: {str(e)}"
                ) from e

    def get_document_chain_ids(self, document_id: int) -> tuple[Optional[int], Optional[int]]:
        """
        Get the document statistics ID and original document statistics ID from a document chain.
        For a given document ID:
        1. First get document.original_document_id as the document_statistics_id
        2. Then get that document's original_document_id as the original_statistics_id
        
        Args:
            document_id: The ID of the document to start from
            
        Returns:
            Tuple containing:
            - ID for document statistics (the first original_document_id)
            - ID for original document statistics (the second original_document_id)
        """
        with self.session_factory() as session:
            try:
                current_doc = session.query(Document).filter_by(document_id=document_id).one_or_none()
                if not current_doc or not current_doc.original_document_id:
                    return None, None
                    
                # Get the first original document (for document_statistics)
                doc_stats_id = current_doc.original_document_id
                doc_stats = session.query(Document).filter_by(document_id=doc_stats_id).one_or_none()
                if not doc_stats or not doc_stats.original_document_id:
                    return doc_stats_id, None
                    
                # Get the second original document (for original_document_statistics)
                original_stats_id = doc_stats.original_document_id
                
                return doc_stats_id, original_stats_id
                
            except Exception as e:
                raise ContentAccessorException(
                    f"Error getting document chain IDs: {str(e)}"
                ) from e

    def get_document_statistics_by_document_id(
        self, document_id: int
    ) -> tuple[Optional[DocumentStatisticsDTO], Optional[DocumentStatisticsDTO]]:
        """
        Get document statistics for a document and its original document.
        
        Args:
            document_id: The ID of the document to query statistics for
            
        Returns:
            Tuple containing:
            - DocumentStatisticsDTO for the optimized document (from first original_document_id)
            - DocumentStatisticsDTO for the original document (from second original_document_id)
        """
        with self.session_factory() as session:
            try:
                document = session.query(Document).filter_by(document_id=document_id).one_or_none()
                if not document or not document.original_document_id:
                    return None, None

                optimized_doc_id = document.original_document_id
                optimized_doc = session.query(Document).filter_by(document_id=optimized_doc_id).one_or_none()
                if not optimized_doc:
                    return None, None

                optimized_stats = session.query(DocumentStatistics).filter_by(
                    document_id=optimized_doc_id
                ).one_or_none()

                original_doc_id = optimized_doc.original_document_id
                original_stats = None
                if original_doc_id:
                    original_stats = session.query(DocumentStatistics).filter_by(
                        document_id=original_doc_id
                    ).one_or_none()

                # Convert to DTOs
                optimized_stats_dto = (
                    DocumentStatisticsDTO(
                        word_count=optimized_stats.word_count,
                        difficult_word_count=optimized_stats.difficult_word_count,
                        average_sentence_length=optimized_stats.average_sentence_length,
                        average_word_length=optimized_stats.average_word_length,
                        flesch_grade_level=optimized_stats.flesch_grade_level,
                        flesch_score=optimized_stats.flesch_score,
                        flesch_reading_ease=optimized_stats.flesch_reading_ease,
                        created_at=optimized_stats.created_at,
                        updated_at=optimized_stats.updated_at,
                    ) if optimized_stats else None
                )

                original_stats_dto = (
                    DocumentStatisticsDTO(
                        word_count=original_stats.word_count,
                        difficult_word_count=original_stats.difficult_word_count,
                        average_sentence_length=original_stats.average_sentence_length,
                        average_word_length=original_stats.average_word_length,
                        flesch_grade_level=original_stats.flesch_grade_level,
                        flesch_score=original_stats.flesch_score,
                        flesch_reading_ease=original_stats.flesch_reading_ease,
                        created_at=original_stats.created_at,
                        updated_at=original_stats.updated_at,
                    ) if original_stats else None
                )

                return optimized_stats_dto, original_stats_dto

            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document statistics: {str(e)}"
                ) from e

    def get_documents_by_job_type(self, job_types: list[str]) -> list[Document]:
        with self.session_factory() as session:
            try:
                query = (
                    session.query(Document)
                    .join(Job, Document.document_id == Job.reference_id)
                    .filter(Job.job_type_id.in_(job_types))
                    .options(joinedload(Document.job))  # Eagerly load the job relationship
                    .order_by(Document.created_at.desc())
                )

                return query.all()

            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching documents by job types {job_types}" + f": {str(e)}"
                ) from e

    def get_document_download_info(self, document_id: int, version: str) -> dict:
        """
        Get document download information for a specific version.
        
        Args:
            document_id (int): The ID of the document
            version (str): The version to download ('original_word', 'optimized_word', 'optimized_pdf', 'original_pdf')
            
        Returns:
            dict: Dictionary containing download information including s3_location
        """
        with self.session_factory() as session:
            try:
                # Get the initial document
                document = session.query(Document).filter_by(document_id=document_id).one_or_none()
                if not document:
                    raise NotFoundException(f"Document with ID {document_id} not found")

                # If original_document_id is not populated, this is an original PDF
                if not document.original_document_id:
                    if version != VERSION_ORIGINAL_PDF:
                        raise NotFoundException(f"Version {version} not available for document {document_id}")
                    return {
                        "s3_location": document.s3_location,
                        "document_id": document_id
                    }

                # If original_document_id is populated, we need to traverse the chain
                current_doc = document
                doc_chain = []
                
                # Build the document chain
                while current_doc:
                    doc_chain.append(current_doc)
                    if not current_doc.original_document_id:
                        break
                    current_doc = session.query(Document).filter_by(document_id=current_doc.original_document_id).one_or_none()

                # Map versions to document IDs and locations
                version_map = {
                    VERSION_ORIGINAL_WORD: doc_chain[-1].document_id if len(doc_chain) >= 1 else None,
                    VERSION_OPTIMIZED_WORD: doc_chain[-2].document_id if len(doc_chain) >= 2 else None,
                    VERSION_OPTIMIZED_PDF: doc_chain[0].document_id if len(doc_chain) >= 1 else None
                }

                # Get the target document ID for the requested version
                target_doc_id = version_map.get(version)
                if not target_doc_id:
                    raise NotFoundException(f"Version {version} not available for document {document_id}")

                # Get the target document
                target_doc = session.query(Document).filter_by(document_id=target_doc_id).one_or_none()
                if not target_doc:
                    raise NotFoundException(f"Target document {target_doc_id} not found")

                return {
                    "s3_location": target_doc.s3_location,
                    "document_id": target_doc_id
                }

            except Exception as e:
                raise ContentAccessorException(f"Error getting document download info: {str(e)}") from e

    def get_available_document_versions_by_ids(self, document_ids: list[int]) -> dict[int, list[dict]]:
        """
        Get available document versions for a list of document IDs.
        
        Args:
            document_ids (list[int]): List of document IDs
            
        Returns:
            dict[int, list[dict]]: Dictionary mapping document IDs to lists of available versions
        """
        with self.session_factory() as session:
            try:
                result = {}
                
                # Fetch all documents in a single query
                documents = session.query(Document).filter(Document.document_id.in_(document_ids)).all()
                
                for document in documents:
                    available_versions = []
                    
                    # If original_document_id is not populated, only show original PDF
                    if not document.original_document_id:
                        available_versions.append({
                            "label": "Original PDF",
                            "type": ".pdf",
                            "version": VERSION_ORIGINAL_PDF
                        })
                    else:
                        # If original_document_id is populated, show all versions
                        available_versions.extend([
                            {
                                "label": "Original Word",
                                "type": ".docx",
                                "version": VERSION_ORIGINAL_WORD
                            },
                            {
                                "label": "Optimized Word",
                                "type": ".docx",
                                "version": VERSION_OPTIMIZED_WORD
                            },
                            {
                                "label": "Optimized PDF",
                                "type": ".pdf",
                                "version": VERSION_OPTIMIZED_PDF
                            }
                        ])
                    
                    result[document.document_id] = available_versions
                
                return result

            except Exception as e:
                raise ContentAccessorException(f"Error getting available document versions: {str(e)}") from e
