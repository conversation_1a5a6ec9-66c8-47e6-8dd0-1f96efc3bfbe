from typing import List, Optional
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.interfaces import IAcceptanceCriteriaAccessor
from accessor.content.exceptions import ContentAccessorException, NotFoundException


from shared.models import (
    AcceptanceCriteria,
    TestCase,
    UniqueStatement,
    UserStory,
    UniqueStatementSubtopicAssociation,
    RatingEnum,
)


class AcceptanceCriteriaAccessor(IAcceptanceCriteriaAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def save_acceptance_criteria(self, insert_fields: dict) -> AcceptanceCriteria:
        with self.session_factory() as session:
            try:
                identifier = self._generate_acceptance_criteria_identifier(
                    insert_fields["user_story_id"]
                )

                acceptance_criteria = AcceptanceCriteria(
                    user_story_id=insert_fields["user_story_id"],
                    description=insert_fields["description"],
                    rating=insert_fields.get("rating", RatingEnum.unrated),
                    user_entered=insert_fields.get("user_entered", False),
                    identifier=identifier,
                )
                session.add(acceptance_criteria)
                session.commit()
                session.refresh(acceptance_criteria)

                return acceptance_criteria
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving acceptance criteria: {str(e)}") from e

    def get_acceptance_criteria_by_id(
        self, acceptance_criteria_id: int
    ) -> Optional[AcceptanceCriteria]:
        """
        Retrieves an acceptance criteria record by its unique ID.

        This method queries the database for an acceptance criteria record
        matching the provided `acceptance_criteria_id`. If a matching record
        is found, it is returned; otherwise, the method returns `None`.

        Args:
            acceptance_criteria_id (int): The unique identifier of the acceptance criteria to retrieve.

        Returns:
            Optional[AcceptanceCriteria]: The matching `AcceptanceCriteria` instance if found,
                                          or `None` if no match exists.
        """
        with self.session_factory() as session:
            return (
                session.query(AcceptanceCriteria)
                .filter_by(acceptance_criteria_id=acceptance_criteria_id)
                .first()
            )

    def delete_acceptance_criteria(self, acceptance_criteria_id: int):
        with self.session_factory() as session:
            try:
                session.query(AcceptanceCriteria).filter_by(
                    acceptance_criteria_id=acceptance_criteria_id
                ).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(
                    f"Error deleting acceptance criteria: {str(e)}"
                ) from e

    def get_all_acceptance_criteria(self) -> List[AcceptanceCriteria]:
        with self.session_factory() as session:
            try:
                ac_list = session.query(AcceptanceCriteria.criteria_text).all()
                return ac_list
            except Exception as e:
                raise ContentAccessorException(
                    f"Error getting all acceptance criteria: {str(e)}"
                ) from e

    def get_all_acceptance_criteria_per_user_story(
        self, user_story_id: int
    ) -> List[AcceptanceCriteria]:
        with self.session_factory() as session:
            try:
                # Query AcceptanceCriteria with a left outer join on TestCase and group by all necessary columns
                criterias = (
                    session.query(
                        AcceptanceCriteria.acceptance_criteria_id,
                        AcceptanceCriteria.user_story_id,
                        AcceptanceCriteria.description,
                        AcceptanceCriteria.identifier,
                        AcceptanceCriteria.rating,
                        AcceptanceCriteria.edited,
                        AcceptanceCriteria.user_entered,
                        func.count(TestCase.test_case_id).label("test_case_count"),
                        UniqueStatement.unique_statement_id,
                        UniqueStatement.document_id,
                    )
                    .join(UserStory, UserStory.user_story_id == AcceptanceCriteria.user_story_id)
                    .join(
                        UniqueStatement,
                        UniqueStatement.unique_statement_id == UserStory.unique_statement_id,
                    )
                    .outerjoin(
                        TestCase,
                        TestCase.acceptance_criteria_id
                        == AcceptanceCriteria.acceptance_criteria_id,
                    )
                    .filter(AcceptanceCriteria.user_story_id == user_story_id)
                    .group_by(
                        AcceptanceCriteria.acceptance_criteria_id,
                        AcceptanceCriteria.user_story_id,
                        AcceptanceCriteria.description,
                        AcceptanceCriteria.identifier,
                        AcceptanceCriteria.rating,
                        AcceptanceCriteria.edited,
                        AcceptanceCriteria.user_entered,
                        UniqueStatement.unique_statement_id,
                        UniqueStatement.document_id,
                    )
                    .order_by(AcceptanceCriteria.acceptance_criteria_id)
                    .all()
                )

                return criterias
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching all acceptance criteria per user story: {str(e)}"
                ) from e

    def verify_acceptance_criteria_exists(self, acceptance_criteria_id: int) -> bool:
        with self.session_factory() as session:
            return session.query(
                session.query(AcceptanceCriteria)
                .filter_by(acceptance_criteria_id=acceptance_criteria_id)
                .exists()
            ).scalar()

    def update_acceptance_criteria(
        self, acceptance_criteria_id: int, updated_fields: dict[str, str]
    ) -> AcceptanceCriteria:
        with self.session_factory() as session:

            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_acceptance_criteria_by_id here because it uses a different session
            acceptance_criteria = session.query(AcceptanceCriteria).get(acceptance_criteria_id)

            if acceptance_criteria is None:
                raise NotFoundException(
                    f"Acceptance criteria with ID {acceptance_criteria_id} does not exist."
                )

            try:
                if "description" in updated_fields:
                    acceptance_criteria.description = updated_fields["description"]
                    acceptance_criteria.edited = True
                if "identifier" in updated_fields:
                    acceptance_criteria.identifier = updated_fields["identifier"]
                if "rating" in updated_fields:
                    acceptance_criteria.rating = RatingEnum(updated_fields["rating"])

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(acceptance_criteria)

                return acceptance_criteria

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(
                    f"Error updating acceptance criteria: {str(e)}"
                ) from e

    def get_document_id_by_acceptance_criteria_id(self, acceptance_criteria_id: int) -> int:
        with self.session_factory() as session:
            try:
                # Fetch the document_id from the associated UniqueStatement through UserStory
                return (
                    session.query(UniqueStatement.document_id)
                    .join(UserStory)
                    .join(AcceptanceCriteria)
                    .filter(AcceptanceCriteria.acceptance_criteria_id == acceptance_criteria_id)
                    .scalar()
                )
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document_id using acceptance_criteria_id: {str(e)}"
                ) from e

    def get_acceptance_criteria_ordered(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[AcceptanceCriteria]:
        with self.session_factory() as session:
            try:
                acceptance_criteria_list = (
                    session.query(AcceptanceCriteria)
                    .join(UserStory, AcceptanceCriteria.user_story_id == UserStory.user_story_id)
                    .join(
                        UniqueStatement,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatement.rating != RatingEnum.negative.value,
                        UserStory.rating != RatingEnum.negative.value,
                        AcceptanceCriteria.rating != RatingEnum.negative.value,
                    )
                    .order_by(AcceptanceCriteria.acceptance_criteria_id)
                    .all()
                )
                return acceptance_criteria_list
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching ordered acceptance criteria: {str(e)}"
                ) from e

    def check_acceptance_criteria_test_cases_exists(self, acceptance_criteria_id: int) -> bool:
        with self.session_factory() as session:
            try:
                # Query to check if there are any TC for the given user_story_id
                return session.query(
                    session.query(TestCase)
                    .filter_by(acceptance_criteria_id=acceptance_criteria_id)
                    .exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if test cases exist for acceptance criteria {acceptance_criteria_id}: {str(e)}"
                ) from e

    def check_acceptance_criteria_has_children(self, acceptance_criteria_id: int) -> bool:
        with self.session_factory() as session:
            try:
                # Determine if user story has children acceptance criteria
                return (
                    session.query(TestCase)
                    .filter_by(acceptance_criteria_id=acceptance_criteria_id)
                    .count()
                    > 0
                )
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if acceptance criteria has children for acceptance criteria {acceptance_criteria_id}: {str(e)}"
                ) from e

    # Helper methods

    def _generate_acceptance_criteria_identifier(self, user_story_id: int) -> Optional[str]:
        with self.session_factory() as session:
            try:
                # Retrieve the UserStory identifier
                user_story_info = (
                    session.query(UserStory.identifier)
                    .filter(UserStory.user_story_id == user_story_id)
                    .first()
                )

                if not user_story_info:
                    print("User Story not found.")
                    return None  # Return None if the UserStory does not exist

                # Get the count of AcceptanceCriteria for this UserStory to use as the next order_id
                criteria_count = (
                    session.query(func.count(AcceptanceCriteria.acceptance_criteria_id))
                    .filter(AcceptanceCriteria.user_story_id == user_story_id)
                    .scalar()
                )

                # Use the count + 1 as the new "order_id"
                new_order_id = criteria_count + 1

                # Replace "US" prefix with "AC" in the UserStory identifier and generate the new identifier
                ac_identifier_prefix = user_story_info.identifier.replace("US", "AC", 1)
                new_identifier = f"{ac_identifier_prefix}.{str(new_order_id).zfill(2)}"

                return new_identifier

            except Exception as e:
                print(f"Error generating identifier for acceptance criteria: {e}")
                return None  # Return None on error
