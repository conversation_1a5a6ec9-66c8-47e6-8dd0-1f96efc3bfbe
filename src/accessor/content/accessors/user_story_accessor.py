"""
user_story_accessor

This module provides the UserStoryAccessor class, which implements the IUserStoryAccessor
interface to manage database operations for UserStory entities. It includes methods for
saving, updating, verifying existence, retrieving ordered lists, and fetching related
document IDs for user stories.

Classes:
    - UserStoryAccessor: Implements database operations for UserStory entities and handles
      interactions with related entities, such as AcceptanceCriteria and UniqueStatement.

Exceptions:
    - ContentAccessorException: Raised when an error occurs during a database transaction.
"""

from typing import List, Optional

from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError

from accessor.content.exceptions import ContentAccessorException, NotFoundException
from accessor.content.interfaces import IUserStoryAccessor
from shared.models import (
    AcceptanceCriteria,
    RatingEnum,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    UserStory,
)


class UserStoryAccessor(IUserStoryAccessor):
    """
    Provides database operations for managing UserStory entities.

    The UserStoryAccessor class implements the IUserStoryAccessor interface, enabling
    CRUD operations and related queries for UserStory objects in the database. It also
    manages associations with related entities, such as AcceptanceCriteria and
    UniqueStatement, allowing for operations that span multiple tables.

    Attributes:
        session_factory (Callable): A callable that returns a new database session
            to ensure each operation runs in its own session context.
    """

    def __init__(self, session_factory):
        self.session_factory = session_factory

    def verify_user_story_exists(self, user_story_id: int) -> bool:
        """
        Checks if a UserStory entity exists in the database by its ID.

        This method queries the database to determine if a UserStory with the specified
        user_story_id exists. It returns True if the user story is found and False otherwise.
        If a database error occurs, a ContentAccessorException is raised.

        Parameters:
            user_story_id (int): The unique identifier of the UserStory to verify.

        Returns:
            bool: True if a UserStory with the given ID exists, False otherwise.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                return session.query(
                    session.query(UserStory).filter_by(user_story_id=user_story_id).exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if user story exists: {str(e)}"
                ) from e

    def get_user_story_by_id(self, user_story_id: int) -> UserStory:
        """
        Retrieves a UserStory entity by its ID.

        This method queries the database for a UserStory with the specified user_story_id.
        It returns the UserStory instance if found, or None if no matching record exists.
        If a database error occurs, a ContentAccessorException is raised.

        Parameters:
            user_story_id (int): The unique identifier of the UserStory to retrieve.

        Returns:
            UserStory: The UserStory instance with the given ID, or None if no match is found.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                return session.query(UserStory).filter_by(user_story_id=user_story_id).one_or_none()
            except SQLAlchemyError as e:
                raise ContentAccessorException(f"Error fetching user story: {str(e)}") from e

    def get_user_stories_ordered(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[UserStory]:
        """
        Retrieves a list of UserStory entities associated with a specific document, subtopic,
        and statement type, ordered by user story ID.

        This method queries the database to find UserStory instances linked to the specified
        document_id, subtopic_id, and statement_type_id. The query filters out user stories
        with a negative rating and orders the results by user_story_id. If a database error
        occurs, a ContentAccessorException is raised.

        Parameters:
            document_id (int): The ID of the document to which the user stories are related.
            subtopic_id (int): The ID of the subtopic for filtering associated user stories.
            statement_type_id (int): The ID of the statement type for filtering associated user stories.

        Returns:
            List[UserStory]: A list of UserStory instances matching the specified criteria,
                            ordered by user_story_id.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:

                user_stories = (
                    session.query(UserStory)
                    .join(
                        UniqueStatement,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.document_id == document_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatement.rating != RatingEnum.negative.value,
                        UserStory.rating != RatingEnum.negative.value,
                    )
                    .order_by(UserStory.user_story_id)
                    .all()
                )
                return user_stories
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error fetching ordered user story: {str(e)}"
                ) from e

    def get_user_stories_for_unique_statement(self, unique_statement_id: int) -> List[UserStory]:
        """
        Retrieves all UserStory entities associated with a specific unique statement.

        This method queries the database to find all UserStory instances linked to the given
        unique_statement_id. It returns a list of matching UserStory instances. If a database
        error occurs, a ContentAccessorException is raised.

        Parameters:
            unique_statement_id (int): The ID of the unique statement for which to retrieve
                                      associated user stories.

        Returns:
            List[UserStory]: A list of UserStory instances associated with the specified
                            unique statement.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                # Query the UserStory table and left outer join AcceptanceCriteria to get the has_children field
                user_stories = (
                    session.query(
                        UserStory.user_story_id,
                        UserStory.unique_statement_id,
                        UserStory.description,
                        UserStory.identifier,
                        UserStory.rating,
                        UserStory.edited,
                        UserStory.user_entered,
                        func.count(AcceptanceCriteria.acceptance_criteria_id).label(
                            "criteria_count"
                        ),
                    )
                    .outerjoin(
                        AcceptanceCriteria,
                        AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                    )
                    .filter(UserStory.unique_statement_id == unique_statement_id)
                    .group_by(
                        UserStory.user_story_id,
                        UserStory.unique_statement_id,
                        UserStory.description,
                        UserStory.identifier,
                        UserStory.rating,
                        UserStory.edited,
                        UserStory.user_entered,
                    )
                    .order_by(UserStory.user_story_id)
                    .all()
                )
                return user_stories
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching user stories for unique statement: {str(e)}"
                ) from e

    def save_user_story(self, insert_fields: dict) -> UserStory:
        """
        Creates and saves a new UserStory entity in the database.

        This method creates a new UserStory with the specified unique_statement_id and description,
        adds it to the database, and commits the transaction. The newly created UserStory instance
        is then refreshed to reflect any generated attributes, such as its primary key, and returned.
        If an error occurs during the database transaction, the changes are rolled back, and a
        ContentAccessorException is raised.

        Parameters:
            unique_statement_id (int): The ID of the unique statement to which this user story is linked.
            description (str): A description of the user story.

        Returns:
            UserStory: The newly created UserStory instance with updated attributes from the database.

        Raises:
            ContentAccessorException: If there is an error during the database transaction.
        """
        with self.session_factory() as session:
            try:
                identifier = self._generate_user_story_identifier(
                    insert_fields["unique_statement_id"]
                )

                user_story = UserStory(
                    unique_statement_id=insert_fields["unique_statement_id"],
                    description=insert_fields["description"],
                    rating=insert_fields.get("rating", RatingEnum.unrated),
                    user_entered=insert_fields.get("user_entered", False),
                    identifier=identifier,
                )
                session.add(user_story)
                session.commit()
                session.refresh(user_story)

                return user_story
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error saving user story: {str(e)}") from e

    def check_user_story_acceptance_criteria_exists(self, user_story_id: int) -> bool:
        """
        Checks if any acceptance criteria exist for a specific UserStory.

        This method queries the database to determine if there are any AcceptanceCriteria entries
        associated with the given user_story_id. It returns True if at least one acceptance
        criteria exists and False otherwise. If a database error occurs, a ContentAccessorException
        is raised.

        Parameters:
            user_story_id (int): The ID of the UserStory to check for associated acceptance criteria.

        Returns:
            bool: True if acceptance criteria exist for the specified UserStory, False otherwise.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                # Query to check if there is any AcceptanceCriteria for the given user_story_id
                return session.query(
                    session.query(AcceptanceCriteria)
                    .filter_by(user_story_id=user_story_id)
                    .exists()
                ).scalar()
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if acceptance criteria exist for user story {user_story_id}: {str(e)}"
                ) from e

    def update_user_story(self, user_story_id: int, updated_fields: dict[str, str]) -> UserStory:
        """
        Updates specified fields of an existing UserStory entity in the database.

        This method updates fields such as "description", "identifier", and "rating" for a
        UserStory identified by user_story_id. It validates data types and ensures that "rating"
        matches an accepted enum value. After committing the changes, the updated attributes
        are returned as a dictionary. If a database error occurs or if the updated data is invalid,
        a ContentAccessorException is raised.

        Parameters:
            user_story_id (int): The unique identifier of the UserStory to update.
            updated_fields (dict[str, str]): A dictionary of fields to update and their new values.
                                            Supported keys are "description", "identifier", and "rating".

        Returns:
            dict[str, str]: A dictionary of the updated UserStory attributes:
                            - "user_story_id": The ID of the UserStory.
                            - "description": The updated description.
                            - "identifier": The updated identifier.
                            - "rating": The updated rating value as a string, or None if not set.

        Raises:
            ContentAccessorException: If there is an error during the database transaction or
                                      if the updated data is invalid.
        """
        with self.session_factory() as session:

            # Retrieve the instance within the current session to keep it persistent
            # We can't use get_user_story_by_id here because it uses a different session
            user_story = session.query(UserStory).get(user_story_id)

            if user_story is None:
                raise NotFoundException(f"User story with ID {user_story_id} does not exist.")

            try:

                if "description" in updated_fields:
                    user_story.description = updated_fields["description"]
                    # Set `edited` to True only if `description` is being updated
                    user_story.edited = True
                if "identifier" in updated_fields:
                    user_story.identifier = updated_fields["identifier"]
                if "rating" in updated_fields:
                    user_story.rating = RatingEnum(
                        updated_fields["rating"]
                    )  # Convert to enum if provided

                # Commit changes to persist them to the database
                session.commit()

                # Refresh the instance to ensure the latest data from the database
                session.refresh(user_story)

                return user_story

            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error updating user story: {str(e)}") from e

    def get_document_id_by_user_story_id(self, user_story_id: int) -> int:
        """
        Retrieves the document ID associated with a specific UserStory.

        This method queries the database to find the document_id linked to the specified
        user_story_id by joining the UserStory and UniqueStatement tables. It returns
        the document_id if found. If an error occurs during the query, a
        ContentAccessorException is raised.

        Parameters:
            user_story_id (int): The unique identifier of the UserStory for which to
                                retrieve the associated document ID.

        Returns:
            int: The document_id associated with the specified UserStory.

        Raises:
            ContentAccessorException: If there is an error during the database query.
        """
        with self.session_factory() as session:
            try:
                # Fetch the document_id from the associated UniqueStatement through UserStory
                return (
                    session.query(UniqueStatement.document_id)
                    .join(UserStory)
                    .filter(UserStory.user_story_id == user_story_id)
                    .scalar()
                )
            except Exception as e:
                raise ContentAccessorException(
                    f"Error fetching document_id using user_story_id: {str(e)}"
                ) from e

    def check_user_story_has_children(self, user_story_id: int) -> bool:
        with self.session_factory() as session:
            try:
                # Determine if user story has children acceptance criteria
                return (
                    session.query(AcceptanceCriteria).filter_by(user_story_id=user_story_id).count()
                    > 0
                )
            except SQLAlchemyError as e:
                raise ContentAccessorException(
                    f"Error determining if user story has children for acceptance criteria {user_story_id}: {str(e)}"
                ) from e

    # Helper methods
    def _generate_user_story_identifier(self, unique_statement_id: int) -> Optional[str]:
        with self.session_factory() as session:
            try:
                # Retrieve the UserStory identifier
                unique_statement_info = (
                    session.query(UniqueStatement.identifier)
                    .filter(UniqueStatement.unique_statement_id == unique_statement_id)
                    .first()
                )

                if not unique_statement_info:
                    print("Unique Statement not found.")
                    return None  # Return None if the UniqueStatement does not exist

                # Get the count of user stories for this unique statement to use as the next order_id
                user_story_count = (
                    session.query(func.count(UserStory.user_story_id))
                    .filter(UserStory.unique_statement_id == unique_statement_id)
                    .scalar()
                )

                # Use the count + 1 as the new "order_id"
                new_order_id = user_story_count + 1

                # Replace "US" prefix with "AC" in the UserStory identifier and generate the new identifier
                us_identifier_prefix = unique_statement_info.identifier.replace("REQ", "US", 1)
                new_identifier = f"{us_identifier_prefix}.{str(new_order_id).zfill(2)}"

                return new_identifier

            except Exception as e:
                print(f"Error generating identifier for user story: {e}")
                return None  # Return None on error

    def delete_user_story(self, user_story_id: int):
        """
        Deletes a UserStory entity by its ID.

        This method removes the UserStory with the specified test_case_id from the database.
        If the deletion operation encounters an error, the transaction is rolled back, and
        a ContentAccessorException is raised.

        Parameters:
            user_story_id (int): The unique identifier of the UserStory to delete.

        Raises:
            ContentAccessorException: If there is an error during the database transaction.
        """
        with self.session_factory() as session:
            try:
                session.query(UserStory).filter_by(user_story_id=user_story_id).delete()
                session.commit()
            except Exception as e:
                session.rollback()
                raise ContentAccessorException(f"Error deleting user story: {str(e)}") from e
