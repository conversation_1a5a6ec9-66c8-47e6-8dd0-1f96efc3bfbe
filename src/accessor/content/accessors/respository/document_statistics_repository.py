from typing import Optional

from sqlalchemy.exc import SQLAlchemyError

from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO
from shared.models.document_statistics import DocumentStatistics


class DocumentStatisticsRepository:
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def create_document_statistics(
        self, document_id: int, stats_dto: DocumentStatisticsDTO
    ) -> Optional[DocumentStatisticsDTO]:
        """
        Inserts a new record into document_statistics based on the given DTO.

        Args:
            document_id: The associated document_id for the statistics.
            stats_dto: The DTO containing statistics data to be inserted.

        Returns:
            A DocumentStatisticsDTO of the newly created record, or None if insertion failed.
        """
        try:
            with self.session_factory() as session:
                # Construct the ORM model from the DTO
                doc_stats = DocumentStatistics(
                    document_id=document_id,
                    word_count=stats_dto.word_count,
                    difficult_word_count=stats_dto.difficult_word_count,
                    average_sentence_length=stats_dto.average_sentence_length,
                    average_word_length=stats_dto.average_word_length,
                    flesch_grade_level=stats_dto.flesch_grade_level,
                    flesch_score=stats_dto.flesch_score,
                    flesch_reading_ease=stats_dto.flesch_reading_ease,
                )

                session.add(doc_stats)
                session.commit()
                session.refresh(doc_stats)

                # Convert the newly inserted record back to a DTO and return it
                return DocumentStatisticsDTO(
                    word_count=doc_stats.word_count,
                    difficult_word_count=doc_stats.difficult_word_count,
                    average_sentence_length=doc_stats.average_sentence_length,
                    average_word_length=doc_stats.average_word_length,
                    flesch_grade_level=doc_stats.flesch_grade_level,
                    flesch_score=doc_stats.flesch_score,
                    flesch_reading_ease=doc_stats.flesch_reading_ease,
                    created_at=doc_stats.created_at,
                    updated_at=doc_stats.updated_at,
                )
        except SQLAlchemyError as e:
            # Log or handle exception as needed
            print(f"Error inserting document statistics: {e}")
            return None
