from typing import List, Optional

from sqlalchemy.orm import joinedload

from accessor.content.accessors.dto import AuditActivityReadDTO
from accessor.content.exceptions import ContentAccessorException
from accessor.content.interfaces import IAuditActivityAccessor
from shared.models import AuditActivity


class AuditActivityAccessor(IAuditActivityAccessor):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_audit_activity_by_id(self, audit_activity_id: int) -> Optional[AuditActivityReadDTO]:
        """
        Retrieves an audit activity record by its unique ID and returns it as a DTO.

        This method queries the database for an audit activity record
        matching the provided `audit_activity_id`. If a matching record
        is found, it is returned as an `AuditActivityReadDTO`; otherwise, None.

        Args:
            audit_activity_id (int): The unique identifier of the audit activity to retrieve.

        Returns:
            Optional[AuditActivityReadDTO]: The matching DTO if found, or `None` if no match exists.
        """
        with self.session_factory() as session:
            try:
                record = (
                    session.query(AuditActivity)
                    .options(
                        joinedload(AuditActivity.user),
                        joinedload(AuditActivity.audit_object_type),
                        joinedload(AuditActivity.audit_operation_type),
                    )
                    .filter_by(audit_activity_id=audit_activity_id)
                    .first()
                )
                return AuditActivityReadDTO.from_model(record) if record else None
            except Exception as e:
                raise ContentAccessorException(
                    f"Failed to retrieve audit activity with ID {audit_activity_id}: {str(e)}"
                ) from e

    def get_all_audit_log_entries_per_user(self, user_id: int) -> List[AuditActivityReadDTO]:
        """
        Retrieves all audit activity records associated with a specific user,
        and returns them as DTOs with related field information.

        Args:
            user_id (int): The unique identifier of the user whose audit activity is to be retrieved.

        Returns:
            List[AuditActivityReadDTO]: A list of audit activity DTOs associated with the specified user.
        """
        with self.session_factory() as session:
            try:
                records = (
                    session.query(AuditActivity)
                    .options(
                        joinedload(AuditActivity.user),
                        joinedload(AuditActivity.audit_object_type),
                        joinedload(AuditActivity.audit_operation_type),
                    )
                    .filter_by(user_id=user_id)
                    .all()
                )
                return [AuditActivityReadDTO.from_model(record) for record in records]
            except Exception as e:
                raise ContentAccessorException(
                    f"Failed to retrieve audit activity for user {user_id}: {str(e)}"
                ) from e

    def get_all_audit_activity_per_object(
        self, audit_object_type_id: int, object_key: int
    ) -> List[AuditActivityReadDTO]:
        """
        Retrieves all audit activity records associated with a specific object,
        and returns them as DTOs including related field data.

        Args:
            audit_object_type_id (int): The audit object type ID.
            object_key (int): The unique identifier of the object whose audit activity is to be retrieved.

        Returns:
            List[AuditActivityReadDTO]: A list of DTOs with audit activity and related info.
        """
        with self.session_factory() as session:
            try:
                records = (
                    session.query(AuditActivity)
                    .options(
                        joinedload(AuditActivity.user),
                        joinedload(AuditActivity.audit_object_type),
                        joinedload(AuditActivity.audit_operation_type),
                    )
                    .filter_by(audit_object_type_id=audit_object_type_id)
                    .filter_by(object_key=object_key)
                    .all()
                )
                return [AuditActivityReadDTO.from_model(record) for record in records]
            except Exception as e:
                raise ContentAccessorException(
                    f"Failed to retrieve audit activity for audit_object_type_id {audit_object_type_id} object_key {object_key}: {str(e)}"
                ) from e
