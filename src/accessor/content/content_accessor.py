import logging
from typing import Dict, List, Optional, Tuple

from sqlalchemy import func

from accessor.content.accessors import (
    AcceptanceCriteriaAccessor,
    AuditActivityAccessor,
    BusinessAccessor,
    ChunkAccessor,
    DepartmentAccessor,
    DocumentAccessor,
    DocumentStatisticsAccessor,
    DomainAccessor,
    JobAccessor,
    PersonaAccessor,
    StatementAccessor,
    SubtopicAccessor,
    TestCaseAccessor,
    TopicAccessor,
    UniqueStatementAccessor,
    UserStoryAccessor,
)
from accessor.content.accessors.dto import AuditActivityReadDTO, DocumentUpdateDTO
from accessor.content.interfaces import (
    IAcceptanceCriteriaAccessor,
    IAuditActivityAccessor,
    IBusinessAccessor,
    IChunkAccessor,
    IDepartmentAccessor,
    IDocumentAccessor,
    IDomainAccessor,
    IJobAccessor,
    IPersonaAccessor,
    IStatementAccessor,
    ISubtopicAccessor,
    ITestCaseAccessor,
    ITopicAccessor,
    IUniqueStatementAccessor,
    IUserStoryAccessor,
)
from accessor.content.interfaces.i_document_statistics_accessor import IDocumentStatisticsAccessor
from accessor.document.dto.document_statistics_dto import DocumentStatisticsDTO
from shared.enums import AuditObjectType, AuditOperationType, StatementTypeEnum
from shared.models import (
    AcceptanceCriteria,
    Chunk,
    Business,
    Department,
    Document,
    Domain,
    Job,
    Persona,
    Statement,
    StatementType,
    Subtopic,
    TestCase,
    Topic,
    UniqueStatement,
    UniqueStatementSubtopicAssociation,
    UserStory,
)
from util.audit import Auditor
from util.audit.dto import AuditActivityInsertDTO
from util.database import Session


class ContentAccessor(
    IAcceptanceCriteriaAccessor,
    IAuditActivityAccessor,
    IChunkAccessor,
    IDepartmentAccessor,
    IDocumentAccessor,
    IDocumentStatisticsAccessor,
    IDomainAccessor,
    IJobAccessor,
    IPersonaAccessor,
    IUniqueStatementAccessor,
    IUserStoryAccessor,
    IStatementAccessor,
    ISubtopicAccessor,
    ITestCaseAccessor,
    ITopicAccessor,
    IBusinessAccessor,
):
    def __init__(self, logger_name):
        self.session_factory = Session
        self.logger = logging.getLogger(logger_name)
        self.acceptance_criteria_accessor = AcceptanceCriteriaAccessor(self.session_factory)
        self.audit_activity_accessor = AuditActivityAccessor(self.session_factory)
        self.business_accessor = BusinessAccessor(self.session_factory)
        self.chunk_accessor = ChunkAccessor(self.session_factory)
        self.department_accessor = DepartmentAccessor(self.session_factory)
        self.document_accessor = DocumentAccessor(self.session_factory)
        self.document_statistics_accessor = DocumentStatisticsAccessor(self.session_factory)
        self.domain_accessor = DomainAccessor(self.session_factory)
        self.job_accessor = JobAccessor(self.session_factory)
        self.persona_accessor = PersonaAccessor(self.session_factory)
        self.statement_accessor = StatementAccessor(self.session_factory)
        self.unique_statement_accessor = UniqueStatementAccessor(self.session_factory)
        self.user_story_accessor = UserStoryAccessor(self.session_factory)
        self.statement_accessor = StatementAccessor(self.session_factory)
        self.subtopic_accessor = SubtopicAccessor(self.session_factory)
        self.test_case_accessor = TestCaseAccessor(self.session_factory)
        self.topic_accessor = TopicAccessor(self.session_factory)

        self.auditor = Auditor(session_factory=Session)

    def clean_text(self, text):
        return text.replace("\u0000", "")

    def save_chunks(self, chunks: List[Dict]):
        session = self.session_factory()
        try:
            session.bulk_insert_mappings(Chunk, chunks)
            session.commit()
        except Exception:
            session.rollback()
            raise

    def remove_chunk_statements(self, chunk_id: int):
        """
        Deletes all statements associated with a given chunk ID using the generic _delete_artifacts method.

        Args:
            chunk_id (int): The ID of the chunk whose statements should be deleted.
        """
        self._delete_artifacts(model=Statement, filter_conditions=[Statement.chunk_id == chunk_id])

    # TODO: When we remove bundle functionality, rename this to find_user_story_resume_point
    def find_bundle_resume_point(self, document_id: int):
        with self.session_factory() as session:

            max_statement_type_id_subquery = (
                session.query(func.max(UniqueStatement.statement_type_id))
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id)
                    ),
                )
                .scalar_subquery()
            )

            max_subtopic_id_subquery = (
                session.query(func.max(UniqueStatementSubtopicAssociation.subtopic_id))
                .join(
                    UniqueStatement,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id)
                    ),
                )
                .scalar_subquery()
            )

            max_unique_statement_id_subquery = (
                session.query(func.max(UniqueStatement.unique_statement_id))
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatementSubtopicAssociation.subtopic_id == max_subtopic_id_subquery,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id)
                    ),
                )
                .scalar_subquery()
            )

            max_ids = (
                session.query(
                    UniqueStatement.statement_type_id.label("max_statement_type_id"),
                    UniqueStatementSubtopicAssociation.subtopic_id.label("max_subtopic_id"),
                    UniqueStatement.unique_statement_id.label("max_unique_statement_id"),
                )
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatementSubtopicAssociation.subtopic_id == max_subtopic_id_subquery,
                    UniqueStatement.unique_statement_id == max_unique_statement_id_subquery,
                )
                .join(
                    UserStory, UniqueStatement.unique_statement_id == UserStory.unique_statement_id
                )
                .first()
            )

            if not max_ids:
                return None, None, None

            return (
                max_ids.max_statement_type_id,
                max_ids.max_subtopic_id,
                max_ids.max_unique_statement_id,
            )

    def find_acceptance_criteria_generation_resume_point(self, document_id: int):
        with self.session_factory() as session:

            # Find the maximum statement_type_id for AcceptanceCriteria through UserStory
            max_statement_type_id_subquery = (
                session.query(func.max(UniqueStatement.statement_type_id))
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id).join(
                            AcceptanceCriteria,
                            AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                        )
                    ),
                )
                .scalar_subquery()
            )

            # Find the maximum subtopic_id for AcceptanceCriteria through UserStory using the association table
            max_subtopic_id_subquery = (
                session.query(func.max(UniqueStatementSubtopicAssociation.subtopic_id))
                .join(
                    UniqueStatement,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id).join(
                            AcceptanceCriteria,
                            AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                        )
                    ),
                )
                .scalar_subquery()
            )

            # Find the maximum unique_statement_id for AcceptanceCriteria through UserStory using the association table
            max_unique_statement_id_subquery = (
                session.query(func.max(UniqueStatement.unique_statement_id))
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatementSubtopicAssociation.subtopic_id == max_subtopic_id_subquery,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id).join(
                            AcceptanceCriteria,
                            AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                        )
                    ),
                )
                .scalar_subquery()
            )

            # Query for the max_ids: statement_type_id, subtopic_id, and unique_statement_id
            max_ids = (
                session.query(
                    UniqueStatement.statement_type_id.label("max_statement_type_id"),
                    UniqueStatementSubtopicAssociation.subtopic_id.label("max_subtopic_id"),
                    UniqueStatement.unique_statement_id.label("max_unique_statement_id"),
                )
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatementSubtopicAssociation.subtopic_id == max_subtopic_id_subquery,
                    UniqueStatement.unique_statement_id == max_unique_statement_id_subquery,
                )
                .join(
                    UserStory, UniqueStatement.unique_statement_id == UserStory.unique_statement_id
                )
                .join(
                    AcceptanceCriteria, AcceptanceCriteria.user_story_id == UserStory.user_story_id
                )
                .first()
            )

            if not max_ids:
                return None, None, None

            return (
                max_ids.max_statement_type_id,
                max_ids.max_subtopic_id,
                max_ids.max_unique_statement_id,
            )

    def find_policy_requirement_comparison_generation_resume_point(self, document_id: int):
        with self.session_factory() as session:

            max_ids_query = (
                session.query(
                    func.max(UniqueStatement.statement_type_id).label("max_statement_type_id"),
                    func.max(UniqueStatementSubtopicAssociation.subtopic_id).label(
                        "max_subtopic_id"
                    ),
                    func.max(UniqueStatement.unique_statement_id).label("max_unique_statement_id"),
                )
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == StatementTypeEnum.PROCEDURE_CHANGE.value,
                )
            )

            # Execute the query and fetch results
            max_ids = max_ids_query.first()

            if not max_ids or not max_ids.max_statement_type_id:
                return None, None, None

            return (
                max_ids.max_statement_type_id,
                max_ids.max_subtopic_id,
                max_ids.max_unique_statement_id,
            )

    def find_federal_register_change_statement_resume_point(self, document_id: int):
        """
        Finds the maximum chunk_id for the given document_id.

        Args:
            document_id (int): The ID of the document for which to find the resume point.

        Returns:
            int or None: The maximum chunk_id for the document, or None if no matching records are found.
        """
        with self.session_factory() as session:
            # Define the query without group_by
            max_chunk_id = (
                session.query(func.max(Chunk.chunk_id))
                .join(Chunk, Document.document_id == Chunk.document_id)
                .join(Statement, Chunk.chunk_id == Statement.chunk_id)
                .filter(Document.document_id == document_id)
                .scalar()
            )

            return max_chunk_id

    def find_test_case_generation_resume_point(self, document_id: int):
        with self.session_factory() as session:

            # Find the maximum statement_type_id for TestCase through AcceptanceCriteria
            max_statement_type_id_subquery = (
                session.query(func.max(UniqueStatement.statement_type_id))
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id)
                        .join(
                            AcceptanceCriteria,
                            AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                        )
                        .join(
                            TestCase,
                            TestCase.acceptance_criteria_id
                            == AcceptanceCriteria.acceptance_criteria_id,
                        )
                    ),
                )
                .scalar_subquery()
            )

            # Find the maximum subtopic_id for TestCase through AcceptanceCriteria using the association table
            max_subtopic_id_subquery = (
                session.query(func.max(UniqueStatementSubtopicAssociation.subtopic_id))
                .join(
                    UniqueStatement,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id)
                        .join(
                            AcceptanceCriteria,
                            AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                        )
                        .join(
                            TestCase,
                            TestCase.acceptance_criteria_id
                            == AcceptanceCriteria.acceptance_criteria_id,
                        )
                    ),
                )
                .scalar_subquery()
            )

            # Find the maximum unique_statement_id for TestCase through AcceptanceCriteria using the association table
            max_unique_statement_id_subquery = (
                session.query(func.max(UniqueStatement.unique_statement_id))
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatementSubtopicAssociation.subtopic_id == max_subtopic_id_subquery,
                    UniqueStatement.unique_statement_id.in_(
                        session.query(UserStory.unique_statement_id)
                        .join(
                            AcceptanceCriteria,
                            AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                        )
                        .join(
                            TestCase,
                            TestCase.acceptance_criteria_id
                            == AcceptanceCriteria.acceptance_criteria_id,
                        )
                    ),
                )
                .scalar_subquery()
            )

            # Query for the max_ids: statement_type_id, subtopic_id, and unique_statement_id
            max_ids = (
                session.query(
                    UniqueStatement.statement_type_id.label("max_statement_type_id"),
                    UniqueStatementSubtopicAssociation.subtopic_id.label("max_subtopic_id"),
                    UniqueStatement.unique_statement_id.label("max_unique_statement_id"),
                )
                .join(
                    UniqueStatementSubtopicAssociation,
                    UniqueStatement.unique_statement_id
                    == UniqueStatementSubtopicAssociation.unique_statement_id,
                )
                .filter(
                    UniqueStatement.document_id == document_id,
                    UniqueStatement.statement_type_id == max_statement_type_id_subquery,
                    UniqueStatementSubtopicAssociation.subtopic_id == max_subtopic_id_subquery,
                    UniqueStatement.unique_statement_id == max_unique_statement_id_subquery,
                )
                .join(
                    UserStory, UniqueStatement.unique_statement_id == UserStory.unique_statement_id
                )
                .join(
                    AcceptanceCriteria, AcceptanceCriteria.user_story_id == UserStory.user_story_id
                )
                .join(
                    TestCase,
                    TestCase.acceptance_criteria_id == AcceptanceCriteria.acceptance_criteria_id,
                )
                .first()
            )

            if not max_ids:
                return None, None, None

            return (
                max_ids.max_statement_type_id,
                max_ids.max_subtopic_id,
                max_ids.max_unique_statement_id,
            )

    def delete_artifacts(self, unique_statement_id: int, statement_type_id: int, subtopic_id: int):
        with self.session_factory() as session:
            try:
                # Fetch user_story_ids based on the updated association with the subtopic
                user_story_ids = (
                    session.query(UserStory.user_story_id)
                    .join(
                        UniqueStatement,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.unique_statement_id == unique_statement_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                    )
                    .all()
                )

                if not user_story_ids:
                    return

                user_story_ids = [id[0] for id in user_story_ids]

                session.query(TestCase).filter(
                    TestCase.acceptance_criteria_id.in_(
                        session.query(AcceptanceCriteria.acceptance_criteria_id).filter(
                            AcceptanceCriteria.user_story_id.in_(user_story_ids)
                        )
                    )
                ).delete(synchronize_session=False)

                session.query(AcceptanceCriteria).filter(
                    AcceptanceCriteria.user_story_id.in_(user_story_ids)
                ).delete(synchronize_session=False)

                session.query(UserStory).filter(UserStory.user_story_id.in_(user_story_ids)).delete(
                    synchronize_session=False
                )

                session.commit()
            except Exception:
                session.rollback()
                raise

    def delete_user_story_artifacts(
        self, unique_statement_id: int, statement_type_id: int, subtopic_id: int
    ):
        with self.session_factory() as session:
            try:
                user_story_ids = (
                    session.query(UserStory.user_story_id)
                    .join(
                        UniqueStatement,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.unique_statement_id == unique_statement_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                    )
                    .all()
                )

                if not user_story_ids:
                    return

                user_story_ids = [id[0] for id in user_story_ids]

                session.query(UserStory).filter(UserStory.user_story_id.in_(user_story_ids)).delete(
                    synchronize_session=False
                )

                session.commit()
            except Exception:
                session.rollback()
                raise

    def delete_acceptance_criteria_artifacts(
        self, unique_statement_id: int, statement_type_id: int, subtopic_id: int
    ):
        with self.session_factory() as session:
            try:
                user_story_ids = (
                    session.query(UserStory.user_story_id)
                    .join(
                        UniqueStatement,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.unique_statement_id == unique_statement_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                    )
                    .all()
                )

                if not user_story_ids:
                    return

                user_story_ids = [id[0] for id in user_story_ids]

                session.query(UserStory).filter(UserStory.user_story_id.in_(user_story_ids)).delete(
                    synchronize_session=False
                )

                session.commit()
            except Exception:
                session.rollback()
                raise

    def delete_procedure_change_artifacts(
        self, unique_statement_id: int, statement_type_id: int, subtopic_id: int
    ):
        """
        Deletes user stories associated with the specified unique_statement_id, statement_type_id, and subtopic_id.

        Args:
            unique_statement_id (int): The ID of the unique statement.
            statement_type_id (int): The ID of the statement type.
            subtopic_id (int): The ID of the subtopic.
        """
        filter_conditions = [
            UniqueStatement.unique_statement_id == unique_statement_id,
            UniqueStatement.statement_type_id == statement_type_id,
            UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
        ]

        self._delete_artifacts(model=UserStory, filter_conditions=filter_conditions)

    def delete_test_case_artifacts(
        self, unique_statement_id: int, statement_type_id: int, subtopic_id: int
    ):
        with self.session_factory() as session:
            try:
                # Retrieve TestCase IDs through related AcceptanceCriteria and UserStory
                test_case_ids = (
                    session.query(TestCase.test_case_id)
                    .join(
                        AcceptanceCriteria,
                        TestCase.acceptance_criteria_id
                        == AcceptanceCriteria.acceptance_criteria_id,
                    )
                    .join(
                        UserStory,
                        AcceptanceCriteria.user_story_id == UserStory.user_story_id,
                    )
                    .join(
                        UniqueStatement,
                        UserStory.unique_statement_id == UniqueStatement.unique_statement_id,
                    )
                    .join(
                        UniqueStatementSubtopicAssociation,
                        UniqueStatement.unique_statement_id
                        == UniqueStatementSubtopicAssociation.unique_statement_id,
                    )
                    .filter(
                        UniqueStatement.unique_statement_id == unique_statement_id,
                        UniqueStatement.statement_type_id == statement_type_id,
                        UniqueStatementSubtopicAssociation.subtopic_id == subtopic_id,
                    )
                    .all()
                )

                if not test_case_ids:
                    return

                test_case_ids = [id[0] for id in test_case_ids]

                # Delete TestCase records based on the gathered IDs
                session.query(TestCase).filter(TestCase.test_case_id.in_(test_case_ids)).delete(
                    synchronize_session=False
                )

                session.commit()
            except Exception:
                session.rollback()
                raise

    def get_ordered_statement_types(self, document_id: int) -> List[StatementType]:
        with self.session_factory() as session:
            statement_types = (
                session.query(StatementType)
                .join(
                    UniqueStatement,
                    UniqueStatement.statement_type_id == StatementType.statement_type_id,
                )
                .filter(UniqueStatement.document_id == document_id)
                .order_by(StatementType.statement_type_id)
                .all()
            )
            return statement_types

    # Domain
    def get_domain_by_id(self, domain_id: int) -> Domain:
        return self.domain_accessor.get_domain_by_id(domain_id)

    # Acceptance Criteria
    def save_acceptance_criteria(self, insert_fields: dict) -> AcceptanceCriteria:
        acceptance_criteria = self.acceptance_criteria_accessor.save_acceptance_criteria(
            insert_fields
        )

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.ACCEPTANCE_CRITERIA,
                acceptance_criteria.acceptance_criteria_id,
                acceptance_criteria.to_dict(),
                AuditOperationType.CREATE,
            )
        )

        return acceptance_criteria

    def get_acceptance_criteria_by_id(self, acceptance_criteria_id: int) -> AcceptanceCriteria:
        return self.acceptance_criteria_accessor.get_acceptance_criteria_by_id(
            acceptance_criteria_id
        )

    def verify_acceptance_criteria_exists(self, acceptance_criteria_id) -> bool:
        return self.acceptance_criteria_accessor.verify_acceptance_criteria_exists(
            acceptance_criteria_id
        )

    def update_acceptance_criteria(
        self, acceptance_criteria_id: int, updated_fields: dict[str, str]
    ) -> AcceptanceCriteria:
        acceptance_criteria = self.acceptance_criteria_accessor.update_acceptance_criteria(
            acceptance_criteria_id, updated_fields
        )

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.ACCEPTANCE_CRITERIA,
                acceptance_criteria.acceptance_criteria_id,
                acceptance_criteria.to_dict(),
                AuditOperationType.UPDATE,
            )
        )

        return acceptance_criteria

    def delete_acceptance_criteria(self, acceptance_criteria_id: int):
        acceptance_criteria = self.acceptance_criteria_accessor.get_acceptance_criteria_by_id(
            acceptance_criteria_id
        )

        self.acceptance_criteria_accessor.delete_acceptance_criteria(acceptance_criteria_id)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.ACCEPTANCE_CRITERIA,
                acceptance_criteria.acceptance_criteria_id,
                acceptance_criteria.to_dict(),
                AuditOperationType.DELETE,
            )
        )

    def get_all_acceptance_criteria_per_user_story(self, user_story_id) -> List[AcceptanceCriteria]:
        return self.acceptance_criteria_accessor.get_all_acceptance_criteria_per_user_story(
            user_story_id
        )

    def get_document_id_by_acceptance_criteria_id(self, acceptance_criteria_id: int) -> int:
        return self.acceptance_criteria_accessor.get_document_id_by_acceptance_criteria_id(
            acceptance_criteria_id
        )

    def get_acceptance_criteria_ordered(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[AcceptanceCriteria]:
        return self.acceptance_criteria_accessor.get_acceptance_criteria_ordered(
            document_id, subtopic_id, statement_type_id
        )

    def check_acceptance_criteria_test_cases_exists(self, acceptance_criteria_id: int) -> bool:
        return self.acceptance_criteria_accessor.check_acceptance_criteria_test_cases_exists(
            acceptance_criteria_id
        )

    def check_acceptance_criteria_has_children(self, acceptance_criteria_id: int) -> bool:
        return self.acceptance_criteria_accessor.check_acceptance_criteria_has_children(
            acceptance_criteria_id
        )

    # Document
    def verify_document_exists(self, document_id: int) -> bool:
        return self.document_accessor.verify_document_exists(document_id)

    def save_document(self, insert_fields: dict):

        return self.document_accessor.save_document(insert_fields)

    def update_document(self, document_id: int, update_dto: DocumentUpdateDTO):

        return self.document_accessor.update_document(document_id, update_dto)

    def delete_document(self, document_id: int) -> str:
        return self.document_accessor.delete_document(document_id)

    def get_document_by_id(self, document_id: int) -> Document:
        return self.document_accessor.get_document_by_id(document_id)

    def get_document_statement_counts(self, document_id: int):
        return self.document_accessor.get_document_statement_counts(document_id)

    def get_document_unique_requirement_counts(self, document_id: int):
        return self.document_accessor.get_document_unique_requirement_counts(document_id)

    def get_document_count_chunks_with_unrated_statements(self, document_id: int):
        return self.document_accessor.get_document_count_chunks_with_unrated_statements(document_id)

    def get_document_bulk_generation_button_statuses(self, document_id: int):
        return self.document_accessor.get_document_bulk_generation_button_statuses(document_id)

    def get_document_counts_all_levels(self, document_id: int) -> dict[str, int]:
        return self.document_accessor.get_document_counts_all_levels(document_id)

    def get_document_by_acceptance_criteria_id(self, acceptance_criteria_id: int) -> Document:
        return self.document_accessor.get_document_by_acceptance_criteria_id(acceptance_criteria_id)

    def get_document_subtopic_counts(self, document_id: int):
        return self.document_accessor.get_document_subtopic_counts(document_id)

    def get_document_statistics_by_document_id(self, document_id: int):
        return self.document_accessor.get_document_statistics_by_document_id(document_id)

    def get_document_download_info(self, document_id: int, version: str) -> dict:
        return self.document_accessor.get_document_download_info(document_id, version)

    def get_available_document_versions_by_ids(self, document_ids: list[int]) -> list[dict]:
        return self.document_accessor.get_available_document_versions_by_ids(document_ids)

    def get_documents_by_job_type(self, job_types: list[str]) -> list[Document]:
        return self.document_accessor.get_documents_by_job_type(job_types)

    # UniqueStatement
    def verify_unique_statement_exists(self, unique_statement_id: int) -> bool:
        return self.unique_statement_accessor.verify_unique_statement_exists(unique_statement_id)

    def get_unique_statement_by_id(self, unique_statement_id: int) -> UniqueStatement:
        return self.unique_statement_accessor.get_unique_statement_by_id(unique_statement_id)

    def get_unique_statements_using_filter(
        self, domain_id: int, document_id: int, topic_id: int, subtopic_id: int
    ) -> List[UniqueStatement]:
        return self.unique_statement_accessor.get_unique_statements_using_filter(
            domain_id, document_id, topic_id, subtopic_id
        )

    def get_all_unique_statements_by_domain_id(self, domain_id: int) -> List[UniqueStatement]:
        return self.unique_statement_accessor.get_all_unique_statements_by_domain_id(domain_id)

    def get_new_unique_statement_identifier(
        self, document_id: int, subtopic_id: int, statement_type_id: int, topic_id: int
    ) -> str:
        return self.unique_statement_accessor.get_new_unique_statement_identifier(
            document_id, subtopic_id, statement_type_id, topic_id
        )

    def create_unique_statement_with_subtopics(self, insert_fields: dict) -> UniqueStatement:
        unique_statement = self.unique_statement_accessor.create_unique_statement_with_subtopics(
            insert_fields
        )

        self.auditor.record(
            AuditActivityInsertDTO(
                object_type=AuditObjectType.UNIQUE_STATEMENT,
                object_key=unique_statement.unique_statement_id,
                snapshot=unique_statement.to_dict(),
                operation_type=AuditOperationType.CREATE,
            )
        )

        for subtopic_association in unique_statement.unique_statement_subtopic_associations:
            self.auditor.record(
                AuditActivityInsertDTO(
                    object_type=AuditObjectType.UNIQUE_STATEMENT_SUBTOPIC_ASSOCIATION,
                    object_key=unique_statement.unique_statement_id,
                    snapshot=subtopic_association.to_dict(),
                    operation_type=AuditOperationType.CREATE,
                )
            )

        return unique_statement

    def update_unique_statement(
        self, unique_statement_id: int, updated_fields: dict
    ) -> UniqueStatement:
        unique_statement, changelog = self.unique_statement_accessor.update_unique_statement(
            unique_statement_id, updated_fields
        )

        # Record audit activity for the statement and each association
        for entry in changelog:
            self.auditor.record(
                AuditActivityInsertDTO(
                    object_type=entry.object_type,
                    object_key=entry.object_key,
                    snapshot=entry.snapshot,
                    operation_type=entry.operation_type,
                )
            )

        return unique_statement

    def update_unique_statements_to_positive_rating_by_document_id(self, document_id: int):
        return self.unique_statement_accessor.update_unique_statements_to_positive_rating_by_document_id(
            document_id
        )

    def update_unique_statement_chunk_id(self, unique_statement_id: int, chunk_id: int):
        self.unique_statement_accessor.update_unique_statement_chunk_id(
            unique_statement_id, chunk_id
        )

    def delete_unique_statement(self, unique_statement_id: int):
        return self.unique_statement_accessor.delete_unique_statement(unique_statement_id)

    def get_unique_statements_ordered(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[UniqueStatement]:
        return self.unique_statement_accessor.get_unique_statements_ordered(
            document_id, subtopic_id, statement_type_id
        )

    def get_unique_statements_for_document_and_subtopic(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[UniqueStatement]:
        return self.unique_statement_accessor.get_unique_statements_for_document_and_subtopic(
            document_id, subtopic_id, statement_type_id
        )

    def get_documents_with_change_statements(self, document_ids: list[int]) -> dict[int, bool]:
        """
        Checks which documents have change statements (statement_type_id = 11) in bulk.

        Args:
            document_ids (list[int]): List of document IDs to check

        Returns:
            dict[int, bool]: Dictionary mapping document IDs to boolean indicating presence of change statements

        Raises:
            ContentAccessorException: If there is an error checking for change statements
        """
        return self.unique_statement_accessor.get_documents_with_change_statements(document_ids)

    def get_subtopics_for_unique_statement(self, unique_statement_id: int) -> List[Subtopic]:
        return self.unique_statement_accessor.get_subtopics_for_unique_statement(
            unique_statement_id
        )

    def add_subtopics_to_unique_statement(
        self, unique_statement_id, subtopic_ids_to_add: list[dict]
    ):
        return self.unique_statement_accessor.add_subtopics_to_unique_statement(
            unique_statement_id, subtopic_ids_to_add
        )

    def get_subtopic_associations_for_unique_statement(
        self, unique_statement_id: int, subtopic_ids: list[int]
    ) -> List[UniqueStatementSubtopicAssociation]:
        return self.unique_statement_accessor.get_subtopic_associations_for_unique_statement(
            unique_statement_id, subtopic_ids
        )

    def update_subtopic_association_for_unique_statement(
        self, unique_statement_id: int, subtopic_id: int, is_primary: bool
    ) -> bool:
        return self.unique_statement_accessor._set_primary_flag_for_subtopic_association(
            unique_statement_id, subtopic_id, is_primary
        )

    # Subtopic
    def verify_subtopic_exists(self, subtopic_id: int) -> bool:
        return self.subtopic_accessor.verify_subtopic_exists(subtopic_id)

    def get_subtopic_by_id(self, subtopic_id: int):
        return self.subtopic_accessor.get_subtopic_by_id(subtopic_id)

    def get_subtopics_ordered(self, document_id: int, statement_type_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_subtopics_ordered(document_id, statement_type_id)

    def get_all_subtopics_ordered(self, document_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_all_subtopics_ordered(document_id)

    def get_subtopic_statements_for_chunk(self, chunk_id: int):
        return self.subtopic_accessor.get_subtopic_statements_for_chunk(chunk_id)

    def get_relevant_subtopics_for_unique_statement_generation(
        self, document_id: int
    ) -> List[Tuple[int, str]]:
        return self.subtopic_accessor.get_relevant_subtopics_for_unique_statement_generation(
            document_id
        )

    def get_subtopics_by_topic_id(self, topic_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_subtopics_by_topic_id(topic_id)

    def get_subtopics_by_business(self, business_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_subtopics_by_business(business_id)

    def get_subtopics_by_document(self, document_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_subtopics_by_document(document_id)

    def get_identified_subtopics_by_chunk(self, chunk_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_identified_subtopics_by_chunk(chunk_id)

    def get_all_subtopics(self) -> List[Subtopic]:
        return self.subtopic_accessor.get_all_subtopics()

    def get_subtopics_by_enterprise(self, enterprise_id: int) -> List[Subtopic]:
        return self.subtopic_accessor.get_subtopics_by_enterprise(enterprise_id)

    # Persona
    def get_persona_by_id(self, persona_id: int):
        return self.persona_accessor.get_persona_by_id(persona_id)

    def get_personas_for_unique_statements(self, document_id: int) -> dict:
        return self.persona_accessor.get_personas_for_unique_statements(document_id)

    def save_persona(self, insert_fields: dict) -> Persona:
        return self.persona_accessor.save_persona(insert_fields)

    def update_persona(self, persona_id: int, updated_fields: dict) -> Persona:
        return self.persona_accessor.update_persona(persona_id, updated_fields)

    def delete_persona(self, persona_id: int):
        return self.persona_accessor.delete_persona(persona_id)

    def get_personas_by_department(self, department_id: int) -> List[Persona]:
        return self.persona_accessor.get_personas_by_department(department_id)

    def get_personas_by_enterprise(self, enterprise_id: int) -> List[Persona]:
        return self.persona_accessor.get_personas_by_enterprise(enterprise_id)

    # Chunk
    def get_chunk_by_id(self, chunk_id: int) -> Chunk:
        return self.chunk_accessor.get_chunk_by_id(chunk_id)

    def get_chunk_ids_for_subtopic_and_document(
        self, subtopic_id: int, document_id: int
    ) -> List[int]:
        return self.chunk_accessor.get_chunk_ids_for_subtopic_and_document(subtopic_id, document_id)

    def save_chunk(
        self, document_id: int, formatted_text: dict, text_content: dict, vector_id: str
    ) -> int:
        return self.chunk_accessor.save_chunk(document_id, formatted_text, text_content, vector_id)

    def get_chunks_by_document(self, document_id: int) -> list[Chunk]:
        return self.chunk_accessor.get_chunks_by_document(document_id)

    def get_document_id_by_chunk_id(self, chunk_id: int) -> int:
        return self.chunk_accessor.get_document_id_by_chunk_id(chunk_id)

    def delete_all_chunks_for_document(self, document_id: int):
        return self.chunk_accessor.delete_all_chunks_for_document(document_id)

    def update_chunk_subtopics(self, chunk_id, subtopic_ids) -> Chunk:
        chunk_current_subtopic_statements = (
            self.statement_accessor.get_statements_by_chunk_and_type(
                chunk_id, StatementTypeEnum.IDENTIFIED_SUBTOPIC.value
            )
        )
        chunk = self.get_chunk_by_id(chunk_id)
        business_id = self.get_document_by_id(chunk.document_id).business_id
        business_subtopics = self.get_subtopics_by_business(business_id)

        return self.chunk_accessor.update_chunk_subtopics(
            chunk_id, business_subtopics, subtopic_ids, chunk_current_subtopic_statements
        )

    # Statement
    def verify_statement_exists(self, statement_id: int) -> bool:
        return self.statement_accessor.verify_statement_exists(statement_id)

    def get_statement_by_id(self, statement_id: int) -> Statement:
        return self.statement_accessor.get_statement_by_id(statement_id)

    def get_statements_by_chunk_and_type(
        self, chunk_id: int, statement_type_id: int
    ) -> List[Statement]:
        return self.statement_accessor.get_statements_by_chunk_and_type(chunk_id, statement_type_id)

    def get_statements_by_document_and_type(
        self, document_id: int, statement_type_id: int
    ) -> List[Statement]:
        return self.statement_accessor.get_statements_by_document_and_type(
            document_id, statement_type_id
        )

    def save_statement(self, insert_fields: dict) -> Statement:
        statement = self.statement_accessor.save_statement(insert_fields)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.STATEMENT,
                statement.statement_id,
                statement.to_dict(),
                AuditOperationType.CREATE,
            )
        )

        return statement

    def get_statements_by_vector_and_type(self, vector_id: str, statement_type_id: int):
        return self.statement_accessor.get_statements_by_vector_and_type(
            vector_id, statement_type_id
        )

    def update_statement(self, statement_id: int, updated_fields: dict) -> Statement:
        statement = self.statement_accessor.update_statement(statement_id, updated_fields)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.STATEMENT,
                statement.statement_id,
                statement.to_dict(),
                AuditOperationType.UPDATE,
            )
        )

        return statement

    def delete_statement(self, statement_id: int):
        statement = self.statement_accessor.get_statement_by_id(statement_id)

        self.statement_accessor.delete_statement(statement_id)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.STATEMENT,
                statement.statement_id,
                statement.to_dict(),
                AuditOperationType.DELETE,
            )
        )

    def get_statement_counts(self, document_id: int) -> dict:
        return self.statement_accessor.get_statement_counts(document_id)

    # User Story
    def verify_user_story_exists(self, user_story_id: int) -> bool:
        return self.user_story_accessor.verify_user_story_exists(user_story_id)

    def get_user_story_by_id(self, user_story_id: int) -> UserStory:
        return self.user_story_accessor.get_user_story_by_id(user_story_id)

    def get_user_stories_ordered(
        self, document_id: int, subtopic_id: int, statement_type_id: int
    ) -> List[UserStory]:
        return self.user_story_accessor.get_user_stories_ordered(
            document_id, subtopic_id, statement_type_id
        )

    def get_user_stories_for_unique_statement(self, unique_statement_id: int) -> List[UserStory]:
        return self.user_story_accessor.get_user_stories_for_unique_statement(unique_statement_id)

    def save_user_story(self, insert_fields: dict) -> UserStory:
        user_story = self.user_story_accessor.save_user_story(insert_fields)

        self.auditor.record(
            AuditActivityInsertDTO(
                object_type=AuditObjectType.USER_STORY,
                object_key=user_story.user_story_id,
                snapshot=user_story.to_dict(),
                operation_type=AuditOperationType.CREATE,
            )
        )

        return user_story

    def check_user_story_acceptance_criteria_exists(self, user_story_id: int) -> bool:
        return self.user_story_accessor.check_user_story_acceptance_criteria_exists(user_story_id)

    def update_user_story(self, user_story_id: int, updated_fields: dict[str, str]) -> UserStory:
        user_story = self.user_story_accessor.update_user_story(user_story_id, updated_fields)

        self.auditor.record(
            AuditActivityInsertDTO(
                object_type=AuditObjectType.USER_STORY,
                object_key=user_story.user_story_id,
                snapshot=user_story.to_dict(),
                operation_type=AuditOperationType.UPDATE,
            )
        )

        return user_story

    def delete_user_story(self, user_story_id: int):
        user_story = self.user_story_accessor.get_user_story_by_id(user_story_id)

        self.user_story_accessor.delete_user_story(user_story_id)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.USER_STORY,
                user_story.user_story_id,
                user_story.to_dict(),
                AuditOperationType.DELETE,
            )
        )

    def get_document_id_by_user_story_id(self, user_story_id: int) -> int:
        return self.user_story_accessor.get_document_id_by_user_story_id(user_story_id)

    def check_user_story_has_children(self, user_story_id: int) -> bool:
        return self.user_story_accessor.check_user_story_has_children(user_story_id)

    # Test Case
    def save_test_case(self, insert_fields: dict) -> TestCase:
        test_case = self.test_case_accessor.save_test_case(insert_fields)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.TEST_CASE,
                test_case.test_case_id,
                test_case.to_dict(),
                AuditOperationType.CREATE,
            )
        )

        return test_case

    def get_test_case_by_id(self, test_case_id: int) -> TestCase:
        return self.test_case_accessor.get_test_case_by_id(test_case_id)

    def delete_test_case(self, test_case_id: int):
        test_case = self.test_case_accessor.get_test_case_by_id(test_case_id)

        self.test_case_accessor.delete_test_case(test_case_id)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.TEST_CASE,
                test_case.test_case_id,
                test_case.to_dict(),
                AuditOperationType.DELETE,
            )
        )

    def get_all_test_cases_per_acceptance_criteria(
        self, acceptance_criteria_id: int
    ) -> List[TestCase]:
        return self.test_case_accessor.get_all_test_cases_per_acceptance_criteria(
            acceptance_criteria_id
        )

    def verify_test_case_exists(self, test_case_id: int) -> bool:
        return self.test_case_accessor.verify_test_case_exists(test_case_id)

    def update_test_case(self, test_case_id: int, updated_fields: dict[str, str]) -> TestCase:

        test_case = self.test_case_accessor.update_test_case(test_case_id, updated_fields)

        self.auditor.record(
            AuditActivityInsertDTO(
                AuditObjectType.TEST_CASE,
                test_case.test_case_id,
                test_case.to_dict(),
                AuditOperationType.UPDATE,
            )
        )

        return test_case

    def get_document_id_by_test_case_id(self, test_case_id: int) -> int:
        return self.test_case_accessor.get_document_id_by_test_case_id(test_case_id)

    # Job
    def save_job(self, insert_fields: dict) -> Job:
        return self.job_accessor.save_job(insert_fields)

    def get_job_by_id(self, job_id: int) -> Job:
        return self.job_accessor.get_job_by_id(job_id)

    def update_job(self, job_id: int, updated_fields: dict) -> Job:
        return self.job_accessor.update_job(job_id, updated_fields)

    def _delete_artifacts(self, model, filter_conditions: list):
        """
        Generalized method to delete artifacts based on filter conditions.

        Args:
            model: The SQLAlchemy model to query and delete from.
            filter_conditions (list): List of filter conditions to apply for deletion.
        """
        with self.session_factory() as session:
            try:
                # Perform a single delete operation
                session.query(model).filter(*filter_conditions).delete(synchronize_session=False)

                # Commit the transaction
                session.commit()
            except Exception as e:
                # Rollback the transaction in case of error
                session.rollback()
                raise Exception(f"Failed to delete artifacts: {e}")

    def create_document_statistics(
        self, document_id: int, stats_dto: DocumentStatisticsDTO
    ) -> Optional[DocumentStatisticsDTO]:
        return self.document_statistics_accessor.create_document_statistics(document_id, stats_dto)

    # Department
    def get_department_by_id(self, department_id: int):
        return self.department_accessor.get_department_by_id(department_id)

    def get_departments_by_enterprise(self, enterprise_id: int) -> List[Department]:
        return self.department_accessor.get_departments_by_enterprise(enterprise_id)

    def get_departments_by_business(self, business_id: int) -> List[Department]:
        return self.department_accessor.get_departments_by_business(business_id)

    def save_department(self, insert_fields: dict) -> Department:
        return self.department_accessor.save_department(insert_fields)

    def update_department(self, department_id: int, updated_fields: dict) -> Department:
        return self.department_accessor.update_department(department_id, updated_fields)

    def delete_department(self, department_id: int):
        return self.department_accessor.delete_department(department_id)

    # Topic
    def get_topic_by_id(self, topic_id: int):
        return self.topic_accessor.get_department_by_id(topic_id)

    def get_topics_by_enterprise(self, enterprise_id: int) -> List[Topic]:
        return self.topic_accessor.get_topics_by_enterprise(enterprise_id)

    # Audit Activity
    def get_audit_activity_by_id(self, audit_activity_id: int) -> AuditActivityReadDTO:
        return self.audit_activity_accessor.get_audit_activity_by_id(audit_activity_id)

    def get_all_audit_activity_per_object(
        self, audit_object_type_id: int, object_key: int
    ) -> List[AuditActivityReadDTO]:
        return self.audit_activity_accessor.get_all_audit_activity_per_object(
            audit_object_type_id, object_key
        )

    def get_all_audit_log_entries_per_user(self, user_id: int) -> List[AuditActivityReadDTO]:
        return self.audit_activity_accessor.get_all_audit_log_entries_per_user(user_id)

    # Business
    def get_all_business(self) -> Business:
        return self.business_accessor.get_all_business()

    def get_business(self, business_id: int) -> Business:
        """
        Retrieves a business entry from the database by its ID.

        Args:
            business_id (int): The ID of the business to retrieve

        Returns:
            Business: The business entry

        Raises:
            ContentAccessorException: If there is an error retrieving the business or if the business is not found
        """
        return self.business_accessor.get_business(business_id)
