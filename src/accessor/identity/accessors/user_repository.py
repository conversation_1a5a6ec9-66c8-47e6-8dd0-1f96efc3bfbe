from accessor.content.exceptions import ContentAccessorException
from accessor.identity.interfaces import IUserRepository
from shared.models import User, Enterprise
from sqlalchemy.orm import joinedload


SYSTEM_USER_EMAIL = "<EMAIL>"


class UserRepository(IUserRepository):
    def __init__(self, session_factory):
        self.session_factory = session_factory

    def get_user_by_auth0_id(self, auth0_id: str) -> User:
        with self.session_factory() as session:
            try:
                user = (
                    session.query(User)
                    .filter_by(auth0_id=auth0_id)
                    .options(joinedload(User.enterprise).joinedload(Enterprise.domain))
                    .first()
                )
                return user

            except Exception as e:
                raise ContentAccessorException(f"Error fetching user: {str(e)}") from e

    def get_system_user(self) -> User:
        """
        Retrieves the system user from the database, used for audit logging or background tasks.
        """
        with self.session_factory() as session:
            try:
                user = (
                    session.query(User)
                    .filter_by(email_address=SYSTEM_USER_EMAIL)
                    .options(joinedload(User.enterprise).joinedload(Enterprise.domain))
                    .first()
                )
                return user
            except Exception as e:
                raise ContentAccessorException(f"Error fetching system user: {str(e)}") from e
