import logging

from accessor.identity.accessors import UserRepository
from accessor.identity.interfaces import IUserRepository
from shared.models import User
from util.database import Session


class IdentityAccessor(IUserRepository):
    def __init__(self, logger_name):
        self.session_factory = Session
        self.logger = logging.getLogger(logger_name)

        self.user_repository = UserRepository(self.session_factory)

    # User
    def get_user_by_auth0_id(self, auth0_id: str) -> User:
        return self.user_repository.get_user_by_auth0_id(auth0_id)

    def get_system_user(self):
        return self.user_repository.get_system_user()
