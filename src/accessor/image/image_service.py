"""

This module defines the ImageService class, which handles operations related to image
management, including uploading, retrieving, updating, and deleting images. The service
interfaces with a repository and a storage client to store and manage image data.
"""

import datetime
import re
from typing import List

from accessor.image.interface.i_image_repository import IImageRepository
from accessor.image.interface.i_image_service import IImageService
from accessor.image.interface.i_storage_client import IStorageClient
from accessor.image.model.chunk_image_association_model import (
    ChunkImageAssociationModel,
)
from accessor.image.model.image_model import ImageModel
from util.database import Session


class ImageService(IImageService):
    """
    A service class for managing image-related operations, such as storing images,
    retrieving images by ID, associating images with chunks, and handling metadata.

    Attributes:
        _image_repository (IImageRepository): The repository for accessing image data.
        _storage_client (IStorageClient): The client for interacting with storage.
    """

    def __init__(self, image_repository: IImageRepository, storage_client: IStorageClient):
        """
        Initializes the ImageService with a repository and storage client.

        Parameters:
            image_repository (IImageRepository): The image repository for CRUD operations.
            storage_client (IStorageClient): The storage client for handling image files.
        """
        self._image_repository = image_repository
        self._storage_client = storage_client
        self.session_factory = Session

    def upload_image(self, file_content: bytes, file_name: str, metadata: dict) -> ImageModel:
        """
        Uploads an image file to storage and creates an image record in the repository.

        Parameters:
            file_content (bytes): The content of the image file.
            file_name (str): The name of the file.
            metadata (dict): Metadata associated with the image.

        Returns:
            ImageModel: The created image model with storage location and metadata.
        """
        location = self._store_image(file_content, file_name)
        image_model = ImageModel(location=location, metadata=metadata)
        image_model = self._image_repository.create_image(image_model)
        return image_model

    def get_image_by_id(self, image_id: int) -> ImageModel:
        """
        Retrieves an image model by its ID.

        Parameters:
            image_id (int): The ID of the image to retrieve.

        Returns:
            ImageModel: The retrieved image model.
        """
        return self._image_repository.get_image_by_id(image_id)

    def get_image_content(self, image_id: int) -> bytes:
        """
        Retrieves the content of an image file by its ID.

        Parameters:
            image_id (int): The ID of the image to retrieve content for.

        Returns:
            bytes: The content of the image file.

        Raises:
            ValueError: If the image with the specified ID is not found.
        """
        image_model = self.get_image_by_id(image_id)
        if image_model:
            return self._retrieve_image(image_model.location)
        else:
            raise ValueError(f"Image with ID {image_id} not found.")

    def delete_image(self, image_id: int) -> None:
        """
        Deletes an image from both storage and the repository.

        Parameters:
            image_id (int): The ID of the image to delete.

        Raises:
            ValueError: If the image with the specified ID is not found.
        """
        image_model = self.get_image_by_id(image_id)
        if image_model:
            self._delete_from_storage(image_model.location)
            self._image_repository.delete_image(image_id)
        else:
            raise ValueError(f"Image with ID {image_id} not found.")

    def update_image_metadata(self, image_id: int, metadata: dict) -> ImageModel:
        """
        Updates metadata for an existing image.

        Parameters:
            image_id (int): The ID of the image to update.
            metadata (dict): The new metadata to associate with the image.

        Returns:
            ImageModel: The updated image model.

        Raises:
            ValueError: If the image with the specified ID is not found.
        """
        image_model = self.get_image_by_id(image_id)
        if image_model:
            image_model.image_metadata = metadata
            self._image_repository.update_image(image_model)
            return image_model
        else:
            raise ValueError(f"Image with ID {image_id} not found.")

    def associate_with_chunk(self, image_id: int, chunk_id: int) -> None:
        """
        Associates an image with a specified chunk.

        Parameters:
            image_id (int): The ID of the image to associate.
            chunk_id (int): The ID of the chunk to associate the image with.
        """
        self._image_repository.associate_with_chunk(image_id, chunk_id)

    def get_images_by_chunk(self, chunk_id: int) -> List[ImageModel]:
        """
        Retrieves all images associated with a specified chunk.

        Parameters:
            chunk_id (int): The ID of the chunk to retrieve images for.

        Returns:
            List[ImageModel]: A list of image models associated with the chunk.
        """
        return self._image_repository.get_images_by_chunk(chunk_id)

    def store_chunk_image(
        self, file_content: bytes, file_name: str, metadata: dict, chunk_id: int
    ) -> int:
        """
        Stores an image and associates it with a chunk in a transactional manner.

        Args:
            file_content (bytes): The binary content of the image file.
            file_name (str): The original name of the image file.
            metadata (dict): Additional metadata for the image.
            chunk_id (int): The unique identifier of the chunk to associate with.

        Returns:
            ImageDTO: Data transfer object containing image details.
        """

        joined_file_name = f"images/chunks/{chunk_id}/{file_name}"
        with self.session_factory() as session:
            try:
                storage_key = self._generate_storage_key(joined_file_name)

                image_model = ImageModel(
                    location=storage_key,
                    metadata=metadata,
                    created_at=datetime.datetime.now(datetime.timezone.utc),
                    updated_at=datetime.datetime.now(datetime.timezone.utc),
                )
                session.add(image_model)
                session.flush()  # To get image_id
                image_id = image_model.image_id

                self._storage_client.upload(storage_key, file_content)

                association = ChunkImageAssociationModel(image_id=image_id, chunk_id=chunk_id)
                session.add(association)
                session.commit()
                return image_id
            except Exception as e:
                raise Exception(f"Failed to store image and associate with chunk: {str(e)}") from e

    def _store_image(self, file_content: bytes, file_name: str) -> str:
        """
        Stores an image file in storage and returns the storage location key.

        Parameters:
            file_content (bytes): The content of the image file.
            file_name (str): The name of the file to store.

        Returns:
            str: The storage location key for the file.
        """
        storage_key = self._generate_storage_key(file_name)
        self._storage_client.upload(storage_key, file_content)
        return storage_key

    def _generate_storage_key(self, file_name: str) -> str:
        """
        Generates a unique storage key for an image file based on environment and file name.

        Parameters:
            file_name (str): The name of the file.

        Returns:
            str: The generated storage key for the file.
        """
        sanitized_file_name = self._sanitize_file_name(file_name)
        storage_key = sanitized_file_name
        return storage_key

    def _sanitize_file_name(self, file_name: str) -> str:
        """
        Sanitizes a file name to remove special characters.

        Parameters:
            file_name (str): The file name to sanitize.

        Returns:
            str: The sanitized file name.
        """
        sanitized_name = re.sub(r"[^a-zA-Z0-9_.-/]", "_", file_name)
        return sanitized_name

    def _retrieve_image(self, location: str) -> bytes:
        """
        Retrieves an image file from storage.

        Parameters:
            location (str): The storage location key of the image.

        Returns:
            bytes: The content of the image file.
        """
        return self._storage_client.download(location)

    def _delete_from_storage(self, location: str) -> None:
        """
        Deletes an image file from storage.

        Parameters:
            location (str): The storage location key of the image to delete.
        """
        self._storage_client.delete(location)
