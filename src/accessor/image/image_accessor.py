"""
This module defines the `ImageAccessor` class, which provides a high-level interface
for managing image operations, including storing, retrieving, updating, and deleting images.
It integrates with AWS S3 for storage, a repository for database operations, and an image
service for processing image-related requests.

The `ImageAccessor` class implements both `IImageAccessor` and `IChunkImageAccessor` interfaces,
allowing it to perform operations on individual images as well as associating images with specific
chunks. This module encapsulates all low-level implementation details, such as the S3 storage 
client, image repository, and image service, making it accessible to external consumers via clean 
and simple method calls.
"""

import logging
import os
from typing import List

from accessor.image.dto import ImageContentDTO, ImageDTO
from accessor.image.image_repository import ImageRepository
from accessor.image.image_service import ImageService
from accessor.image.interface import IChunkImageAccessor, IImageAccessor
from accessor.image.model.image_model import ImageModel
from accessor.image.s3_storage_client import S3StorageClient
from util.config import (
    AWS_FEED_MANAGER_ACCESS_KEY,
    AWS_FEED_MANAGER_SECRET_KEY,
    AWS_REGION,
    AWS_S3_BUCKET_NAME,
)


class ImageAccessor(IImageAccessor, IChunkImageAccessor):
    """
    ImageAccessor provides image access operations to outside consumers.

    It encapsulates all internal implementations such as storage, repositories, and services.
    """

    def __init__(self, logger_name):
        """
        Initializes the ImageAccessor.
        """
        self.logger = logging.getLogger(logger_name)
        self._image_service = self._create_image_service(
            aws_access_key=AWS_FEED_MANAGER_ACCESS_KEY,
            aws_secret_key=AWS_FEED_MANAGER_SECRET_KEY,
            region=AWS_REGION,
            bucket_name=AWS_S3_BUCKET_NAME,
        )

    def _create_image_service(
        self,
        aws_access_key: str,
        aws_secret_key: str,
        region: str,
        bucket_name: str,
    ):
        image_repository = self._create_image_repository()
        storage_client = self._create_storage_client(
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key,
            region=region,
            bucket_name=bucket_name,
        )
        return ImageService(
            image_repository=image_repository,
            storage_client=storage_client,
        )

    def _create_storage_client(
        self, aws_access_key: str, aws_secret_key: str, region: str, bucket_name: str
    ):
        return S3StorageClient(
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key,
            region=region,
            bucket_name=bucket_name,
        )

    def _create_image_repository(self):
        return ImageRepository()

    def upload_image(self, file_content: bytes, file_name: str, metadata: dict) -> ImageDTO:
        image_model = self._image_service.upload_image(file_content, file_name, metadata)
        return self._convert_to_dto(image_model)

    def get_image_metadata(self, image_id: int) -> ImageDTO:
        image_model = self._image_service.get_image_by_id(image_id)
        if image_model:
            return self._convert_to_dto(image_model)
        else:
            raise ValueError(f"Image with ID {image_id} not found.")

    def get_image_content(self, image_id: int) -> ImageContentDTO:
        image_metadata = self._image_service.get_image_by_id(image_id)
        if not image_metadata:
            raise ValueError(f"Image with ID {image_id} not found.")

        # Retrieve content using the existing get_image_content method
        image_content = self._image_service.get_image_content(image_id)

        # Construct and return the ImageContentDTO
        return ImageContentDTO(
            image_id=image_metadata.image_id,
            content=image_content,
            file_name=self._extract_file_name(image_metadata.location),
            metadata=image_metadata.metadata,
            created_at=image_metadata.created_at,
            updated_at=image_metadata.updated_at,
        )

    def delete_image(self, image_id: int) -> None:
        self._image_service.delete_image(image_id)

    def update_image_metadata(self, image_id: int, metadata: dict) -> ImageDTO:
        image_model = self._image_service.update_image_metadata(image_id, metadata)
        return self._convert_to_dto(image_model)

    def associate_image_with_chunk(self, image_id: int, chunk_id: int) -> None:
        self._image_service.associate_with_chunk(image_id, chunk_id)

    def get_images_by_chunk(self, chunk_id: int) -> List[ImageDTO]:
        image_models = self._image_service.get_images_by_chunk(chunk_id)
        return [self._convert_to_dto(img_model) for img_model in image_models]

    def store_chunk_image(
        self, file_content: bytes, file_name: str, metadata: dict, chunk_id: int
    ) -> int:
        """
        Method to store an image and associate it with a chunk.

        Args:
            file_content (bytes): The binary content of the image file.
            file_name (str): The original name of the image file.
            metadata (dict): Additional metadata for the image.
            chunk_id (int): The unique identifier of the chunk to associate with.

        Returns:
            ImageDTO: Data transfer object containing image details.
        """
        return self._image_service.store_chunk_image(
            file_content=file_content, file_name=file_name, metadata=metadata, chunk_id=chunk_id
        )

    # Internal helper methods

    def _convert_to_dto(self, image_model: ImageModel) -> ImageDTO:
        file_name = self._extract_file_name(image_model.location)
        return ImageDTO(
            image_id=image_model.image_id,
            file_name=file_name,
            metadata=image_model.image_metadata,
            created_at=image_model.created_at,
            updated_at=image_model.updated_at,
        )

    def _extract_file_name(self, location: str) -> str:
        return os.path.basename(location).split("/", 1)[-1]
