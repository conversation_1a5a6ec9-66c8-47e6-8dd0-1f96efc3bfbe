"""

This module defines the ImageModel class, a SQLAlchemy ORM model representing a stored image entity.
The Image model includes metadata, timestamps, and a location for
where the image is stored.
"""

from sqlalchemy import JSON, Column, DateTime, Integer, String, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class ImageModel(Base):
    """
    Represents an image entity in the database, including its location in storage, metadata,
    and timestamps for creation and modification.

    Attributes:
        image_id (int): The primary key for the image record.
        location (str): The storage location or URL of the image.
        metadata (JSON): Optional metadata associated with the image (e.g., tags, description).
        created_at (datetime): The timestamp for when the image record was created.
        updated_at (datetime): The timestamp for the last update to the image record.
    """

    __tablename__ = "image"

    image_id = Column(Integer, primary_key=True)
    location = Column(String, nullable=False)
    image_metadata = Column(JSON, name="metadata")
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),  # pylint: disable=not-callable
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        onupdate=func.now(),  # pylint: disable=not-callable
        server_default=func.now(),  # pylint: disable=not-callable
        nullable=False,
    )
