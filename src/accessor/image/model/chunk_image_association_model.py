"""
Module: chunk_image_association

This module defines the association between chunks and images in the database. 
It facilitates a many-to-many relationship between chunks and images.
"""

from sqlalchemy import Column, DateTime, Integer, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class ChunkImageAssociationModel(Base):
    """
    Represents the association table between chunks and images.

    This model establishes a many-to-many relationship by linking `chunk_id` and `image_id`.

    Attributes:
        chunk_id (Integer): The primary key of the chunk.
        image_id (Integer): The primary key of the image.
        created_at (DateTime): Timestamp when the association was created.
        updated_at (DateTime): Timestamp when the association was last updated.
    """

    __tablename__ = "chunk_image"

    chunk_id = Column(Integer, primary_key=True)
    image_id = Column(Integer, primary_key=True)
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),  # pylint: disable=not-callable
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        onupdate=func.now(),  # pylint: disable=not-callable
        server_default=func.now(),  # pylint: disable=not-callable
        nullable=False,
    )
