from typing import List

from accessor.image.interface.i_image_repository import IImageRepository
from accessor.image.model.chunk_image_association_model import (
    ChunkImageAssociationModel,
)
from accessor.image.model.image_model import ImageModel
from util.database import Session


class ImageRepository(IImageRepository):
    def __init__(self):
        self.session_factory = Session

    def create_image(self, image_model: ImageModel) -> ImageModel:
        with self.session_factory() as session:
            session.add(image_model)
            session.commit()
            session.refresh(image_model)
            return image_model

    def get_image_by_id(self, image_id: int) -> ImageModel:
        with self.session_factory() as session:
            return session.query(ImageModel).filter_by(image_id=image_id).first()

    def update_image(self, image_model: ImageModel) -> ImageModel:
        """
        Updates an image record in the database with the given metadata.

        Parameters:
            image_model (ImageModel): The modified image model to be saved.

        Returns:
            ImageModel: The updated image model after committing to the database.
        """
        with self.session_factory() as session:
            # Attach the image_model to the session if it’s detached
            if not session.contains(image_model):
                image_model = session.merge(image_model)

            # Mark image_model as modified and commit the changes
            session.add(image_model)
            session.commit()
            return image_model

    def delete_image(self, image_id: int) -> None:
        image_model = self.get_image_by_id(image_id)
        if image_model:
            with self.session_factory() as session:
                session.delete(image_model)
                session.commit()

    def associate_with_chunk(self, image_id: int, chunk_id: int) -> None:
        association = ChunkImageAssociationModel(chunk_id=chunk_id, image_id=image_id)
        with self.session_factory() as session:
            session.add(association)
            session.commit()

    def get_images_by_chunk(self, chunk_id: int) -> List[ImageModel]:
        with self.session_factory() as session:
            return (
                session.query(ImageModel)
                .join(
                    ChunkImageAssociationModel,
                    ImageModel.image_id == ChunkImageAssociationModel.image_id,
                )
                .filter(ChunkImageAssociationModel.chunk_id == chunk_id)
                .all()
            )
