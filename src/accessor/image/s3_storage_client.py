"""
This module provides the `S3StorageClient` class, which implements the `IStorageClient` 
interface using the `S3Helper` for interactions with Amazon S3. The `S3StorageClient` 
class facilitates uploading, downloading, and deleting data in an Amazon S3 bucket by 
leveraging the functionalities provided by `S3Helper`. It abstracts the underlying S3 
operations and presents a simple interface conforming to `IStorageClient`, allowing 
other components of the application to perform storage operations without needing to
 know the specifics of Amazon S3.
"""

from accessor.image.interface.i_storage_client import IStorageClient
from util.s3_helper import S3Helper


class S3StorageClient(IStorageClient):
    """
    S3StorageClient implements IStorageClient using S3<PERSON>elper for S3 operations.
    """

    def __init__(self, aws_access_key: str, aws_secret_key: str, region: str, bucket_name: str):
        """
        Initializes the S3StorageClient with AWS credentials and bucket information.

        Args:
            aws_access_key (str): The AWS access key ID.
            aws_secret_key (str): The AWS secret access key.
            region (str): The AWS region where the S3 bucket is located.
            bucket_name (str): The name of the S3 bucket.
        """
        self._s3_helper = S3Helper(
            aws_access_key=aws_access_key,
            aws_secret_key=aws_secret_key,
            region=region,
            bucket_name=bucket_name,
        )

    def upload(self, key: str, data: bytes) -> None:
        """
        Uploads data to S3 using S3Helper.
        """
        self._s3_helper.upload_document_to_s3(file_content=data, s3_key=key)

    def download(self, key: str) -> bytes:
        """
        Downloads data from S3 using S3Helper.
        """
        return self._s3_helper.get_document_from_s3(s3_location=key)

    def delete(self, key: str) -> None:
        """
        Deletes data from S3 using S3Helper.
        """
        self._s3_helper.delete_document_from_s3(s3_key=key)
