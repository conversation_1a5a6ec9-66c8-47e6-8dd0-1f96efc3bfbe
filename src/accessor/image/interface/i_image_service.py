from abc import ABC, abstractmethod
from typing import List

from accessor.image.model.image_model import ImageModel


class IImageService(ABC):
    @abstractmethod
    def upload_image(self, file_content: bytes, file_name: str, metadata: dict) -> ImageModel:
        pass

    @abstractmethod
    def get_image_by_id(self, image_id: int) -> ImageModel:
        pass

    @abstractmethod
    def delete_image(self, image_id: int) -> None:
        pass

    @abstractmethod
    def update_image_metadata(self, image_id: int, metadata: dict) -> ImageModel:
        pass

    @abstractmethod
    def associate_with_chunk(self, image_id: int, chunk_id: int) -> None:
        pass

    @abstractmethod
    def get_images_by_chunk(self, chunk_id: int) -> List[ImageModel]:
        pass

    @abstractmethod
    def store_chunk_image(
        self, file_content: bytes, file_name: str, metadata: dict, chunk_id: int
    ) -> int:
        pass
