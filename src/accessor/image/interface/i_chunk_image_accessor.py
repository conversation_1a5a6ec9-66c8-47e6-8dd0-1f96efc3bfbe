"""
    Interface for chunk-image association operations.
"""

from abc import ABC, abstractmethod
from typing import List

from accessor.image.dto import ImageDTO


class IChunkImageAccessor(ABC):
    """
    Interface for chunk-image association operations.
    """

    @abstractmethod
    def store_chunk_image(
        self, file_content: bytes, file_name: str, metadata: dict, chunk_id: int
    ) -> ImageDTO:
        """
        Stores an image and associates it with a chunk in a single transactional operation.

        Args:
            file_content (bytes): The binary content of the image file.
            file_name (str): The original name of the image file.
            metadata (dict): Additional metadata for the image.
            chunk_id (int): The unique identifier of the chunk to associate with.

        Returns:
            ImageDTO: Data transfer object containing image details.

        Raises:
            Exception: If the operation fails.
        """

    @abstractmethod
    def associate_image_with_chunk(self, image_id: int, chunk_id: int) -> None:
        """
        Associates an image with a chunk.

        Args:
            image_id (int): The unique identifier of the image.
            chunk_id (int): The unique identifier of the chunk.
        """

    @abstractmethod
    def get_images_by_chunk(self, chunk_id: int) -> List[ImageDTO]:
        """
        Retrieves all images associated with a specific chunk.

        Args:
            chunk_id (int): The unique identifier of the chunk.

        Returns:
            List[ImageDTO]: A list of image data transfer objects.
        """
