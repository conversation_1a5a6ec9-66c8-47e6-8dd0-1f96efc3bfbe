"""
This module defines the `IStorageClient` abstract base class, 
which serves as an interface for storage client implementations. 
The `IStorageClient` specifies the required methods for uploading, 
downloading, and deleting data, providing a consistent API for 
interacting with different storage backends.
"""

from abc import ABC, abstractmethod


class IStorageClient(ABC):
    """
    Abstract base class for storage client interfaces.

    The `IStorageClient` defines the methods that any storage
    client must implement to be compatible with the storage system.
    It provides a standard interface for uploading, downloading, and
    deleting data, abstracting away the specifics of the underlying
    storage mechanism.
    """

    @abstractmethod
    def upload(self, key: str, data: bytes) -> None:
        """
        Uploads data to the storage.

        Args:
            key (str): The storage key (path) where the data will be stored.
            data (bytes): The binary data to upload.

        Raises:
            NotImplementedError: This method should be overridden by subclasses.
        """

    @abstractmethod
    def download(self, key: str) -> bytes:
        """
        Downloads data from the storage.

        Args:
            key (str): The storage key (path) of the data to download.

        Returns:
            bytes: The binary data downloaded from the storage.

        Raises:
            NotImplementedError: This method should be overridden by subclasses.
        """

    @abstractmethod
    def delete(self, key: str) -> None:
        """
        Deletes data from the storage.

        Args:
            key (str): The storage key (path) of the data to delete.

        Raises:
            NotImplementedError: This method should be overridden by subclasses.
        """
