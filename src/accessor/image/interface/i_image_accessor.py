from abc import ABC, abstractmethod

from accessor.image.dto import ImageD<PERSON>
from accessor.image.dto.image_content_dto import ImageContentDTO


class IImageAccessor(ABC):
    """
    Interface for pure image access operations.
    """

    @abstractmethod
    def upload_image(self, file_content: bytes, file_name: str, metadata: dict) -> ImageDTO:
        """
        Uploads an image.
        """

    @abstractmethod
    def get_image_metadata(self, image_id: int) -> ImageDTO:
        """
        Retrieves metadata for a specific image.
        """

    @abstractmethod
    def get_image_content(self, image_id: int) -> ImageContentDTO:
        """
        Retrieves the content and metadata of a specific image.

        Args:
            image_id (int): The unique identifier of the image.

        Returns:
            ImageContentDTO: Data transfer object containing
            image content, file name, metadata, and size.
        """

    @abstractmethod
    def delete_image(self, image_id: int) -> None:
        """
        Deletes a specific image.
        """

    @abstractmethod
    def update_image_metadata(self, image_id: int, metadata: dict) -> ImageDTO:
        """
        Updates metadata for a specific image.
        """
