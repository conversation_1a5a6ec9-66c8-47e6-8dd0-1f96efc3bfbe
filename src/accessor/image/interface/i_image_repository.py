from abc import ABC, abstractmethod
from typing import List

from accessor.image.model.image_model import ImageModel


class IImageRepository(ABC):
    @abstractmethod
    def create_image(self, image_model: ImageModel) -> ImageModel:
        pass

    @abstractmethod
    def get_image_by_id(self, image_id: int) -> ImageModel:
        pass

    @abstractmethod
    def update_image(self, image_model: ImageModel) -> ImageModel:
        pass

    @abstractmethod
    def delete_image(self, image_id: int) -> None:
        pass

    @abstractmethod
    def associate_with_chunk(self, image_id: int, chunk_id: int) -> None:
        pass

    @abstractmethod
    def get_images_by_chunk(self, chunk_id: int) -> List[ImageModel]:
        pass
