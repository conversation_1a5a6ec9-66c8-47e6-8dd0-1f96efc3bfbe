"""
This module defines the `ImageContentDTO` data class, which encapsulates 
the content of an image along with its file name, metadata, and size. 
It serves as a Data Transfer Object for retrieving complete image 
information in a single method call.
"""

import datetime
from dataclasses import dataclass
from typing import Dict, Optional


@dataclass
class ImageContentDTO:
    """
    Data Transfer Object (DTO) for image content and metadata.

    Attributes:
        image_id (int): The unique identifier of the image.
        content (bytes): The binary content of the image.
        file_name (str): The original name of the image file.
        metadata (Optional[Dict]): Additional metadata associated with the image.
        size (int): The size of the image content in bytes.
        created_at (datetime): The timestamp when the image was created.
        updated_at (datetime): The timestamp when the image was last updated.
    """

    image_id: int
    content: bytes
    file_name: str
    metadata: Optional[Dict]
    created_at: datetime.datetime
    updated_at: datetime.datetime
