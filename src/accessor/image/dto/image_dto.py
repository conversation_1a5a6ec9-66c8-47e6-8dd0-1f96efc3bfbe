"""
This module defines the `ImageDTO` data class, which serves as a Data Transfer Object 
for image-related operations. The `ImageDTO` encapsulates essential image information, 
including identifiers, metadata, and timestamps, for transfer between different layers 
of the application without exposing internal implementation details.

Classes:
    ImageDTO: Represents an image with its associated metadata and timestamps.
"""

import datetime
from dataclasses import dataclass
from typing import Dict, Optional


@dataclass
class ImageDTO:
    """
    Data Transfer Object (DTO) for image-related operations.

    The `ImageDTO` class encapsulates image information that is transferred between different layers
    of the application.
    """

    image_id: int
    file_name: str
    metadata: Optional[Dict]
    created_at: datetime
    updated_at: datetime
