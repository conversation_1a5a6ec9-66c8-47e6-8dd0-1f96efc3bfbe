data = [
    "//Document/Table[5]/TR/TH/P:Term",
    "//Document/Table[5]/TR/TH[2]/P:Definition",
    "//Document/Table[5]/TR[2]/TD/P:servicing violation",
    "//Document/Table[5]/TR[2]/TD[2]/P:A breach of any servicer requirement or obligation contained in the Lender Contract related to servicing functions including, but not limited to",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI/Lbl:•",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI/LBody:processing of payments,",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[2]/Lbl:•",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[2]/LBody:collections,",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[3]/Lbl:•",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[3]/LBody:communications,",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[4]/Lbl:•",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[4]/LBody:loss mitigation,",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[5]/Lbl:•",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[5]/LBody:property preservation, and",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[6]/Lbl:•",
    "//Document/Table[5]/TR[2]/TD[2]/L/LI[6]/LBody:ensuring appropriate insurance is on the mortgage loan or property.",
    "//Document/Table[5]/TR[3]/TD/P:servicing defect",
    "//Document/Table[5]/TR[3]/TD[2]/P:A loan-level deﬁciency based on a servicing violation resulting from a breach of a term contained in the Lender Contract in eﬀect at the time of the servicing violation.",
    "//Document/Table[5]/TR[4]/TD/P:servicing correction",
    "//Document/Table[5]/TR[4]/TD[2]/P/Sub:An action taken by the seller/servicer that",
    "//Document/Table[5]/TR[4]/TD[2]/P/Sub[2]:demonstrates that the identified servicing defect either",
    "//Document/Table[5]/TR[4]/TD[2]/P[2]:(i) did not, in fact, exist, or (ii) has been corrected in the time frame specified by Fannie Mae, such that the servicing defect is no longer considered by Fannie Mae to be a servicing defect.",
    "//Document/Table[5]/TR[5]/TD/P:servicing remedy",
    "//Document/Table[5]/TR[5]/TD[2]/P:An action to resolve a servicing defect elected by Fannie Mae per the Lender Contract, which may be either a servicing alternative remedy or a repurchase.",
    "//Document/Table[5]/TR[6]/TD/P:servicing alternative remedy",
    "//Document/Table[5]/TR[6]/TD[2]/P:Remedies other than repurchase of the identified mortgage loan including, after foreclosure, the acquired property, that compensates Fannie Mae for damages, expenses and losses resulting from the identified servicing defect. The costs associated with calculating any servicing alternative remedy could include, but are not limited to",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI/LBody:a daily carrying cost that is not duplicative of any other cost or fee below;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[2]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[2]/LBody:property maintenance costs;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[3]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[3]/LBody:taxes;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[4]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[4]/LBody:insurance;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[5]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[5]/LBody:HOA/condo association fees;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[6]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[6]/LBody:appraisal/BPO fees;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[7]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[7]/LBody:legal fees and costs;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[8]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[8]/LBody:property inspection costs;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[9]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[9]/LBody:utility costs;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[10]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[10]/LBody:any documented property value decline, where appropriate;",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[11]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[11]/LBody:costs to repair; and",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[12]/Lbl:•",
    "//Document/Table[5]/TR[6]/TD[2]/L/LI[12]/LBody:outstanding fees/fines/liens.",
    "//Document/Table[5]/TR[7]/TD/P:servicing repurchase defect",
    "//Document/Table[5]/TR[7]/TD[2]/P:A servicing defect attributable to a servicing violation for which a demand for a repurchase servicing remedy could be issued without first issuing a notice of servicing defect or a demand for a servicing alternative remedy. Servicing repurchase defects shall be limited to servicing defects that",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI/Lbl:•",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI/LBody:cause Fannie Mae's lien, security interest or other property interest to be subordinated, extinguished or become inadequate for the realization against the related mortgaged premises for the benefit of the security;",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[2]/Lbl:•",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[2]/LBody:pose a significant reputational risk to Fannie Mae;",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[3]/Lbl:•",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[3]/LBody:result from the servicer modifying or applying a payment deferral to a mortgage loan that was sold to Fannie Mae with recourse or full indemnification in violation of Fannie Mae’s workout option eligibility requirements;",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[4]/Lbl:•",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[4]/LBody:result in the mortgage loan to not be, or continue to be, supported by Fannie Mae’s servicing systems;",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[5]/Lbl:•",
    "//Document/Table[5]/TR[7]/TD[2]/L/LI[5]/LBody:cause irreparable damage to the physical improvements to the property or render the property uninhabitable."
]

# data = []
# with open('tables-only.txt', 'r') as file:
#     for line in file:
#         data.append(line)

# Parse and organize data
headers = {}
cells = {}

# Step 1 - Parse out the headers and cells, giving each one a key
for entry in data:
    path, content = entry.split(":", 1) # Split on the first instance of :
    parts = path.split("/")
    table_row = parts[3]
    row_type = parts[4]
    cell_type = parts[5]
    row_index = int(parts[4].split("[")[-1][:-1]) if "[" in parts[4] else 1
    column_index = int(parts[5].split("[")[-1][:-1]) if "[" in parts[5] else 1  # Get the index within TR
    
    # Join all parts after parts[5] into a single string
    if len(parts) > 6:
        remaining_parts = "/".join(parts[6:])
    else:
        remaining_parts = ""  # No additional parts after parts[5]
    
    needs_to_be_combined_with_previous_content = remaining_parts != 'P'
    
    key = (table_row, row_index, column_index)
    
    if 'TR' in row_type and '[' not in row_type:
        headers[(table_row, row_index, column_index)] = content.strip()
    elif 'TD' in cell_type:
        if needs_to_be_combined_with_previous_content:
            if key in cells:
                cells[key] += " " + content.strip()
            else:
                cells[key] = content.strip()
        else:
            cells[key] = content.strip()

output = []
# Sort keys to ensure rows and columns are processed in order
sorted_keys = sorted(cells.keys(), key=lambda x: (int(x[0].split('[')[1][:-1]), int(x[1])))

concat_cells = {}

# Step 2 - Concatenate the cells with the appropriate header
for key in sorted_keys:
    header_key = (key[0], 1, key[2])  # Matching header index
    if header_key in headers:
        header = headers[header_key]
        cell = cells[key]
        
        # Ensure the key exists in concat_cells
        if (key[0], key[1]) not in concat_cells:
            concat_cells[(key[0], key[1])] = f"{header} - {cell}"
        else:
            concat_cells[(key[0], key[1])] += f": {header} - {cell}"

for key, value in concat_cells.items():
  print(value)
