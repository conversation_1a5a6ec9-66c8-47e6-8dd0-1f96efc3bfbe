"""
This module processes data and inserts it into a vector database index 
using LlamaIndex and Pinecone. 

Usage:
Run the module with appropriate command-line arguments to process documents and 
insert data into the vector database index.
"""

import logging
import os
import sys
import argparse
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader
from llama_index.core.node_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>litter
from llama_index.core.schema import MetadataMode
from llama_index.core.schema import TextNode
from llama_index.core.ingestion import IngestionPipeline
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.vector_stores.pinecone import PineconeVectorStore
from pinecone import Pinecone, ServerlessSpec
from typing import List
from accessor.content import content_accessor
from data_access import DataAccess
from engine.parsing.llama_index_extensions.parser.pdf_parser.pdf_node_parser import PDFNodeParser
from engine.parsing.llama_index_extensions.node_preprocessor import NodePreprocessor


OPEN_AI_MODEL_NAME = "gpt-4o-mini"
EMBEDDING_MODEL_NAME = "text-embedding-3-large"
DEFAULT_DOCUMENTS_PATH = "./data"
CHUNK_SIZE = 2000
CHUNK_OVERLAP = 200
MAX_TOKENS = 512
BATCH_SIZE = 50


def setup_logging(log_file="app.log"):
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    if root_logger.hasHandlers():
        root_logger.handlers.clear()
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.propagate = False


def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Processes data and inserts into vector database index."
    )
    parser.add_argument(
        "--documents_path",
        type=str,
        default=DEFAULT_DOCUMENTS_PATH,
        help="the path to the documents (default: ./data)",
        metavar="path_to_documents",
    )
    parser.add_argument(
        "--recursive", action="store_true", help="process directories recursively (default: False)"
    )
    parser.add_argument(
        "--index_name",
        type=str,
        help="the name of the index (lowercase, numbers, and hyphens only)",
    )
    parser.add_argument("--document_id", type=str, help="id number of the document in the database")
    parser.add_argument("--document_path", type=str, help="path to single document to load")
    parser.add_argument("--domain_name", type=str, required=True, help="the name of the domain")
    return parser.parse_args()


def main():
    setup_logging()
    args = parse_arguments()
    documents_path, recursive, index_name, document_id, document_path, domain_name = (
        args.documents_path,
        args.recursive,
        args.index_name,
        args.document_id,
        args.document_path,
        args.domain_name,
    )
    logging.info("Starting processing with Llama Index for document %s.", document_id)
    llm = OpenAI(temperature=0.1, model=OPEN_AI_MODEL_NAME, max_tokens=MAX_TOKENS)
    pinecone_index = initialize_vector_index(index_name)
    if args.document_path is None:
        splitter = SentenceSplitter(chunk_size=CHUNK_SIZE, chunk_overlap=CHUNK_OVERLAP)
        logging.info("Loading documents.")
        documents = SimpleDirectoryReader(documents_path, recursive=recursive).load_data()
        logging.info("Splitting documents.")
        nodes = splitter.get_nodes_from_documents(documents)
    else:
        logging.info("Opening file %s.", document_path)
        with open(document_path, "rb") as file:
            pdf_bytes = file.read()
        logging.info("Calling PDFNodeParser.")
        parser = PDFNodeParser()
        document_name = os.path.basename(document_path)
        nodes = parser._parse_nodes(pdf_bytes, document_name, domain_name, 1, True)
        logging.info("Preprocessing nodes.")
        node_preprocessor = NodePreprocessor()
        nodes = node_preprocessor.preprocess_nodes(nodes)
    nodes = run_vector_pipeline(pinecone_index, nodes, index_name, document_id, llm)
    persist_to_rdb(document_id, nodes)
    logging.info("Completed processing document %s with Llama Index.", document_id)


def initialize_vector_index(index_name):
    logging.info("Initializing vector database index.")
    pc = Pinecone(os.environ["PINECONE_API_KEY"])
    index_list_response = pc.list_indexes()
    index_names = [index_info["name"] for index_info in index_list_response.get("indexes", [])]
    if index_name not in index_names:
        logging.info("Creating new vector database index: %s.", index_name)
        pc.create_index(
            name=index_name,
            dimension=3072,
            metric="cosine",
            spec=ServerlessSpec(cloud="aws", region="us-west-2"),
        )
    pinecone_index = pc.Index(index_name)
    logging.info("Vector database index initialized.")
    return pinecone_index


def persist_to_rdb(document_id: str, nodes: List[TextNode]):
    logging.info("Persisting to RDB.")
    data_access = DataAccess()
    for node in nodes:
        metadata = node.metadata
        content_accessor.save_chunk(document_id, node.text, node.text, node.node_id, metadata)


def run_vector_pipeline(pinecone_index, nodes, index_name, document_id, llm):
    logging.info("Building the vector store pipeline.")
    vector_store = PineconeVectorStore(pinecone_index=pinecone_index, batch_size=BATCH_SIZE)
    pipeline = IngestionPipeline(
        transformations=[OpenAIEmbedding(model=EMBEDDING_MODEL_NAME)], vector_store=vector_store
    )
    logging.info("Running the vector store pipeline.")
    nodes = pipeline.run(nodes=nodes, show_progress=True)
    try:
        storage_index = VectorStoreIndex(nodes)
        storage_index.storage_context.persist(
            persist_dir=f"./storage/{index_name}/{document_id}/preprocessed"
        )
    except Exception as e:
        logging.error("Error saving nodes to disk: %s", e, exc_info=True)
    return nodes


if __name__ == "__main__":
    main()
