# from api.models.domain import Domain
from shared.models.domain_comparison import DomainComparison
from shared.models.test_case import TestCase

from util.database import Session


class DataAccess:
    def __init__(self):
        self.session = Session

    def clean_text(self, text):
        return text.replace("\u0000", "")

    def upsert_domain_comparison(
        self,
        domain_1_id: int,
        domain_2_id: int,
        subtopic_id: int,
        statement_type_id: int,
        comparison_level_id: int,
        domain_1_exclusive_summary: str,
        domain_1_exclusive_detail: str,
        domain_2_exclusive_summary: str,
        domain_2_exclusive_detail: str,
        similarities_summary: str,
        similarities_detail: str,
        similarities_with_material_differences_summary: str,
        similarities_with_material_differences_detail: str,
    ):
        session = self.Session()
        try:
            domain_comparison = (
                session.query(DomainComparison)
                .filter_by(
                    domain_1_id=domain_1_id,
                    domain_2_id=domain_2_id,
                    subtopic_id=subtopic_id,
                    statement_type_id=statement_type_id,
                    comparison_level_id=comparison_level_id,
                )
                .first()
            )

            if domain_comparison:
                # Update existing record
                domain_comparison.domain_1_exclusive_summary = domain_1_exclusive_summary
                domain_comparison.domain_1_exclusive_detail = domain_1_exclusive_detail
                domain_comparison.domain_2_exclusive_summary = domain_2_exclusive_summary
                domain_comparison.domain_2_exclusive_detail = domain_2_exclusive_detail
                domain_comparison.similarities_summary = similarities_summary
                domain_comparison.similarities_detail = similarities_detail
                domain_comparison.similarities_with_material_differences_summary = (
                    similarities_with_material_differences_summary
                )
                domain_comparison.similarities_with_material_differences_detail = (
                    similarities_with_material_differences_detail
                )
            else:
                # Insert new record
                domain_comparison = DomainComparison(
                    domain_1_id=domain_1_id,
                    domain_2_id=domain_2_id,
                    subtopic_id=subtopic_id,
                    statement_type_id=statement_type_id,
                    comparison_level_id=comparison_level_id,
                    domain_1_exclusive_summary=domain_1_exclusive_summary,
                    domain_1_exclusive_detail=domain_1_exclusive_detail,
                    domain_2_exclusive_summary=domain_2_exclusive_summary,
                    domain_2_exclusive_detail=domain_2_exclusive_detail,
                    similarities_summary=similarities_summary,
                    similarities_detail=similarities_detail,
                    similarities_with_material_differences_summary=similarities_with_material_differences_summary,
                    similarities_with_material_differences_detail=similarities_with_material_differences_detail,
                )
                session.add(domain_comparison)

            session.commit()
            session.refresh(domain_comparison)
            return domain_comparison
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()

    def bulk_insert_test_cases(self, acceptance_criteria_id: int, test_case_descriptions: list):
        session = self.Session()
        try:
            test_cases = [
                TestCase(acceptance_criteria_id=acceptance_criteria_id, description=description)
                for i, description in enumerate(test_case_descriptions)
            ]
            session.bulk_save_objects(test_cases)
            session.commit()
        except Exception as e:
            session.rollback()
            print(f"Error during bulk insert: {e}")
        finally:
            session.close()
