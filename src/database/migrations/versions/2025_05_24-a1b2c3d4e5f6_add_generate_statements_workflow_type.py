"""Add GENERATE_STATEMENTS workflow type.

Revision ID: 2025_05_24
Revises: 2025_05_23-f234fd4da1ed
Create Date: 2025-05-24 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = 'f234fd4da1ed'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("INSERT INTO public.workflow_type (workflow_type_id, name) VALUES (8, 'GENERATE_STATEMENTS');")


def downgrade():
    op.execute("DELETE FROM public.workflow_type WHERE workflow_type_id = 8;") 