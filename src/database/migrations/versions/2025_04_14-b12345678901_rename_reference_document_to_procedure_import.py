"""Rename reference document to procedure import

Revision ID: b12345678901
Revises: a55da6654150
Create Date: 2025-04-14 10:00:00.000000

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b12345678901"
down_revision: Union[str, None] = "a55da6654150"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Update job_type table to rename "reference document" to "procedure import"
    op.execute(
        """
        UPDATE job_type
        SET name = 'procedure import'
        WHERE job_type_id = 3 AND name = 'reference document'
        """
    )


def downgrade() -> None:
    # Revert job_type table changes
    op.execute(
        """
        UPDATE job_type
        SET name = 'reference document'
        WHERE job_type_id = 3 AND name = 'procedure import'
        """
    )
