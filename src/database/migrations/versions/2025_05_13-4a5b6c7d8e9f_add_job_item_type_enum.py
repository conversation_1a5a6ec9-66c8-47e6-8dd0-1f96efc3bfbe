"""add job_item_type enum

Revision ID: 4a5b6c7d8e9f
Revises: 3b1984b60d09
Create Date: 2024-03-19

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '4a5b6c7d8e9f'
down_revision = '3b1984b60d09'
branch_labels = None
depends_on = None

def upgrade():
    # Check if enum exists before creating it
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    enums = inspector.get_enums()
    
    if not any(enum['name'] == 'job_item_type' for enum in enums):
        # Create the enum type
        job_item_type = postgresql.ENUM('CHUNK', name='job_item_type')
        job_item_type.create(connection)

def downgrade():
    # Drop the enum type
    job_item_type = postgresql.ENUM(name='job_item_type')
    job_item_type.drop(op.get_bind()) 