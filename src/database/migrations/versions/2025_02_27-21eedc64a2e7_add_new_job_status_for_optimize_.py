"""Add new job_status for 'Optimize Procedure'

Revision ID: 21eedc64a2e7
Revises: 7b5cee25253e
Create Date: 2025-02-24 08:17:09.035701

"""

from typing import Sequence, Union
from alembic import op
from sqlalchemy.sql import text

# Revision identifiers, used by Alembic
revision = "21eedc64a2e7"
down_revision = "7b5cee25253e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # Insert a new row if it doesn't already exist
    conn = op.get_bind()
    conn.execute(
        text(
            """
        INSERT INTO job_status (name, description, state)
        SELECT 'Optimizing Procedure', 'Optimizing your procedure', 'Chunking'
        WHERE NOT EXISTS (
            SELECT 1 FROM job_status WHERE name = 'Optimizing Procedure'
        );
    """
        )
    )


def downgrade():
    # Remove the row if it exists
    conn = op.get_bind()
    conn.execute(
        text(
            """
        DELETE FROM job_status WHERE name = 'Optimizing Procedure';
    """
        )
    )
