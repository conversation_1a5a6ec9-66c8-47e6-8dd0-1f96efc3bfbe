"""Add enterprise_id to domain

Revision ID: 3b1984b60d09
Revises: 10d752952b1a
Create Date: 2025-05-12 14:03:25.235727

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "3b1984b60d09"
down_revision: Union[str, None] = "10d752952b1a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add enterprise_id column to domain table
    op.add_column(
        "domain",
        sa.Column(
            "enterprise_id",
            sa.Integer(),
            nullable=True,
        ),
    )

    # Add foreign key constraint
    op.create_foreign_key(
        "fk_domain_enterprise",
        "domain",
        "enterprise",
        ["enterprise_id"],
        ["enterprise_id"],
        ondelete="CASCADE",
    )

    # Add unique constraint on enterprise_id
    op.create_unique_constraint("uq_domain_enterprise_id", "domain", ["enterprise_id"])

    # Insert or update the Phoenix domain
    op.execute(
        """
        WITH phoenix_enterprise AS (
            SELECT enterprise_id FROM enterprise WHERE name = 'PhoenixTeam Mortgage'
        )
        UPDATE domain 
        SET enterprise_id = (SELECT enterprise_id FROM phoenix_enterprise)
        WHERE name = 'Phoenix';
        
        INSERT INTO domain (name, display_name, vector_database_name, is_active, enterprise_id)
        SELECT 'Phoenix', 'Phoenix', 'dev-phoenix', TRUE, (SELECT enterprise_id FROM enterprise WHERE name = 'PhoenixTeam Mortgage')
        WHERE NOT EXISTS (
            SELECT 1 FROM domain WHERE name = 'Phoenix'
        );
        """
    )


def downgrade() -> None:
    # Unlink the Phoenix domain from the enterprise
    op.execute(
        "UPDATE domain SET enterprise_id = NULL WHERE name = 'Phoenix' AND enterprise_id = 2"
    )

    # Remove unique constraint on enterprise_id
    op.drop_constraint("uq_domain_enterprise_id", "domain", type_="unique")

    # Remove foreign key constraint
    op.drop_constraint("fk_domain_enterprise", "domain", type_="foreignkey")

    # Remove enterprise_id column
    op.drop_column("domain", "enterprise_id")
