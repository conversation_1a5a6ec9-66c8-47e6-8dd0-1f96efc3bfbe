"""Added Katie automation user

Revision ID: 36e7cd7880ad
Revises: 10d752952b1a
Create Date: 2025-05-02 12:39:54.211073

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "36e7cd7880ad"
down_revision: Union[str, None] = "834cc146fd76"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Katie', 'Automated', '<EMAIL>', 2, 'auth0|680bb6be6b883c49d0e6b134')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
        DELETE FROM public.user
        WHERE email_address = '<EMAIL>'
        """
    )
    # ### end Alembic commands ###
