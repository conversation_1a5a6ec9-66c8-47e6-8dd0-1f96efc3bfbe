"""<PERSON><PERSON> heads ba49f157fd5d and b12345678901

Revision ID: 4e8f61625bcd
Revises: ba49f157fd5d, b12345678901
Create Date: 2025-04-15 10:47:05.477095

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "4e8f61625bcd"
down_revision: Union[str, None] = ("ba49f157fd5d", "b12345678901")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
