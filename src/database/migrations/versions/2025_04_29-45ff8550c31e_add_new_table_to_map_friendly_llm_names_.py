"""Add new table to map friendly LLM names to their longer strings that the providers expect

Revision ID: 45ff8550c31e
Revises: c32aafc043b7
Create Date: 2025-04-29 13:10:12.160663

"""

from typing import Sequence, Union
import os
import sys

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "45ff8550c31e"
down_revision: Union[str, None] = "c32aafc043b7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "ai_model_mapping",
        sa.Column("friendly_name", sa.Text(), nullable=False),
        sa.Column("provider_model_name", sa.Text(), nullable=False),
        sa.PrimaryKeyConstraint("friendly_name"),
    )
    op.create_index(
        "unique_friendly_name_lower",
        "ai_model_mapping",
        [sa.text("lower(friendly_name)")],
        unique=True,
        postgresql_using="btree",
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop index if it exists
    op.execute(
        """
        DO $$
        BEGIN
            IF EXISTS (
                SELECT 1 FROM pg_indexes 
                WHERE tablename = 'ai_model_mapping' AND indexname = 'unique_friendly_name_lower'
            ) THEN
                DROP INDEX IF EXISTS "unique_friendly_name_lower";
            END IF;
        END$$;
    """
    )

    # Drop table if it exists
    op.execute(
        """
        DROP TABLE IF EXISTS ai_model_mapping;
    """
    )
    # ### end Alembic commands ###
