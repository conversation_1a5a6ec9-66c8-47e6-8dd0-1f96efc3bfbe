"""Add department and schema data objects

Revision ID: d958e4c587c0
Revises: 21eedc64a2e7
Create Date: 2025-03-04 13:46:35.756564

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "d958e4c587c0"
down_revision: Union[str, None] = "21eedc64a2e7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "department",
        sa.Column("department_id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("business_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=False,
            comment="Record creation time",
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=True,
            comment="Record update time",
        ),
        sa.ForeignKeyConstraint(["business_id"], ["business.business_id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("department_id"),
        sa.UniqueConstraint("business_id"),
    )
    op.create_table(
        "persona",
        sa.Column("persona_id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("department_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.Text(), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=False,
            comment="Record creation time",
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=True,
            comment="Record update time",
        ),
        sa.ForeignKeyConstraint(
            ["department_id"], ["department.department_id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("persona_id"),
        sa.UniqueConstraint("department_id"),
    )
    op.create_table(
        "department_subtopic_association",
        sa.Column("department_id", sa.Integer(), nullable=False),
        sa.Column("subtopic_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["department_id"],
            ["department.department_id"],
        ),
        sa.ForeignKeyConstraint(
            ["subtopic_id"],
            ["subtopic.subtopic_id"],
        ),
        sa.PrimaryKeyConstraint("department_id", "subtopic_id"),
    )
    # Insert a new job type into the existing table
    op.execute("INSERT INTO job_type (name, description) VALUES ('optimize procedure', 'null')")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DELETE FROM job_type WHERE name = 'optimize procedure'")

    op.drop_table("department_subtopic_association")
    op.drop_table("persona")
    op.drop_table("department")
    # ### end Alembic commands ###
