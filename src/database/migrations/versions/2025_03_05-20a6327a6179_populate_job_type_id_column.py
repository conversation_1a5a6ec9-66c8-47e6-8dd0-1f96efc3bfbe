"""Populate job_type_id Column and Remove job_type Column

Revision ID: 20a6327a6179
Revises: d958e4c587c0
Create Date: 2025-03-05 11:51:26.984435

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "20a6327a6179"
down_revision: Union[str, None] = "d958e4c587c0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Update job_type_id in job table using the existing job_type column
    op.execute(
        """
        UPDATE job
        SET job_type_id = (
            SELECT job_type_id FROM job_type WHERE job_type.name = job.job_type::VARCHAR
        )
        """
    )

def downgrade() -> None:
    # Recreate job_type_enum type (if it needs to be restored)
    op.execute(
        "CREATE TYPE job_type_enum AS ENUM ('process document', 'domain comparison', 'reference document', 'artifact generation', 'markup change identification', 'policy requirement comparison', 'federal register change identification', 'optimize procedure')"
    )

    # Add back job_type column to job table
    op.add_column(
        "job",
        sa.Column(
            "job_type",
            sa.Enum(
                "process document",
                "domain comparison",
                "reference document",
                "artifact generation",
                "markup change identification",
                "policy requirement comparison",
                "federal register change identification",
                "optimize procedure",
                name="job_type_enum",
            ),
            nullable=True,
        ),
    )

    # Restore job_type values from job_type_id
    op.execute(
        """
        UPDATE job
        SET job_type = (
            SELECT name FROM job_type WHERE job_type.job_type_id = job.job_type_id
        )
        """
    )
