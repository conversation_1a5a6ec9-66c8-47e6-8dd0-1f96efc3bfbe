"""add last_error column to job_item_workflow table

Revision ID: add_last_error_jiw
Revises: d6a57d6253a2
Create Date: 2025-05-29

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "add_last_error_jiw"
down_revision: Union[str, None] = "d6a57d6253a2"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add the last_error column to job_item_workflow table
    op.add_column('job_item_workflow', sa.Column('last_error', sa.Text(), nullable=True))


def downgrade() -> None:
    # Remove the last_error column from job_item_workflow table
    op.drop_column('job_item_workflow', 'last_error')
