"""Rename process change to procedure change

Revision ID: a55da6654150
Revises: 20a6327a6179
Create Date: 2025-04-02 12:27:59.958814

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a55da6654150'
down_revision: Union[str, None] = '20a6327a6179'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Update statement_type.name from 'process change' to 'procedure change'
    op.execute(
        """
        UPDATE statement_type
        SET name = 'procedure change'
        WHERE statement_type_id = 11 AND name = 'process change'
        """
    )

    # Remove the job_type column from the job table
    op.drop_column('job', 'job_type')

    # Drop the job_type_enum enum type
    op.execute('DROP TYPE IF EXISTS job_type_enum')


def downgrade() -> None:
    # Revert statement_type.name from 'procedure change' back to 'process change'
    op.execute(
        """
        UPDATE statement_type
        SET name = 'process change'
        WHERE statement_type_id = 11 AND name = 'procedure change'
        """
    )

    # Add the job_type_enum type back (assuming you want to re-add the enum)
    op.execute(
        """
        CREATE TYPE job_type_enum AS ENUM ('process document', 'domain comparison', 'reference document', 'artifact generation', 'markup change identification', 'policy requirement comparison', 'federal register change identification', 'optimize procedure')
        """
    )

    # Add the job_type column back to the job table with the job_type_enum
    op.add_column(
        'job',
        sa.Column(
            'job_type',
            sa.Enum(
                'process document', 
                'domain comparison', 
                'reference document', 
                'artifact generation', 
                'markup change identification', 
                'policy requirement comparison', 
                'federal register change identification', 
                'optimize procedure', 
                name='job_type_enum'
            ),
            nullable=True
        )
    )
