"""Rename procedure change to procedure impact and add parent_requirement_id

Revision ID: f234fd4da1ed
Revises: 3b1984b60d09
Create Date: 2025-05-23 13:34:22.081310

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "f234fd4da1ed"
down_revision: Union[str, None] = "3b1984b60d09"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Update statement_type.name from 'procedure change' to 'procedure impact'
    op.execute(
        """
        UPDATE statement_type
        SET name = 'procedure impact'
        WHERE statement_type_id = 11 AND name = 'procedure change'
        """
    )

    # Add parent_requirement_id column to unique_statement table
    op.add_column(
        "unique_statement", sa.Column("parent_requirement_id", sa.Integer(), nullable=True)
    )

    # Add foreign key constraint to self-reference the unique_statement table
    op.create_foreign_key(
        "fk_unique_statement_parent",
        "unique_statement",
        "unique_statement",
        ["parent_requirement_id"],
        ["unique_statement_id"],
        ondelete="SET NULL",
    )


def downgrade() -> None:
    # Remove foreign key constraint
    op.drop_constraint("fk_unique_statement_parent", "unique_statement", type_="foreignkey")

    # Remove parent_requirement_id column
    op.drop_column("unique_statement", "parent_requirement_id")

    # Revert statement_type.name from 'procedure impact' back to 'procedure change'
    op.execute(
        """
        UPDATE statement_type
        SET name = 'procedure change'
        WHERE statement_type_id = 11 AND name = 'procedure impact'
        """
    )
