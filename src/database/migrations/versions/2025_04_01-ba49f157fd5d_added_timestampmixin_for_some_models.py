"""Added TimestampMixin for some models

Revision ID: ba49f157fd5d
Revises: 20a6327a6179
Create Date: 2025-04-01 19:55:14.197109

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "ba49f157fd5d"
down_revision: Union[str, None] = "20a6327a6179"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "document",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "document",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "subtopic",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "subtopic",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "topic",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "topic",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "topic",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        comment=None,
        existing_comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "topic",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment=None,
        existing_comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "subtopic",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        comment=None,
        existing_comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "subtopic",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment=None,
        existing_comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "document",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        comment=None,
        existing_comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "document",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment=None,
        existing_comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    # ### end Alembic commands ###
