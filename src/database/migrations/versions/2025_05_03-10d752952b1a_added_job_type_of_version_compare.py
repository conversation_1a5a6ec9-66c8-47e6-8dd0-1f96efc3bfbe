"""Added job_type of 'version comparison'

Revision ID: 10d752952b1a
Revises: 834cc146fd76
Create Date: 2025-05-01 14:56:57.224050

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "10d752952b1a"
down_revision: Union[str, None] = "36e7cd7880ad"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
        INSERT INTO job_type (name)
        VALUES ('version comparison')
        ON CONFLICT (name) DO NOTHING;
    """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
        DELETE FROM job_type
        WHERE name = 'version comparison';
    """
    )
    # ### end Alembic commands ###
