"""add job_item_workflow table

Revision ID: 5f6e7d8c9b0a
Revises: 4a5b6c7d8e9f
Create Date: 2024-03-19

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5f6e7d8c9b0a'
down_revision = '4a5b6c7d8e9f'
branch_labels = None
depends_on = None

def upgrade():
    # Create the table
    op.create_table(
        'job_item_workflow',
        sa.Column('job_item_workflow_id', sa.Integer(), nullable=False),
        sa.Column('job_workflow_id', sa.Integer(), nullable=False),
        sa.Column('item_key', sa.String(), nullable=False),
        sa.Column('item_type', postgresql.ENUM('CHUNK', name='job_item_type', create_type=False), nullable=False, server_default='CHUNK'),
        sa.Column('status', postgresql.ENUM('STARTING', name='job_workflow_status', create_type=False), nullable=False, server_default='STARTING'),
        sa.Column('run_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['job_workflow_id'], ['public.job_workflow.job_workflow_id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('job_item_workflow_id'),
        schema='public'
    )
    
    # Create the index
    op.create_index(
        'idx_job_item_workflow__job_workflow_id',
        'job_item_workflow',
        ['job_workflow_id'],
        schema='public'
    )

def downgrade():
    # Drop the index
    op.drop_index('idx_job_item_workflow__job_workflow_id', table_name='job_item_workflow', schema='public')
    
    # Drop the table
    op.drop_table('job_item_workflow', schema='public') 