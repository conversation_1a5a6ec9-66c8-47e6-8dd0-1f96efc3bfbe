"""Added statement_id to unique_statement

Revision ID: c32aafc043b7
Revises: 75e9a50a1e0a
Create Date: 2025-04-28 12:11:53.919016

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "c32aafc043b7"
down_revision: Union[str, None] = "75e9a50a1e0a"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("unique_statement", sa.Column("statement_id", sa.Integer(), nullable=True))
    op.alter_column(
        "unique_statement",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "unique_statement",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.create_foreign_key(
        "fk_unique_statement_statement",
        "unique_statement",
        "statement",
        ["statement_id"],
        ["statement_id"],
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_constraint("fk_unique_statement_statement", "unique_statement", type_="foreignkey")
    op.alter_column(
        "unique_statement",
        "updated_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        comment=None,
        existing_comment="Record update time",
        existing_server_default=sa.text("now()"),
    )
    op.alter_column(
        "unique_statement",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        comment=None,
        existing_comment="Record creation time",
        existing_nullable=False,
        existing_server_default=sa.text("now()"),
    )
    op.drop_column("unique_statement", "statement_id")
    # ### end Alembic commands ###
