"""Add new document_statistics table

Revision ID: deba5660e31e
Revises:
Create Date: 2025-02-20 14:14:01.522648

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "deba5660e31e"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Alembic doesn't yet generate the correct code for the Sequence object
    # so we need to manually add the Sequence object to the upgrade function
    # and remove it from the downgrade function
    op.execute(
        "CREATE SEQUENCE document_statistics_document_statistics_id_seq START 1"
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "document_statistics",
        sa.Column(
            "document_statistics_id",
            sa.Integer(),
            server_default=sa.text(
                "nextval('document_statistics_document_statistics_id_seq')"
            ),
            nullable=False,
        ),
        sa.Column("document_id", sa.Integer(), nullable=False),
        sa.Column("word_count", sa.Integer(), nullable=False),
        sa.Column("difficult_word_count", sa.Integer(), nullable=False),
        sa.Column("average_sentence_length", sa.Float(), nullable=False),
        sa.Column("average_word_length", sa.Float(), nullable=False),
        sa.Column("flesch_grade_level", sa.Integer(), nullable=False),
        sa.Column("flesch_score", sa.Integer(), nullable=False),
        sa.Column("flesch_reading_ease", sa.Text(), nullable=False),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=False,
            comment="Record creation time",
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=True,
            comment="Record update time",
        ),
        sa.ForeignKeyConstraint(
            ["document_id"], ["document.document_id"], ondelete="CASCADE"
        ),
        sa.UniqueConstraint("document_id"),
        sa.PrimaryKeyConstraint("document_statistics_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("document_statistics")
    op.execute("DROP SEQUENCE document_statistics_document_statistics_id_seq")
    # ### end Alembic commands ###
