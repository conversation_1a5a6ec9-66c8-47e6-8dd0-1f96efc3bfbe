"""Add a self-referential foreign key to link a doc to another original doc

Revision ID: 7b5cee25253e
Revises: deba5660e31e
Create Date: 2025-02-24 14:21:45.466777

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "7b5cee25253e"
down_revision: Union[str, None] = "deba5660e31e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "document", sa.Column("original_document_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        "document_original_document_fkey",
        "document",
        "document",
        ["original_document_id"],
        ["document_id"],
        onupdate="NO ACTION",
        ondelete="NO ACTION",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("document_original_document_fkey", "document", type_="foreignkey")
    op.drop_column("document", "original_document_id")
    # ### end Alembic commands ###
