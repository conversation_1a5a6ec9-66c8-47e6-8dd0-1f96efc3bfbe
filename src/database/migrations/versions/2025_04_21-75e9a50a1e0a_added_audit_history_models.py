"""Added audit activity models

Revision ID: 75e9a50a1e0a
Revises: 4e8f61625bcd
Create Date: 2025-04-15 13:41:06.629394

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "75e9a50a1e0a"
down_revision: Union[str, None] = "4e8f61625bcd"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # ### Creation of tables needed for audit activity
    # Creation of audit_object_type table
    op.create_table(
        "audit_object_type",
        sa.Column("audit_object_type_id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint("audit_object_type_id"),
        sa.UniqueConstraint("name"),
    )
    # Creation of audit_operation_type table
    op.create_table(
        "audit_operation_type",
        sa.Column("audit_operation_type_id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.PrimaryKeyConstraint("audit_operation_type_id"),
        sa.UniqueConstraint("name"),
    )
    # Creation of the audit_activity PK sequence
    op.execute(sa.text("CREATE SEQUENCE audit_activity_id_seq"))
    # Creation of the audit_activity table
    op.create_table(
        "audit_activity",
        sa.Column(
            "audit_activity_id",
            sa.Integer(),
            server_default=sa.text("nextval('audit_activity_id_seq')"),
            nullable=False,
        ),
        sa.Column("audit_object_type_id", sa.Integer(), nullable=False),
        sa.Column("object_key", sa.Integer(), nullable=False),
        sa.Column("data_snapshot", sa.JSON(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("audit_operation_type_id", sa.Integer(), nullable=False),
        sa.Column(
            "recorded_at",
            sa.TIMESTAMP(),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["audit_object_type_id"],
            ["audit_object_type.audit_object_type_id"],
        ),
        sa.ForeignKeyConstraint(
            ["audit_operation_type_id"],
            ["audit_operation_type.audit_operation_type_id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["user.user_id"], ondelete="SET NULL"),
        sa.PrimaryKeyConstraint("audit_activity_id"),
    )
    op.create_index(
        "idx_audit_activity_audit_object_type_id_object_key",
        "audit_activity",
        ["audit_object_type_id", "object_key"],
        unique=False,
    )

    # ### Insert seed data into audit_object_type ###
    op.bulk_insert(
        sa.table(
            "audit_object_type",
            sa.column("name", sa.String),
            sa.column("description", sa.Text),
        ),
        [
            {"name": "unique_statement", "description": "Unique Statement object"},
            {"name": "statement", "description": "Statement object"},
            {"name": "user_story", "description": "User Story object"},
            {"name": "acceptance_criteria", "description": "Acceptance Criteria object"},
            {"name": "test_case", "description": "Test Case object"},
            {
                "name": "unique_statement_subtopic_association",
                "description": "Unique Statement Subtopic Association object",
            },
        ],
    )

    # ### Insert seed data into audit_operation_type ###
    op.bulk_insert(
        sa.table(
            "audit_operation_type",
            sa.column("name", sa.String),
            sa.column("description", sa.Text),
        ),
        [
            {"name": "create", "description": "Creation of the object"},
            {"name": "update", "description": "Modification of the object"},
            {"name": "delete", "description": "Deletion of the object"},
        ],
    )

    # ### Add auth0_id column to user table ###
    op.add_column("user", sa.Column("auth0_id", sa.Text(), nullable=True))

    # ### User cleanup ###
    # Delete users no longer involved with Burst
    op.execute(
        """
        DELETE FROM public.user
        WHERE email_address IN (
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        )
    """
    )

    op.create_unique_constraint("uq_user_auth0_id", "user", ["auth0_id"])
    op.create_unique_constraint("uq_user_email_address", "user", ["email_address"])

    # Create <NAME_EMAIL> user
    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id)
        VALUES
        ('Trava Vulnerability Scanning', 'User', '<EMAIL>', 2)
        ON CONFLICT (email_address)
        DO UPDATE SET
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            enterprise_id = EXCLUDED.enterprise_id
        """
    )

    # Create <NAME_EMAIL> user
    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id)
        VALUES
        ('Burst System', 'User', '<EMAIL>', 2)
        ON CONFLICT (email_address)
        DO UPDATE SET
            first_name = EXCLUDED.first_name,
            last_name = EXCLUDED.last_name,
            enterprise_id = EXCLUDED.enterprise_id
        """
    )

    # Add auth0_id data to users
    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Noah', 'Krueger', '<EMAIL>', 2, 'auth0|67bca8b99a5c5443d7a075f7')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Lauren', 'Guse', '<EMAIL>', 2, 'auth0|67bce6667c361c1ddb7d016d')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Carlos', 'Bennazar', '<EMAIL>', 2, 'auth0|67bccf112d399849ba41df03')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Trava Vulnerability Scanning', '', '<EMAIL>', 2, 'auth0|67ec000072c3630f10665c57')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Marie', 'Pitts', '<EMAIL>', 2, 'auth0|67ed68bcd93f7e2903b6e532')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Nate', 'Pierce', '<EMAIL>', 2, 'auth0|67bcceb6dc04738cd9ad22bd')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Kenny', 'Akridge', '<EMAIL>', 2, 'auth0|67bccef62313b766092853d2')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Kenny', 'Akridge', '<EMAIL>', 2, 'auth0|67bccef62313b766092853d2')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Thomas', 'Loth', '<EMAIL>', 2, 'auth0|67bce69b5dba52c290390099')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Sara', 'Ciffa', '<EMAIL>', 2, 'auth0|67bccf231c9d249d90f5032d')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES
        ('Vicki', 'Withrow', '<EMAIL>', 2, 'auth0|67ed97daa272001d4025d340')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES ('Katie','Brewster','<EMAIL>', 2, 'auth0|67ed640b528770478420ea6b')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES ('Matias','Ellera','<EMAIL>', 2, 'auth0|67b8ec2d8922efcdfc37b40e')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    op.execute(
        """
        INSERT INTO public.user (first_name, last_name, email_address, enterprise_id, auth0_id)
        VALUES ('Jeremy','Romano','<EMAIL>', 2, 'auth0|67bccee45dba52c29038f13c')
        ON CONFLICT (email_address)
        DO UPDATE SET
            auth0_id = EXCLUDED.auth0_id
        """
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_audit_activity_audit_object_type_id_object_key", table_name="audit_activity")
    op.drop_table("audit_activity")
    # Drop sequence after table drop
    op.execute(sa.text("DROP SEQUENCE IF EXISTS audit_activity_id_seq"))

    # Delete inserted values before dropping tables
    op.execute("DELETE FROM audit_operation_type")
    op.execute("DELETE FROM audit_object_type")

    op.drop_table("audit_operation_type")
    op.drop_table("audit_object_type")

    op.drop_constraint("uq_user_auth0_id", "user", type_="unique")
    op.drop_constraint("uq_user_email_address", "user", type_="unique")

    op.drop_column("user", "auth0_id")

    # ### end Alembic commands ###
