"""merge job_item_workflow and generate_statements

Revision ID: d6a57d6253a2
Revises: 5f6e7d8c9b0a, a1b2c3d4e5f6
Create Date: 2025-05-29 10:38:45.359859

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "d6a57d6253a2"
down_revision: Union[str, None] = ("5f6e7d8c9b0a", "a1b2c3d4e5f6")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass
