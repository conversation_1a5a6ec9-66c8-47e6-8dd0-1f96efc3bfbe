"""add_more_model_mappings

Revision ID: 834cc146fd76
Revises: 20250501_seed_ai_model_mappings
Create Date: 2025-05-01 09:33:48.236376

"""

import csv
from typing import Sequence, Union
import os

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import insert as pg_insert


# revision identifiers, used by Alembic.
revision: str = "834cc146fd76"
down_revision: Union[str, None] = "20250501_seed_ai_model_mappings"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # locate the CSV we shipped with this revision
    data_path = os.path.join(os.path.dirname(__file__), 'ai_model_mappings.csv')
    with open(data_path, newline='') as f:
        reader = csv.DictReader(f)
        rows = [ {'friendly_name': r['friendly_name'], 'provider_model_name': r['provider_model_name']} 
                 for r in reader ]

    # build a lightweight Table object for core insert
    model_map = sa.table(
        'ai_model_mapping',
        sa.column('friendly_name', sa.Text),
        sa.column('provider_model_name', sa.Text),
    )

    # on_conflict_do_nothing is PG-only; skip if empty
    if rows:
        stmt = pg_insert(model_map).values(rows)
        stmt = stmt.on_conflict_do_nothing(index_elements=['friendly_name'])
        op.get_bind().execute(stmt)


def downgrade() -> None:
    pass
