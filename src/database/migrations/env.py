import os
import sys
import re
from pathlib import Path
from alembic import context
from sqlalchemy import engine_from_config, pool
from logging.config import fileConfig

# Add 'src' to sys.path dynamically
current_dir = Path(__file__).resolve()
src_path = current_dir.parents[2]  # Two levels up to locate 'src'
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))  # Add 'src' to Python path

# Import Base after fixing sys.path
from shared.models import Base

# Locate alembic.ini in `src/database`
alembic_ini_path = current_dir.parents[1] / "alembic.ini"
if not alembic_ini_path.exists():
    raise FileNotFoundError(f"Alembic configuration file not found at: {alembic_ini_path}")

config = context.config

# Get environment variables
db_url = os.getenv("ALEMBIC_DB_URL")
env = os.getenv("ALEMBIC_ENV", "unknown")
migration_file_name = os.getenv("ALEMBIC_MIGRATION_FILE", None)

if not env == "local" and (not db_url or env == "unknown"):
    raise ValueError("❌ Required environment variables are not set correctly.")

config.set_main_option("sqlalchemy.url", db_url)

# Configure logging
fileConfig(str(alembic_ini_path))

# Metadata for autogenerate support
print(f"Registered tables: {Base.metadata.tables.keys()}")
target_metadata = Base.metadata

# Set migration path (no environment subfolders)
migration_path = Path("src/database/migrations/versions/")
migration_path.mkdir(parents=True, exist_ok=True)
print(f"Using migration path: {migration_path}")

# Set version location dynamically based on environment
config.set_main_option("version_locations", str(migration_path))


def run_migrations_online():
    """Run migrations in 'online' mode with DB connection."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata)
        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    """Run migrations in 'offline' mode (without DB connection)."""
    context.configure(url=db_url, target_metadata=target_metadata)
    with context.begin_transaction():
        context.run_migrations()
else:
    run_migrations_online()
