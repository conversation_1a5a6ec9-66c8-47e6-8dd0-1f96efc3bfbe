import json

from werkzeug.exceptions import HTTPException


class AuthError(HTTPException):
    """Flask-compatible Auth error that returns proper HTTP response"""

    def __init__(self, error: dict, status_code: int):
        self.code = status_code
        self.error = error
        self.description = error.get("description", "Unauthorized")
        super().__init__(description=self.description)
        self.response = self.get_response()

    def get_body(self, environ=None, scope=None):
        return json.dumps(
            {
                "code": self.error.get("code", "auth_error"),
                "message": self.description,
            }
        )

    def get_headers(self, environ=None, scope=None):
        return [("Content-Type", "application/json")]
