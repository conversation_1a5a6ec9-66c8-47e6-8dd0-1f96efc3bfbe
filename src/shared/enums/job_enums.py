from enum import Enum


class MessageType(Enum):
    # Comparison
    GENERATE_POLICY_REQUIREMENT_COMPARISON = "job-generate-policy-requirement-comparison"
    # File upload
    PROCESS_DOCUMENT_SUBMITTED = "job-process-document-submitted"
    PROCESS_DOCUMENT_CHUNKS_CREATED = "job-process-document-chunks-created"
    PROCESS_DOCUMENT_STATEMENTS_CREATED = "job-process-document-statements-created"
    # Chunk curator
    GENERATE_UNIQUE_REQUIREMENTS = "job-generate-unique-requirements"
    REGENERATE_STATEMENTS = "job-regenerate-statements"
    # Requirements curator
    GENERATE_USER_STORIES = "job-generate-user-stories"
    GENERATE_ACCEPTANCE_CRITERIA = "job-generate-acceptance-criteria"
    GENERATE_TEST_CASES = "job-generate-test-cases"
    GENERATE_ACCEPTANCE_CRITERIA_FOR_USER_STORY = "job-generate_acceptance_criteria_for_user_story"
    # Change Statement curator
    GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS = "job-generate_federal_register_change_statements"
    # Optimize Document
    OPTIMIZE_PROCEDURE = "job-optimize-procedure"
    # Compare 2 PDFs
    VERSION_COMPARISON = "job-version-comparison"
    # Obsolete - TODO remove
    GENERATE_REQUIREMENTS_BUNDLE = "job-generate-requirements-bundle"


class JobStatusEnum(Enum):
    """
    An enumeration representing the possible statuses for a job.

    This enum defines various stages that a job can be in throughout its lifecycle.
    Each status corresponds to a specific state of the job, allowing for easy
    comparison and assignment of job statuses within the system.
    """

    INITIAL_PROCESSING = 1
    READY_FOR_CHUNK_CURATION = 2
    GENERATING_REQUIREMENTS = 3
    READY_FOR_REQUIREMENT_CURATION = 4
    JOB_COMPLETE = 7
    JOB_FAILURE = 8
    GENERATING_USER_STORIES = 16
    GENERATING_ACCEPTANCE_CRITERIA = 17
    GENERATING_TEST_CASES = 18
    ARTIFACTS_ARE_READY = 19
    IDENTIFYING_POLICY_IMPACTS = 20
    SUBTOPICS_READY_FOR_CURATION = 21
    REGENERATING_STATEMENTS = 22
    OPTIMIZING_PROCEDURE = 23


class JobTypeEnum(Enum):
    PROCESS_DOCUMENT = 1
    DOMAIN_COMPARISON = 2
    PROCEDURE_IMPORT = 3  # Previously REFERENCE_DOCUMENT
    ARTIFACT_GENERATION = 4
    MARKUP_CHANGE_IDENTIFICATION = 5
    POLICY_REQUIREMENT_COMPARISON = 6
    FEDERAL_REGISTER_CHANGE_IDENTIFICATION = 7
    OPTIMIZE_PROCEDURE = 8
    VERSION_COMPARISON = 9

    @classmethod
    def from_underscored(cls, underscored_value: str):
        """
        Converts an underscored string to the corresponding JobType value.
        """
        mapping = {key: value.value for key, value in cls.__members__.items()}
        normalized_value = underscored_value.replace("_", " ")
        if normalized_value in mapping.values():
            return cls(normalized_value)
        raise ValueError(
            f"Invalid input '{underscored_value}'. No matching JobType found. Valid options are: {list(mapping.keys())}."
        )

    @classmethod
    def from_id(cls, job_type_id: int):
        """Convert job_type_id to human-readable job type string."""
        try:
            return cls(job_type_id).name.replace("_", " ").lower()
        except ValueError:
            return None

    @classmethod
    def map_job_type_to_enum(cls, job_type_name: str):
        """
        Converts a human-readable job type name to the corresponding JobTypeEnum value.
        """
        mapping = {
            "process document": cls.PROCESS_DOCUMENT,  # deprecated, use artifact_generation instead
            "domain comparison": cls.DOMAIN_COMPARISON,
            "procedure import": cls.PROCEDURE_IMPORT,
            "reference document": cls.PROCEDURE_IMPORT,  # Keep for backward compatibility
            "artifact generation": cls.ARTIFACT_GENERATION,
            "markup change identification": cls.MARKUP_CHANGE_IDENTIFICATION,
            "policy requirement comparison": cls.POLICY_REQUIREMENT_COMPARISON,
            "federal register change identification": cls.FEDERAL_REGISTER_CHANGE_IDENTIFICATION,
            "optimize procedure": cls.OPTIMIZE_PROCEDURE,
            "version comparison": cls.VERSION_COMPARISON,
        }
        return mapping.get(job_type_name.lower())


class JobMessageKey(Enum):
    # Comparison
    GENERATE_POLICY_REQUIREMENT_COMPARISON = "generate_policy_requirement_comparison"
    # File upload
    SUBMIT_INITIAL_DOCUMENT = "submit_initial_document"
    CHUNKS_GENERATED = "chunks_generated"
    # Chunk curator
    GENERATE_UNIQUE_REQUIREMENTS = "generate_unique_requirements"
    REGENERATE_STATEMENTS = "regenerate_statements"
    # Requirements curator
    GENERATE_USER_STORIES = "generate_user_stories"
    GENERATE_ACCEPTANCE_CRITERIA = "generate_acceptance_criteria"
    GENERATE_TEST_CASES = "generate_test_cases"
    # Change Statement curator
    GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS = "generate_federal_register_change_statements"
    # Optimize Document
    OPTIMIZE_PROCEDURE = "optimize_procedure"
    # Compare 2 PDFs
    VERSION_COMPARISON = "version_comparison"
    # Obsolete - TODO remove
    GENERATE_REQUIREMENTS_BUNDLE = "generate_requirements_bundle"


class JobItemTypeEnum(Enum):
    """Enum for types of items in a job workflow."""
    CHUNK = 'CHUNK'
