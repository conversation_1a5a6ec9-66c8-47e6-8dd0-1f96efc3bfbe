from sqlalchemy import (
    Column,
    Integer,
    Text,
    ForeignKey,
    Sequence,
    TIMESTAMP,
    func,
    Index,
)
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.enterprise import (
    Enterprise,
)  # noqa: F401 - import needed to resolve relationship

from shared.models.mixins import TimestampMixin


class User(Base, TimestampMixin):
    __tablename__ = "user"
    __table_args__ = (
        Index("idx_user_enterprise", "enterprise_id", "user_id"),
        Index("idx_user_id", "user_id"),
        {"extend_existing": True},
    )

    user_id = Column(Integer, Sequence("user_user_id_seq"), primary_key=True)
    first_name = Column(Text, nullable=False)
    last_name = Column(Text, nullable=False)
    email_address = Column(Text, nullable=False, unique=True)
    enterprise_id = Column(
        Integer,
        ForeignKey("enterprise.enterprise_id", ondelete="CASCADE"),
        nullable=False,
    )
    role_id = Column(Integer)
    auth0_id = Column(Text, nullable=True, unique=True)

    # Parent relationship
    enterprise = relationship("Enterprise", back_populates="users")

    # You can add more relationships here if necessary
