from sqlalchemy import Column, Integer, Text, Sequence, TIMESTAMP, func, Index
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from shared.models.base import Base


class Image(Base):
    __tablename__ = "image"
    __table_args__ = (
        Index("idx_image_created_at", "created_at"),  # Index for created_at column
        {"extend_existing": True},
    )

    image_id = Column(Integer, Sequence("image_image_id_seq"), primary_key=True)
    location = Column(Text, nullable=False)
    image_metadata = Column(JSONB, name="metadata", nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    chunks = relationship("ChunkImage", back_populates="image")
