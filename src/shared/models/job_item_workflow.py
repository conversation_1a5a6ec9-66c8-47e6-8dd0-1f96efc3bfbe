from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ENUM

from shared.models.base import Base
from shared.enums.job_enums import JobItemTypeEnum
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum


def get_enum_values(enum_class):
    return [member.value for member in enum_class]


class JobItemWorkflow(Base):
    __tablename__ = 'job_item_workflow'
    __table_args__ = {'extend_existing': True}

    job_item_workflow_id = Column(Integer, primary_key=True)
    job_workflow_id = Column(Integer, ForeignKey('job_workflow.job_workflow_id', ondelete='CASCADE'), nullable=False)
    item_key = Column(String, nullable=False)
    item_type = Column(
        ENUM(JobItemTypeEnum, name='job_item_type', values_callable=get_enum_values, create_type=False),
        nullable=False,
        default=JobItemTypeEnum.CHUNK.value
    )
    status = Column(
        ENUM(JobWorkflowStatusEnum, name='job_workflow_status', values_callable=get_enum_values, create_type=False),
        nullable=False,
        default=JobWorkflowStatusEnum.STARTING.value
    )
    run_count = Column(Integer, nullable=False, default=0)
    last_error = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    job_workflow = relationship("JobWorkflow", back_populates="job_items") 