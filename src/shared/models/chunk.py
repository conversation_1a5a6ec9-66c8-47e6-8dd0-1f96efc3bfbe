from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Integer,
    Sequence,
    Text,
    TIMESTAMP,
    func,
    Index,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
from shared.models.base import Base


class Chunk(Base):
    __tablename__ = "chunk"
    __table_args__ = (
        Index("idx_chunk_document_id", "document_id"),
        {"extend_existing": True},
    )

    chunk_id = Column(
        Integer,
        Sequence("semantic_chunk_chunk_id_seq"),
        primary_key=True,
        nullable=False,
    )
    document_id = Column(Integer, ForeignKey("document.document_id"), nullable=False)
    formatted_text = Column(JSONB, nullable=True)
    text_content = Column(JSONB, nullable=True)
    vector_id = Column(UUID, nullable=True)
    chunk_metadata = Column(JSONB, name="metadata", nullable=True)
    identifier = Column(Text, nullable=False, default="")
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now())


Chunk.document = relationship("Document", back_populates="chunks")
# When we delete a chunk, also delete all children statements
Chunk.statements = relationship(
    "Statement", back_populates="chunk", cascade="all, delete-orphan", passive_deletes=True
)
Chunk.unique_statements = relationship("UniqueStatement", back_populates="chunk")
Chunk.images = relationship("ChunkImage", back_populates="chunk")
