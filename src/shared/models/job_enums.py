import enum
from shared.enums.job_enums import JobTypeEnum

VALID_JOB_TYPES = [
    JobTypeEnum.PROCESS_DOCUMENT.value,
    JobTypeEnum.DOMAIN_COMPARISON.value,
]

# JobStatus Model uses a default of [0] for state, so be careful when changing this list.
VALID_JOB_STATES = [
    "Chunking",
    "Artifacts",
    "Done",
    "Requirements",
    "Statements",
    "Subtopics",
]


class JobStateEnum(enum.Enum):
    BACKLOG = "backlog"
    CURATING = "curating"
    DONE = "Done"
    CHUNKING = "Chunking"
    STATEMENTS = "Statements"
    SUBTOPICS = "Subtopics"
    REQUIREMENTS = "Requirements"
    ARTIFACTS = "Artifacts"
