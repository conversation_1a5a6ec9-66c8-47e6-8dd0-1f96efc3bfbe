from sqlalchemy import <PERSON><PERSON><PERSON>, TIMESTAMP, Column, Index, Integer, Sequence, Text, func, ForeignKey
from sqlalchemy.orm import relationship

from shared.models.base import Base


class AuditActivity(Base):
    __tablename__ = "audit_activity"

    audit_activity_id = Column(
        Integer,
        Sequence("audit_activity_id_seq"),
        primary_key=True,
        nullable=False,
    )
    audit_object_type_id = Column(
        Integer, ForeignKey("audit_object_type.audit_object_type_id"), nullable=False
    )
    object_key = Column(Integer, nullable=False)  # the object's ID

    data_snapshot = Column(JSON, nullable=False)  # full snapshot of the data after the change

    user_id = Column(Integer, ForeignKey("user.user_id", ondelete="SET NULL"), nullable=True)

    audit_operation_type_id = Column(
        Integer, ForeignKey("audit_operation_type.audit_operation_type_id"), nullable=False
    )

    recorded_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)

    __table_args__ = (
        Index(
            "idx_audit_activity_audit_object_type_id_object_key", audit_object_type_id, object_key
        ),  # Compound Index on audit_object_type_id, object_key
        {"extend_existing": True},
    )

    # One-way relationships (read-only from AuditActivity)
    user = relationship("User", lazy="joined")
    audit_object_type = relationship("AuditObjectType", lazy="joined")
    audit_operation_type = relationship("AuditOperationType", lazy="joined")

    @property
    def user_name(self):
        if self.user:
            return f"{self.user.first_name} {self.user.last_name}".strip()
        return None

    @property
    def audit_object_type_name(self):
        return self.audit_object_type.name if self.audit_object_type else None

    @property
    def audit_operation_type_name(self):
        return self.audit_operation_type.name if self.audit_operation_type else None
