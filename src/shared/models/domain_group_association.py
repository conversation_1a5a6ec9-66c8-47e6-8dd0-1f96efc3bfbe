from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Table
from shared.models.base import Base


domain_group_association = Table(
    "domain_group_association",
    Base.metadata,
    Column("domain_id", Integer, ForeignKey("domain.domain_id"), primary_key=True),
    Column(
        "domain_group_id",
        Integer,
        ForeignKey("domain_group.domain_group_id"),
        primary_key=True,
    ),
)
