from sqlalchemy import Column, Integer, Text, Sequence, ForeignKey, TIMESTAMP, func
from sqlalchemy.orm import relationship
from shared.models.base import Base


class Keyword(Base):
    __tablename__ = "keyword"
    __table_args__ = {"extend_existing": True}

    keyword_id = Column(Integer, Sequence("keyword_keyword_id_seq"), primary_key=True)
    domain_id = Column(
        Integer, ForeignKey("domain.domain_id", ondelete="CASCADE"), nullable=False
    )
    subtopic_id = Column(
        Integer, ForeignKey("subtopic.subtopic_id", ondelete="CASCADE"), nullable=False
    )
    text = Column(Text, nullable=False)
    topic_id = Column(
        Integer, ForeignKey("topic.topic_id", ondelete="CASCADE"), nullable=False
    )
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now()
    )

    # Parent relationship
    domain = relationship("Domain", back_populates="keywords")
