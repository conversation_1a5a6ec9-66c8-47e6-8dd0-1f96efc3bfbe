from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, Sequence, Text
from sqlalchemy.orm import relationship

# Import Base from the centralized base module
from shared.models.base import Base
from shared.models.mixins import TimestampMixin


class Topic(Base, TimestampMixin):
    __tablename__ = "topic"
    __table_args__ = {"extend_existing": True}

    topic_id = Column(Integer, Sequence("topic_topic_id_seq"), primary_key=True)
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=False)
    business_id = Column(Integer, ForeignKey("business.business_id"), nullable=False, default=1)

    # Relationships
    subtopics = relationship("Subtopic", back_populates="topic")
    business = relationship("Business", back_populates="topics")
