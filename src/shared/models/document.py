from sqlalchemy import Column, DateTime, Foreign<PERSON>ey, Integer, Sequence, Text, func, text
from sqlalchemy.orm import relationship

from shared.models.base import Base
from shared.models.mixins import TimestampMixin


class Document(Base, TimestampMixin):
    __tablename__ = "document"
    __table_args__ = {"extend_existing": True}

    document_id = Column(Integer, Sequence("document_document_id_seq"), primary_key=True)
    domain_id = Column(
        Integer,
        ForeignKey("domain.domain_id", onupdate="NO ACTION", ondelete="NO ACTION"),
        nullable=False,
    )
    name = Column(Text, nullable=False)
    s3_location = Column(Text, nullable=False)
    business_id = Column(
        Integer,
        ForeignKey("business.business_id", onupdate="NO ACTION", ondelete="NO ACTION"),
        nullable=False,
        default=1,
    )

    effective_datetime = Column(
        DateTime(timezone=False),
        nullable=False,
        server_default=func.current_timestamp(),
    )
    start_page = Column(Integer, default=0)
    end_page = Column(Integer, default=0)

    # Add a self-referential foreign key so each document can refer to an 'original' document
    original_document_id = Column(
        Integer,
        ForeignKey("document.document_id", onupdate="NO ACTION", ondelete="NO ACTION"),
        nullable=True,
    )

    # # Relationship back to the original Document, if any.
    original = relationship("Document", remote_side="Document.document_id", uselist=False)


Document.domain = relationship("Domain", back_populates="documents")
Document.business = relationship("Business", back_populates="documents")
Document.chunks = relationship("Chunk", back_populates="document")
Document.job = relationship("Job", back_populates="document", uselist=False)
Document.unique_statements = relationship("UniqueStatement", back_populates="document")
Document.document_statistics = relationship(
    "DocumentStatistics", back_populates="document", uselist=False
)
