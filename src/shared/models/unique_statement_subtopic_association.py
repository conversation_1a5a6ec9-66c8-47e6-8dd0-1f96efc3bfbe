from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PrimaryKeyConstraint
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.mixins import DictConvertibleMixin


class UniqueStatementSubtopicAssociation(Base, DictConvertibleMixin):
    __tablename__ = "unique_statement_subtopic_association"

    unique_statement_id = Column(
        Integer,
        ForeignKey("unique_statement.unique_statement_id", ondelete="CASCADE"),
        primary_key=True,
    )
    subtopic_id = Column(
        Integer,
        ForeignKey("subtopic.subtopic_id", ondelete="CASCADE"),
        primary_key=True,
    )
    is_primary = Column(Boolean, nullable=False, default=False)

    __table_args__ = (
        PrimaryKeyConstraint(
            "unique_statement_id",
            "subtopic_id",
            name="unique_statement_subtopic_association_pkey",
        ),
        {"extend_existing": True},
    )

    # Relationships back to UniqueStatement and Subtopic
    unique_statement = relationship(
        "UniqueStatement", back_populates="unique_statement_subtopic_associations"
    )
    subtopic = relationship("Subtopic", overlaps="subtopic")
