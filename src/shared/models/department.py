from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, Text
from sqlalchemy.orm import relationship

from shared.models.base import Base

# Needed for relationship - direct import instead of through models libary to avoid circular dependency
from shared.models.department_subtopic_association import department_subtopic_association
from shared.models.mixins import TimestampMixin


class Department(Base, TimestampMixin):
    __tablename__ = "department"
    __table_args__ = {"extend_existing": True}

    department_id = Column(
        Integer,
        primary_key=True,
        autoincrement=True,  # Allow PostgreSQL to manage the sequence
    )
    business_id = Column(
        Integer, ForeignKey("business.business_id", ondelete="CASCADE"), nullable=False
    )
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=False)

    # Parent Relationship
    business = relationship("Business", back_populates="departments")

    # Child relationships
    personas = relationship("Persona", back_populates="department")

    # Direct relationship to subtopics via the association table
    subtopics = relationship(
        "Subtopic",
        secondary=department_subtopic_association,
        back_populates="departments",
    )
