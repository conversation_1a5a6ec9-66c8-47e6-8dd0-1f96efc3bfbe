from sqlalchemy import (
    Column,
    Enum as SQLAlchemyEnum,
    Integer,
    Text,
    ForeignKey,
    Sequence,
    Boolean,
    TIMESTAMP,
    func,
    Index,
)
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.rating_enum import RatingEnum
from shared.models.mixins import DictConvertibleMixin


class TestCase(Base, DictConvertibleMixin):
    __tablename__ = "test_case"
    __table_args__ = (
        Index("idx_test_case", "acceptance_criteria_id", "rating"),
        {"extend_existing": True},
    )

    test_case_id = Column(Integer, Sequence("test_case_test_case_id_seq"), primary_key=True)
    acceptance_criteria_id = Column(
        Integer,
        ForeignKey("acceptance_criteria.acceptance_criteria_id", ondelete="CASCADE"),
        nullable=False,
    )
    description = Column(Text, nullable=False)
    identifier = Column(Text, nullable=False, default="")
    rating = Column(
        SQLAlchemyEnum(RatingEnum, name="rating_enum"),
        default=RatingEnum.unrated,
        nullable=False,
    )
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now())
    user_entered = Column(Boolean, nullable=False, default=False)
    edited = Column(Boolean, nullable=False, default=False)

    # Parent relationship
    acceptance_criteria = relationship("AcceptanceCriteria", back_populates="test_cases")
