from sqlalchemy import <PERSON>umn, Integer, TIMESTAMP, ForeignKey, Index, func
from sqlalchemy.orm import relationship
from shared.models.base import Base


class ChunkImage(Base):
    __tablename__ = "chunk_image"
    __table_args__ = (
        Index("idx_chunk_image_chunk_id", "chunk_id"),  # Index on chunk_id
        Index("idx_chunk_image_image_id", "image_id"),  # Index on image_id
        {"extend_existing": True},
    )

    chunk_id = Column(
        Integer, ForeignKey("chunk.chunk_id"), primary_key=True, nullable=False
    )
    image_id = Column(
        Integer, ForeignKey("image.image_id"), primary_key=True, nullable=False
    )
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    chunk = relationship("Chunk", back_populates="images")
    image = relationship("Image", back_populates="chunks")
