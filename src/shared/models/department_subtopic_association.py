from sqlalchemy import Column, Foreign<PERSON>ey, Integer, PrimaryKeyConstraint
from sqlalchemy.orm import relationship

from shared.models.base import Base
from shared.models.mixins import TimestampMixin


from sqlalchemy import Column, Integer, ForeignKey, Table
from shared.models.base import Base


department_subtopic_association = Table(
    "department_subtopic_association",
    Base.metadata,
    Column(
        "department_id",
        Integer,
        ForeignKey("department.department_id"),
        primary_key=True,
    ),
    Column("subtopic_id", Integer, ForeignKey("subtopic.subtopic_id"), primary_key=True),
)
