from sqlalchemy import (
    Column,
    Text,
    Index,
    func
)
from shared.models.base import Base


class AiModelMapping(Base):
    """
    Maps user-friendly AI model names to actual provider model names.
    
    This model maintains a mapping between human-readable model names used in the application
    and the corresponding model identifiers used by the AI providers.
    """
    __tablename__ = "ai_model_mapping"
    
    friendly_name = Column(Text, primary_key=True)  # Human-readable name for the model
    provider_model_name = Column(Text, nullable=False)  # Actual model identifier used by the provider 
    
    __table_args__ = (
        Index(
            "unique_friendly_name_lower",
            func.lower(friendly_name),
            unique=True,
            postgresql_using='btree'
        ),
        {"extend_existing": True},
    ) 