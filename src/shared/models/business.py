from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String, TIMESTAMP, func
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.enterprise_business_association import (
    enterprise_business_association,
)


class Business(Base):
    __tablename__ = "business"
    __table_args__ = {"extend_existing": True}

    business_id = Column(Integer, primary_key=True, nullable=False)
    name = Column(String(255), nullable=False)
    enterprise_id = Column(Integer, ForeignKey("enterprise.enterprise_id"), nullable=True)
    persona = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now())


Business.enterprises = relationship(
    "Enterprise",
    secondary=enterprise_business_association,
    back_populates="businesses",
)
Business.documents = relationship("Document", back_populates="business")
Business.topics = relationship("Topic", back_populates="business")
Business.subtopics = relationship("Subtopic", back_populates="business")
Business.departments = relationship("Department", back_populates="business")
