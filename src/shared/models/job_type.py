from sqlalchemy import Column, Integer, String, Text
from sqlalchemy.orm import relationship

from shared.models.base import Base


class JobType(Base):
    __tablename__ = "job_type"

    job_type_id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    name = Column(String, unique=True, nullable=False)
    description = Column(Text)

    jobs = relationship("Job", back_populates="job_type")
