from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    <PERSON><PERSON>ey,
    Integer,
    Sequence,
    func,
    Index,
)
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from shared.models.base import Base


def get_enum_values(enum_class):
    return [member.value for member in enum_class]


class Job(Base):
    __tablename__ = "job"
    __table_args__ = (
        Index("idx_job_status_id", "job_status_id"),
        Index("idx_reference_id", "reference_id"),
        {"extend_existing": True},
    )

    job_id = Column(Integer, Sequence("job_job_id_seq"), primary_key=True)
    reference_id = Column(Integer, ForeignKey("document.document_id", ondelete="CASCADE"), nullable=False)

    # Without values_callable, SQLAlchemy may misinterpret the enum or fail to extract the
    # .value correctly. For example, it might try to map the enum instance (JobType.artifact_generation)
    # directly to the database instead of the string "artifact generation".
    job_type_id = <PERSON>umn(Integer, ForeignKey('job_type.job_type_id'), nullable=False)
    job_status_id = Column(Integer, ForeignKey("job_status.job_status_id"), nullable=False)
    submitted_datetime = Column(TIMESTAMP(timezone=True), server_default=func.now(), nullable=False)
    created_at = Column(TIMESTAMP(timezone=False), server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=False), server_default=func.now(), nullable=False)
    deleted = Column(Boolean, nullable=False, default=False)
    global_knowledge = Column(Boolean, nullable=False, default=False)
    curation_complete_datetime = Column(TIMESTAMP(timezone=True), nullable=True)

    job_type = relationship("JobType", back_populates="jobs")
    job_status = relationship("JobStatus")
    document = relationship("Document", back_populates="job", uselist=False)

    @hybrid_property
    def job_name(self):
        return self.document.name if self.document else None

    @property
    def job_status_description(self):
        return getattr(self, "_job_status_description", None)

    @job_status_description.setter
    def job_status_description(self, value):
        self._job_status_description = value
