from datetime import datetime
from enum import Enum

from sqlalchemy import TIMESTAMP, Column, func
from sqlalchemy.inspection import inspect


class TimestampMixin:
    created_at = Column(
        TIMESTAMP, server_default=func.now(), nullable=False, comment="Record creation time"
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=func.now(),
        onupdate=func.now(),
        nullable=True,
        comment="Record update time",
    )


class DictConvertibleMixin:
    def to_dict(self):
        result = {}
        for column in inspect(self).mapper.column_attrs:
            value = getattr(self, column.key)

            if isinstance(value, Enum):
                result[column.key] = value.value
            elif isinstance(value, datetime):
                result[column.key] = value.isoformat()  # Convert datetime to ISO 8601 string
            else:
                result[column.key] = value

        return result
