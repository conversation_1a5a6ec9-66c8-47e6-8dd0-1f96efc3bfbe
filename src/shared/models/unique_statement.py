from sqlalchemy import <PERSON><PERSON><PERSON>, Column
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, Sequence, Text, TIMESTAMP, func, Index
from sqlalchemy.orm import relationship


# Keep separate - circular dependency issue when consolidating all models into a single from statement
from shared.models.base import Base
from shared.models.rating_enum import RatingEnum
from shared.models.unique_statement_subtopic_association import (
    UniqueStatementSubtopicAssociation,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.user_story import (
    UserStory,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.mixins import DictConvertibleMixin, TimestampMixin


class UniqueStatement(Base, DictConvertibleMixin, TimestampMixin):
    __tablename__ = "unique_statement"
    __table_args__ = (
        Index("idx_unique_statement", "document_id", "rating"),
        {"extend_existing": True},
    )

    unique_statement_id = Column(
        Integer, Sequence("unique_statement_unique_statement_id_seq"), primary_key=True
    )
    domain_id = Column(Integer, ForeignKey("domain.domain_id", ondelete="CASCADE"), nullable=True)
    statement_type_id = Column(
        Integer, ForeignKey("statement_type.statement_type_id"), nullable=False
    )
    subtopic_id = Column(
        Integer, ForeignKey("subtopic.subtopic_id", ondelete="CASCADE"), nullable=True
    )
    text = Column(Text, nullable=False)
    identifier = Column(Text, nullable=False, default="")
    rating = Column(
        SQLAlchemyEnum(RatingEnum, name="rating_enum"),
        default=RatingEnum.unrated,
        nullable=False,
    )
    document_id = Column(Integer, ForeignKey("document.document_id"), nullable=True)
    chunk_id = Column(Integer, ForeignKey("chunk.chunk_id", ondelete="CASCADE"), nullable=True)
    user_entered = Column(Boolean, nullable=False, default=False)
    edited = Column(Boolean, nullable=False, default=False)
    statement_id = Column(Integer, ForeignKey("statement.statement_id"), nullable=True)
    parent_requirement_id = Column(
        Integer,
        ForeignKey("unique_statement.unique_statement_id", ondelete="SET NULL"),
        nullable=True,
    )


# Direct relationship to subtopics via the association table
UniqueStatement.subtopics = relationship(
    "Subtopic",
    secondary=UniqueStatementSubtopicAssociation.__table__,
    back_populates="unique_statements",
    overlaps="subtopic, unique_statement",
)

# Define relationships after class definition
UniqueStatement.statement_type = relationship(
    "StatementType", foreign_keys=[UniqueStatement.statement_type_id]
)
UniqueStatement.user_stories = relationship("UserStory", back_populates="unique_statement")
UniqueStatement.domain = relationship("Domain", back_populates="unique_statements")
UniqueStatement.document = relationship("Document", back_populates="unique_statements")
UniqueStatement.chunk = relationship("Chunk", back_populates="unique_statements")

# Direct relationship to the association table
UniqueStatement.unique_statement_subtopic_associations = relationship(
    "UniqueStatementSubtopicAssociation",
    back_populates="unique_statement",
    viewonly=True,
)

# Parent-child relationship
UniqueStatement.parent = relationship(
    "UniqueStatement",
    remote_side=[UniqueStatement.unique_statement_id],
    backref="child_requirements",
)
