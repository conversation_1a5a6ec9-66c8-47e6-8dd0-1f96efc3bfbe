from sqlalchemy import (
    <PERSON>umn,
    Integer,
    SMALLINT,
    ForeignKey,
    Enum as SQLAlchemyEnum,
    Sequence,
    TIMESTAMP,
    func,
    UniqueConstraint,
    Index,
)
from sqlalchemy.orm import relationship
from shared.models.job_workflow_status_enum import JobWorkflowStatusEnum
from shared.models.base import Base


def get_enum_values(enum_class):
    return [member.value for member in enum_class]


class JobWorkflow(Base):
    __tablename__ = "job_workflow"
    __table_args__ = (
        UniqueConstraint("job_id", "workflow_type_id", name="unique_job_workflow"),
        Index("idx_job_workflow_job_id", "job_id"),
        {"extend_existing": True},
    )

    job_workflow_id = Column(
        Integer, Sequence("job_workflow_job_workflow_id_seq"), primary_key=True
    )
    job_id = Column(Integer, ForeignKey("job.job_id"), nullable=False)
    workflow_type_id = Column(
        Integer, ForeignKey("workflow_type.workflow_type_id"), nullable=False
    )
    status = Column(
        SQLAlchemyEnum(
            JobWorkflowStatusEnum,
            name="job_workflow_status",
            values_callable=get_enum_values,
        ),
        nullable=False,
        default=JobWorkflowStatusEnum.STARTING.value,
    )
    run_count = Column(SMALLINT, default=0, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now()
    )

    workflow_type = relationship("WorkflowType")
    job = relationship("Job")
    job_items = relationship("JobItemWorkflow", back_populates="job_workflow", cascade="all, delete-orphan")
