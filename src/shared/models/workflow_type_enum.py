from enum import Enum
from typing import Type


class WorkflowTypeEnum(Enum):
    GENERATE_REQUIREMENTS_BUNDLE = (1, "GENERATE_REQUIREMENTS_BUNDLE")
    GENERATE_USER_STORIES = (2, "GENERATE_USER_STORIES")
    GENERATE_ACCEPTANCE_CRITERIA = (3, "GENERATE_ACCEPTANCE_CRITERIA")
    GENERATE_TEST_CASES = (4, "GENERATE_TEST_CASES")
    GENERATE_CHUNK_IMAGES = (5, "GENERATE_CHUNK_IMAGES")
    IDENTIFYING_POLICY_IMPACTS = (6, "IDENTIFYING_POLICY_IMPACTS")
    IDENTIFYING_CHANGE_STATEMENTS = (7, "IDENTIFYING_CHANGE_STATEMENTS")
    GENERATE_STATEMENTS = (8, "GENERATE_STATEMENTS")

    def __init__(self, workflow_type_id: int, value_name: str):
        self.workflow_type_id = workflow_type_id
        self.value_name = value_name

    @classmethod
    def from_id(cls: Type["WorkflowTypeEnum"], workflow_type_id: int) -> "WorkflowTypeEnum":
        for member in cls:
            if member.workflow_type_id == workflow_type_id:
                return member
        raise ValueError(f"No WorkflowTypeEnum with id {workflow_type_id}")

    @classmethod
    def from_name(cls: Type["WorkflowTypeEnum"], name: str) -> "WorkflowTypeEnum":
        for member in cls:
            if member.value_name == name:
                return member
        raise ValueError(f"No WorkflowTypeEnum with name {name}")
