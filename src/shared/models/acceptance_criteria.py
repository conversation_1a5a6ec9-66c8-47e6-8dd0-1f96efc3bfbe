from sqlalchemy import (
    Column,
    Enum as SQLAlchemyEnum,
    Integer,
    Text,
    ForeignKey,
    Sequence,
    Boolean,
    TIMESTAMP,
    func,
    Index,
)
from sqlalchemy.orm import relationship
from sqlalchemy.schema import DefaultClause  # Import this for server default

from shared.models.base import Base
from shared.models.rating_enum import RatingEnum
from shared.models.test_case import (
    TestCase,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.rating_enum import RatingEnum
from shared.models.mixins import DictConvertibleMixin


class AcceptanceCriteria(Base, DictConvertibleMixin):
    __tablename__ = "acceptance_criteria"
    __table_args__ = (
        Index("idx_acceptance_criteria", "user_story_id", "rating"),
        {"extend_existing": True},
    )

    acceptance_criteria_id = Column(
        Integer,
        Sequence("acceptance_criteria_criteria_id_seq"),
        primary_key=True,
        nullable=False,
    )
    user_story_id = Column(
        Integer,
        ForeignKey("user_story.user_story_id", ondelete="CASCADE"),
        nullable=False,
    )
    description = Column(Text, nullable=False)
    identifier = Column(
        Text, nullable=False, default="", server_default=DefaultClause("''")
    )  # Ensures default is applied both in Python and DB
    rating = Column(
        SQLAlchemyEnum(RatingEnum, name="rating_enum"),
        default=RatingEnum.unrated,
        nullable=False,
    )
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now())
    user_entered = Column(Boolean, nullable=False, default=False)
    edited = Column(Boolean, nullable=False, default=False)

    # Parent Relationship
    user_story = relationship("UserStory", back_populates="acceptance_criterias")

    # Child relationships
    test_cases = relationship("TestCase", back_populates="acceptance_criteria")
