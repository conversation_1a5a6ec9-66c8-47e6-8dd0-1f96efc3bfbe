from sqlalchemy import Column, Integer, String, Text, Sequence
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.domain_group_association import domain_group_association


class DomainGroup(Base):
    __tablename__ = "domain_group"

    domain_group_id = Column(
        Integer, Sequence("domain_group_domain_group_id_seq"), primary_key=True
    )
    # using String type for 'character varying'
    name = Column(String(255), nullable=False)
    display_name = Column(Text, nullable=False)

    # Association relationship
    domains = relationship(
        "Domain", secondary=domain_group_association, back_populates="domain_groups"
    )
