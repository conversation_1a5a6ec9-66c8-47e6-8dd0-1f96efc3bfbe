from sqlalchemy import Column, Integer, String, Text, Sequence, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship

from shared.models.base import Base
from shared.models.job_enums import VALID_JOB_STATES, JobStateEnum


def get_enum_values(enum_class):
    return [member.value for member in enum_class]


class JobStatus(Base):
    __tablename__ = "job_status"
    __table_args__ = {"extend_existing": True}

    job_status_id = Column(Integer, Sequence("job_status_job_status_id_seq"), primary_key=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=False)
    state = Column(
        SQLAlchemyEnum(JobStateEnum, name="job_state", values_callable=get_enum_values),
        nullable=False,
        default=VALID_JOB_STATES[0],
    )
    jobs = relationship("Job", back_populates="job_status")
