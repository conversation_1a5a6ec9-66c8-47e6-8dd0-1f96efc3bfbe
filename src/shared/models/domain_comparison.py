from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from shared.models.comparison_level import (
    ComparisonLevel,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.base import Base


class DomainComparison(Base):
    __tablename__ = "domain_comparison"
    __table_args__ = {"extend_existing": True}

    domain_1_id = Column(
        Integer, ForeignKey("domain.domain_id", ondelete="CASCADE"), primary_key=True
    )
    domain_2_id = Column(
        Integer, ForeignKey("domain.domain_id", ondelete="CASCADE"), primary_key=True
    )
    subtopic_id = Column(
        Integer,
        ForeignKey("subtopic.subtopic_id", ondelete="CASCADE"),
        primary_key=True,
    )
    statement_type_id = Column(
        Integer,
        ForeignKey("statement_type.statement_type_id", ondelete="CASCADE"),
        primary_key=True,
    )
    comparison_level_id = Column(
        Integer,
        ForeignKey("comparison_level.comparison_level_id", ondelete="CASCADE"),
        nullable=False,
    )
    domain_1_exclusive_summary = Column(Text, nullable=False)
    domain_1_exclusive_detail = Column(Text, nullable=False)
    domain_2_exclusive_summary = Column(Text, nullable=False)
    domain_2_exclusive_detail = Column(Text, nullable=False)
    similarities_summary = Column(Text, nullable=False)
    similarities_detail = Column(Text, nullable=False)
    similarities_with_material_differences_summary = Column(Text, nullable=False)
    similarities_with_material_differences_detail = Column(Text, nullable=False)

    # Relationships
    domain_1 = relationship("Domain", foreign_keys=[domain_1_id])
    domain_2 = relationship("Domain", foreign_keys=[domain_2_id])
    comparison_level = relationship("ComparisonLevel")
    statement_type = relationship("StatementType")
    subtopic = relationship("Subtopic")
