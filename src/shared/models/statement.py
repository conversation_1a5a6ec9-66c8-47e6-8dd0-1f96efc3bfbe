from sqlalchemy import (
    Column,
    Integer,
    Text,
    ForeignKey,
    Sequence,
    Enum as SQLAlchemyEnum,
    Boolean,
    TIMESTAMP,
    func,
    Index,
)
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.rating_enum import RatingEnum
from shared.models.mixins import DictConvertibleMixin


class Statement(Base, DictConvertibleMixin):
    __tablename__ = "statement"
    __table_args__ = (
        Index("idx_statement_chunk_id", "chunk_id"),
        {"extend_existing": True},
    )

    statement_id = Column(Integer, Sequence("statement_statement_id_seq"), primary_key=True)
    chunk_id = Column(Integer, ForeignKey("chunk.chunk_id", ondelete="CASCADE"), nullable=False)
    statement_type_id = Column(
        Integer, ForeignKey("statement_type.statement_type_id"), nullable=False
    )
    text = Column(Text, nullable=False)
    rating = Column(
        SQLAlchemyEnum(RatingEnum, name="rating_enum"),
        default=RatingEnum.unrated,
        nullable=False,
    )
    user_entered = Column(Boolean, nullable=False, default=False)
    edited = Column(Boolean, nullable=False, default=False)
    deleted = Column(Boolean, nullable=False, default=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now())


Statement.chunk = relationship("Chunk", back_populates="statements")
Statement.statement_type = relationship("StatementType", foreign_keys=[Statement.statement_type_id])
