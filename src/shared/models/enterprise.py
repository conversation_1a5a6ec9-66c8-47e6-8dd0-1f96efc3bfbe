from sqlalchemy import Column, Integer, String, TIMESTAMP, func
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.enterprise_business_association import (
    enterprise_business_association,
)


class Enterprise(Base):
    __tablename__ = "enterprise"
    __table_args__ = {"extend_existing": True}

    enterprise_id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now()
    )


Enterprise.businesses = relationship(
    "Business",
    secondary=enterprise_business_association,
    back_populates="enterprises",
)

Enterprise.users = relationship("User", back_populates="enterprise")
Enterprise.domain = relationship("Domain", back_populates="enterprise", uselist=False)
