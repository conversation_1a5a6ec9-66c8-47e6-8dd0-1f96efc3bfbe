from sqlalchemy import <PERSON>umn, Foreign<PERSON>ey, Index, Integer, Sequence, Text
from sqlalchemy.orm import relationship

from shared.models.base import Base
from shared.models.department_subtopic_association import (
    department_subtopic_association,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.mixins import TimestampMixin
from shared.models.topic import Topic  # noqa: F401 - import needed to resolve relationship
from shared.models.unique_statement import (
    UniqueStatement,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.unique_statement_subtopic_association import (
    UniqueStatementSubtopicAssociation,
)  # noqa: F401 - import needed to resolve relationship


class Subtopic(Base, TimestampMixin):
    __tablename__ = "subtopic"
    __table_args__ = (
        Index("idx_subtopic", "topic_id"),
        {"extend_existing": True},
    )

    subtopic_id = Column(Integer, Sequence("subtopic_subtopic_id_seq"), primary_key=True)
    topic_id = Column(Integer, <PERSON>Key("topic.topic_id", ondelete="CASCADE"), nullable=False)
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=False)
    business_id = Column(Integer, ForeignKey("business.business_id"), nullable=False, default=1)

    business = relationship("Business", back_populates="subtopics")
    topic = relationship("Topic", back_populates="subtopics")

    # Back reference to unique statements
    unique_statements = relationship(
        "UniqueStatement",
        secondary=UniqueStatementSubtopicAssociation.__table__,
        back_populates="subtopics",
        overlaps="unique_statement, subtopic",
    )

    # Back reference to unique statements
    departments = relationship(
        "Department",
        secondary=department_subtopic_association,
        back_populates="subtopics",
        overlaps="department, subtopic",
    )
