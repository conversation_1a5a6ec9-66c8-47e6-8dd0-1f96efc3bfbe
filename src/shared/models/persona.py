from sqlalchemy import Column, <PERSON><PERSON><PERSON>, Integer, Text
from sqlalchemy.orm import relationship

from shared.models.base import Base
from shared.models.mixins import TimestampMixin


class Persona(Base, TimestampMixin):
    __tablename__ = "persona"
    __table_args__ = {"extend_existing": True}

    persona_id = Column(
        Integer,
        primary_key=True,
        autoincrement=True,  # Allow PostgreSQL to manage the sequence
    )
    department_id = Column(
        Integer,
        ForeignKey("department.department_id", ondelete="CASCADE"),
        nullable=False,
    )
    name = Column(Text, nullable=False)
    description = Column(Text, nullable=False)

    # Parent Relationship
    department = relationship("Department", back_populates="personas")
