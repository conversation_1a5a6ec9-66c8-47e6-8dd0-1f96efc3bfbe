from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, Text, Sequence, TIMESTAMP, func, ForeignKey
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.domain_group_association import domain_group_association
from shared.models.unique_statement import UniqueStatement
from shared.models.keyword import Keyword
from shared.models.domain_group import DomainGroup


class Domain(Base):
    __tablename__ = "domain"
    __table_args__ = {"extend_existing": True}

    domain_id = Column(Integer, Sequence("domain_domain_id_seq"), primary_key=True)
    name = Column(Text, nullable=False)
    display_name = Column(Text, nullable=False)
    vector_database_name = Column(Text)
    is_active = Column(Boolean, default=True, nullable=False)
    enterprise_id = Column(Integer, ForeignKey("enterprise.enterprise_id", ondelete="CASCADE"), nullable=True, unique=True)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=False)
    updated_at = Column(
        TIMESTAMP, server_default=func.now(), nullable=False, onupdate=func.now()
    )


# Define relationships after class definition
Domain.documents = relationship("Document", back_populates="domain")
Domain.unique_statements = relationship("UniqueStatement", back_populates="domain")
Domain.keywords = relationship("Keyword", back_populates="domain")
Domain.domain_groups = relationship(
    "DomainGroup", secondary=domain_group_association, back_populates="domains"
)
Domain.enterprise = relationship("Enterprise", back_populates="domain", uselist=False)
