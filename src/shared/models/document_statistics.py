from sqlalchemy import Column, Integer, Float, Sequence, Text, ForeignKey
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.mixins import TimestampMixin

class DocumentStatistics(Base, TimestampMixin):
    __tablename__ = "document_statistics"
    __table_args__ = {"extend_existing": True}
    document_statistics_sequence = Sequence("document_statistics_document_statistics_id_seq")
    document_statistics_id = Column(
        Integer, document_statistics_sequence, primary_key=True, server_default=document_statistics_sequence.next_value()
    )
    document_id = Column(
        Integer,
        ForeignKey("document.document_id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )
    word_count = Column(Integer, nullable=False)
    difficult_word_count = Column(Integer, nullable=False)
    average_sentence_length = Column(Float, nullable=False)
    average_word_length = Column(Float, nullable=False)
    flesch_grade_level = Column(Integer, nullable=False)
    flesch_score = Column(Integer, nullable=False)
    flesch_reading_ease = Column(Text, nullable=False)

    document = relationship("Document", back_populates="document_statistics")
# Relationships
DocumentStatistics.document = relationship("Document", back_populates="document_statistics")
