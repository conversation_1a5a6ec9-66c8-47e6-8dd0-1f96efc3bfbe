from sqlalchemy import Column, Integer, String, Text, Sequence
from shared.models.base import Base


class StatementType(Base):
    __tablename__ = "statement_type"
    __table_args__ = {"extend_existing": True}

    statement_type_id = Column(
        Integer, Sequence("statement_type_statement_type_id_seq"), primary_key=True
    )
    name = Column(String(50), nullable=False, unique=True)
    description = Column(Text)
