from sqlalchemy import (
    Column,
    Enum as SQLAlchemyEnum,
    Integer,
    Text,
    ForeignKey,
    Sequence,
    Boolean,
    TIMESTAMP,
    func,
    Index,
)
from sqlalchemy.orm import relationship
from shared.models.base import Base
from shared.models.acceptance_criteria import (
    AcceptanceCriteria,
)  # noqa: F401 - import needed to resolve relationship
from shared.models.rating_enum import RatingEnum
from shared.models.mixins import DictConvertibleMixin


class UserStory(Base, DictConvertibleMixin):
    __tablename__ = "user_story"
    __table_args__ = (
        Index("idx_user_story", "unique_statement_id", "rating"),
        {"extend_existing": True},
    )

    user_story_id = Column(Integer, Sequence("user_story_user_story_id_seq"), primary_key=True)
    unique_statement_id = Column(
        Integer,
        ForeignKey("unique_statement.unique_statement_id", ondelete="CASCADE"),
        nullable=False,
    )
    description = Column(Text, nullable=False)
    identifier = Column(Text, nullable=False, default="")
    rating = Column(
        SQLAlchemyEnum(RatingEnum, name="rating_enum"),
        default=RatingEnum.unrated,
        nullable=False,
    )
    created_at = Column(TIMESTAMP(timezone=False), server_default=func.now(), nullable=False)
    updated_at = Column(TIMESTAMP(timezone=False), server_default=func.now(), nullable=False)
    user_entered = Column(Boolean, nullable=False, default=False)
    edited = Column(Boolean, nullable=False, default=False)

    # Parent relationship
    unique_statement = relationship("UniqueStatement", back_populates="user_stories")

    # # Child relationships
    acceptance_criterias = relationship("AcceptanceCriteria", back_populates="user_story")
