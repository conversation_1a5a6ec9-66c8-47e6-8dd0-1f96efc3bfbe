from sqlalchemy import <PERSON><PERSON>n, <PERSON><PERSON><PERSON>, ForeignKey, Table
from shared.models.base import Base


enterprise_business_association = Table(
    "enterprise_business_association",
    Base.metadata,
    Column(
        "enterprise_id",
        Integer,
        ForeignKey("enterprise.enterprise_id"),
        primary_key=True,
    ),
    Column(
        "business_id", Integer, ForeignKey("business.business_id"), primary_key=True
    ),
)
