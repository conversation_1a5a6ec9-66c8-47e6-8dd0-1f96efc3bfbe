from shared.enums.job_enums import MessageType


COMMAND_MAP = {
    # Comparison
    MessageType.GENERATE_POLICY_REQUIREMENT_COMPARISON.value: "GeneratePolicyRequirementComparisonCommand",
    # File upload commands
    MessageType.PROCESS_DOCUMENT_SUBMITTED.value: "ProcessDocumentJobCommand",
    MessageType.PROCESS_DOCUMENT_CHUNKS_CREATED.value: "ProcessDocumentChunksCreatedCommand",
    MessageType.PROCESS_DOCUMENT_STATEMENTS_CREATED.value: "ProcessDocumentGenerateChunkImagesCommand",
    # Chunk curator command
    MessageType.GENERATE_UNIQUE_REQUIREMENTS.value: "GenerateUniqueRequirementsCommand",
    MessageType.REGENERATE_STATEMENTS.value: "RegenerateStatementsCommand",
    # Requirement curator commands
    MessageType.GENERATE_USER_STORIES.value: "GenerateUserStoriesCommand",
    MessageType.GENERATE_ACCEPTANCE_CRITERIA.value: "GenerateAcceptanceCriteriaCommand",
    MessageType.GENERATE_TEST_CASES.value: "GenerateTestCasesCommand",
    # Change Statement commands
    MessageType.GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS.value: "GenerateFederalRegisterChangeStatementsCommand",
    # Optimized Document
    MessageType.OPTIMIZE_PROCEDURE.value: "OptimizeDocumentCommand",
    # Obsolete - TODO delete
    MessageType.GENERATE_REQUIREMENTS_BUNDLE.value: "GenerateRequirementsBundleCommand",
}