from shared.enums.job_enums import Job<PERSON><PERSON><PERSON><PERSON><PERSON>, MessageType

from util.config import (
    AWS_SNS_JOB_ARN,
    AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN,
    AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN,
    AWS_SNS_JOB_GENERATE_TEST_CASES_ARN,
    AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN,
    AWS_SNS_JOB_GENERATE_USER_STORIES_ARN,
    AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN,
    AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN,
    AWS_SNS_JOB_REGENERATE_STATEMENTS_ARN,
)

JOB_CONFIGS = {
    # File upload jobs
    JobMessageKey.SUBMIT_INITIAL_DOCUMENT: {
        "message_type": MessageType.PROCESS_DOCUMENT_SUBMITTED,
        "sns_topic_arn": AWS_SNS_JOB_PROCESS_DOCUMENT_SUBMITTED_ARN,
    },
    JobMessageKey.CHUNKS_GENERATED: {
        "message_type": MessageType.PROCESS_DOCUMENT_CHUNKS_CREATED,
        "sns_topic_arn": AWS_SNS_JOB_PROCESS_DOCUMENT_CHUNKS_CREATED_ARN,
    },
    # Chunk curator jobs
    JobMessageKey.GENERATE_UNIQUE_REQUIREMENTS: {
        "message_type": MessageType.GENERATE_UNIQUE_REQUIREMENTS,
        "sns_topic_arn": AWS_SNS_JOB_GENERATE_UNIQUE_REQUIREMENTS_ARN,
    },
    JobMessageKey.REGENERATE_STATEMENTS: {
        "message_type": MessageType.REGENERATE_STATEMENTS,
        "sns_topic_arn": AWS_SNS_JOB_REGENERATE_STATEMENTS_ARN,
        "requires_chunk_id": True,
    },
    # Requirement curator jobs
    JobMessageKey.GENERATE_USER_STORIES: {
        "message_type": MessageType.GENERATE_USER_STORIES,
        "sns_topic_arn": AWS_SNS_JOB_GENERATE_USER_STORIES_ARN,
    },
    JobMessageKey.GENERATE_ACCEPTANCE_CRITERIA: {
        "message_type": MessageType.GENERATE_ACCEPTANCE_CRITERIA,
        "sns_topic_arn": AWS_SNS_JOB_GENERATE_ACCEPTANCE_CRITERIA_ARN,
    },
    JobMessageKey.GENERATE_TEST_CASES: {
        "message_type": MessageType.GENERATE_TEST_CASES,
        "sns_topic_arn": AWS_SNS_JOB_GENERATE_TEST_CASES_ARN,
    },
    # Change Statement jobs
    JobMessageKey.GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS: {
        "message_type": MessageType.GENERATE_FEDERAL_REGISTER_CHANGE_STATEMENTS,
        "sns_topic_arn": AWS_SNS_JOB_ARN,
        "requires_domain_id": True,
    },
    # Comparison jobs
    JobMessageKey.GENERATE_POLICY_REQUIREMENT_COMPARISON: {
        "message_type": MessageType.GENERATE_POLICY_REQUIREMENT_COMPARISON,
        "sns_topic_arn": AWS_SNS_JOB_ARN,
        "requires_domain_id": True,
    },
    # Optimize Document
    JobMessageKey.OPTIMIZE_PROCEDURE: {
        "message_type": MessageType.OPTIMIZE_PROCEDURE,
        "sns_topic_arn": AWS_SNS_JOB_ARN,
    },
    # Compare 2 PDFs
    JobMessageKey.VERSION_COMPARISON: {
        "message_type": MessageType.VERSION_COMPARISON,
        "sns_topic_arn": AWS_SNS_JOB_ARN,
    },
    # Obsolete - TODO delete
    JobMessageKey.GENERATE_REQUIREMENTS_BUNDLE: {
        "message_type": MessageType.GENERATE_REQUIREMENTS_BUNDLE,
        "sns_topic_arn": AWS_SNS_JOB_GENERATE_REQUIREMENTS_BUNDLE_ARN,
    },
}
