name: Alembic Migration Workflow

on:
  workflow_dispatch:
    inputs:
      target_environment:
        description: "Target environment for migration"
        required: true
        type: choice
        options: ["dev", "sit", "prod", "demo", "client-demo"]
      migration_script_name:
        description: "Optional: Name of the migration script (e.g., abc123_add_users_table) without the .py. Leave blank to upgrade head."
        required: false

jobs:
  alembic_migration:
    name: Run Alembic Migration
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Cache Python Dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r src/api/requirements.txt

      - name: Set Environment Variables
        run: |
          export ALEMBIC_DB_URL="${{ secrets.ALEMBIC_BASE_DB_URL }}-${{ github.event.inputs.target_environment }}"
          export DB_NAME="burst-${{ github.event.inputs.target_environment }}"
          export DB_USER="${{ vars.AWS_POSTGRES_USERNAME }}"
          export DB_PASSWORD="${{ secrets.AWS_POSTGRES_PASSWORD }}"
          export DB_HOST="${{ vars.AWS_POSTGRES_HOST }}"
          export DB_PORT="${{ vars.AWS_POSTGRES_PORT }}"
          export ALEMBIC_ENV="${{ github.event.inputs.target_environment }}"
          export ALEMBIC_MIGRATION_FILE="${{ github.event.inputs.migration_script_name }}.py"
          echo "ALEMBIC_DB_URL=$ALEMBIC_DB_URL" >> $GITHUB_ENV
          echo "DB_NAME=$DB_NAME" >> $GITHUB_ENV
          echo "DB_USER=$DB_USER" >> $GITHUB_ENV
          echo "DB_PASSWORD=$DB_PASSWORD" >> $GITHUB_ENV
          echo "DB_HOST=$DB_HOST" >> $GITHUB_ENV
          echo "DB_PORT=$DB_PORT" >> $GITHUB_ENV
          echo "ALEMBIC_ENV=$ALEMBIC_ENV" >> $GITHUB_ENV
          echo "ALEMBIC_MIGRATION_FILE=$ALEMBIC_MIGRATION_FILE" >> $GITHUB_ENV

      - name: Verify Migration Script (If Specified)
        if: ${{ github.event.inputs.migration_script_name != '' }}
        working-directory: src/database/migrations/versions
        run: |
          MIGRATION_FILE="${{ github.event.inputs.migration_script_name }}.py"
          if [ ! -f "$MIGRATION_FILE" ]; then
            echo "❌ Migration script not found: $MIGRATION_FILE"
            exit 1
          else
            echo "✅ Found migration script: $MIGRATION_FILE"
          fi

      - name: Apply Alembic Migration
        working-directory: src/database
        run: |
          export PYTHONPATH=$PYTHONPATH:$(pwd)/../../
          if [[ -z "${{ github.event.inputs.migration_script_name }}" ]]; then
            alembic -c alembic.ini upgrade head
          else
            alembic -c alembic.ini upgrade ${{ github.event.inputs.migration_script_name }}
          fi
        env:
          ALEMBIC_DB_URL: ${{ env.ALEMBIC_DB_URL }}

      - name: Confirm Applied Migrations
        working-directory: src/database
        run: |
          export PYTHONPATH=$PYTHONPATH:$(pwd)/../../
          alembic -c alembic.ini current
        env:
          ALEMBIC_DB_URL: ${{ env.ALEMBIC_DB_URL }}

      - name: Success Notification
        if: success()
        run: |
          echo "✅ Migration ${{ github.event.inputs.migration_script_name }} applied successfully to ${{ github.event.inputs.target_environment }}."

      - name: Failure Notification
        if: failure()
        run: |
          echo "❌ Migration ${{ github.event.inputs.migration_script_name }} failed for ${{ github.event.inputs.target_environment }}."
          exit 1
