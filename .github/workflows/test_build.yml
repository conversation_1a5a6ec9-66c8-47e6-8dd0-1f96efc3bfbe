name: PR Created - Testing the Burst Build

on:
  pull_request:
    branches:
      - main
    paths:
      - "src/api/**"
      - "src/engine/**"
      - "src/accessor/**"
      - "src/manager/feed/**"
      - "src/util/**"
      - '!(.github/workflows/**)'
      - '!(src/api/deploy/demo/**)'
      - '!(src/api/deploy/prod/**)'
      - '!(src/api/deploy/client-demo/**)'
      - '!(src/api/deploy/sit/**)'
      - '!(src/manager/feed/deploy/demo/**)'
      - '!(src/manager/feed/deploy/prod/**)'
      - '!(src/manager/feed/deploy/client-demo/**)'
      - '!(src/manager/feed/deploy/sit/**)'

# Add concurrency control to prevent multiple workflows running simultaneously
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  ########################## DETERMINE ENVIRONMENT ##########################
  determine-environment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.determine_env.outputs.env }}

    steps:
      - name: determine environment
        id: determine_env
        run: |
          echo "env=dev" >> $GITHUB_ENV
          echo "env=dev" >> $GITHUB_OUTPUT

  ########################## CHECK FOR CHANGES ##########################
  check-changes:
    needs: determine-environment
    if: always()
    runs-on: ubuntu-latest
    outputs:
      api_path_changed: ${{ steps.check_paths.outputs.api_path_changed }}
      feed_manager_path_changed: ${{ steps.check_paths.outputs.feed_manager_path_changed }}

    steps:
      - name: checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: check for changes
        id: check_paths
        run: |
          # Check API changes
          if git diff --name-only HEAD^ | grep -q '^src/api/\|^src/engine/transformer/|^src/util|^src/accessor'; then
            echo "api_path_changed=true" >> $GITHUB_ENV
            echo "api_path_changed=true" >> $GITHUB_OUTPUT
            echo "API changes detected"
          else
            echo "api_path_changed=false" >> $GITHUB_ENV
            echo "api_path_changed=false" >> $GITHUB_OUTPUT
            echo "No API changes detected"
          fi

          # Check Feed Manager changes
          if git diff --name-only HEAD^ | grep -qE '^src/accessor/content|^src/accessor/document|^src/accessor/ai|^src/engine/parsing|^src/engine/synthesis|^src/engine/transformer|^src/manager/feed|^src/util'; then
            echo "feed_manager_path_changed=true" >> $GITHUB_ENV
            echo "feed_manager_path_changed=true" >> $GITHUB_OUTPUT
            echo "Feed Manager changes detected"
          else
            echo "feed_manager_path_changed=false" >> $GITHUB_ENV
            echo "feed_manager_path_changed=false" >> $GITHUB_OUTPUT
            echo "No Feed Manager changes detected"
          fi

  ########################## SETUP COMMON INFRASTRUCTURE ##########################
  setup-infrastructure:
    needs: [determine-environment, check-changes]
    if: always()
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ needs.determine-environment.outputs.environment }}
    steps:
      - name: configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.29.4"

      - name: update kubeconfig
        run: aws eks update-kubeconfig --region ${{ vars.AWS_EKS_REGION }} --name phoenix-burst

      - name: login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

  ########################## SETUP API JOB ##########################
  setup-api:
    needs: [check-changes, setup-infrastructure]
    if: needs.check-changes.outputs.api_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      environment: ${{ needs.determine-environment.outputs.environment }}

    steps:
      - name: checkout code
        uses: actions/checkout@v4

      - name: set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: install dependencies
        working-directory: ./src/api
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: lint with flake8
        run: |
          pip install flake8 pytest
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  ########################## BUILD API JOB ##########################
  build-api:
    needs: [check-changes, setup-api, determine-environment]
    if: needs.check-changes.outputs.api_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 15

    env:
      IMAGE_NAME: 581701393095.dkr.ecr.us-east-1.amazonaws.com/phoenix-burst-api
      ENVIRONMENT: dev

    steps:
      - name: checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.docker-cache
          key: ${{ runner.os }}-docker-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-docker-

      - name: Build Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: src/api/Dockerfile
          push: false
          tags: ${{ env.IMAGE_NAME }}:${{ env.ENVIRONMENT }}-latest
          cache-from: type=local,src=/tmp/.docker-cache
          cache-to: type=local,dest=/tmp/.docker-cache-new,mode=max

      - name: Move cache
        run: |
          rm -rf /tmp/.docker-cache
          mv /tmp/.docker-cache-new /tmp/.docker-cache

  ########################## SETUP FEED MANAGER JOB ##########################
  setup-feed-manager:
    needs: [check-changes, setup-infrastructure, determine-environment]
    if: needs.check-changes.outputs.feed_manager_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      environment: ${{ needs.determine-environment.outputs.environment }}

    steps:
      - name: checkout code
        uses: actions/checkout@v4

      - name: set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12.4"
          cache: 'pip'

      - name: install dependencies
        working-directory: ./src/manager/feed
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: lint with flake8
        run: |
          pip install flake8 pytest
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  ########################## BUILD FEED MANAGER JOB ##########################
  build-feed-manager:
    needs: [check-changes, setup-feed-manager, determine-environment]
    if: needs.check-changes.outputs.feed_manager_path_changed == 'true'
    runs-on: ubuntu-22.04
    timeout-minutes: 15

    env:
      IMAGE_NAME: 581701393095.dkr.ecr.us-east-1.amazonaws.com/feed-manager
      ENVIRONMENT: dev

    steps:
      - name: checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Clean up disk space
        run: |
          docker system prune --volumes -f
          docker image prune -f
          sudo rm -rf /usr/share/dotnet /opt/ghc /usr/local/share/boost "$AGENT_TOOLSDIRECTORY"
          sudo rm -rf /usr/local/lib/android /usr/local/.ghcup /usr/share/swift
          sudo rm -rf /tmp/*
          sudo journalctl --vacuum-time=1s || true

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.docker-cache
          key: ${{ runner.os }}-docker-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-docker-

      - name: Build Docker image
        uses: docker/build-push-action@v5
        env:
          DOCKER_BUILDKIT: 0
        with:
          context: .
          file: src/manager/feed/Dockerfile
          push: false
          tags: ${{ env.IMAGE_NAME }}:${{ env.ENVIRONMENT }}-latest
          cache-from: type=local,src=/tmp/.docker-cache
          cache-to: type=local,dest=/tmp/.docker-cache-new,mode=max

      - name: Move cache
        run: |
          rm -rf /tmp/.docker-cache
          mv /tmp/.docker-cache-new /tmp/.docker-cache
