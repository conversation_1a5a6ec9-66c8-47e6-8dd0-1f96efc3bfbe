name: Deploy <PERSON><PERSON><PERSON> Backend

on:
  push:
    branches:
      - main
    paths:
      - "src/api/**"
      - "src/engine/**"
      - "src/accessor/**"
      - "src/manager/feed/**"
      - "src/util/**"
      - '!(.github/workflows/**)'
      - '!(src/api/deploy/demo/**)'
      - '!(src/api/deploy/prod/**)'
      - '!(src/api/deploy/client-demo/**)'
      - '!(src/api/deploy/sit/**)'
      - '!(src/manager/feed/deploy/demo/**)'
      - '!(src/manager/feed/deploy/prod/**)'
      - '!(src/manager/feed/deploy/client-demo/**)'
      - '!(src/manager/feed/deploy/sit/**)'
  workflow_dispatch:
    inputs:
      environment:
        description: "Deploy Environment"
        required: true
        default: "dev"
        type: choice
        options: ["dev", "sit", "prod", "demo", "client-demo"]
      version:
        description: "Version Tag (ex. v0.0.1)"
        required: false
        type: string
      skip_api_deploy:
        description: "Skip the API Deployment"
        required: true
        default: "false"
        type: choice
        options: ["true", "false"]
      skip_feed_manager_deploy:
        description: "Skip the Feed Manager Deployment"
        required: true
        default: "false"
        type: choice
        options: ["true", "false"]

# Add concurrency control to prevent multiple workflows running simultaneously
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  ########################## DETERMINE ENVIRONMENT ##########################
  determine-environment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.determine_env.outputs.env }}
      skip_api_deploy: ${{ github.event.inputs.skip_api_deploy }}
      skip_feed_manager_deploy: ${{ github.event.inputs.skip_feed_manager_deploy }}

    steps:
      - name: Determine Environment
        id: determine_env
        run: |
          if [[ "${{ github.event_name }}" == "push" && "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "env=dev" >> $GITHUB_ENV
            echo "env=dev" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "env=${{ github.event.inputs.environment }}" >> $GITHUB_ENV
            echo "env=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          else
            echo "env=dev" >> $GITHUB_ENV
            echo "env=dev" >> $GITHUB_OUTPUT
          fi

  ########################## UPDATE LANGFUSE PROMPT LABELS ##########################
  update-langfuse-prompt-labels:
    needs: determine-environment
    if: needs.determine-environment.outputs.environment != 'dev'
    runs-on: ubuntu-latest
    env:
      LANGFUSE_HOST: ${{ vars.LANGFUSE_HOST }}
      ENVIRONMENT: ${{ needs.determine-environment.outputs.environment }}
      LANGFUSE_AUTH_HEADER: ${{ secrets.LANGFUSE_AUTH_HEADER }}
  
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Make Script Executable
        run: chmod +x .github/scripts/update_prompt_labels.sh

      - name: Run Langfuse Prompt Label Update
        run: .github/scripts/update_prompt_labels.sh

  ########################## FETCH PROPOSED VERSION ##########################
  fetch-proposed-version:
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.fetch_tag.outputs.new_tag }}
      release_name: ${{ steps.fetch_tag.outputs.release_name }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Fetch Proposed Tag
        id: fetch_tag
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" && -n "${{ github.event.inputs.version }}" ]]; then
            NEW_TAG="${{ github.event.inputs.version }}"
          else
            git fetch --tags
            LAST_TAG=$(git describe --tags `git rev-list --tags --max-count=1` 2>/dev/null) || LAST_TAG=""
            if [ -z "$LAST_TAG" ]; then
              NEW_TAG="v0.0.1"
            else
              VERSION_PART=$(echo $LAST_TAG | grep -oP '(?<=v)\d+\.\d+\.\d+')
              BASE_TAG=$(echo $VERSION_PART | awk -F. -v OFS=. '{$NF++; print}')
              NEW_TAG="v${BASE_TAG}"
            fi
          fi
          echo "new_tag=$NEW_TAG" >> $GITHUB_OUTPUT
          echo "release_name=Release_${NEW_TAG#v}" >> $GITHUB_OUTPUT

  ########################## SETUP COMMON INFRASTRUCTURE ##########################
  setup-infrastructure:
    needs: determine-environment
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.29.4"

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --region ${{ vars.AWS_EKS_REGION }} --name phoenix-burst

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

  ########################## CHECK FOR CHANGES ##########################
  check-changes:
    needs: [determine-environment, setup-infrastructure]
    runs-on: ubuntu-latest
    outputs:
      api_path_changed: ${{ steps.check_paths.outputs.api_path_changed }}
      feed_manager_path_changed: ${{ steps.check_paths.outputs.feed_manager_path_changed }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for Changes
        id: check_paths
        run: |
          # Check API changes
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ github.event.inputs.skip_api_deploy }}" == 'false' ]]; then
              echo "api_path_changed=true" >> $GITHUB_ENV
              echo "api_path_changed=true" >> $GITHUB_OUTPUT
            else
              echo "api_path_changed=false" >> $GITHUB_ENV
              echo "api_path_changed=false" >> $GITHUB_OUTPUT
            fi
          elif git diff --name-only HEAD^ | grep -q '^src/api/\|^src/engine/transformer/|^src/util|^src/accessor'; then
            echo "api_path_changed=true" >> $GITHUB_ENV
            echo "api_path_changed=true" >> $GITHUB_OUTPUT
          else
            echo "api_path_changed=false" >> $GITHUB_ENV
            echo "api_path_changed=false" >> $GITHUB_OUTPUT
          fi

          # Check Feed Manager changes
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            if [[ "${{ github.event.inputs.skip_feed_manager_deploy }}" == 'false' ]]; then
              echo "feed_manager_path_changed=true" >> $GITHUB_ENV
              echo "feed_manager_path_changed=true" >> $GITHUB_OUTPUT
            else
              echo "feed_manager_path_changed=false" >> $GITHUB_ENV
              echo "feed_manager_path_changed=false" >> $GITHUB_OUTPUT
            fi
          elif git diff --name-only HEAD^ | grep -qE '^src/accessor/content|^src/accessor/document|^src/accessor/ai|^src/engine/parsing|^src/engine/synthesis|^src/engine/transformer|^src/manager/feed|^src/util'; then
            echo "feed_manager_path_changed=true" >> $GITHUB_ENV
            echo "feed_manager_path_changed=true" >> $GITHUB_OUTPUT
          else
            echo "feed_manager_path_changed=false" >> $GITHUB_ENV
            echo "feed_manager_path_changed=false" >> $GITHUB_OUTPUT
          fi

  ########################## SETUP API JOB ##########################
  setup-api:
    needs: [check-changes, setup-infrastructure, determine-environment]
    if: needs.check-changes.outputs.api_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      environment: ${{ needs.determine-environment.outputs.environment }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.10"
          cache: 'pip'

      - name: Install Dependencies
        working-directory: ./src/api
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with flake8
        run: |
          pip install flake8 pytest
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  ########################## BUILD API JOB ##########################
  build-api:
    needs: [check-changes, setup-api, determine-environment, fetch-proposed-version]
    if: needs.check-changes.outputs.api_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 15

    env:
      IMAGE_NAME: 581701393095.dkr.ecr.us-east-1.amazonaws.com/phoenix-burst-api
      ENVIRONMENT: ${{ needs.determine-environment.outputs.environment }}

    outputs:
      environment: ${{ steps.set_env.outputs.environment }}
      new_tag: ${{ needs.fetch-proposed-version.outputs.new_tag }}
      release_name: ${{ needs.fetch-proposed-version.outputs.release_name }}
      skip_api_deploy: ${{ github.event.inputs.skip_api_deploy }}
      was_built: "true"

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set Environment Output
        id: set_env
        run: echo "::set-output name=environment::${{ env.ENVIRONMENT }}"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        run: aws ecr get-login-password --region ${{ vars.AWS_ECR_REGION }} | docker login --username ${{ vars.DOCKER_USER }} --password-stdin ${{ vars.AWS_ECR_REGISTRY }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker Layers
        uses: actions/cache@v4
        with:
          path: /tmp/.docker-cache
          key: ${{ runner.os }}-docker-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-docker-

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: src/api/Dockerfile
          push: true
          tags: |
            ${{ env.IMAGE_NAME }}:${{ env.ENVIRONMENT }}-latest
            ${{ env.IMAGE_NAME }}:${{ env.ENVIRONMENT }}-${{ needs.fetch-proposed-version.outputs.new_tag }}
          cache-from: type=local,src=/tmp/.docker-cache
          cache-to: type=local,dest=/tmp/.docker-cache-new,mode=max
          no-cache-filters: |
            src/api/requirements.txt
            src/api/Dockerfile

      - name: Move Cache
        run: |
          rm -rf /tmp/.docker-cache
          mv /tmp/.docker-cache-new /tmp/.docker-cache

  ########################## SETUP FEED MANAGER JOB ##########################
  setup-feed-manager:
    needs: [check-changes, setup-infrastructure, determine-environment, fetch-proposed-version]
    if: needs.check-changes.outputs.feed_manager_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      environment: ${{ needs.determine-environment.outputs.environment }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12.4"
          cache: 'pip'

      - name: Install Dependencies
        working-directory: ./src/manager/feed
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with flake8
        run: |
          pip install flake8 pytest
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

  ########################## BUILD FEED MANAGER JOB ##########################
  build-feed-manager:
    needs: [check-changes, setup-feed-manager, determine-environment, fetch-proposed-version]
    if: needs.check-changes.outputs.feed_manager_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 15

    env:
      IMAGE_NAME: 581701393095.dkr.ecr.us-east-1.amazonaws.com/feed-manager
      ENVIRONMENT: ${{ needs.determine-environment.outputs.environment }}

    outputs:
      environment: ${{ steps.set_env.outputs.environment }}
      new_tag: ${{ needs.fetch-proposed-version.outputs.new_tag }}
      release_name: ${{ needs.fetch-proposed-version.outputs.release_name }}
      skip_feed_manager_deploy: ${{ github.event.inputs.skip_feed_manager_deploy }}
      was_built: "true"

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set Environment Output
        id: set_env
        run: echo "::set-output name=environment::${{ env.ENVIRONMENT }}"

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        run: aws ecr get-login-password --region ${{ vars.AWS_ECR_REGION }} | docker login --username ${{ vars.DOCKER_USER }} --password-stdin ${{ vars.AWS_ECR_REGISTRY }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Show Runner Specs
        run: |
          echo "Memory Info:" && free -m
          echo "Disk Usage:" && df -h
          echo "Runner Kernel:" && uname -a
          echo "Docker Version:" && docker version

      - name: Clean Up Disk Space
        run: |
          docker system prune --volumes -f
          docker image prune -f
          sudo rm -rf /usr/share/dotnet /opt/ghc /usr/local/share/boost "$AGENT_TOOLSDIRECTORY"
          sudo rm -rf /usr/local/lib/android /usr/local/.ghcup /usr/share/swift
          sudo rm -rf /tmp/*
          sudo journalctl --vacuum-time=1s || true

      - name: Cache Docker Layers
        uses: actions/cache@v4
        with:
          path: /tmp/.docker-cache
          key: ${{ runner.os }}-docker-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-docker-

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: src/manager/feed/Dockerfile
          push: true
          tags: |
            ${{ env.IMAGE_NAME }}:${{ env.ENVIRONMENT }}-latest
            ${{ env.IMAGE_NAME }}:${{ env.ENVIRONMENT }}-${{ needs.fetch-proposed-version.outputs.new_tag }}
          cache-from: type=local,src=/tmp/.docker-cache
          cache-to: type=local,dest=/tmp/.docker-cache-new,mode=max
          no-cache-filters: |
            src/manager/feed/requirements.txt
            src/manager/feed/Dockerfile

      - name: Move Cache
        run: |
          rm -rf /tmp/.docker-cache
          mv /tmp/.docker-cache-new /tmp/.docker-cache

  ########################## DEPLOY API JOB ##########################
  deploy-api:
    needs: [check-changes, build-api]
    if: needs.check-changes.outputs.api_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      environment: ${{ needs.build-api.outputs.environment }}

    env:
      ENVIRONMENT: ${{ needs.build-api.outputs.environment }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.29.4"

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --region ${{ vars.AWS_EKS_REGION }} --name phoenix-burst

      - name: Create GitHub Deployment
        id: create_deployment
        uses: chrnorm/deployment-action@releases/v1
        with:
          environment: ${{ env.ENVIRONMENT }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Apply Deployment Config
        run: kubectl apply -f ./src/api/deploy/${{ env.ENVIRONMENT }}/deployment.yaml --validate=false

      - name: Restart Deployment
        run: kubectl rollout restart deployment/phoenix-burst-api-deployment -n ${{ env.ENVIRONMENT }}-phoenix-burst

      - name: Update Deployment Status
        uses: chrnorm/deployment-status@releases/v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          environment: ${{ env.ENVIRONMENT }}
          deployment_id: ${{ steps.create_deployment.outputs.deployment_id }}
          state: success

  ########################## DEPLOY FEED MANAGER JOB ##########################
  deploy-feed-manager:
    needs: [check-changes, build-feed-manager]
    if: needs.check-changes.outputs.feed_manager_path_changed == 'true'
    runs-on: ubuntu-latest
    timeout-minutes: 10
    outputs:
      environment: ${{ needs.build-feed-manager.outputs.environment }}

    env:
      ENVIRONMENT: ${{ needs.build-feed-manager.outputs.environment }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.29.4"

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --region ${{ vars.AWS_EKS_REGION }} --name phoenix-burst

      - name: Create GitHub Deployment
        id: create_deployment
        uses: chrnorm/deployment-action@releases/v1
        with:
          environment: ${{ env.ENVIRONMENT }}
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Apply Deployment Config
        run: kubectl apply -f ./src/manager/feed/deploy/${{ env.ENVIRONMENT }}/deployment.yaml --validate=false

      - name: Restart Deployment
        run: kubectl rollout restart deployment/phoenix-burst-feed-manager-deployment -n ${{ env.ENVIRONMENT }}-phoenix-burst

      - name: Update Deployment Status
        uses: chrnorm/deployment-status@releases/v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          environment: ${{ env.ENVIRONMENT }}
          deployment_id: ${{ steps.create_deployment.outputs.deployment_id }}
          state: success

  ########################## CREATE TAG AND RELEASE ##########################
  create-tag-and-release:
    needs: [build-api, deploy-api, build-feed-manager, deploy-feed-manager, fetch-proposed-version]
    runs-on: ubuntu-latest
    if: always()

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Tag Release
        if: github.event.inputs.version == '' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          git config --global user.email "github-actions[bot]@users.noreply.github.com"
          git config --global user.name "GitHub Actions"
          git tag -a ${{ needs.fetch-proposed-version.outputs.new_tag }} -m "Release ${{ needs.fetch-proposed-version.outputs.new_tag }}"
          git push origin ${{ needs.fetch-proposed-version.outputs.new_tag }}

      - name: Create Release
        if: github.event.inputs.version == '' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ needs.fetch-proposed-version.outputs.new_tag }}
          release_name: ${{ needs.fetch-proposed-version.outputs.release_name }}
          body: "Release ${{ needs.fetch-proposed-version.outputs.new_tag }}"
          draft: false
          prerelease: false

  ########################## CHECK DEPLOYMENT STATUS ##########################
  check-deployment-status:
    needs: [setup-infrastructure, deploy-api, deploy-feed-manager, determine-environment]
    if: always()
    runs-on: ubuntu-latest
    timeout-minutes: 15

    env:
      ENVIRONMENT: ${{ needs.determine-environment.outputs.environment }}

    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ vars.AWS_EKS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_EKS_SECRET_ACCESS_KEY }}
          aws-region: ${{ vars.AWS_EKS_REGION }}

      - name: Install kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: "v1.29.4"

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --region ${{ vars.AWS_EKS_REGION }} --name phoenix-burst

      - name: Check Feed Manager Deployment Status
        if: needs.deploy-feed-manager.result == 'success'
        run: kubectl rollout status deployment/phoenix-burst-feed-manager-deployment -n ${{ env.ENVIRONMENT }}-phoenix-burst --timeout=15m

      - name: Check API Deployment Status
        if: needs.deploy-api.result == 'success'
        run: kubectl rollout status deployment/phoenix-burst-api-deployment -n ${{ env.ENVIRONMENT }}-phoenix-burst --timeout=15m


  ########################## UPDATE CONFLUENCE ##########################
  update-confluence:
    runs-on: ubuntu-latest
    needs: [create-tag-and-release, check-deployment-status, determine-environment]
    if: always()

    env:
      REPO: phoenix-burst-api
      ENVIRONMENT: ${{ needs.determine-environment.outputs.environment }}
      NEW_TAG: ${{ needs.fetch-proposed-version.outputs.new_tag }}
      DATE: ${{ github.run_started_at }}
      LOGS_LINK: "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
      SKIPPED_API_DEPLOY: ${{ needs.determine-environment.outputs.skip_api_deploy }}
      SKIPPED_FEED_MANAGER_DEPLOY: ${{ needs.determine-environment.outputs.skip_feed_manager_deploy }}

    steps:
      - name: Fetch Confluence Page Details
        id: fetch_confluence
        run: |
          curl -u ${{ secrets.CONFLUENCE_USER }}:${{ secrets.CONFLUENCE_API_TOKEN }} \
               -X GET "https://thepog.atlassian.net/wiki/rest/api/content/${{ vars.CONFLUENCE_PAGE_ID }}?expand=body.storage,version" \
               -H "Accept: application/json" > confluence_page.json
  
      - name: Extract Current Page Version and Content
        id: extract_version_content
        run: |
          CURRENT_VERSION=$(jq '.version.number' confluence_page.json)
          CURRENT_CONTENT=$(jq -r '.body.storage.value' confluence_page.json)
          echo "CURRENT_VERSION=$CURRENT_VERSION" >> $GITHUB_ENV
          echo "CURRENT_CONTENT=$CURRENT_CONTENT" >> $GITHUB_ENV
  
      - name: Construct New Content
        id: construct_content
        run: |
          NEW_ROW="<tr><td>${{ env.ENVIRONMENT }}</td><td>${{ github.ref_name }}</td><td>${{ env.NEW_TAG }}</td><td>${{ env.SKIPPED_API_DEPLOY }}</td><td>${{ env.SKIPPED_FEED_MANAGER_DEPLOY }}</td><td>${{ env.DATE }}</td><td>${{ env.STATUS }}</td><td>${{ github.sha }}</td><td><a href='${{ env.LOGS_LINK }}'>View Logs</a></td></tr>"
          UPDATED_CONTENT=$(echo "$CURRENT_CONTENT" | sed "s|</table>|$NEW_ROW</table>|")
          echo "UPDATED_CONTENT=$UPDATED_CONTENT" >> $GITHUB_ENV
  
      - name: Update Confluence Page
        run: |
          NEW_VERSION=$((CURRENT_VERSION + 1))
          PAYLOAD=$(jq -n --arg id "${{ vars.CONFLUENCE_PAGE_ID }}" \
                               --arg title "API+and+Feed+Manager+Deployments" \
                               --arg space "burstDocs" \
                               --arg content "$UPDATED_CONTENT" \
                               --argjson version "$NEW_VERSION" \
                               '{
                                 id: $id,
                                 type: "page",
                                 title: $title,
                                 space: {key: $space},
                                 body: {
                                   storage: {
                                     value: $content,
                                     representation: "storage"
                                   }
                                 },
                                 version: {
                                   number: $version
                                 }
                               }')
  
          curl -u ${{ secrets.CONFLUENCE_USER }}:${{ secrets.CONFLUENCE_API_TOKEN }} \
               -X PUT "https://thepog.atlassian.net/wiki/rest/api/content/${{ vars.CONFLUENCE_PAGE_ID }}" \
               -H "Content-Type: application/json" \
               -d "$PAYLOAD"
