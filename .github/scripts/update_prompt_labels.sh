#!/bin/bash

set -e  # Exit script if any command fails
set -o pipefail  # Prevents errors in a pipeline from being masked

echo "Starting Langfuse label update process for environment: $ENVIRONMENT"
echo "LANGFUSE_HOST: $LANGFUSE_HOST"
echo "ENVIRONMENT: $ENVIRONMENT"
echo "Using Auth Header (truncated for security): ${LANGFUSE_AUTH_HEADER:0:10}..."

# Initialize pagination variables
PAGE=1
TOTAL_PAGES=1

# Function to get the version for a specific label
get_version_for_label() {
    local PROMPT_NAME=$1
    local LABEL=$2
    local RESPONSE=$(curl -s -X GET "$LANGFUSE_HOST/api/public/v2/prompts/$PROMPT_NAME?label=$LABEL" \
        -H "Authorization: Basic $LANGFUSE_AUTH_HEADER" \
        -H "Content-Type: application/json")

    # Extract the version associated with the label
    echo "$RESPONSE" | jq -r '.version' | grep -E '^[0-9]+$' || echo ""
}

# Loop through pages
while [[ $PAGE -le $TOTAL_PAGES ]]; do
    echo "Fetching Page: $PAGE..."
    
    RESPONSE=$(curl -s -X GET "$LANGFUSE_HOST/api/public/v2/prompts?page=$PAGE" \
        -H "Authorization: Basic $LANGFUSE_AUTH_HEADER" \
        -H "Content-Type: application/json")
    
    TOTAL_PAGES=$(echo "$RESPONSE" | jq -r '.meta.totalPages // 1')

    if [[ -z "$TOTAL_PAGES" || "$TOTAL_PAGES" == "null" ]]; then
        echo "Error: Failed to fetch prompts from Langfuse (Page: $PAGE)."
        exit 1
    fi

    # Extract prompt names
    PROMPT_NAMES=($(echo "$RESPONSE" | jq -r '.data[].name' | grep -v 'null'))

    if [[ ${#PROMPT_NAMES[@]} -eq 0 ]]; then
        echo "No prompts found on Page $PAGE. Moving to next page..."
        PAGE=$((PAGE + 1))
        continue
    fi

    echo "Found ${#PROMPT_NAMES[@]} prompts to process on Page $PAGE."

    # Loop through each prompt on the page
    for PROMPT_NAME in "${PROMPT_NAMES[@]}"; do
        echo "Processing prompt: $PROMPT_NAME"

        # Get version for 'dev' label
        DEV_VERSION=$(get_version_for_label "$PROMPT_NAME" "dev")
        if [[ -z "$DEV_VERSION" ]]; then
            echo "Skipping $PROMPT_NAME: No 'dev' label found."
            continue
        fi

        # Get version for target environment label
        ENV_VERSION=$(get_version_for_label "$PROMPT_NAME" "$ENVIRONMENT")

        echo "Found 'dev' version: $DEV_VERSION | '$ENVIRONMENT' version: ${ENV_VERSION:-None}"

        # If versions match, skip
        if [[ "$DEV_VERSION" == "$ENV_VERSION" ]]; then
            echo "Versions already match for $PROMPT_NAME. Skipping..."
            continue
        fi

        # Construct new labels list
        PATCH_DATA=$(jq -c --arg env "$ENVIRONMENT" '{newLabels: ["dev", $env]}' <<< '{}')

        echo "Updating '$PROMPT_NAME' - Adding '$ENVIRONMENT' to Version: $DEV_VERSION"

        HTTP_STATUS=$(curl -s -o update_response.json -w "%{http_code}" -X PATCH "$LANGFUSE_HOST/api/public/v2/prompts/$PROMPT_NAME/versions/$DEV_VERSION" \
            -H "Authorization: Basic $LANGFUSE_AUTH_HEADER" \
            -H "Content-Type: application/json" \
            -d "$PATCH_DATA")

        if [[ "$HTTP_STATUS" -eq 200 ]]; then
            echo "Successfully added '$ENVIRONMENT' to Version: $DEV_VERSION for prompt: $PROMPT_NAME"
        else
            echo "Error updating labels for prompt: $PROMPT_NAME (HTTP $HTTP_STATUS)"
            cat update_response.json
            exit 1
        fi
    done

    # Move to the next page
    PAGE=$((PAGE + 1))
done

echo "Langfuse label update process completed successfully!"