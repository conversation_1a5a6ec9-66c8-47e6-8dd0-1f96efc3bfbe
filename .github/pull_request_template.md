^Please edit the title of the Pull Request to use the following format: [BAI-XXX] - short description of change

------------------------------- Fill out the items in the template below -------------------------------

## Description

Please include a summary of the changes, if necessary, and the issue(s) this PR addresses.

Related Jira Issue: [BAI-XXX](https://thepog.atlassian.net/browse/BAI-XXX)

## Type of Change

- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Other

## Screenshots (if applicable):

If there are any UI changes, please add screenshots to demonstrate them.

## Checklist:

- [ ] I have performed a self-review of my own code
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published in downstream modules
- [ ] I have included the Jira ticket number in the title of this PR
- [ ] The build is passing the Test Burst Build job after my changes
- [ ] My changes generate no new warnings

## Additional Notes:

Please add any additional information or context about the pull request here.
